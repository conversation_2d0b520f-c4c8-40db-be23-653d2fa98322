# Installation Management Architecture

## Overview

The Installation Management system is a foundational component of the Perkd application that orchestrates device registration, capability detection, permission management, and system configuration. This sophisticated system serves as the bridge between the mobile device's hardware capabilities and the application's feature set, ensuring optimal user experience through intelligent adaptation to device characteristics and user preferences.

The installation system operates as a centralized registry that maintains comprehensive device profiles, manages security permissions, handles network resilience, and provides real-time capability detection. It integrates deeply with the application's authentication, notification, payment, and location services to deliver a seamless, personalized experience across diverse mobile platforms and device configurations.

## Business Logic Architecture

### Core Responsibilities

The installation management system encompasses several critical business domains:

**Device Identity Management**: Establishes and maintains unique device identities through hardware fingerprinting, ensuring consistent user experience across app sessions while supporting multi-device scenarios for shared accounts.

**Capability Intelligence**: Dynamically detects and adapts to device hardware capabilities through sophisticated detection algorithms that implement business rules for payment method availability, biometric authentication compatibility, and platform-specific feature enablement. The system employs intelligent capability caching with selective refresh strategies that balance performance with accuracy, ensuring users always see the most current available features while minimizing resource consumption.

**Permission Orchestration**: Manages the complex lifecycle of system permissions across iOS and Android platforms, handling permission requests, status tracking, and user experience flows that guide users through permission granting processes.

**Network Resilience**: Implements sophisticated offline-first patterns with deferred synchronization, ensuring installation data remains consistent even during network interruptions while providing seamless recovery when connectivity is restored.

**Localization Intelligence**: Automatically detects and configures regional settings, language preferences, and cultural adaptations to provide localized experiences without manual user configuration.

### Advanced Business Logic Patterns

The installation system implements several sophisticated business logic patterns that demonstrate advanced architectural thinking:

#### Defensive Data Structure Management

**Null-Safe Initialization**: The system implements comprehensive null-safe initialization patterns that ensure data structures are always in a valid state. Permission arrays are defensively initialized (`CACHED.permissions = CACHED.permissions || []`) to prevent null reference errors, while object properties are validated before access to ensure consistent behavior across different initialization scenarios.

**Immutable Data Access**: All data access methods implement immutable return patterns using object spreading (`{ ...CACHED.device }`), ensuring that external code cannot accidentally mutate the installation state. This defensive programming pattern prevents data corruption and ensures that the installation system maintains authoritative control over its data structures.

**Graceful Degradation Patterns**: When optional data is unavailable or corrupted, the system implements graceful degradation that provides sensible defaults rather than failing completely. This ensures that core app functionality remains available even when peripheral installation data is compromised.

#### Sophisticated State Synchronization

**Optimistic Local Updates**: The installation system implements optimistic update patterns where local state changes are immediately applied and persisted, with server synchronization occurring asynchronously. This pattern ensures responsive user experience while maintaining eventual consistency with backend services.

**Selective Synchronization**: Different installation data components implement selective synchronization strategies based on their criticality and change frequency. High-frequency data like permissions use immediate synchronization, while stable data like device characteristics use lazy synchronization to optimize network usage.

**Conflict Resolution Strategies**: When synchronization conflicts occur, the system employs intelligent resolution strategies that prioritize user-initiated changes over system-detected changes, ensuring that user preferences are preserved while maintaining data consistency.

#### Edge Case Handling Excellence

**Token Duplication Prevention**: The token management system implements sophisticated duplication prevention that checks both token value and timestamp to prevent unnecessary server requests. This optimization reduces server load while ensuring that token updates are processed when genuinely needed.

**Capability Detection Resilience**: The capability detection system implements resilient error handling that continues operation even when individual capability checks fail. This ensures that partial capability information is available rather than complete capability detection failure.

**Permission State Reconciliation**: The permission management system implements continuous state reconciliation that handles edge cases such as permissions revoked through system settings, ensuring that the app's understanding of permission states remains accurate.

### Installation Lifecycle Management

The installation system follows a sophisticated lifecycle that adapts to various app states and user scenarios:

#### Fresh Installation Flow
1. **Device Fingerprinting**: Collects hardware identifiers, device characteristics, and platform information
2. **Capability Detection**: Probes available hardware features and platform capabilities
3. **Permission Assessment**: Evaluates required permissions and prepares request strategies
4. **Localization Setup**: Configures regional settings based on device locale and carrier information
5. **Service Registration**: Registers the installation with backend services for personalization and security

#### Upgrade and Migration Flow
1. **Version Comparison**: Detects app version changes and determines upgrade requirements
2. **Data Migration**: Handles installation data schema migrations and compatibility updates
3. **Capability Refresh**: Re-evaluates device capabilities that may have changed with OS updates
4. **Permission Reconciliation**: Updates permission status and handles new permission requirements
5. **Service Synchronization**: Synchronizes updated installation profile with backend services

#### Runtime Adaptation Flow
1. **Dynamic Capability Updates**: Responds to runtime changes in device capabilities (e.g., payment method additions)
2. **Permission State Changes**: Handles user-initiated permission changes and system-level revocations
3. **Network State Management**: Manages online/offline transitions and deferred operation queuing
4. **Carrier Changes**: Detects and responds to SIM card changes and carrier switching

#### Sophisticated Carrier Change Detection

The installation system implements advanced carrier change detection that goes beyond simple carrier name comparison:

**Multi-Factor Carrier Validation**: The system employs a sophisticated validation algorithm that compares carrier name, country code, and mobile country code to detect genuine carrier changes versus temporary network fluctuations. This prevents false positive carrier change notifications that could trigger unnecessary mobile number validation updates.

**Business Impact Cascade**: When a genuine carrier change is detected, the system implements a business logic cascade that automatically triggers mobile number validation updates through `MobileNumber.carrierChanged()`. This ensures that phone number validation rules are updated to match the new carrier's requirements, preventing authentication failures due to outdated validation patterns.

**State Persistence Strategy**: Carrier change detection implements a defensive persistence strategy where the carrier change flag is immediately persisted to prevent data loss during app crashes or unexpected terminations. This ensures that carrier change notifications are not lost and can be properly processed during subsequent app launches.

#### Android 13+ Notification Compliance

The installation system implements sophisticated Android 13+ compliance logic that demonstrates advanced platform adaptation:

**Version-Aware Notification Cleanup**: The system implements intelligent notification cleanup logic that specifically targets Android 13+ upgrade scenarios. When detecting an upgrade from pre-Android 13 to Android 13+, the system automatically clears all existing notifications using `notifee.cancelAllNotifications()`. This business rule addresses Android 13's stricter notification permission requirements by ensuring a clean notification state that complies with the new permission model.

**Graceful Error Handling**: The Android 13+ notification cleanup implements graceful error handling that logs cleanup failures without blocking the initialization process. This design recognizes that notification cleanup is an optimization rather than a critical requirement, ensuring that app functionality remains intact even if notification cleanup fails.

**Platform-Specific Business Logic**: The Android 13+ handling demonstrates the installation system's platform-aware business logic that adapts behavior based on detected platform capabilities and requirements. This pattern ensures that the app remains compliant with evolving platform requirements without requiring manual configuration or user intervention.

## User Experience Flow Architecture

### Permission Request Experience

The installation system provides sophisticated user experience flows for permission management that balance security requirements with user convenience:

**Contextual Permission Requests**: Rather than requesting all permissions at app launch, the system implements just-in-time permission requests that occur when users attempt to use features requiring specific permissions. This approach reduces permission fatigue and increases grant rates by providing clear context for why permissions are needed.

**Progressive Permission Disclosure**: The system employs a progressive disclosure pattern where basic permissions are requested first, followed by advanced permissions as users engage with more sophisticated features. This creates a natural permission escalation that feels organic to the user journey.

**Permission Recovery Flows**: When permissions are denied or revoked, the system provides intelligent recovery flows that guide users to system settings with clear instructions, while offering alternative functionality where possible to maintain app usability.

### Device Capability Adaptation

The user experience dynamically adapts based on detected device capabilities through sophisticated business logic patterns:

**Payment Method Discovery with Support Filtering**: The system implements a sophisticated payment method detection algorithm that not only identifies available payment providers but applies support filtering based on device capabilities and regional availability. The business logic ensures that only genuinely supported payment methods are presented to users by checking both platform capability (`supported: true`) and method-specific requirements. This prevents user frustration from attempting to use payment methods that appear available but fail during transaction processing.

**Dynamic Payment Method Grouping**: The payment capability detection implements intelligent grouping logic that organizes payment methods by type (e.g., digital wallets, card methods) while preserving method-specific configurations. Empty method objects indicate simple availability (like Apple Pay), while complex method objects with arrays indicate multiple options within a category (like multiple card types). This grouping strategy enables the UI to present payment options in a logical hierarchy that matches user mental models.

**Biometric Authentication Integration**: When biometric capabilities are detected, the system seamlessly integrates fingerprint or face recognition into authentication flows, while maintaining fallback options for devices without biometric hardware.

**Hardware Feature Utilization**: Advanced hardware features like NFC are automatically enabled when available, providing enhanced functionality such as tap-to-share or contactless interactions without requiring user configuration.

**App List Management with Regional Intelligence**: The system implements sophisticated app detection logic that combines global app definitions with country-specific app lists, applying a 30-app limit to prevent performance degradation. The business rule prioritizes country-specific apps over global apps when conflicts occur, ensuring users see the most relevant applications for their geographic region. The system also implements dynamic app list updates that can add or remove apps based on installation status changes, maintaining accuracy without requiring full re-initialization.

### Localization and Regional Adaptation

The installation system provides intelligent localization that goes beyond simple language translation:

**Cultural Adaptation**: The system adapts user interface patterns, date formats, number representations, and interaction paradigms based on detected regional preferences and cultural norms.

**Regulatory Compliance**: Regional settings automatically configure compliance features such as GDPR consent flows in European markets or specific privacy controls required by local regulations.

**Market-Specific Features**: The system enables or disables features based on regional availability, such as specific payment methods, loyalty programs, or merchant partnerships that vary by geographic market.

## Data Flow Architecture

### Installation Data Structure

The installation system maintains a comprehensive data structure that captures all aspects of device configuration and user preferences. This structure serves as the single source of truth for device capabilities and user settings throughout the application lifecycle.

The data architecture follows a hierarchical organization with distinct domains for device hardware, operating system characteristics, application configuration, carrier information, localization settings, hardware capabilities, security tokens, permission states, geographic data, installed applications, and payment configurations.

### Synchronization Patterns

The installation system implements sophisticated synchronization patterns that ensure data consistency across local storage and remote services:

**Optimistic Updates**: Local changes are immediately reflected in the user interface while background synchronization ensures server consistency, providing responsive user experience even during network latency.

**Conflict Resolution**: When synchronization conflicts occur, the system employs intelligent resolution strategies that prioritize user preferences while maintaining data integrity and security requirements.

**Deferred Operations**: Network operations that fail due to connectivity issues are automatically queued and retried when network connectivity is restored, ensuring no data loss during offline periods.

### Event-Driven Updates

The installation system leverages the application's event architecture to provide real-time updates across all system components:

**Permission Change Events**: When permission states change, events are automatically emitted to update relevant UI components and trigger necessary business logic adaptations.

**Capability Update Events**: Hardware capability changes trigger events that allow other system components to adapt their behavior and update available feature sets.

**Network State Events**: Online/offline transitions trigger events that manage deferred operations and update user interface elements to reflect current connectivity status.

## Security Architecture

### Data Protection Strategies

The installation system implements comprehensive security measures to protect sensitive device and user information:

**Encryption at Rest**: Sensitive installation data is encrypted using platform-specific secure storage mechanisms, ensuring protection against unauthorized access even if device storage is compromised.

**Secure Transmission**: All communication with backend services uses TLS encryption with certificate pinning to prevent man-in-the-middle attacks and ensure data integrity during transmission.

**Token Management**: Security tokens are managed through secure storage with automatic rotation and expiration handling to maintain authentication security over time.

### Privacy Compliance

The installation system is designed with privacy-by-design principles that ensure compliance with global privacy regulations:

**Data Minimization**: Only necessary device information is collected and stored, with regular audits to ensure data collection remains minimal and purposeful.

**User Consent**: All data collection and processing activities respect user consent preferences and provide clear mechanisms for users to control their data.

**Right to Deletion**: The system supports complete data deletion requests while maintaining necessary security and fraud prevention capabilities.

## Performance Optimization

### Initialization Performance

The installation system is optimized for fast application startup through several performance strategies:

**Staged Initialization**: Critical installation data is loaded first to enable core application functionality, while non-essential data is loaded in background processes to minimize startup time.

**Caching Strategies**: Frequently accessed installation data is cached in memory with intelligent cache invalidation to balance performance with data freshness.

**Lazy Loading**: Complex capability detection and permission checking operations are performed on-demand rather than during application startup to reduce initial load time.

### Runtime Performance

During application runtime, the installation system maintains optimal performance through:

**Efficient Data Access**: Installation data access patterns are optimized to minimize database queries and memory allocations during normal application operation.

**Background Processing**: Resource-intensive operations such as capability detection and server synchronization are performed on background threads to maintain UI responsiveness.

**Memory Management**: The installation system implements careful memory management to prevent memory leaks and optimize memory usage patterns throughout the application lifecycle.

## Integration Architecture

### System Integration Points

The installation system integrates with numerous application components and external services:

**Authentication System**: Provides device identity and biometric capability information to support secure authentication flows and multi-factor authentication scenarios.

**Notification Infrastructure**: Manages push notification tokens and permission states to ensure reliable message delivery and appropriate user notification experiences.

**Payment Processing**: Supplies payment capability information to enable appropriate payment method selection and secure transaction processing.

**Location Services**: Coordinates with location services to manage location permissions and provide geographic context for personalization features.

**Analytics and Monitoring**: Provides device and capability information to analytics systems while respecting user privacy preferences and regulatory requirements.

### External Service Dependencies

The installation system maintains connections with several external services:

**Backend Registration Services**: Communicates with backend APIs to register device installations and synchronize configuration data across user devices.

**Platform Services**: Integrates with iOS and Android platform services to access device capabilities, manage permissions, and handle platform-specific functionality.

**Third-Party SDKs**: Coordinates with payment providers, analytics services, and other third-party integrations to provide comprehensive functionality while maintaining security and privacy standards.

## Error Handling and Resilience

### Error Classification and Recovery

The installation system implements comprehensive error handling strategies that ensure robust operation under various failure conditions:

**Network Errors**: Connectivity issues are handled through automatic retry mechanisms with exponential backoff, while offline capabilities ensure continued operation during network outages.

**Permission Errors**: Permission denial scenarios trigger appropriate user guidance flows while maintaining application functionality through graceful degradation strategies.

**Hardware Errors**: Device capability detection failures are handled through fallback mechanisms that ensure basic functionality remains available even when advanced features cannot be detected.

### Monitoring and Diagnostics

The installation system includes comprehensive monitoring capabilities:

**Health Monitoring**: Continuous monitoring of installation system health with automatic alerting for critical failures or performance degradation.

**Diagnostic Logging**: Detailed logging of installation operations with appropriate privacy protections to enable effective troubleshooting and system optimization.

**Performance Metrics**: Collection of performance metrics to identify optimization opportunities and ensure system performance meets user experience requirements.

## Development Guidelines

### Implementation Patterns

When working with the installation system, developers should follow established patterns:

**Capability Checking**: Always verify device capabilities before attempting to use hardware-dependent features, with appropriate fallback mechanisms for unsupported capabilities.

**Permission Management**: Use the centralized permission management system rather than directly accessing platform permission APIs to ensure consistent behavior and proper event handling.

**Data Access**: Access installation data through provided getter methods rather than direct data structure access to ensure proper data encapsulation and consistency.

**Error Handling**: Implement comprehensive error handling for all installation system interactions, with appropriate user feedback and recovery mechanisms.

### Installation System Guidelines

#### Initialization Requirements

The Installation module must be initialized at app startup before using any of its features. The initialization process follows a specific sequence defined in `index.js`:

```javascript
// Initialization sequence in index.js
initSettings().then(() => {
    Splash.show();
    BugSnag.init();
    initNetwork();
    initInstallation().then(initLocation);  // Location depends on carrier country

    Screen.start('BootStart', { done: () => booted(true) }, {}, {}, 'boot');
});
```

The installation system initialization is critical for proper app functionality as it establishes device identity, detects capabilities, and configures regional settings that other systems depend on.

#### Data Access Patterns

All installation data should be accessed through the designated getter methods to ensure data consistency and proper cloning:

```javascript
// Correct data access patterns
const deviceInfo = Device.get();           // Returns cloned device data
const capabilities = Capabilities.get();   // Returns all capabilities
const biometrics = Capabilities.get('biometrics'); // Returns specific capability
const permissions = Permissions.get();     // Returns cloned permissions array
const tokens = Tokens.get();              // Returns cloned tokens object
```

**Why Getter Methods**: Direct access to the `CACHED` object should be avoided as it bypasses the cloning mechanism that prevents accidental mutations and ensures data integrity across the application.

#### Capability Checking Best Practices

Always check for the existence of device capabilities before using hardware-dependent features:

```javascript
// Check payment capabilities before showing payment options
const paymentCaps = Capabilities.get('payment');
if (paymentCaps.support && paymentCaps.support.includes('applepay')) {
    // Show Apple Pay option
}

// Check biometric capabilities before enabling biometric authentication
const biometricCaps = Capabilities.get('biometrics');
if (biometricCaps.support && biometricCaps.support.length > 0) {
    // Enable biometric authentication UI
}
```

#### Permission Management Guidelines

Update permissions through the dedicated API to ensure proper synchronization with backend services and event triggering:

```javascript
// Correct permission updates
import { refresh as refreshPermissions } from 'common/permissions';

// Refresh specific permission
refreshPermissions('camera').then(permission => {
    // Handle permission state change
});

// The installation system automatically updates its permission cache
// and triggers events when permissions change
```

#### Token Handling Protocols

Register and update tokens through the provided methods to maintain proper tracking and server synchronization:

```javascript
// Correct token management
Tokens.set('fcm', newFcmToken);  // Automatically handles server sync and persistence

// Check token existence
if (Tokens.has('fcm')) {
    // Token is available
}
```

#### Error Handling and Network Resilience

The module provides automatic retry for failed server updates when network connectivity is restored using the Defer system:

```javascript
// The system automatically handles network errors
// Failed operations are queued and retried when online
// No manual intervention required for basic network resilience

// Monitor network state for custom handling
$.Event.on(EVENT.Network.online, () => {
    // Network restored - deferred operations will automatically retry
});
```

#### Platform-Specific Considerations

**Android 13+ Notification Handling**: The system automatically handles Android 13+ notification requirements by clearing all notifications when upgrading from older versions:

```javascript
// Automatic Android 13+ handling in Device.init()
if (CACHED?.os?.version < '13' && DEVICE.ANDROID_13) {
    try {
        await notifee.cancelAllNotifications();
    } catch (err) {
        console.log('[Android13]clear reminders error', err);
    }
}
```

**Carrier Change Detection**: The system automatically detects carrier changes and triggers appropriate updates:

```javascript
// Automatic carrier change handling
if (changed) {
    $.log(`[Installation]Carrier changed from '${CACHED.carrier.name}' to '${name}'`, 'Carrier');
    MobileNumber.carrierChanged();  // Triggers mobile number validation update
}
```

#### Locale and Regional Settings

Consider locale settings when formatting dates, currencies, and implementing region-specific features:

```javascript
// Access locale information for formatting
const locale = Locale.get();
const primaryLanguage = Locale.language();  // Returns primary language code

// Use locale information for formatting
const formattedNumber = number.toLocaleString(locale.languages[0], {
    style: 'currency',
    currency: locale.currency
});
```

### Best Practices Summary

1. **Initialize First**: Always initialize the installation module before accessing any data
2. **Use Getter Methods**: Access data through getter methods, not directly from CACHED object
3. **Check Capabilities**: Verify capability support before using hardware features
4. **Handle Carrier Changes**: Implement appropriate responses to carrier change notifications
5. **Leverage Deferred Updates**: Use deferred updates when network operations may be unreliable
6. **Monitor Permission Changes**: Listen for permission change events to update UI accordingly
7. **Platform Awareness**: Be aware of platform-specific requirements like Android 13+ notification handling
8. **Locale Consideration**: Consider locale settings when formatting dates, currencies, and implementing regional features
9. **Error Resilience**: Rely on the built-in network resilience for automatic error recovery
10. **Event Integration**: Use the event system for responding to installation data changes

### Testing Strategies

The installation system requires comprehensive testing across multiple dimensions:

**Device Compatibility**: Testing across diverse device types, operating system versions, and hardware configurations to ensure broad compatibility.

**Permission Scenarios**: Testing various permission grant/denial scenarios to ensure robust permission handling and appropriate user experience flows.

**Network Conditions**: Testing under various network conditions including offline scenarios to verify resilience and data consistency.

**Upgrade Scenarios**: Testing application upgrades and data migration scenarios to ensure smooth user experience during app updates.

## Technical Implementation Architecture

### Core Module Structure

The installation system is implemented through a sophisticated module architecture centered around `src/lib/common/installation.js`, which provides a comprehensive API for device management and capability detection.

#### Installation Module Components

**Device Management (`Device`)**: Handles hardware identification, manufacturer detection, and IP address resolution. Implements special handling for Android 13+ notification requirements and maintains device fingerprinting for consistent identity across app sessions.

**Application Configuration (`App`)**: Manages application version tracking, environment detection, and build configuration. Provides version comparison capabilities for upgrade scenarios and environment-specific feature enablement.

**Carrier Intelligence (`Carrier`)**: Detects mobile carrier information, country codes, and SIM card changes. Implements carrier change detection with automatic mobile number validation updates and country code mapping for international users.

**Localization Engine (`Locale`)**: Configures regional settings, language preferences, and cultural adaptations. Integrates with the application's internationalization system to provide seamless localized experiences.

**Capability Detection (`Capabilities`)**: Dynamically probes device hardware capabilities including payment methods, biometric authentication, and NFC connectivity. Implements real-time capability updates and graceful degradation for unsupported features.

**Token Management (`Tokens`)**: Manages security tokens, push notification tokens, and authentication credentials. Implements automatic token refresh, expiration handling, and secure storage integration.

**Permission Orchestration (`Permissions`)**: Coordinates with the platform permission system to manage permission states, request flows, and user experience guidance. Provides event-driven permission updates and status tracking.

**Geographic Services (`Geo`)**: Handles location data storage and geographic context management. Implements GeoJSON standards for location representation and integrates with location services.

**Application Registry (`AppList`)**: Tracks installed applications and manages app-specific configurations. Provides country-specific app filtering and installation detection capabilities.

**Payment Integration (`Payments`)**: Detects available payment methods and manages payment capability information. Integrates with multiple payment providers and handles dynamic payment method updates.

### Data Persistence Architecture

The installation system implements a sophisticated data persistence strategy that balances performance, security, and reliability:

#### Persistent Storage Strategy

**Primary Storage**: Installation data is persisted using the application's AsyncStorage-based persistence layer (`src/lib/common/persist.js`), providing reliable key-value storage with automatic serialization and deserialization.

**Memory Caching**: Frequently accessed installation data is maintained in memory through the `CACHED` object, providing immediate access without storage I/O overhead while ensuring data consistency through proper cache invalidation.

**Secure Storage Integration**: Sensitive installation data such as tokens and credentials are stored using platform-specific secure storage mechanisms, ensuring protection against unauthorized access and maintaining encryption at rest.

#### Installation Data Structure

The installation system maintains a comprehensive data structure that serves as the device profile and configuration registry. The following structure represents the complete installation data format as implemented in the `CACHED` object:

```json
{
  "id": "57AF10DB-0A54-46E0-A38B-9D97C931923A",
  "device": {
    "uuid": "57AF10DB-0A54-46E0-A38B-9D97C931923A",
    "name": "Wilson's iPhone",
    "brand": "Apple",
    "manufacturer": "Apple",
    "model": "iPhone XS",
    "simulator": false,
    "ip": "*************"
  },
  "os": {
    "name": "ios",
    "version": "16.5"
  },
  "app": {
    "id": "me.perkd.dev",
    "version": "4.0.0",
    "env": "dev"
  },
  "carrier": {
    "name": "Singtel",
    "country": "SG",
    "mobileCountryCode": "65",
    "changed": false
  },
  "locale": {
    "languages": ["en", "zh-Hans", "zh-Hant", "ms", "id", "zh-Hant-HK", "ja", "ko"],
    "country": "SG",
    "currency": "SGD",
    "timeZone": "Asia/Singapore",
    "regions": [],
    "numberFormat": {
      "decimalSeparator": ".",
      "groupingSeparator": ","
    },
    "useMetric": true,
    "temperature": "celsius",
    "calendar": "gregorian"
  },
  "capabilities": [
    {
      "name": "payment",
      "support": ["applepay", "linepay", "alipay"]
    },
    {
      "name": "biometrics",
      "support": ["faceid"]
    },
    {
      "name": "connectivity",
      "support": ["nfc"]
    }
  ],
  "tokens": {
    "apns": {
      "token": "dcbede4f9426681f1841d913228c02dc28fded6627ddb3660f7865f29347316e",
      "modifiedAt": "2020-03-24T07:44:13.336Z"
    }
  },
  "permissions": [
    {
      "feature": "camera",
      "status": "allowed",
      "options": [],
      "grantedAt": "2020-03-24T03:21:38.925Z",
      "revokedAt": null
    },
    {
      "feature": "location",
      "status": "allowed",
      "options": ["whileInApp"],
      "grantedAt": "2020-03-23T14:53:07.755Z",
      "revokedAt": null
    }
  ],
  "geo": {
    "type": "Point",
    "coordinates": [103.8198, 1.3521]
  },
  "appList": ["grab", "foodpanda", "shopee"],
  "payments": {
    "applepay": {},
    "card": {
      "methods": []
    }
  }
}
```

**Data Structure Components**:

- **Device Information**: Hardware identifiers, model details, OS version, platform type, IP address, and simulator detection
- **Carrier Information**: Carrier name, country code, mobile country code mapping, SIM card detection, and carrier change notifications
- **Locale Settings**: Language preferences, regional settings, country and timezone information, currency and number formatting, temperature and measurement units, calendar preferences
- **Device Capabilities**: Payment method support (detected dynamically using `Payment.supportedMethods()`), biometric authentication types (FaceID, TouchID), hardware features (NFC), automatic capability detection at initialization
- **App Management**: App version tracking, environment detection, installed app detection, country-specific app filtering (limited to 30 apps)
- **Permission Tracking**: Permission status objects with feature, status, options, timestamps, system permission integration via the Permissions module, change detection and event triggering, compatible with react-native-permissions status mapping
- **Token Management**: Push notification tokens, token timestamp tracking, server synchronization, offline queueing with network event listeners
- **Location Tracking**: GeoJSON Point format, longitude/latitude coordinate storage ([longitude, latitude] order), location persistence
- **Payment Integration**: Detects available payment methods via `Payment.supportedMethods()`, groups payment methods by type, updates when payment capabilities change, integrates with payment providers like Apple Pay, Google Pay, and card systems
- **Network Resilience**: Offline operation support, update queueing using `Defer.add` mechanism, error handling for network-related failures, network event monitoring via `EVENT.online`

#### Installation Data Structure

The installation system maintains a comprehensive data structure that serves as the device profile and configuration registry. The following structure represents the complete installation data format as implemented in the `CACHED` object:

```json
{
  "id": "57AF10DB-0A54-46E0-A38B-9D97C931923A",
  "device": {
    "uuid": "57AF10DB-0A54-46E0-A38B-9D97C931923A",
    "name": "Wilson's iPhone",
    "brand": "Apple",
    "manufacturer": "Apple",
    "model": "iPhone XS",
    "simulator": false,
    "ip": "*************"
  },
  "os": {
    "name": "ios",
    "version": "16.5"
  },
  "app": {
    "id": "me.perkd.dev",
    "version": "4.0.0",
    "env": "dev"
  },
  "carrier": {
    "name": "Singtel",
    "country": "SG",
    "mobileCountryCode": "65",
    "changed": false
  },
  "locale": {
    "languages": ["en", "zh-Hans", "zh-Hant", "ms", "id", "zh-Hant-HK", "ja", "ko"],
    "country": "SG",
    "currency": "SGD",
    "timeZone": "Asia/Singapore",
    "regions": [],
    "numberFormat": {
      "decimalSeparator": ".",
      "groupingSeparator": ","
    },
    "useMetric": true,
    "temperature": "celsius",
    "calendar": "gregorian"
  },
  "capabilities": [
    {
      "name": "payment",
      "support": ["applepay", "linepay", "alipay"]
    },
    {
      "name": "biometrics",
      "support": ["faceid"]
    },
    {
      "name": "connectivity",
      "support": ["nfc"]
    }
  ],
  "tokens": {
    "apns": {
      "token": "dcbede4f9426681f1841d913228c02dc28fded6627ddb3660f7865f29347316e",
      "modifiedAt": "2020-03-24T07:44:13.336Z"
    }
  },
  "permissions": [
    {
      "feature": "camera",
      "status": "allowed",
      "options": [],
      "grantedAt": "2020-03-24T03:21:38.925Z",
      "revokedAt": null
    },
    {
      "feature": "location",
      "status": "allowed",
      "options": ["whileInApp"],
      "grantedAt": "2020-03-23T14:53:07.755Z",
      "revokedAt": null
    }
  ],
  "geo": {
    "type": "Point",
    "coordinates": [103.8198, 1.3521]
  },
  "appList": ["grab", "foodpanda", "shopee"],
  "payments": {
    "applepay": {},
    "card": {
      "methods": []
    }
  }
}
```

**Data Structure Components**:

- **Device Information**: Hardware identifiers, model details, OS version, platform type, IP address, and simulator detection
- **Carrier Information**: Carrier name, country code, mobile country code mapping, SIM card detection, and carrier change notifications
- **Locale Settings**: Language preferences, regional settings, country and timezone information, currency and number formatting, temperature and measurement units, calendar preferences
- **Device Capabilities**: Payment method support (detected dynamically), biometric authentication types (FaceID, TouchID), hardware features (NFC), automatic capability detection
- **App Management**: App version tracking, environment detection, installed app detection, country-specific app filtering
- **Permission Tracking**: Permission status objects with feature, status, options, timestamps, system permission integration, change detection and event triggering
- **Token Management**: Push notification tokens, token timestamp tracking, server synchronization, offline queueing with network event listeners
- **Location Tracking**: GeoJSON Point format, longitude/latitude coordinate storage ([longitude, latitude] order), location persistence
- **Payment Integration**: Available payment methods detection, payment method grouping by type, dynamic payment capability updates

#### Data Synchronization Mechanisms

**Deferred Operations**: The system implements a sophisticated deferred operation mechanism that queues failed network operations for automatic retry when connectivity is restored. This ensures data consistency even during network interruptions.

**Event-Driven Synchronization**: Installation data changes trigger automatic synchronization with backend services through the `InstallationService`, maintaining server-side consistency while providing immediate local updates.

**Conflict Resolution**: When synchronization conflicts occur, the system employs intelligent resolution strategies that prioritize user preferences while maintaining data integrity and security requirements.

### Network Resilience Implementation

The installation system implements comprehensive network resilience patterns that ensure robust operation under various connectivity conditions:

#### Sophisticated Deferred Operations Architecture

The installation system employs a sophisticated deferred operations mechanism that demonstrates advanced business logic for handling network failures and ensuring data consistency:

**Single-Instance Queuing Strategy**: The `Defer.add` mechanism implements a critical business rule where only one deferred operation instance can exist at a time. When a network operation fails, the system creates a deferred operation that consolidates all subsequent failures until network connectivity is restored. This prevents operation duplication and ensures efficient resource utilization.

**Intelligent Data Consolidation**: When multiple operations fail while a deferred operation is already queued, the system intelligently merges the new data with existing deferred data using `Object.assign(Defer.data, update)`. This consolidation strategy ensures that the most recent state is preserved while preventing data loss from intermediate operations.

**Event-Driven Recovery**: The deferred operation mechanism leverages the application's event system to monitor network state changes. When the `EVENT.online` event is triggered, the system automatically attempts to synchronize all queued data, demonstrating a reactive architecture that responds to environmental changes without manual intervention.

**Graceful Cleanup**: After successful synchronization, the deferred operation data is automatically cleared (`Defer.data = null`), ensuring that the system doesn't accumulate stale operation data and maintains optimal memory usage patterns.

#### Token Management Business Logic

The token management system implements sophisticated business rules that ensure security and user experience optimization:

**Duplicate Token Prevention**: The system implements a critical business rule that prevents unnecessary token updates when the same token is provided (`if (CACHED.notifyToken === token) return`). This optimization reduces server load and prevents redundant permission refresh operations that could impact user experience.

**Atomic Token Operations**: Token updates follow an atomic operation pattern where the token is first stored locally with a timestamp, then synchronized with the server. Only after successful server confirmation is the `notifyToken` cache updated, ensuring data consistency and preventing race conditions.

**Permission Synchronization Coupling**: Token updates automatically trigger notification permission refresh (`refreshPermissions(NOTIFICATIONS)`), demonstrating the tight coupling between token management and permission orchestration. This ensures that permission states remain synchronized with token availability.

**Selective Error Handling**: The token management system implements selective error handling that only defers operations for specific error types (network errors, timeouts, and request failures). Other error types are logged but not deferred, preventing the system from attempting to retry operations that are unlikely to succeed.

#### Permission State Management Sophistication

The permission management system demonstrates advanced state management patterns that ensure consistency and user experience optimization:

**Defensive Initialization**: The permission initialization process implements defensive programming by ensuring the permissions array is always initialized (`CACHED.permissions = CACHED.permissions || []`), preventing null reference errors and ensuring consistent data structures.

**Immediate Local Updates**: Permission changes are immediately reflected in local storage through the `persist()` call, ensuring that permission states are preserved even if server synchronization fails. This local-first approach maintains user experience continuity.

**Asymmetric Error Handling**: Permission synchronization implements asymmetric error handling where only network-related errors trigger deferred operations. This business rule recognizes that permission errors are often related to authorization or validation issues that won't be resolved by simple retry mechanisms.

#### Offline-First Architecture

**Local-First Operations**: All installation operations are designed to work locally first, with background synchronization ensuring server consistency without blocking user interactions.

**Intelligent Queuing**: Failed network operations are automatically queued using the `Defer` mechanism, which monitors network state changes and retries operations when connectivity is restored.

**Error Classification**: Network errors are classified into recoverable and non-recoverable categories, with appropriate retry strategies and user feedback mechanisms for each error type.

#### Connectivity Management

**Network State Monitoring**: The system integrates with the application's network monitoring infrastructure to respond to connectivity changes and manage deferred operations appropriately.

**Exponential Backoff**: Failed network operations implement exponential backoff retry strategies to prevent overwhelming servers during connectivity issues while ensuring eventual consistency.

**Graceful Degradation**: When network operations fail, the system provides graceful degradation that maintains core functionality while clearly communicating connectivity status to users.

## Architecture Diagrams

### Installation System Overview

The following diagram illustrates the high-level architecture of the installation management system and its integration points:

```mermaid
graph TB
    subgraph "📱 Installation Management Core"
        INSTALL[Installation Module<br/>src/lib/common/installation.js]
        DEVICE[Device Management<br/>Hardware Detection]
        CAPS[Capability Detection<br/>Feature Probing]
        PERMS[Permission Management<br/>Security Orchestration]
        TOKENS[Token Management<br/>Authentication & Notifications]
    end

    subgraph "💾 Data Persistence Layer"
        CACHE[Memory Cache<br/>CACHED Object]
        PERSIST[AsyncStorage<br/>Persistent Storage]
        SECURE[Secure Storage<br/>Keychain/Keystore]
        DEFER[Deferred Operations<br/>Network Resilience]
    end

    subgraph "🌐 External Integration"
        BACKEND[Backend Services<br/>Installation API]
        PLATFORM[Platform Services<br/>iOS/Android APIs]
        PAYMENT[Payment Providers<br/>Capability Detection]
        NOTIFY[Notification Services<br/>Token Management]
    end

    subgraph "🎯 Application Integration"
        AUTH[Authentication System<br/>Biometric & Security]
        LOCATION[Location Services<br/>Geographic Context]
        ANALYTICS[Analytics & Monitoring<br/>Device Insights]
        UX[User Experience<br/>Adaptive UI]
    end

    %% Core Installation Connections
    INSTALL --> DEVICE
    INSTALL --> CAPS
    INSTALL --> PERMS
    INSTALL --> TOKENS

    %% Data Persistence Connections
    DEVICE --> CACHE
    CAPS --> CACHE
    PERMS --> CACHE
    TOKENS --> CACHE
    CACHE --> PERSIST
    TOKENS --> SECURE
    INSTALL --> DEFER

    %% External Integration Connections
    INSTALL --> BACKEND
    DEVICE --> PLATFORM
    CAPS --> PLATFORM
    PERMS --> PLATFORM
    CAPS --> PAYMENT
    TOKENS --> NOTIFY

    %% Application Integration Connections
    PERMS --> AUTH
    DEVICE --> LOCATION
    INSTALL --> ANALYTICS
    CAPS --> UX

    %% Network Resilience
    DEFER --> BACKEND

    %% Styling with darker backgrounds and white text
    classDef core fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef persistence fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef external fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef integration fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff

    class INSTALL,DEVICE,CAPS,PERMS,TOKENS core
    class CACHE,PERSIST,SECURE,DEFER persistence
    class BACKEND,PLATFORM,PAYMENT,NOTIFY external
    class AUTH,LOCATION,ANALYTICS,UX integration
```

### Installation Lifecycle Flow

This diagram shows the complete installation lifecycle from app launch through runtime adaptation:

```mermaid
graph TB
    subgraph "🚀 App Launch Sequence"
        LAUNCH[App Launch<br/>index.js]
        INIT[Installation Init<br/>initInstallation()]
        RESTORE[Data Restoration<br/>Persist.get()]
        PARALLEL[Parallel Initialization<br/>All Components]
    end

    subgraph "🔍 Component Initialization"
        DEVICE_INIT[Device.init()<br/>Hardware Detection]
        APP_INIT[App.init()<br/>Version & Environment]
        CARRIER_INIT[Carrier.init()<br/>Network Provider]
        LOCALE_INIT[Locale.init()<br/>Regional Settings]
        CAPS_INIT[Capabilities.init()<br/>Feature Detection]
        TOKENS_INIT[Tokens.init()<br/>Security Tokens]
        PERMS_INIT[Permissions.init()<br/>System Permissions]
        PAYMENTS_INIT[Payments.init()<br/>Payment Methods]
    end

    subgraph "⚡ Runtime Operations"
        CAPABILITY_UPDATE[Capability Updates<br/>Dynamic Detection]
        PERMISSION_CHANGE[Permission Changes<br/>User Actions]
        TOKEN_REFRESH[Token Refresh<br/>Security Maintenance]
        NETWORK_SYNC[Network Sync<br/>Server Communication]
        CARRIER_CHANGE[Carrier Changes<br/>SIM Detection]
    end

    subgraph "💾 Data Management"
        LOCAL_PERSIST[Local Persistence<br/>AsyncStorage]
        MEMORY_CACHE[Memory Caching<br/>Performance]
        SECURE_STORE[Secure Storage<br/>Sensitive Data]
        DEFERRED_OPS[Deferred Operations<br/>Network Resilience]
    end

    subgraph "🔄 Synchronization"
        BACKEND_SYNC[Backend Sync<br/>Installation Service]
        CONFLICT_RESOLVE[Conflict Resolution<br/>Data Consistency]
        EVENT_EMIT[Event Emission<br/>System Updates]
        ERROR_HANDLE[Error Handling<br/>Resilience]
    end

    %% Launch Sequence Flow
    LAUNCH --> INIT
    INIT --> RESTORE
    RESTORE --> PARALLEL

    %% Parallel Initialization Flow
    PARALLEL --> DEVICE_INIT
    PARALLEL --> APP_INIT
    PARALLEL --> CARRIER_INIT
    PARALLEL --> LOCALE_INIT
    PARALLEL --> CAPS_INIT
    PARALLEL --> TOKENS_INIT
    PARALLEL --> PERMS_INIT
    PARALLEL --> PAYMENTS_INIT

    %% Runtime Operations Flow
    CAPS_INIT --> CAPABILITY_UPDATE
    PERMS_INIT --> PERMISSION_CHANGE
    TOKENS_INIT --> TOKEN_REFRESH
    CARRIER_INIT --> CARRIER_CHANGE

    %% Data Management Flow
    DEVICE_INIT --> LOCAL_PERSIST
    APP_INIT --> MEMORY_CACHE
    TOKENS_INIT --> SECURE_STORE
    CAPABILITY_UPDATE --> DEFERRED_OPS

    %% Synchronization Flow
    LOCAL_PERSIST --> BACKEND_SYNC
    BACKEND_SYNC --> CONFLICT_RESOLVE
    CONFLICT_RESOLVE --> EVENT_EMIT
    BACKEND_SYNC --> ERROR_HANDLE
    ERROR_HANDLE --> DEFERRED_OPS

    %% Cross-connections
    PERMISSION_CHANGE --> EVENT_EMIT
    TOKEN_REFRESH --> BACKEND_SYNC
    NETWORK_SYNC --> BACKEND_SYNC

    %% Styling with darker backgrounds and white text
    classDef launch fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef init fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef runtime fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef data fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef sync fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff

    class LAUNCH,INIT,RESTORE,PARALLEL launch
    class DEVICE_INIT,APP_INIT,CARRIER_INIT,LOCALE_INIT,CAPS_INIT,TOKENS_INIT,PERMS_INIT,PAYMENTS_INIT init
    class CAPABILITY_UPDATE,PERMISSION_CHANGE,TOKEN_REFRESH,NETWORK_SYNC,CARRIER_CHANGE runtime
    class LOCAL_PERSIST,MEMORY_CACHE,SECURE_STORE,DEFERRED_OPS data
    class BACKEND_SYNC,CONFLICT_RESOLVE,EVENT_EMIT,ERROR_HANDLE sync
```

### User Experience & Permission Flow

This diagram illustrates how the installation system manages user experience flows and permission requests:

```mermaid
graph TB
    subgraph "👤 User Journey Entry"
        USER[User Interaction<br/>Feature Access]
        FEATURE_REQ[Feature Request<br/>Capability Required]
        CONTEXT[Contextual Analysis<br/>Permission Assessment]
    end

    subgraph "🔐 Permission Management Flow"
        PERM_CHECK[Permission Check<br/>Current Status]
        PERM_GRANTED[Permission Granted<br/>Feature Available]
        PERM_DENIED[Permission Denied<br/>Request Required]
        PERM_BLOCKED[Permission Blocked<br/>Settings Required]
    end

    subgraph "🎯 User Experience Adaptation"
        CAPABILITY_ADAPT[Capability Adaptation<br/>Feature Availability]
        UI_ADJUST[UI Adjustment<br/>Dynamic Interface]
        FALLBACK[Fallback Options<br/>Alternative Features]
        GUIDANCE[User Guidance<br/>Permission Education]
    end

    subgraph "⚙️ Device Capability Detection"
        HARDWARE_PROBE[Hardware Probing<br/>Feature Detection]
        PAYMENT_DETECT[Payment Detection<br/>Method Availability]
        BIOMETRIC_CHECK[Biometric Check<br/>Authentication Options]
        PLATFORM_QUERY[Platform Query<br/>OS Capabilities]
    end

    subgraph "🌍 Localization & Regional"
        LOCALE_DETECT[Locale Detection<br/>Regional Settings]
        CULTURAL_ADAPT[Cultural Adaptation<br/>UI Patterns]
        REGULATORY[Regulatory Compliance<br/>Regional Requirements]
        MARKET_FEATURES[Market Features<br/>Geographic Availability]
    end

    subgraph "📱 Runtime Adaptation"
        DYNAMIC_UPDATE[Dynamic Updates<br/>Real-time Changes]
        STATE_SYNC[State Synchronization<br/>Cross-component Updates]
        EVENT_PROPAGATION[Event Propagation<br/>System Notifications]
        PERFORMANCE_OPT[Performance Optimization<br/>Resource Management]
    end

    %% User Journey Flow
    USER --> FEATURE_REQ
    FEATURE_REQ --> CONTEXT
    CONTEXT --> PERM_CHECK

    %% Permission Flow
    PERM_CHECK --> PERM_GRANTED
    PERM_CHECK --> PERM_DENIED
    PERM_CHECK --> PERM_BLOCKED
    PERM_DENIED --> GUIDANCE
    PERM_BLOCKED --> GUIDANCE

    %% Experience Adaptation Flow
    PERM_GRANTED --> CAPABILITY_ADAPT
    CAPABILITY_ADAPT --> UI_ADJUST
    PERM_DENIED --> FALLBACK
    PERM_BLOCKED --> FALLBACK

    %% Capability Detection Flow
    CONTEXT --> HARDWARE_PROBE
    HARDWARE_PROBE --> PAYMENT_DETECT
    HARDWARE_PROBE --> BIOMETRIC_CHECK
    HARDWARE_PROBE --> PLATFORM_QUERY

    %% Localization Flow
    CONTEXT --> LOCALE_DETECT
    LOCALE_DETECT --> CULTURAL_ADAPT
    LOCALE_DETECT --> REGULATORY
    LOCALE_DETECT --> MARKET_FEATURES

    %% Runtime Adaptation Flow
    CAPABILITY_ADAPT --> DYNAMIC_UPDATE
    UI_ADJUST --> STATE_SYNC
    STATE_SYNC --> EVENT_PROPAGATION
    DYNAMIC_UPDATE --> PERFORMANCE_OPT

    %% Cross-connections
    PAYMENT_DETECT --> UI_ADJUST
    BIOMETRIC_CHECK --> UI_ADJUST
    CULTURAL_ADAPT --> UI_ADJUST
    REGULATORY --> FALLBACK

    %% Styling with darker backgrounds and white text
    classDef user fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef permission fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef experience fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef capability fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef localization fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff
    classDef runtime fill:#d69e2e,stroke:#ed8936,stroke-width:2px,color:#ffffff

    class USER,FEATURE_REQ,CONTEXT user
    class PERM_CHECK,PERM_GRANTED,PERM_DENIED,PERM_BLOCKED permission
    class CAPABILITY_ADAPT,UI_ADJUST,FALLBACK,GUIDANCE experience
    class HARDWARE_PROBE,PAYMENT_DETECT,BIOMETRIC_CHECK,PLATFORM_QUERY capability
    class LOCALE_DETECT,CULTURAL_ADAPT,REGULATORY,MARKET_FEATURES localization
    class DYNAMIC_UPDATE,STATE_SYNC,EVENT_PROPAGATION,PERFORMANCE_OPT runtime
```

### Business Logic Decision Flow

This diagram illustrates the sophisticated business logic decision trees and validation patterns within the installation system:

```mermaid
graph TB
    subgraph "🔍 Capability Detection Logic"
        CAP_START[Capability Detection<br/>Request]
        PAYMENT_CHECK{Payment Methods<br/>Available?}
        SUPPORT_FILTER[Support Filtering<br/>Platform + Regional]
        METHOD_GROUP[Method Grouping<br/>Type-Based Organization]
        CAP_CACHE[Capability Caching<br/>Performance Optimization]
    end

    subgraph "🔄 Token Management Logic"
        TOKEN_UPDATE[Token Update<br/>Request]
        DUPLICATE_CHECK{Same Token<br/>Already Stored?}
        ATOMIC_STORE[Atomic Storage<br/>Local + Timestamp]
        SERVER_SYNC[Server Sync<br/>Backend Update]
        PERM_REFRESH[Permission Refresh<br/>Notification Sync]
    end

    subgraph "📡 Network Resilience Logic"
        NET_OP[Network Operation<br/>Attempt]
        NET_FAIL{Operation<br/>Failed?}
        ERROR_TYPE{Error Type<br/>Classification}
        DEFER_CHECK{Deferred Op<br/>Exists?}
        DATA_MERGE[Data Consolidation<br/>Object.assign]
        QUEUE_CREATE[Create Deferred<br/>Operation]
        NET_RECOVERY[Network Recovery<br/>EVENT.online]
    end

    subgraph "🏗️ Carrier Change Logic"
        CARRIER_DETECT[Carrier Detection<br/>SIM Analysis]
        MULTI_FACTOR{Multi-Factor<br/>Validation}
        CHANGE_CONFIRM[Change Confirmation<br/>Business Impact]
        MOBILE_UPDATE[Mobile Number<br/>Validation Update]
        STATE_PERSIST[State Persistence<br/>Crash Protection]
    end

    subgraph "🛡️ Permission Logic"
        PERM_UPDATE[Permission Update<br/>Request]
        DEFENSIVE_INIT[Defensive Init<br/>Array Validation]
        LOCAL_PERSIST[Local Persistence<br/>Immediate Storage]
        ASYNC_SYNC[Async Sync<br/>Server Update]
        ERROR_HANDLE{Error Type<br/>Analysis}
    end

    subgraph "🤖 Android 13+ Logic"
        VERSION_CHECK[Version Detection<br/>OS Analysis]
        UPGRADE_DETECT{Android 13+<br/>Upgrade?}
        NOTIF_CLEAR[Notification Cleanup<br/>Compliance Reset]
        GRACEFUL_ERROR[Graceful Error<br/>Non-Blocking]
    end

    %% Capability Detection Flow
    CAP_START --> PAYMENT_CHECK
    PAYMENT_CHECK -->|Yes| SUPPORT_FILTER
    PAYMENT_CHECK -->|No| CAP_CACHE
    SUPPORT_FILTER --> METHOD_GROUP
    METHOD_GROUP --> CAP_CACHE

    %% Token Management Flow
    TOKEN_UPDATE --> DUPLICATE_CHECK
    DUPLICATE_CHECK -->|No| ATOMIC_STORE
    DUPLICATE_CHECK -->|Yes| TOKEN_UPDATE
    ATOMIC_STORE --> SERVER_SYNC
    SERVER_SYNC --> PERM_REFRESH

    %% Network Resilience Flow
    NET_OP --> NET_FAIL
    NET_FAIL -->|Yes| ERROR_TYPE
    NET_FAIL -->|No| NET_OP
    ERROR_TYPE -->|Recoverable| DEFER_CHECK
    ERROR_TYPE -->|Non-Recoverable| NET_OP
    DEFER_CHECK -->|Exists| DATA_MERGE
    DEFER_CHECK -->|None| QUEUE_CREATE
    DATA_MERGE --> NET_RECOVERY
    QUEUE_CREATE --> NET_RECOVERY
    NET_RECOVERY --> NET_OP

    %% Carrier Change Flow
    CARRIER_DETECT --> MULTI_FACTOR
    MULTI_FACTOR -->|Valid Change| CHANGE_CONFIRM
    MULTI_FACTOR -->|False Positive| CARRIER_DETECT
    CHANGE_CONFIRM --> MOBILE_UPDATE
    MOBILE_UPDATE --> STATE_PERSIST

    %% Permission Flow
    PERM_UPDATE --> DEFENSIVE_INIT
    DEFENSIVE_INIT --> LOCAL_PERSIST
    LOCAL_PERSIST --> ASYNC_SYNC
    ASYNC_SYNC --> ERROR_HANDLE
    ERROR_HANDLE -->|Network Error| DEFER_CHECK
    ERROR_HANDLE -->|Other Error| PERM_UPDATE

    %% Android 13+ Flow
    VERSION_CHECK --> UPGRADE_DETECT
    UPGRADE_DETECT -->|Yes| NOTIF_CLEAR
    UPGRADE_DETECT -->|No| VERSION_CHECK
    NOTIF_CLEAR --> GRACEFUL_ERROR

    %% Cross-connections
    SERVER_SYNC --> NET_OP
    ASYNC_SYNC --> NET_OP
    MOBILE_UPDATE --> NET_OP

    %% Styling with darker backgrounds and white text
    classDef capability fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef token fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef network fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef carrier fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef permission fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff
    classDef android fill:#d69e2e,stroke:#ed8936,stroke-width:2px,color:#ffffff

    class CAP_START,PAYMENT_CHECK,SUPPORT_FILTER,METHOD_GROUP,CAP_CACHE capability
    class TOKEN_UPDATE,DUPLICATE_CHECK,ATOMIC_STORE,SERVER_SYNC,PERM_REFRESH token
    class NET_OP,NET_FAIL,ERROR_TYPE,DEFER_CHECK,DATA_MERGE,QUEUE_CREATE,NET_RECOVERY network
    class CARRIER_DETECT,MULTI_FACTOR,CHANGE_CONFIRM,MOBILE_UPDATE,STATE_PERSIST carrier
    class PERM_UPDATE,DEFENSIVE_INIT,LOCAL_PERSIST,ASYNC_SYNC,ERROR_HANDLE permission
    class VERSION_CHECK,UPGRADE_DETECT,NOTIF_CLEAR,GRACEFUL_ERROR android
```

## Integration Patterns

### Authentication System Integration

The installation system provides critical support for the authentication infrastructure through several integration patterns:

**Biometric Capability Provision**: The installation system detects available biometric authentication methods (FaceID, TouchID) and provides this information to the authentication system, enabling adaptive authentication flows that utilize the most secure available methods.

**Device Identity Management**: Unique device identifiers maintained by the installation system support device-based authentication policies and multi-device account management, ensuring consistent security across user devices.

**Permission Coordination**: The installation system coordinates biometric permission requests with the authentication system, ensuring users are guided through appropriate permission flows when biometric authentication is enabled.

### Notification Infrastructure Integration

The installation system serves as the foundation for the notification infrastructure through comprehensive token and permission management:

**Token Lifecycle Management**: Push notification tokens are managed through the installation system, including registration, renewal, and synchronization with backend services to ensure reliable message delivery.

**Permission State Tracking**: Notification permission states are continuously monitored and updated, enabling the notification system to adapt its behavior based on current permission status and provide appropriate user guidance.

**Platform-Specific Handling**: The installation system handles platform-specific notification requirements, such as Android 13+ notification channel management and iOS notification authorization levels.

### Payment System Integration

The payment processing infrastructure relies heavily on installation system capabilities for optimal user experience:

**Payment Method Detection**: The installation system dynamically detects available payment methods including Apple Pay, Google Pay, and various card processing capabilities, enabling the payment system to present appropriate options.

**Capability-Based UI Adaptation**: Payment interfaces adapt based on detected capabilities, hiding unsupported payment methods and highlighting preferred options based on device capabilities and regional availability.

**Security Context Provision**: Device security capabilities detected by the installation system inform payment security decisions, such as requiring additional authentication for high-value transactions on devices without biometric capabilities.

### Location Services Integration

The installation system provides essential support for location-based features through geographic and permission management:

**Location Permission Orchestration**: Complex location permission flows are managed through the installation system, including precision location requests and background location access for geofencing features.

**Geographic Context Management**: The installation system maintains geographic context information that supports location-based personalization and regional feature availability.

**Carrier-Based Location Intelligence**: Carrier information detected by the installation system provides additional geographic context that enhances location services accuracy and supports roaming scenarios.

## Advanced Implementation Patterns

### Capability Detection Strategies

The installation system implements sophisticated capability detection that goes beyond simple feature availability checking:

**Progressive Capability Discovery**: Rather than detecting all capabilities at startup, the system implements progressive discovery that probes capabilities as they become relevant to user interactions, reducing startup time while ensuring comprehensive capability coverage.

**Capability Caching and Invalidation**: Detected capabilities are cached with intelligent invalidation strategies that refresh capability information when system changes occur, such as OS updates or hardware configuration changes.

**Graceful Degradation Patterns**: When capabilities cannot be detected or are unavailable, the system implements graceful degradation that maintains core functionality while clearly communicating limitations to users.

### Permission Management Sophistication

The permission management system implements advanced patterns that optimize user experience while maintaining security:

**Contextual Permission Timing**: Permission requests are timed to coincide with user actions that clearly demonstrate the need for specific permissions, increasing grant rates and reducing permission fatigue.

**Permission State Reconciliation**: The system continuously reconciles permission states between the application's understanding and the platform's actual permission status, handling edge cases such as permissions revoked through system settings.

**Progressive Permission Escalation**: Basic permissions are requested first, with advanced permissions requested as users engage with more sophisticated features, creating a natural progression that feels organic to the user journey.

### Network Resilience Implementation

The installation system's network resilience goes beyond simple retry mechanisms:

**Intelligent Operation Queuing**: Failed operations are queued with priority and dependency management, ensuring critical operations are retried first while maintaining operation ordering where necessary.

**Adaptive Retry Strategies**: Retry strategies adapt based on error types and network conditions, using exponential backoff for transient errors while immediately retrying operations when network connectivity is restored.

**Partial Synchronization Support**: The system supports partial synchronization scenarios where some installation data can be synchronized while other data remains queued, maintaining maximum functionality during intermittent connectivity.

## Performance Optimization Strategies

### Startup Performance

The installation system is optimized for minimal impact on application startup time:

**Critical Path Optimization**: Only essential installation data required for core application functionality is loaded during the critical startup path, with comprehensive data loading deferred to background processes.

**Parallel Initialization**: Installation components are initialized in parallel where possible, reducing total initialization time while maintaining proper dependency ordering.

**Lazy Capability Detection**: Resource-intensive capability detection operations are performed on-demand rather than during startup, reducing initial load time while ensuring capabilities are available when needed.

### Runtime Performance

During application runtime, the installation system maintains optimal performance through several strategies:

**Memory-Efficient Caching**: Installation data caching strategies balance memory usage with access performance, using intelligent cache eviction policies that maintain frequently accessed data while freeing memory for other application needs.

**Background Processing**: Resource-intensive operations such as capability updates and server synchronization are performed on background threads, maintaining UI responsiveness while ensuring data consistency.

**Event-Driven Updates**: Rather than polling for changes, the installation system uses event-driven patterns that minimize resource usage while ensuring timely updates when installation data changes.

### Data Access Optimization

The installation system optimizes data access patterns for maximum efficiency:

**Batch Operations**: Related installation data operations are batched together to minimize storage I/O and network requests, improving overall system performance.

**Predictive Caching**: Frequently accessed installation data is predictively cached based on usage patterns, reducing access latency for common operations.

**Efficient Serialization**: Installation data serialization and deserialization operations are optimized for both speed and memory usage, using efficient encoding strategies that minimize overhead.

## Summary

The Installation Management Architecture represents a sophisticated, multi-layered system that serves as the foundation for device adaptation, user experience optimization, and system integration within the Perkd application. This architecture demonstrates several key strengths:

**Comprehensive Device Intelligence**: The system provides deep understanding of device capabilities, user preferences, and environmental context that enables sophisticated feature adaptation and personalized user experiences.

**Robust Network Resilience**: Through intelligent offline-first patterns, deferred operation queuing, and adaptive synchronization strategies, the installation system ensures consistent operation across diverse network conditions.

**Security-First Design**: The architecture implements comprehensive security measures including encrypted storage, secure token management, and privacy-compliant data handling that protects user information while enabling rich functionality.

**Performance-Optimized Implementation**: From startup optimization through runtime efficiency, the installation system is designed to minimize resource usage while maximizing functionality and user experience quality.

**Extensible Integration Architecture**: The system's modular design and comprehensive integration patterns enable seamless coordination with authentication, notification, payment, and location services while maintaining clean architectural boundaries.

## Business Logic Impact Analysis

### Why These Patterns Matter

The sophisticated business logic patterns implemented in the installation management system directly impact user experience and system reliability in measurable ways:

**Deferred Operations Prevent Data Loss**: The single-instance queuing strategy with intelligent data consolidation ensures that no user actions or system state changes are lost during network interruptions. This pattern prevents the frustrating scenario where users must repeat actions after connectivity is restored, maintaining user confidence in the application's reliability.

**Token Duplication Prevention Optimizes Performance**: By preventing unnecessary token updates, the system reduces server load and eliminates redundant permission refresh operations that could cause UI flickering or temporary feature unavailability. This optimization is particularly important for users on slower networks or devices with limited processing power.

**Multi-Factor Carrier Validation Prevents False Alarms**: The sophisticated carrier change detection prevents unnecessary mobile number validation updates that could temporarily disable SMS-based authentication or verification features. This business logic ensures that carrier change notifications only occur for genuine carrier switches, maintaining authentication reliability.

**Payment Method Support Filtering Reduces Transaction Failures**: The intelligent payment capability detection with support filtering ensures that users only see payment methods that will actually work on their device and in their region. This prevents the frustrating experience of selecting a payment method that fails during checkout, improving conversion rates and user satisfaction.

**Defensive Data Structure Management Ensures Consistency**: The null-safe initialization and immutable data access patterns prevent runtime errors that could crash the application or corrupt user data. These defensive programming patterns ensure that the installation system remains stable even when external systems provide unexpected data or when the app is running under resource constraints.

**Android 13+ Compliance Prevents Permission Issues**: The automatic notification cleanup for Android 13+ upgrades prevents permission-related crashes and ensures that the app remains compliant with evolving platform requirements without requiring user intervention or manual configuration.

### Architectural Decision Rationale

**Why Optimistic Updates**: The installation system prioritizes user experience responsiveness over perfect consistency by implementing optimistic updates that immediately reflect changes in the UI while synchronizing with servers in the background. This decision recognizes that users expect immediate feedback from their actions, and temporary inconsistencies are preferable to UI lag.

**Why Selective Error Handling**: Different types of errors receive different handling strategies because the business impact varies significantly. Network errors are retried because they're likely to succeed later, while validation errors are logged but not retried because they indicate fundamental data issues that won't be resolved by simple retry mechanisms.

**Why Capability Caching**: Device capabilities change infrequently but are accessed frequently throughout the app lifecycle. Caching these capabilities with intelligent invalidation provides significant performance benefits while ensuring that capability information remains accurate when genuine changes occur.

**Why Event-Driven Architecture**: The installation system leverages events to decouple components and enable reactive behavior. This architectural decision allows the system to respond to environmental changes (like network connectivity) without tight coupling between components, improving maintainability and enabling sophisticated behaviors like automatic retry mechanisms.

### User Experience Optimization Principles

The installation management architecture embodies several key principles that optimize user experience:

**Invisible Complexity**: The most sophisticated business logic operates transparently to users, handling edge cases and error conditions without requiring user intervention or awareness. Users benefit from the reliability and performance optimizations without needing to understand the underlying complexity.

**Graceful Degradation**: When advanced features are unavailable due to device limitations or network conditions, the system provides alternative functionality rather than complete feature unavailability. This ensures that core app functionality remains accessible across diverse device and network conditions.

**Proactive Problem Prevention**: Rather than reacting to problems after they occur, the installation system implements proactive patterns that prevent common issues like data loss during network interruptions, permission conflicts during OS upgrades, and payment method failures during checkout.

**Performance-First Design**: All business logic patterns prioritize performance and responsiveness, recognizing that user experience quality is directly correlated with application performance. Sophisticated caching, deferred operations, and optimistic updates all serve to maintain responsive user interactions.

This installation management architecture provides a solid foundation for mobile application development that adapts to diverse device capabilities, respects user preferences, and maintains robust operation across the complex mobile ecosystem. The sophisticated business logic patterns, comprehensive user experience flows, and technical implementation strategies ensure that the Perkd application can deliver optimal experiences across the full spectrum of mobile devices and user scenarios while maintaining security, privacy, and performance standards.

The architectural decisions documented here represent years of evolution and refinement based on real-world usage patterns, edge case discoveries, and performance optimization requirements. Each business logic pattern serves a specific purpose in creating a seamless, reliable, and performant user experience that adapts intelligently to the diverse and ever-changing mobile ecosystem.
