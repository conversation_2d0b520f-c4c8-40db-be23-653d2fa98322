# Actions System Architecture

## Table of Contents

1. [Overview](#overview)
2. [Technology Stack](#technology-stack)
3. [Architectural Patterns](#architectural-patterns)
   - [Command Pattern Implementation](#1-command-pattern-implementation)
   - [Object-Action Registry Pattern](#2-object-action-registry-pattern)
   - [Execution Pipeline Architecture](#3-execution-pipeline-architecture)
4. [Core Business Logic](#core-business-logic)
   - [Action Lifecycle Management](#action-lifecycle-management)
   - [Integration with Core Systems](#integration-with-core-systems)
5. [Security Architecture](#security-architecture)
6. [Performance Optimization](#performance-optimization)
7. [Error Handling and Recovery](#error-handling-and-recovery)
8. [User Experience Flow](#user-experience-flow)
9. [Integration Architecture](#integration-architecture)
10. [Sophisticated Patterns and Business Logic](#sophisticated-patterns-and-business-logic)
    - [Deferred Action Processing](#deferred-action-processing)
    - [Remote Action Execution](#remote-action-execution)
    - [Action Chaining and Composition](#action-chaining-and-composition)
    - [Advanced Validation Patterns](#advanced-validation-patterns)
    - [Event-Driven State Management](#event-driven-state-management)
    - [Performance Optimization Patterns](#performance-optimization-patterns)
11. [Data Flow Architecture](#data-flow-architecture)
12. [Comprehensive Action Reference](#comprehensive-action-reference)
    - [Core Action Domains](#core-action-domains)
    - [AppBridge SDK Integration](#appbridge-sdk-integration)
13. [Development Patterns and Guidelines](#development-patterns-and-guidelines)
    - [Action Implementation Patterns](#action-implementation-patterns)
    - [Error Handling Standards](#error-handling-standards)
    - [Testing and Quality Assurance](#testing-and-quality-assurance)
14. [Summary](#summary)

## Overview

The Actions system is a sophisticated command execution framework that serves as the primary mechanism for handling user interactions and business operations within the Perkd application. It provides a unified, modular approach to executing complex business logic while maintaining strict separation of concerns, comprehensive error handling, and seamless integration with the application's event-driven architecture.

The Actions system operates as a central orchestration layer that coordinates between the presentation layer, business logic, and data persistence, ensuring consistent execution patterns across all user interactions and automated processes.

## Technology Stack

### Core Components
- **Action Registry**: Centralized object-action mapping system with 23+ action domains
- **Execution Engine**: Asynchronous action processing with validation and error handling
- **Deferred Actions**: Sophisticated retry mechanism for failed operations
- **Remote Actions**: Server-side action execution with local caching
- **Event Integration**: Seamless integration with the application's event system
- **Validation Framework**: Multi-layered validation including permissions, timing, and business rules

### Key Dependencies
- **Registry System**: Centralized model and instance management (`src/lib/common/registry.js`)
- **Event System**: Event-driven communication (`src/lib/Events.json`)
- **Sync Engine**: Data synchronization and persistence (`src/lib/common/sync.js`)
- **Permissions Framework**: Security and access control (`src/lib/common/permissions.js`)
- **Error Handling**: Standardized error management (`src/lib/common/Errors.json`)

## Architectural Patterns

### 1. Command Pattern Implementation

The Actions system implements a sophisticated command pattern that encapsulates business operations as discrete, executable units:

```javascript
// Action execution pattern
Actions.do({
    object: 'card',
    action: 'add',
    data: { masterId: 'loyalty_program_123' }
}, cardMaster, card);
```

**Key Benefits:**
- **Encapsulation**: Business logic contained within specific action handlers
- **Reusability**: Actions can be invoked from multiple contexts (UI, notifications, automation)
- **Testability**: Individual actions can be tested in isolation
- **Consistency**: Uniform execution pattern across all business operations

### 2. Object-Action Registry Pattern

Actions are organized into domain-specific objects, each containing related action handlers:

| Action Object | Primary Responsibility | Key Actions |
|---------------|----------------------|-------------|
| **card** | Card lifecycle management | `add`, `request`, `qualified`, `clockin`, `clockout` |
| **offer** | Promotional campaigns | `request`, `redeem` |
| **reward** | Loyalty rewards | `request`, `redeem` |
| **bag** | Shopping cart operations | `build`, `additems`, `updateitems`, `removeitems` |
| **pay** | Payment processing | `pay`, `applepay`, `googlepay`, `checkout` |
| **shop** | E-commerce functionality | `products`, `likebrand` |
| **location** | Location services | `checkin`, `navigate`, `current`, `distance` |
| **media** | Media operations | `scan`, `takephoto`, `choosephoto` |
| **engage** | User engagement | `dialog`, `applet`, `ticket`, `auth` |
| **sync** | Data synchronization | `all`, `cache` |

### 3. Execution Pipeline Architecture

The action execution follows a sophisticated multi-stage pipeline:

```mermaid
graph TB
    subgraph "🎯 Action Execution Pipeline"
        A[Action Request<br/>object, action, data]
        B[Activity Check<br/>Actions.active]
        C[Preprocessing<br/>Actions.prep]
        D[Validation<br/>Timing, Permissions, Business Rules]
        E[Context Resolution<br/>Card, Master, Place, Person]
        F[Data Merging<br/>Template Rendering]
        G[Steps Execution<br/>Applet Processing]
        H[Action Dispatch<br/>Object.action invocation]
        I[Result Processing<br/>Event Emission, Caching]
    end

    subgraph "🔄 Parallel Processes"
        J[Multiple Actions<br/>doMultiple]
        K[Deferred Actions<br/>Retry Mechanism]
        L[Remote Actions<br/>Server Execution]
    end

    subgraph "⚡ Integration Points"
        M[Event System<br/>State Notifications]
        N[Sync Engine<br/>Data Persistence]
        O[Permissions<br/>Security Validation]
        P[Error Handling<br/>Recovery Strategies]
    end

    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I

    C --> J
    C --> K
    C --> L

    I --> M
    I --> N
    H --> O
    H --> P

    %% Styling with darker backgrounds and white text
    classDef pipeline fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef parallel fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef integration fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff

    class A,B,C,D,E,F,G,H,I pipeline
    class J,K,L parallel
    class M,N,O,P integration
```

#### Business Logic Decision Tree

The action validation implements sophisticated decision-making logic that considers multiple business factors:

```mermaid
graph TD
    A[Action Request] --> B{Actions.active?}
    B -->|No| C[Log & Reject<br/>System Maintenance Mode]
    B -->|Yes| D{Timing Valid?}
    D -->|No| E[Reject<br/>Outside Time Window]
    D -->|Yes| F{App Version OK?}
    F -->|No| G[Reject<br/>Version Incompatible]
    F -->|Yes| H{Permissions OK?}
    H -->|No| I{Can Request?}
    I -->|No| J[Reject<br/>Permission Blocked]
    I -->|Yes| K[Request Permission<br/>Show Rationale]
    K --> L{Permission Granted?}
    L -->|No| J
    L -->|Yes| M{Context Required?}
    H -->|Yes| M
    M -->|Yes| N{Context Valid?}
    N -->|No| O[Show Context Dialog<br/>Guide User]
    N -->|Yes| P{Card Required?}
    M -->|No| P
    P -->|Yes| Q{Card Available?}
    Q -->|No| R{Can Request Card?}
    R -->|No| S[Reject<br/>Card Unavailable]
    R -->|Yes| T[Request Card<br/>Show Registration]
    Q -->|Yes| U[Execute Action<br/>Business Logic]
    P -->|No| U

    %% Styling with darker backgrounds and white text
    classDef decision fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef process fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef reject fill:#e53e3e,stroke:#c53030,stroke-width:2px,color:#ffffff
    classDef success fill:#38a169,stroke:#2f855a,stroke-width:2px,color:#ffffff

    class B,D,F,H,I,L,M,N,P,Q,R decision
    class A,K,O,T process
    class C,E,G,J,S reject
    class U success
```

#### Detailed Execution Flow

The action execution pipeline implements the following detailed flow:

```mermaid
flowchart TD
    A[Actions.do] --> B[Prepare Action<br/>Actions.prep]
    B --> C{Has steps?}
    C -->|Yes| D[Execute Steps<br/>Applet Processing]
    C -->|No| E{Chain actions?}
    D --> E
    E -->|Yes| F[Execute Actions Sequentially<br/>doMultiple]
    E -->|No| G{Valid action?}
    G -->|No| H[Reject Error<br/>invalidObject/invalidAction]
    G -->|Yes| I[Execute Single Action<br/>Object.action invocation]
    F --> J[Return Result<br/>Context Propagation]
    I --> J

    %% Styling with darker backgrounds and white text
    classDef default fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef decision fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef process fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef error fill:#e53e3e,stroke:#c53030,stroke-width:2px,color:#ffffff

    class A,B,D,F,I,J process
    class C,E,G decision
    class H error
```

## Core Business Logic

### Action Lifecycle Management

#### 1. Action Validation and Preprocessing

The system implements comprehensive validation at multiple levels:

**Timing Validation:**
- `startTime` and `endTime` constraints ensure actions execute within valid time windows
- `minAppVersion` requirements prevent execution on incompatible app versions
- Expiration handling for cached remote actions (default 10-minute TTL)

**Permission Validation:**
The system implements a sophisticated permission escalation framework that balances security with user experience:
- **Progressive Permission Requests**: Permissions requested just-in-time based on action requirements, minimizing user friction
- **Biometric Authentication Gates**: Sensitive operations require biometric confirmation with fallback to device passcode
- **Context-Aware Permission Logic**: Permission requirements dynamically adjusted based on user context, location, and action sensitivity
- **Rationale-Driven Requests**: Permission requests include business rationale to improve user acceptance rates
- **Graceful Degradation**: Actions adapt functionality when permissions are denied rather than failing completely

**Business Rule Validation:**
- Card requirement validation based on `cardMasterIds` and `cardRequired` flags
- Context requirement validation for location-dependent actions
- Machine-specific validation for place-based operations

#### 2. Context Resolution and Data Merging

The preprocessing stage resolves complex contextual relationships:

**Context Assembly:**
```javascript
const mergeData = {
    card: resolvedCard,
    place: machinePlace || spotPlace,
    person: _.Person.mergeData,
    context: Context.get()
};
```

**Template Rendering and Security:**
The system implements sophisticated template rendering with built-in security measures:
- **Dynamic Data Interpolation**: Mustache-style template rendering with comprehensive context merging from multiple data sources
- **Nested Object Resolution**: Support for complex object path references with safe navigation to prevent runtime errors
- **Conditional Logic Evaluation**: Template-based conditional rendering that adapts to runtime business conditions
- **Sensitive Key Exclusion**: Automatic exclusion of security-sensitive keys (passwords, tokens, private data) during template rendering
- **Remote Action Security**: Special handling for remote actions where template rendering is bypassed to prevent data exposure
- **Context Sanitization**: Input sanitization and validation to prevent template injection attacks

#### 3. Sophisticated Action Patterns

**Activity State Management:**
The Actions system implements a global activity control mechanism that serves as a circuit breaker for the entire action execution pipeline. When `Actions.active` is false, all action requests are immediately rejected with logging, preventing system overload during critical operations or maintenance windows. This business rule ensures system stability during high-stress scenarios and provides a centralized control point for action execution.

**Context Requirement Validation:**
Actions requiring specific context (location, card presence, machine availability) implement sophisticated validation logic that goes beyond simple presence checks. The system evaluates context quality, recency, and business relevance before allowing action execution. For location-dependent actions, the system validates GPS accuracy, geofence boundaries, and place-specific business rules. This prevents actions from executing with stale or inappropriate context data.

**Card Master Resolution and Qualification Logic:**
The system implements complex card master resolution that considers multiple business factors:
- **Merchant ID Translation**: Automatic conversion of merchant IDs to card master IDs for backward compatibility
- **Place-Specific Card Filtering**: When machine ID is present, cards are filtered to those valid at the specific location
- **Qualification Assessment**: Dynamic evaluation of user eligibility based on existing card ownership, business policies, and temporal constraints
- **Fallback Strategies**: Graceful degradation when preferred cards are unavailable, with intelligent alternative selection

**Deferred Action Processing:**
The deferred action system implements sophisticated business logic for handling failed operations:
- **Failure Categorization**: Different retry strategies based on failure type (network, permission, business rule, resource)
- **Sequential Processing**: Actions processed in creation order to maintain business logic consistency
- **Batch Size Optimization**: Maximum 10 deferred actions per cycle to prevent system overload while ensuring timely processing
- **Image Upload Specialization**: Separate processing pipeline for image uploads with different retry logic and resource management
- **User Isolation**: Deferred actions scoped to individual users to prevent cross-user data leakage

**Multiple Action Execution:**
- Sequential execution with result chaining and context propagation
- Error isolation preventing cascade failures
- Result merging and key-based access to intermediate results

**Remote Action Execution:**
- Server-side action processing with local caching
- Automatic fallback to remote execution for missing local actions
- TTL-based cache invalidation and refresh strategies

### Integration with Core Systems

#### 1. Event System Integration

Actions seamlessly integrate with the application's event-driven architecture:

**Event Emission Patterns:**
- Automatic event emission for state changes and completion notifications
- Domain-specific event namespacing (Actions.doAction, Actions.updated)
- Integration with analytics and tracking systems

**Event-Driven Action Triggers:**
- Actions can be triggered by system events and user interactions
- Push notification action handling with context preservation
- Deep link action execution with parameter validation

#### 2. Sync Engine Integration

The Actions system maintains tight integration with data synchronization:

**Sync Action Categories:**
- Personal sync operations (deferred actions, cache, app events)
- Background sync with image upload handling
- Selective sync strategies based on data priority and network conditions

**Deferred Action Sync Integration:**
The sync engine implements sophisticated business rules for deferred action processing:
- **Sync-Triggered Execution**: Deferred actions automatically executed during sync cycles to ensure eventual consistency
- **Priority-Based Processing**: Image upload deferrals processed separately with different resource allocation and retry logic
- **Conflict Resolution Strategies**: When local and remote changes conflict, the system applies business-specific resolution rules
- **Transaction Isolation**: Deferred action execution isolated within sync transactions to prevent partial state corruption
- **Failure Recovery**: Failed sync operations trigger intelligent retry strategies with exponential backoff and circuit breaker patterns
- **Resource Management**: Sync operations throttled based on device capabilities and network conditions to maintain user experience

#### 3. Storage and Persistence Integration

Actions interact with multiple storage layers:

**Multi-Tiered Storage Access:**
- Realm database operations for structured data
- AsyncStorage for configuration and preferences
- Secure storage for sensitive action parameters
- In-memory caching for performance optimization

**Data Consistency Patterns:**
- Transactional operations ensuring data integrity
- Optimistic updates with rollback capabilities
- Cache invalidation strategies aligned with action execution

## Security Architecture

### Permission-Based Access Control

The Actions system implements comprehensive security measures:

**Feature-Based Permissions:**
- Camera access for scanning operations
- Location access for place-based actions
- Biometric authentication for sensitive operations
- Notification permissions for engagement actions

**Dynamic Permission Validation:**
- Runtime permission checking before action execution
- Graceful degradation for permission-denied scenarios
- User-friendly permission request flows with rationale

### Data Protection Patterns

**Sensitive Data Handling:**
- Secure parameter passing for payment and authentication actions
- Encryption of deferred action data containing sensitive information
- Sanitization of action logs and error reporting

**Input Validation:**
- Comprehensive parameter validation for all action types
- SQL injection prevention through parameterized queries
- XSS prevention in template rendering and data merging

## Performance Optimization

### Execution Optimization Strategies

**Asynchronous Processing:**
- Non-blocking action execution with Promise-based architecture
- Background processing for resource-intensive operations
- Intelligent scheduling to prevent UI blocking

**Caching and Memoization:**
- Action result caching for frequently accessed data
- Template rendering optimization with context memoization
- Remote action response caching with TTL management

**Resource Management:**
- Memory-efficient action execution with cleanup procedures
- Batch processing for multiple related actions
- Lazy loading of action dependencies and resources

### Scalability Patterns

**Modular Action Architecture:**
- Domain-specific action modules enabling independent scaling
- Plugin-like architecture for extending action capabilities
- Minimal coupling between action objects and shared infrastructure

**Load Distribution:**
- Client-side action execution for immediate operations
- Server-side action processing for complex business logic
- Hybrid execution strategies based on operation complexity

## Error Handling and Recovery

### Comprehensive Error Classification

The system categorizes errors into distinct types for appropriate handling:

**Action-Specific Errors:**
- `invalidObject`: Unknown action object requested
- `invalidAction`: Unsupported action for specified object
- `cardNotFound`: Required card not available
- `cardMasterNotFound`: Card template not found
- `noPermission`: Insufficient permissions for action
- `missingParam`: Required parameters not provided
- `failed`: General action execution failure
- `cancelled`: User-cancelled operation
- `socketNotFound`: Network connection issues

### Recovery Strategies

**Automatic Recovery:**
- Deferred action retry with exponential backoff
- Network error recovery with offline queue management
- Permission error recovery with user guidance

**User-Guided Recovery:**
- Clear error messaging with actionable guidance
- Alternative action suggestions for blocked operations
- Graceful degradation with reduced functionality

## User Experience Flow

### Primary User Journeys

#### 1. Card Management Flow

```mermaid
graph TB
    subgraph "💳 Card Management Journey"
        A[Card Discovery<br/>Location-based recommendations]
        B[Card Addition<br/>Scan or manual entry]
        C[Card Validation<br/>Business rules check]
        D[Card Activation<br/>Registration completion]
        E[Card Usage<br/>Check-in, offers, rewards]
        F[Card Maintenance<br/>Updates and sync]
    end

    subgraph "🎯 Action Execution"
        G[card.qualified<br/>Eligibility check]
        H[card.add<br/>Registration process]
        I[card.clockin<br/>Location check-in]
        J[offer.request<br/>Offer acquisition]
        K[reward.redeem<br/>Reward usage]
    end

    subgraph "🔄 System Integration"
        L[Location Services<br/>GPS and geofencing]
        M[Sync Engine<br/>Data persistence]
        N[Event System<br/>State notifications]
        O[Permissions<br/>Access validation]
    end

    A --> G
    B --> H
    E --> I
    E --> J
    E --> K

    G --> L
    H --> M
    I --> N
    J --> O

    %% Styling with darker backgrounds and white text
    classDef journey fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef actions fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef system fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff

    class A,B,C,D,E,F journey
    class G,H,I,J,K actions
    class L,M,N,O system
```

#### 2. Commerce and Shopping Flow

The Actions system orchestrates complex e-commerce operations:

**Shopping Cart Management:**
- `bag.build`: Constructs shopping cart with pricing, taxes, and offers
- `bag.additems`: Adds products with variant and option selection
- `bag.updateitems`: Modifies quantities and configurations
- `bag.removeitems`: Removes items with inventory release

**Payment Processing:**
- `pay.checkout`: Initiates payment preview with method selection
- `pay.pay`: Processes payment with multiple provider support
- `pay.applepay`/`pay.googlepay`: Platform-specific payment flows
- `order.card`: Handles card ordering with pricing and fulfillment

#### 3. Engagement and Interaction Flow

**User Engagement Actions:**
- `engage.dialog`: Displays contextual dialogs and prompts
- `engage.applet`: Executes interactive mini-applications
- `engage.ticket`: Manages support ticket creation and tracking
- `engage.auth`: Handles biometric authentication flows

**Location-Based Interactions:**
- `location.checkin`: Manages place-based check-ins with context
- `location.navigate`: Provides navigation assistance
- `location.current`: Retrieves current location and context
- `location.distance`: Calculates distances for proximity features

## Integration Architecture

### External System Integration

**Payment Provider Integration:**
- Multi-provider support (Stripe, Apple Pay, Google Pay, regional providers)
- Secure token management and transaction processing
- Fraud prevention and compliance validation

**Location Services Integration:**
- GPS and geofencing capabilities
- Place recognition and context determination
- Navigation and mapping service integration

**Media and Communication Integration:**
- Camera and barcode scanning functionality
- Photo capture and processing capabilities
- Communication services (SMS, email, phone)

### Internal System Coordination

**Registry System Integration:**
- Centralized model access and instance management
- Lazy loading and dependency resolution
- Type safety and consistency validation

**Navigation System Integration:**
- Deep link handling and route resolution
- Screen transition management
- Context preservation across navigation

## Comprehensive Action Reference

### Core Action Domains

The Actions system provides 23+ specialized action domains, each handling specific business functionality:

#### Application Management
- **app**: Core application functionality
  - `navto`: Navigate to specific routes with support for card, widget, and offer navigation
  - `colorscheme`: Get current app color scheme (light/dark mode) with dynamic updates
  - `sync`: Synchronize all application data with server including cache, cards, and settings
  - `update`: Open platform-specific app store for updates (iOS App Store/Google Play)

#### Commerce and Shopping
- **bag**: Shopping cart and order management
  - `build`: Construct shopping cart with pricing, taxes, and offers
  - `additems`: Add products with variant and option selection, emits `Bag.addItems` event
  - `configitems`: Configure items before adding with modal interface
  - `updateitems`: Modify quantities and configurations, emits `Bag.updateItems` event
  - `removeitems`: Remove items with inventory release, emits `Bag.removeItems` event
  - `configbundle`: Configure bundle items with specialized modal
  - `get`: Retrieve current shopping bag contents and state
  - `from`: Load shopping bag from specific source or template
  - `open`: Open shopping bag view with current contents
  - `use`: Apply shopping bag template or preset configuration

- **order**: Order processing and fulfillment
  - `at`: Check in and navigate to store front with location services integration
  - `card`: Place order for card or membership with pricing and payment options
  - `products`: Place order for products with multiple payment methods and fulfillment
  - `cancel`: Cancel order with payment intent cleanup

- **pay**: Payment processing and transactions
  - `pay`: Process payment using selected method with authentication flows
  - `select`: Select payment methods with multiple provider support
  - `request`: Request payment with multiple currencies and methods
  - `params`: Get payment parameters for specific methods and providers
  - `preview`: Show payment preview modal with cost breakdown
  - `send`: Send money to another card with transfer limits and validation
  - `addmoney`: Show add money modal for balance top-up
  - `requestpay`: Request payment from others with multiple methods
  - `cancel`: Cancel payment with cleanup across providers
  - `setup`: Configure payment methods with platform-specific flows
  - `applepay`: Process Apple Pay payments with merchant validation
  - `googlepay`: Process Google Pay payments
  - `alipay`: Process Alipay payments with multiple providers
  - `safepay`: Process SafePay payments with URL scheme flows
  - `linepay`: Process LINE Pay payments with test/production environments
  - `tng`: Process Touch 'n Go payments with multiple providers

#### Card and Loyalty Management
- **card**: Digital loyalty card lifecycle
  - `request`: Request new loyalty card with qualification validation
  - `add`: Add cards to wallet with principal card relationships
  - `qualified`: Check user qualification for specific card based on master ID
  - `clockin`: Record location-based check-in with TTL support
  - `clockout`: Record clock-out with duration calculation

- **offer**: Promotional campaigns and offers
  - `request`: Request offer issuance from CRM with master and offer IDs
  - `redeem`: Redeem offers with optional actions and item matching
  - `share`: Share offers with other users using multiple modes

- **reward**: Loyalty rewards and redemption
  - `request`: Request reward issuance from CRM with state transitions

#### User Engagement and Interaction
- **engage**: User engagement and interaction features
  - `dialog`: Show customizable dialog with content and actions, locks DND state
  - `optionsdialog`: Show dialog with selectable options for user choice
  - `applet`: Launch interactive applet with custom HTML content and resource loading
  - `ticket`: Manage support ticket creation and tracking
  - `checkin`: Perform location-based check-in with optional ticket validation
  - `next`: Get next engagement action for specified card
  - `joinmember`: Handle membership joining process
  - `auth`: Handle biometric authentication with title, message, and confirmation
  - `updateapp`: Show app update prompt with platform-specific navigation
  - `rateapp`: Show app rating prompt using native platform interface

- **interact**: User interaction and dialog management
  - `bottommodal`: Show bottom modal with custom HTML content and pull-to-close
  - `showloading`: Show loading indicator overlay with duplicate prevention
  - `hideloading`: Hide currently displayed loading indicator
  - `notify`: Schedule local notification with title, subtitle, body, and image
  - `toast`: Show toast message with custom position and auto-dismissal
  - `vibrate`: Trigger device haptic feedback with pattern support
  - `dialog`: Show dialog with customizable title and buttons
  - `inputdialog`: Show dialog with text input field and validation
  - `playsound`: Play predefined sound effects with category support

#### Location and Navigation
- **location**: Location services and navigation
  - `navigate`: Navigate to location using device navigation with multiple map providers
  - `current`: Get current location with country details and geocoded data
  - `checkin`: Manage place-based check-ins with context and event emission
  - `setspot`: Alias for checkin functionality
  - `removespot`: Remove location spot with manual override options
  - `distance`: Calculate distance between two points in meters
  - `duration`: Calculate travel duration with ETA and place information
  - `nearestplaces`: Find nearest places within radius sorted by distance

#### Media and Communication
- **media**: Media operations and device capabilities
  - `scan`: Scan barcodes and QR codes with camera, supports multiple formats
  - `barcode`: Generate and display barcode or QR code with styling options
  - `takephoto`: Open camera for photo capture with quality and flash control
  - `choosephoto`: Open photo picker for gallery selection with multiple selection
  - `addcalendar`: Add event to device calendar with dates and custom colors
  - `getcalendar`: Retrieve calendar event by ID
  - `removecalendar`: Remove calendar event by ID

- **communicate**: Communication services
  - `call`: Make phone call with automatic number formatting
  - `email`: Send email using device client with template variables and CC/BCC
  - `sms`: Send SMS to multiple recipients with template variable support
  - `chat`: Open chat application or web interface with deep linking

#### System Integration
- **system**: System-level operations
  - `settings`: Open device system settings for application
  - `time`: Get current system time in milliseconds
  - `toclipboard`: Copy text to device clipboard
  - `fromclipboard`: Retrieve text from device clipboard
  - `openapp`: Open another application using URI schemes with platform-specific parameters
  - `download`: Download content from URL to temporary directory
  - `share`: Share content using system share sheet

- **network**: Network connectivity operations
  - `info`: Get current network information including connectivity, type, and configuration

- **permissions**: Permission handling and requests
  - `check`: Check current permission status with detailed information
  - `request`: Check and request permission with platform-specific types and rationale

#### Advanced Features
- **nfc**: NFC tag operations
  - `read`: Read data from NFC tag with content decoding and QR fallback
  - `write`: Write data to NFC tag with text and URI record support
  - `setpassword`: Set password protection on NFC tag with multiple tag types
  - `removepassword`: Remove password protection requiring current password

- **remote**: API and WebSocket operations
  - `api`: Make remote API call with card context and spot information
  - `connect`: Create WebSocket connection with lifecycle and reconnection handling
  - `send`: Send data through WebSocket supporting string and object messages
  - `close`: Close WebSocket connection with proper termination
  - `clean`: Clean up WebSocket connections for specific source

- **sync**: Data synchronization
  - `all`: Complete synchronization of all data types including cache, masters, settings
  - `cache`: Synchronize cache data with up and down sync operations

- **track**: Analytics and monitoring
  - `log`: Log message with optional topic and priority
  - `event`: Track application event with associated data
  - `watch`: Watch for errors with optional topic and priority

- **file**: File system operations
  - `share`: Share content using system share sheet with file contents and URLs
  - `download`: Download file from URL to app temporary directory with directory creation
  - `write`: Write content to file in app storage with custom paths and encodings

- **form**: Form handling and validation
  - `validate`: Validate form data against JSON Schema with custom rules and localized errors
  - `openinghours`: Configure business opening hours with visual picker interface
  - `showpicker`: Show picker modal for date, time, or duration selection

- **web**: Web content and URL handling
  - `open`: Open web URL with optional context and multiple opening modes
  - `search`: Trigger web search event with applet search event emission

- **widget**: Widget data management
  - `data`: Manage widget data persistence and state with synchronization

- **window**: Window and modal management
  - `close`: Close current window or modal view with cleanup and transitions

### AppBridge SDK Integration

For applet developers, all actions are accessible through the AppBridge SDK using the standardized syntax:

```javascript
// Basic action execution
$perkd.do('app.navto', { route: [{ card: { id: '123' } }] });

// Payment processing
$perkd.do('pay.applepay', {
    amount: 1000,
    currency: 'USD',
    description: 'Product purchase'
});

// Location-based check-in
$perkd.do('location.checkin', {
    spot: { placeId: 'place_123' },
    options: { noEvent: false }
});
```

### Direct Action Module Usage

For internal application development, actions can be imported and used directly:

```javascript
import { Actions as BagActions } from 'common/actions/bag';

// Add items to shopping bag
await BagActions.additems({
    bag: 'default',
    items: [{
        variantId: '123',
        quantity: 1,
        options: {
            color: 'blue'
        }
    }]
}, cardmaster, card);
```

### Action Chaining Patterns

Actions support sophisticated chaining for complex workflows:

```javascript
// Sequential action execution with context propagation
await Actions.do({
    actions: [
        {
            object: 'bag',
            action: 'configitems',
            data: { /* configuration data */ },
            key: 'config'
        },
        {
            object: 'bag',
            action: 'additems',
            data: {
                items: '{{config.items}}' // Reference previous result
            },
            key: 'add'
        },
        {
            object: 'pay',
            action: 'checkout',
            data: {
                bag: '{{add.bagId}}' // Chain results
            }
        }
    ]
}, cardmaster, card);
```

### Action Validation Requirements

Actions undergo comprehensive validation before execution:

#### Version Validation
- **Minimum App Version**: Some actions require specific app versions
- **Platform Compatibility**: Actions may be platform-specific (iOS/Android)
- **Feature Availability**: Actions validate feature availability on device

#### Temporal Validation
- **Start/End Time Constraints**: Actions can have time-based availability windows
- **Timezone Awareness**: Global operations consider timezone differences
- **Expiration Handling**: Cached actions have TTL-based expiration

#### Context Validation
- **Location Context**: Place-specific actions require location services
- **Card Context**: Card operations require valid card and master relationships
- **Machine Context**: Hardware-dependent operations validate machine availability
- **User Context**: Personalized actions validate user authentication state

#### Business Rule Validation
- **Card Requirements**: Actions validate card master IDs and card availability
- **Permission Requirements**: Feature-specific permissions validated before execution
- **Resource Availability**: Actions check for required resources (camera, network, etc.)

### Data Preparation and Context Resolution

The action system automatically handles complex data preparation:

#### Automatic Resolution
- **Card and Master Resolution**: IDs automatically resolved to full objects
- **Context Injection**: Current app context injected into action data
- **Location Data**: GPS and place information added when relevant
- **User Data**: Person and profile information merged into context

#### Template Rendering
- **Dynamic Value Substitution**: Mustache-style template rendering with context data
- **Nested Object References**: Support for complex object path references
- **Conditional Logic**: Template-based conditional rendering
- **Merchant and Place Resolution**: Automatic resolution of merchant and place references

## Development Patterns and Guidelines

### Action Implementation Patterns

**Consistent Action Structure:**
```javascript
export const Actions = {
    actionName: async (data, cardMaster, card, ctxId) => {
        // 1. Parameter validation
        // 2. Business logic execution
        // 3. Result processing and event emission
        // 4. Error handling and recovery
    }
};
```

**Error Handling Best Practices:**
- Use standardized error codes from `Errors.json`
- Provide meaningful error context for debugging
- Implement graceful degradation for non-critical failures
- Log errors appropriately for monitoring and analytics

**Performance Considerations:**
- Minimize blocking operations in action execution
- Use appropriate caching strategies for expensive operations
- Implement proper cleanup for resource-intensive actions
- Consider memory usage in batch processing scenarios

### Error Handling Standards

**Standard Error Pattern:**
All actions follow a consistent error format for predictable error handling:

```javascript
{
    code: 'ERROR_CODE',
    message: 'Human readable message',
    details: {
        // Additional error context
    }
}
```

**Common Error Codes:**
- `NETWORK_ERROR`: Network connectivity issues
- `VERSION_MISMATCH`: App version incompatibility
- `PERMISSION_DENIED`: Required permission not granted
- `INVALID_PARAMETER`: Invalid action parameters
- `NOT_SUPPORTED`: Action not supported on current platform
- `VALIDATION_ERROR`: Invalid input parameters
- `NOT_FOUND`: Requested resource not found
- `INVALID_STATE`: Action cannot be performed in current state

**Module-Specific Error Codes:**
- **Payment Errors**: `INSUFFICIENT_FUNDS`, `PAYMENT_DECLINED`, `INVALID_PAYMENT_METHOD`
- **Card Errors**: `CARD_EXISTS`, `NOT_QUALIFIED`, `CARD_EXPIRED`, `INVALID_CARD_STATE`
- **Order Errors**: `OUT_OF_STOCK`, `DELIVERY_UNAVAILABLE`, `INVALID_ORDER_STATE`
- **Media Errors**: `NO_PERMISSION`, `CANCELLED`, `DEVICE_NOT_SUPPORTED`
- **Location Errors**: `LOCATION_DISABLED`, `GEOFENCE_ERROR`, `INVALID_COORDINATES`

**Error Handling Best Practices:**

```javascript
try {
    await BagActions.additems({
        bag: 'default',
        items: [{ variantId: '123', quantity: 1 }]
    }, cardmaster, card);
} catch (error) {
    const { code, message, details } = error;

    switch (code) {
        case 'NETWORK_ERROR':
            // Handle network connectivity issues
            showRetryDialog();
            break;
        case 'VALIDATION_ERROR':
            // Handle validation errors with specific details
            showValidationErrors(details);
            break;
        case 'PERMISSION_DENIED':
            // Guide user to grant required permissions
            showPermissionRequest();
            break;
        default:
            // Handle unexpected errors gracefully
            showGenericError(message);
    }
}
```

**Development Guidelines:**

1. **Error Handling**
   - Always implement proper error handling
   - Show user-friendly error messages
   - Implement retry logic where appropriate

2. **Network Awareness**
   - Check network connectivity before actions
   - Implement offline fallbacks where possible
   - Cache responses when appropriate

3. **Security**
   - Validate all input parameters
   - Handle sensitive data appropriately
   - Follow platform security guidelines

4. **Performance**
   - Minimize action chaining
   - Implement proper loading states
   - Cache results when possible

### Testing and Quality Assurance

**Action Testing Strategies:**
- Unit testing for individual action handlers
- Integration testing for action chains and dependencies
- Mock testing for external service interactions
- Performance testing for resource-intensive operations

**Quality Metrics:**
- Action execution time monitoring
- Error rate tracking and alerting
- Resource usage optimization
- User experience impact measurement

## Contributing Guidelines

### Adding New Actions

When implementing new actions, follow these established patterns:

#### 1. **Action Structure Standards**
```javascript
export const Actions = {
    /**
     * Action description
     * @param {Object} data - Action parameters
     * @param {Object} cardMaster - Card master object (optional)
     * @param {Object} card - Card object (optional)
     * @param {String} ctxId - Context ID for AppBridge (optional)
     * @returns {Promise} - Action result
     */
    actionName: async (data, cardMaster, card, ctxId) => {
        // 1. Parameter validation
        const { requiredParam, optionalParam = defaultValue } = data;
        if (!requiredParam) {
            return rejectError(ERROR.Actions.missingParam, { requiredParam });
        }

        // 2. Business logic execution
        try {
            const result = await performBusinessLogic(data, cardMaster, card);

            // 3. Event emission (if applicable)
            $.Event.emit(EVENT.Module.actionCompleted, { result, cardId: card?.id });

            return result;
        } catch (error) {
            // 4. Error handling and recovery
            return rejectError(ERROR.Actions.failed, { error: error.message });
        }
    }
};
```

#### 2. **Documentation Standards**

Each action must be documented using this template:

```markdown
### actionName(data, cardMaster, card, ctxId)

Brief description of what the action does and its primary use case.

**Parameters**:
- `data`: Object - Action parameters
  - `requiredParam`: Type - Description of required parameter
  - `optionalParam`: Type (optional) - Description with default value
- `cardMaster`: Object (optional) - Card master context for program operations
- `card`: Object (optional) - Card context for card-specific operations
- `ctxId`: String (optional) - Context ID for AppBridge integration

**Returns**: Promise<Type>
- Description of return value and its structure

**Events Emitted**:
- `EVENT.Module.actionCompleted` - When action completes successfully

**Error Codes**:
- `MISSING_PARAM` - Required parameter not provided
- `VALIDATION_ERROR` - Invalid parameter values
- `BUSINESS_RULE_ERROR` - Business logic validation failed

**Example**:
```javascript
// Example usage with all parameters
await Actions.actionName({
    requiredParam: 'value',
    optionalParam: 'custom'
}, cardMaster, card);
```

**Integration Notes**:
- Requires specific permissions: camera, location, etc.
- Integrates with: sync engine, event system, etc.
- Performance considerations: caching, resource usage, etc.
```

#### 3. **Implementation Requirements**

**Parameter Validation:**
- Validate all required parameters at the start of the action
- Use standardized error codes from `Errors.json`
- Provide meaningful error context for debugging

**Error Handling:**
- Implement comprehensive error handling with recovery strategies
- Use consistent error format: `{ code, message, details }`
- Log errors appropriately for monitoring and analytics

**Performance Considerations:**
- Minimize blocking operations in action execution
- Use appropriate caching strategies for expensive operations
- Implement proper cleanup for resource-intensive actions
- Consider memory usage in batch processing scenarios

**Integration Standards:**
- Follow established patterns for event emission
- Integrate with sync engine for data persistence when applicable
- Respect permission requirements and validation patterns
- Maintain consistency with existing action interfaces

#### 4. **Testing Requirements**

**Unit Testing:**
- Test individual action handlers in isolation
- Mock external dependencies and services
- Validate parameter handling and error scenarios
- Test return value formats and structures

**Integration Testing:**
- Test action chains and dependencies
- Validate event emission and system integration
- Test with real card master and card contexts
- Verify sync engine integration

**Performance Testing:**
- Measure action execution time under various conditions
- Test resource usage for memory and CPU intensive operations
- Validate behavior under network connectivity issues
- Test concurrent action execution scenarios

#### 5. **Code Review Checklist**

- [ ] Action follows established naming conventions
- [ ] Parameter validation implemented with appropriate error codes
- [ ] Business logic properly separated and testable
- [ ] Error handling comprehensive with recovery strategies
- [ ] Events emitted following established patterns
- [ ] Documentation complete with examples and integration notes
- [ ] Unit tests cover all code paths and error scenarios
- [ ] Integration tests validate system interactions
- [ ] Performance impact assessed and optimized
- [ ] Security considerations addressed (permissions, validation)

### Module Organization

Actions are organized into domain-specific modules for maintainability:

- **Core Operations**: app, sync, system, permissions
- **Commerce**: bag, order, pay, shop
- **User Engagement**: engage, interact, communicate
- **Content Management**: media, file, web, form
- **Location Services**: location, place
- **Integration**: remote, network, nfc, track

Each module should maintain clear boundaries and minimal coupling with other modules while leveraging shared infrastructure for common patterns like validation, error handling, and event emission.

## Architectural Decision Rationale

### Why Centralized Action Registry?

The centralized action registry pattern was chosen to address several critical business requirements:

**Business Continuity**: A single registry ensures consistent action availability across all application contexts, preventing business workflow interruptions due to missing action handlers.

**Maintainability**: Centralized registration simplifies action discovery and modification, reducing development overhead and enabling rapid business requirement changes.

**Security**: Single point of control for action validation and security policies, ensuring consistent security enforcement across all business operations.

**Performance**: Registry-based dispatch eliminates runtime action resolution overhead, critical for user-facing operations that require immediate response.

### Why Deferred Action Pattern?

The deferred action architecture addresses fundamental business resilience requirements:

**Revenue Protection**: Failed payment and registration operations are automatically retried, preventing revenue loss due to temporary technical issues.

**User Experience**: Users aren't blocked by temporary failures; operations complete transparently in the background, maintaining engagement and satisfaction.

**Data Consistency**: Sequential processing of deferred actions ensures business logic consistency and prevents race conditions that could corrupt user data.

**Operational Efficiency**: Automatic retry reduces support burden and manual intervention requirements, lowering operational costs.

### Why Multi-Layer Validation?

The sophisticated validation framework serves critical business protection needs:

**Risk Mitigation**: Multiple validation layers prevent invalid operations that could damage business relationships or violate compliance requirements.

**User Guidance**: Progressive validation provides clear user feedback, improving conversion rates and reducing abandonment.

**System Stability**: Early validation prevents resource waste and system overload from invalid operations.

**Business Rule Enforcement**: Dynamic validation ensures business policies are consistently enforced across all user interactions.

### Why Template Rendering with Security?

The secure template rendering approach balances flexibility with protection:

**Business Agility**: Template-based configuration enables rapid business rule changes without code deployment.

**Data Protection**: Automatic sensitive key exclusion prevents accidental exposure of confidential information.

**Personalization**: Dynamic data merging enables personalized user experiences that drive engagement and conversion.

**Compliance**: Built-in security measures ensure regulatory compliance for data handling and privacy protection.

## Sophisticated Patterns and Business Logic

### Deferred Action Processing

The Actions system implements a sophisticated deferred execution pattern that ensures reliable operation even under adverse conditions:

#### Deferred Action Lifecycle

**Action Deferral Triggers:**
- Network connectivity issues during remote operations
- Permission denial requiring user intervention
- Resource unavailability (camera, location services)
- Business rule violations requiring later retry
- Image upload failures requiring background processing

**Deferral Storage and Management:**
```javascript
// Deferred action creation
Action.defer(object, action, data, reason);

// Specialized image deferral
Action.deferImage(object, action, data, imageId, ownerId, reason);
```

**Processing Strategies:**
- **Batch Processing**: Maximum 10 deferred actions per execution cycle
- **Sequential Execution**: Actions processed in creation order to maintain consistency
- **Error Isolation**: Individual action failures don't affect batch processing
- **Automatic Cleanup**: Successful actions removed from deferred queue

#### Retry Logic and Recovery

**Exponential Backoff Implementation:**
- Initial retry after sync cycle completion
- Progressive delay increases for persistent failures
- Maximum retry attempts to prevent infinite loops
- Intelligent failure categorization for retry decisions

**Sophisticated Error Categorization and Recovery:**
The system implements intelligent error categorization that drives business-appropriate recovery strategies:

**Network Error Business Logic:**
- **Connectivity Assessment**: Distinguish between temporary network issues and persistent connectivity problems
- **Business Impact Evaluation**: Critical operations (payments, registrations) get immediate retry attempts while background operations are deferred
- **Adaptive Retry Strategies**: Retry intervals adjusted based on error patterns and business operation urgency
- **Offline Queue Management**: Failed operations queued with business priority ordering for execution when connectivity returns

**Permission Error Recovery:**
- **User Education Strategies**: Context-aware rationale provided based on specific business operation and user journey stage
- **Alternative Flow Provision**: When permissions are denied, alternative workflows offered to maintain business continuity
- **Progressive Permission Requests**: Permissions requested incrementally as needed rather than all at once to reduce user friction
- **Graceful Feature Degradation**: Core business functionality maintained even when optional permissions are denied

**Business Rule Error Handling:**
- **Context-Dependent Retry Logic**: Failed business rule validations trigger context refresh and retry with updated parameters
- **Rule Evolution Support**: Business rule changes handled gracefully with automatic parameter adaptation
- **User Guidance Integration**: Business rule violations explained to users with actionable guidance for resolution
- **Escalation Pathways**: Complex business rule failures escalated to appropriate support channels with full context preservation

**Resource Error Management:**
- **Resource Availability Monitoring**: Continuous monitoring of required resources (camera, location, storage) with proactive user notification
- **Alternative Resource Strategies**: When primary resources unavailable, alternative approaches offered (manual entry vs. scanning)
- **Resource Conflict Resolution**: Multiple actions competing for same resource handled with business priority-based scheduling
- **Performance Impact Mitigation**: Resource-intensive operations scheduled during optimal device conditions to minimize user impact

### Remote Action Execution

The system provides seamless integration between local and remote action processing:

#### Remote Action Architecture

**Execution Flow:**
1. **Local Action Check**: Verify if action exists in local registry
2. **Remote Fallback**: Execute on server if local action unavailable
3. **Response Caching**: Store remote action results with TTL
4. **Local Execution**: Process cached actions locally when possible

**Caching Strategy:**
- **Default TTL**: 10 minutes for remote action responses
- **Intelligent Invalidation**: Context-based cache refresh
- **Memory Optimization**: Automatic cleanup of expired cached actions
- **Offline Support**: Graceful degradation when remote unavailable

#### Server-Client Coordination

**Action Intent Processing:**
```javascript
// Remote action execution
ActionService.intent(id, params)
    .then(payload => {
        // Cache response with expiration
        const expiresAt = new Date(Date.now() + (ttl * 60 * 1000));
        _.Action.save({ ...payload, expiresAt });

        // Execute locally if action/actions present
        if (payload.actions || payload.action) {
            return doAction(payload);
        }
    });
```

**Synchronization Integration:**
- Remote actions trigger sync operations for data consistency
- Conflict resolution for concurrent local and remote modifications
- Optimistic updates with server validation and rollback capabilities

### Action Chaining and Composition

#### Multiple Action Execution

The system supports sophisticated action composition patterns:

**Sequential Execution with Context Propagation:**
```javascript
const mergeData = { card, person: _.Person.mergeData };

for (const action of actions) {
    // Context resolution for each action
    const context = await resolveActionContext(action, mergeData);

    // Execute with accumulated context
    const result = await Actions.do(action, master, card);

    // Propagate results to subsequent actions
    mergeData[action.key] = result;
}
```

**Result Chaining Patterns:**
- **Key-Based Access**: Results accessible by action key in subsequent actions
- **JSON Parsing**: Automatic parsing of string results to objects
- **Error Isolation**: Individual action failures contained within chain
- **Context Accumulation**: Progressive context building through action sequence

#### Conditional Action Execution

**Business Rule Evaluation:**
- Dynamic action selection based on runtime conditions
- Context-dependent action parameter modification
- Conditional action skipping based on business logic
- Result-dependent action flow modification

### Advanced Validation Patterns

#### Multi-Layer Validation Architecture

**Temporal Validation:**
- `startTime` and `endTime` constraints for time-sensitive actions
- App version compatibility checks (`minAppVersion`)
- Action expiration handling for cached remote actions
- Timezone-aware validation for global operations

**Contextual Validation:**
- Location-based validation for place-specific actions
- Card requirement validation with fallback strategies
- Machine-specific validation for hardware-dependent operations
- User context validation for personalized actions

**Business Rule Validation:**
- Dynamic rule evaluation based on card master configuration
- Merchant-specific validation rules and constraints
- Offer and reward eligibility validation
- Payment method availability and restriction validation

#### Permission Integration Patterns

**Dynamic Permission Resolution:**
```javascript
// Feature-based permission checking
const allowed = await checkAndRequest(feature, { type, showRationale });

// Context-aware permission validation
if (!allowed) {
    return rejectError(ERROR.Actions.noPermission, { feature, action });
}
```

**Permission Escalation:**
- Progressive permission requests based on action requirements
- Graceful degradation for partial permission grants
- User education and rationale presentation
- Alternative action suggestions for permission-denied scenarios

### Event-Driven State Management

#### Event Emission Patterns

**Action Lifecycle Events:**
- `Actions.doAction`: Emitted when action execution begins
- `Actions.updated`: Emitted when action state changes
- `Actions.deleted`: Emitted when action is removed or expires

**Domain-Specific Events:**
- Card events for loyalty program state changes
- Location events for place-based interactions
- Widget events for UI component updates
- Sync events for data synchronization triggers

#### Event-Action Integration

**Bidirectional Communication:**
```javascript
// Action triggering events
$.Event.emit(EVENT.Widget.checkin, { cardId, spot, ...through });

// Events triggering actions
$.Event.on(EVENT.Card.edit, () => Actions.do({ object: 'sync', action: 'cacheWithImages' }));
```

**State Synchronization:**
- Automatic UI updates through event propagation
- Cross-component communication via action-triggered events
- Analytics integration through event emission
- Real-time state synchronization across application layers

### Performance Optimization Patterns

#### Execution Optimization

**Asynchronous Processing:**
- Non-blocking action execution with Promise-based architecture
- Background processing for resource-intensive operations
- Intelligent task scheduling to prevent UI blocking
- Concurrent execution where dependencies allow

**Resource Management:**
- Memory-efficient action execution with automatic cleanup
- Lazy loading of action dependencies and resources
- Connection pooling for network-intensive actions
- Cache warming for frequently accessed actions

#### Scalability Considerations

**Load Distribution:**
- Client-side execution for immediate operations
- Server-side processing for complex business logic
- Hybrid execution strategies based on operation complexity
- Dynamic load balancing based on device capabilities

**Performance Threshold Business Rules:**
The system implements sophisticated performance optimization strategies based on business impact analysis:

**Batch Processing Intelligence:**
- **Dynamic Batch Sizing**: Batch sizes automatically adjusted based on device capabilities, network conditions, and user interaction patterns
- **Business Priority Weighting**: Critical business operations (payments, card registration) prioritized over background tasks
- **Progressive Processing**: Large operations broken into user-perceivable chunks with progress feedback to maintain engagement
- **Memory Pressure Management**: Automatic memory cleanup and resource release based on system pressure and business operation priority
- **Concurrent Execution Limits**: Maximum concurrent actions limited to prevent UI blocking while ensuring business-critical operations complete

**Caching Strategy Business Logic:**
- **Business Value-Based TTL**: Cache expiration times determined by business value and data volatility rather than fixed timeouts
- **Context-Aware Invalidation**: Cache invalidation triggered by business events (card changes, location updates) rather than time-based expiration
- **Predictive Preloading**: Frequently accessed actions preloaded based on user behavior patterns and business workflow analysis
- **Resource-Conscious Caching**: Cache size and retention policies adapted to device capabilities and user usage patterns

## Data Flow Architecture

### Action Data Flow Patterns

#### Input Data Processing

**Parameter Validation and Sanitization:**
```javascript
// Comprehensive parameter validation
const { masterId, cardId, id, quantity = 1 } = data;
if (!(masterId && id)) {
    return rejectError(ERROR.Actions.missingParam, data);
}
```

**Context Resolution and Merging:**
- Dynamic context assembly from multiple sources
- Template rendering with variable substitution
- Nested object reference resolution
- Conditional logic evaluation in data templates

#### Output Data Management

**Result Processing:**
- Standardized result format across all action types
- Automatic serialization and deserialization
- Result caching for performance optimization
- Event emission for state change notifications

**Error Response Handling:**
- Consistent error format with actionable information
- Error context preservation for debugging
- User-friendly error message generation
- Recovery suggestion provision

### Integration Data Flows

#### Storage Integration

**Multi-Tier Storage Access:**
- **Realm Database**: Structured data operations with relationships
- **AsyncStorage**: Configuration and preference management
- **Secure Storage**: Sensitive parameter and credential handling
- **Memory Cache**: Performance optimization for frequent access

**Data Consistency Patterns:**
- Transactional operations ensuring atomicity
- Optimistic updates with conflict resolution
- Cache invalidation aligned with action execution
- Eventual consistency for distributed operations

#### Sync Integration

**Synchronization Triggers:**
- Automatic sync initiation for data-modifying actions
- Selective sync based on action type and priority
- Background sync scheduling for deferred actions
- Conflict resolution for concurrent modifications

**Data Propagation:**
- Local-to-remote data synchronization
- Remote-to-local update propagation
- Cross-device state synchronization
- Offline operation queuing and replay

## Summary

The Actions system represents a sophisticated command execution framework that serves as the backbone of the Perkd application's business logic. Through its modular architecture, comprehensive validation, and seamless integration with core systems, it provides:

- **Unified Execution Model**: Consistent patterns for all business operations
- **Robust Error Handling**: Comprehensive error classification and recovery strategies
- **Scalable Architecture**: Modular design supporting independent scaling and extension
- **Security Integration**: Multi-layered security with permission-based access control
- **Performance Optimization**: Efficient execution with caching and resource management
- **User Experience Focus**: Smooth, responsive interactions with graceful error handling

This architecture enables the application to handle complex business workflows while maintaining excellent performance, security, and user experience across all interaction patterns.
