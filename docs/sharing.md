# Sharing System Architecture

## Overview

The Perkd sharing system is a comprehensive, multi-entity sharing platform that enables users to share digital assets across the application ecosystem. The system supports sharing of cards, offers, tickets, rewards, and messages with sophisticated business logic, security controls, and external platform integrations. This document provides a detailed technical analysis of the sharing system's architecture, design patterns, and component interactions with a focus on user experience flow and data flow patterns.

## Supported Sharable Entities

The sharing system supports five primary entity types, each with distinct sharing characteristics and business rules:

### 1. Cards
- **Share Modes**: `invite`, `transfer`, `clone`
- **Business Logic**: Card invitation and registration workflows
- **Security**: Share policy validation, recipient verification
- **Integration**: SMS notifications, contact management

### 2. Offers
- **Share Modes**: `transfer`, `subobject`
- **Business Logic**: Offer transfer with ownership change
- **Security**: Single-recipient limitation, expiration handling
- **Integration**: Offer lifecycle management

### 3. Tickets
- **Share Modes**: Inherits from offer sharing modes
- **Business Logic**: Event ticket transfer with validation
- **Security**: Fraud prevention, venue coordination
- **Integration**: Event management systems

### 4. Rewards
- **Share Modes**: Limited sharing capabilities
- **Business Logic**: Reward point transfers (where applicable)
- **Security**: Balance validation, transaction integrity
- **Integration**: Loyalty program coordination

### 5. Messages
- **Share Modes**: Currently not directly shareable
- **Business Logic**: Message forwarding capabilities
- **Security**: Privacy controls, content filtering
- **Integration**: Notification systems

## Core Architecture Components

### Sharing Controller (`src/controllers/Share.js`)

The central orchestrator for all sharing operations, providing:

- **Unified Interface**: Consistent sharing experience across entity types
- **Contact Management**: Integration with device contacts and permissions
- **Validation Logic**: Business rule enforcement and security checks
- **Error Handling**: Comprehensive error management and user feedback
- **Modal Management**: UI flow coordination and navigation

**Key Responsibilities:**
- Contact list presentation and selection
- Recipient validation and profile resolution
- Share confirmation and execution
- Error handling and user feedback
- Permission management for contact access

### Sharing Services Layer

#### Card Sharing (`src/lib/share.js` - Card)
```javascript
export const Card = {
  getShareMessage: (srcBrand, obj, sharePolicies, shareMode, srcCardName) => {
    // Generate contextual share messages based on policies
  },
  share: (targets, obj, cardId, mode, hideLoading) => {
    // Execute card sharing via CardService API
  },
  cancel: (id, sharingId) => {
    // Cancel pending card shares
  }
};
```

#### Offer Sharing (`src/lib/share.js` - Offer)
```javascript
export const Offer = {
  createSharings: (recipients, mode) => {
    // Create sharing records for multiple recipients
  },
  getShareMessage: (srcBrand, obj, sharePolicies, shareMode, offerName) => {
    // Generate offer-specific share messages
  },
  share: (targets, obj, offerId, mode) => {
    // Execute offer sharing via OfferService API
  },
  cancel: (id, sharingId) => {
    // Cancel pending offer shares
  }
};
```

### Data Models and Persistence

#### Core Sharing Models

**Sharing Model** (`src/lib/models/shared/Sharing.js`):
- Primary sharing record with lifecycle tracking
- Links to recipient and temporal data
- Supports multiple sharing modes and channels

**SharingRecipient Model** (`src/lib/models/shared/SharingRecipient.js`):
- Recipient information and contact details
- Person ID linking for registered users
- Mobile number and name storage

**SharingWhen Model** (`src/lib/models/shared/SharingWhen.js`):
- Temporal tracking of sharing lifecycle events
- Status timestamps (received, accepted, declined, etc.)
- Audit trail for sharing operations

#### Entity-Specific Sharing Models

**CardSharer Model** (`src/lib/models/Card/Sharer.js`):
- Card-specific sharing metadata
- Generation tracking for share chains
- Origin and sharing ID relationships

**OfferSharer Model** (`src/lib/models/Offer/Sharer.js`):
- Offer-specific sharing context
- Transfer mode and recipient tracking
- Integration with offer lifecycle

## Sharing Policies and Business Logic

### Share Policy Framework

Share policies are defined at the CardMaster level and control:

- **Available Share Modes**: Which sharing modes are permitted
- **Target Card Masters**: Where shares can be directed
- **Message Templates**: Custom messaging for share invitations
- **Validation Rules**: Business logic constraints
- **Security Controls**: Access and permission requirements

### Share Mode Definitions

#### Invite Mode
- **Purpose**: Invite users to register for a new card
- **Behavior**: Creates invitation with registration workflow
- **Target**: New users without existing cards
- **Validation**: Contact verification, duplicate prevention

#### Transfer Mode
- **Purpose**: Transfer ownership of existing entities
- **Behavior**: Moves entity from sender to recipient
- **Target**: Registered users with compatible cards
- **Validation**: Ownership verification, recipient capability

#### Clone Mode
- **Purpose**: Create copies of shareable entities
- **Behavior**: Duplicates entity for recipient
- **Target**: Users with compatible card types
- **Validation**: Cloning permissions, resource limits

#### Subobject Mode
- **Purpose**: Share derived or related entities
- **Behavior**: Creates related entity instances
- **Target**: Users within same card ecosystem
- **Validation**: Relationship constraints, policy compliance

### Validation and Security Framework

#### Contact Validation and Processing

**Mobile Number Handling**:
- Mobile number cleansing via `MobileNumber.cleanse()` for consistent formatting
- Full number resolution with country code preference (defaults to user's country)
- Self-sharing prevention: blocks sharing to user's own mobile number
- Number validation against preferred country codes with fallback logic

**Contact Name Processing**:
- Numeric-only name detection: when contact name is purely numeric, system attempts to replace with resolved profile name
- Contact name fallback hierarchy: resolved profile name → contact name → mobile number
- Contact image retrieval with thumbnail path support for enhanced UI experience

**Profile Resolution Logic**:
- Asynchronous profile lookup via `PersonService.profile()` for registered users
- Profile enrichment: name, image, mobile, and personId resolution
- Graceful degradation: continues with contact information if profile resolution fails
- Silent error handling: profile resolution errors don't block sharing process

#### Business Rule Enforcement

**Share Limit Validation**:
- Single vs. multiple recipient enforcement based on entity type and share mode
- Offers and tickets: strictly single recipient (limit = 1)
- Cards: supports multiple recipients based on share mode and policy configuration
- Real-time limit checking during contact selection with UI feedback

**Entity State Verification**:
- Entity existence validation via `_.className(obj).findById(id)`
- Active state requirement: entities must be in shareable state
- Transferable state checking for transfer mode operations
- Entity ownership verification for sharing permissions

**Policy Missing Scenarios**:
- Policy missing warning: logs `policy_missing` error when share policy not found for mode
- Graceful fallback: continues operation with default behavior when policy missing
- Share mode fallback: uses first available share mode or defaults (invite for cards, transfer for offers)

**Share Mode Resolution Logic**:
- Cards: defaults to 'invite' mode when no mode specified
- Offers: uses `shareModes[0]` or defaults to 'transfer' mode
- Policy-driven mode selection: respects CardMaster-level share mode restrictions
- Mode validation: ensures selected mode is supported by entity type

#### Security Controls
- Network connectivity requirements
- Authentication state verification
- Permission-based access control
- Fraud prevention measures

## User Experience Flows

### Contact Selection Flow

The sharing system provides a sophisticated contact selection experience with specific implementation behaviors:

1. **Permission Request**: Requests device contact access with graceful degradation
2. **Contact Loading**: Retrieves and processes device contacts with error handling
3. **Contact Processing**: Filters contacts requiring name and phone number presence
4. **Contact Display**: Presents searchable, filterable contact list with alphabetical sorting
5. **Multiple Phone Handling**: Shows selection dialog for contacts with multiple numbers
6. **Selection Management**: Handles single/multiple recipient selection with limit enforcement
7. **Self-Share Prevention**: Blocks sharing to user's own mobile number with error dialog
8. **Validation Feedback**: Provides real-time validation feedback and limit notifications
9. **Confirmation Flow**: Guides user through share confirmation with mode-specific messaging

**Contact Processing Specifics**:
- **Contact Filtering**: Only includes contacts with both name and at least one phone number
- **Name Resolution**: Uses `contactName()` function with fallback to phone number
- **Phone Number Cleansing**: Applies `MobileNumber.cleanse()` for consistent formatting
- **Country Code Handling**: Adds country code using `getFullNumber()` with user's country preference
- **Duplicate Detection**: Identifies previously shared contacts and marks them visually
- **Image Support**: Retrieves contact thumbnails when available for enhanced UI

**Selection Behavior Edge Cases**:
- **Single Contact Limit**: Immediately opens share detail modal for single-recipient modes
- **Multiple Phone Numbers**: Presents labeled selection dialog (e.g., "Mobile +1234567890")
- **Search Functionality**: Real-time filtering with search string clearing on selection
- **Limit Enforcement**: Disables further selection when recipient limit reached
- **Self-Share Detection**: Compares cleansed numbers with `_.Person.phone` to prevent self-sharing

### Share Execution Flow

The share execution process includes specific implementation behaviors and edge case handling:

1. **Pre-validation**: Checks network connectivity, permissions, and entity state
2. **Recipient Processing**: Resolves contact details and profiles with fallback handling
3. **Confirmation Dialog**: Presents mode-specific confirmation with recipient details
4. **Policy Validation**: Enforces business rules and constraints with error handling
5. **API Execution**: Calls appropriate sharing service endpoints with loading indicators
6. **Response Processing**: Handles partial success scenarios and recipient-specific failures
7. **Notification Delivery**: Sends SMS notifications to successful recipients
8. **User Feedback**: Provides detailed success/failure feedback with actionable options
9. **State Updates**: Updates local data and UI state with proper cleanup

**Implementation-Specific Behaviors**:

**Confirmation Dialog Logic**:
- **Empty Target Handling**: Silently returns without dialog if no recipients selected
- **Network Validation**: Shows network required dialog if offline before confirmation
- **Localization Fallback**: Uses 'discount.one' if entity-specific translation missing
- **Recipient Display**: Shows first recipient name or mobile number in confirmation message

**Response Processing Edge Cases**:
- **Partial Success Handling**: Filters successful vs. failed recipients from API response
- **Success Notification**: Attempts SMS notification even if some recipients failed
- **Failure Analysis**: Examines individual recipient failure reasons for appropriate handling
- **Timeout Handling**: 1-second delay before error presentation for better UX

**Loading State Management**:
- **Conditional Loading**: Respects `hideLoading` parameter for nested operations
- **Loading Cleanup**: Ensures loading indicators are hidden even on errors
- **Modal Dismissal**: Automatically dismisses share modal on successful completion

**Notification Integration**:
- **SMS Fallback**: Sends SMS notifications for successful shares regardless of push notification status
- **Error Tolerance**: Logs notification errors but doesn't fail the sharing operation
- **Clipboard Integration**: Copies recipient number to clipboard during notification process

## Integration Architecture

### External Platform Integration

#### SMS Integration (`src/lib/common/utils/system.js`)
- Native SMS composition and sending
- Contact number formatting and validation
- Cross-platform compatibility (iOS/Android)
- Permission handling and error management

#### Contact System Integration
- Device contact access and retrieval
- Contact image and metadata extraction
- Permission management and user consent
- Privacy-compliant contact handling

#### Notification Integration
- Push notification delivery for share events
- Local notification scheduling
- Badge management and update coordination
- Multi-channel notification support

### API Integration Patterns

#### RESTful Sharing Endpoints
- Standardized sharing API across entity types
- Consistent request/response patterns
- Error handling and status reporting
- Timeout management and retry logic

#### Service Layer Architecture
- Entity-specific service implementations
- Shared infrastructure and utilities
- Consistent error handling patterns
- Performance optimization strategies

## Data Flow Patterns

### Sharing Request Flow

```
User Selection → Contact Validation → Policy Check → API Call → Response Processing → UI Update
```

### Share Notification Flow

```
Share Creation → Notification Generation → Delivery → Recipient Processing → Status Update
```

### Share Acceptance Flow

```
Notification Receipt → User Action → Validation → Entity Creation → Confirmation → Sync
```

## Data Models and Database Schema

### Core Sharing Data Models

The sharing system uses a sophisticated data model architecture with clear separation of concerns and strong relational integrity. All sharing-related models are stored in the Realm database with proper indexing and relationship management.

#### Sharing Model Schema

The primary sharing record that tracks all sharing operations across entity types:

```typescript
interface Sharing {
  id: string;                    // Primary key
  mode: string;                  // Share mode: 'invite', 'transfer', 'clone', 'subobject'
  recipient: SharingRecipient;   // Recipient information (relationship)
  originId: string;              // ID of the original shared entity
  sharedId: string;              // ID of the created/shared entity (if applicable)
  generation: number;            // Share generation for tracking share chains
  channel: string;               // Delivery channel: 'sms', 'email', 'push', etc.
  when: SharingWhen;             // Temporal tracking (relationship)
  createdAt: Date;               // Share creation timestamp
  modifiedAt: Date;              // Last modification timestamp
}
```

**Database Schema** (`src/lib/common/db.js`):
```javascript
class Sharing extends Realm.Object {}
Sharing.schema = {
  name: 'Sharing',
  primaryKey: 'id',
  properties: {
    id: 'string',
    mode: 'string?',
    recipient: 'SharingRecipient',      // Required relationship
    originId: 'string?',
    sharedId: 'string?',
    generation: 'int?',
    channel: 'string?',
    when: 'SharingWhen',                // Required relationship
    createdAt: 'date?',
    modifiedAt: 'date?',
  },
};
```

#### SharingRecipient Model Schema

Stores recipient information with support for both registered and unregistered users:

```typescript
interface SharingRecipient {
  id: string;           // Primary key
  personId: string;     // Person ID for registered users (optional)
  mobile: string;       // Mobile number (primary contact method)
  name: string;         // Display name
  imageUrl: string;     // Profile image URL (optional)
}
```

**Database Schema**:
```javascript
class SharingRecipient extends Realm.Object {}
SharingRecipient.schema = {
  name: 'SharingRecipient',
  primaryKey: 'id',
  properties: {
    id: 'string',
    personId: 'string?',               // Links to Person model if registered
    mobile: 'string?',                 // Cleansed mobile number
    name: 'string?',                   // Contact or user name
    imageUrl: 'string?',               // Profile image URL
  },
};
```

#### SharingWhen Model Schema

Comprehensive temporal tracking for sharing lifecycle events:

```typescript
interface SharingWhen {
  id: string;           // Primary key
  received: Date;       // When recipient received the share notification
  accepted: Date;       // When recipient accepted the share
  declined: Date;       // When recipient declined the share
  registered: Date;     // When recipient completed registration (for invites)
  activated: Date;      // When shared entity was activated
  cancelled: Date;      // When share was cancelled by sender
}
```

**Database Schema**:
```javascript
class SharingWhen extends Realm.Object {}
SharingWhen.schema = {
  name: 'SharingWhen',
  primaryKey: 'id',
  properties: {
    id: 'string',
    received: { type: 'date?', default: null },
    accepted: { type: 'date?', default: null },
    declined: { type: 'date?', default: null },
    registered: { type: 'date?', default: null },
    activated: { type: 'date?', default: null },
    cancelled: { type: 'date?', default: null },
  },
};
```

### Entity-Specific Sharing Models

#### CardSharer Model Schema

Card-specific sharing metadata with generation tracking:

```typescript
interface CardSharer {
  id: string;           // Primary key
  personId: string;     // Person who shared the card
  name: string;         // Sharer's name
  imageUrl: string;     // Sharer's profile image
  mode: string;         // Share mode used
  originId: string;     // Original card ID
  sharingId: string;    // Associated sharing record ID
  generation: number;   // Share generation level
}
```

**Database Schema**:
```javascript
class CardSharer extends Realm.Object {}
CardSharer.schema = {
  name: 'CardSharer',
  primaryKey: 'id',
  properties: {
    id: 'string',
    personId: 'string?',
    name: 'string?',
    imageUrl: 'string?',
    mode: 'string?',                   // Share mode: 'invite', 'transfer', 'clone'
    originId: 'string?',               // Original card ID
    sharingId: 'string?',              // Links to Sharing record
    generation: 'int?',                // Share chain generation
  },
};
```

#### OfferSharer Model Schema

Offer-specific sharing context and metadata:

```typescript
interface OfferSharer {
  id: string;           // Primary key
  mode: string;         // Share mode: 'transfer', 'subobject'
  personId: string;     // Person who shared the offer
  name: string;         // Sharer's name
  originId: string;     // Original offer ID
  sharingId: string;    // Associated sharing record ID
  generation: number;   // Share generation level
  imageUrl: string;     // Sharer's profile image
}
```

**Database Schema**:
```javascript
class OfferSharer extends Realm.Object {}
OfferSharer.schema = {
  name: 'OfferSharer',
  primaryKey: 'id',
  properties: {
    id: 'string',
    mode: 'string?',                   // Share mode: 'transfer', 'subobject'
    personId: 'string?',
    name: 'string?',
    originId: 'string?',               // Original offer ID
    sharingId: 'string?',              // Links to Sharing record
    generation: 'int?',                // Share chain generation
    imageUrl: 'string?',               // Sharer's profile image
  },
};
```

### Entity Integration Patterns

#### Card Model Integration

Cards maintain sharing relationships through the `sharings` array property with specific behavioral patterns:

**Card Sharing Schema Integration**:
- `sharings`: Array of sharing records tracking all share operations for the card
- `sharer`: CardSharer information populated only for cards received via sharing
- Share mode determination: Uses CardMaster `sharePolicies` to determine available modes
- Generation tracking: Inherited from sharer for share chain depth management

**Card-Specific Sharing Behaviors**:
- **Multiple Share Support**: Cards can be shared to multiple recipients simultaneously
- **Share Mode Flexibility**: Supports invite, transfer, and clone modes based on policy
- **Image Handling**: Uses card-specific image properties (`imageProps`, `frontParam`) for sharing UI
- **Policy Inheritance**: Inherits sharing policies from associated CardMaster

#### Offer Model Integration

Offers integrate sharing through similar patterns with offer-specific constraints:

**Offer Sharing Schema Integration**:
- `sharings`: Array of sharing records (typically single entry due to transfer nature)
- `sharer`: OfferSharer information for tracking transfer provenance
- `shareModes`: Entity-level array defining available sharing modes for the offer
- Share mode resolution: Uses `shareModes[0]` or defaults to 'transfer'

**Offer-Specific Sharing Behaviors**:
- **Single Recipient Limitation**: Offers strictly enforce single recipient sharing (limit = 1)
- **Transfer-Focused**: Primarily uses transfer mode for ownership change
- **Share Cancellation**: Supports cancellation via first sharing record's `sharingId`
- **Policy Anomaly**: Message generation searches for 'subobject' policy regardless of actual mode
- **Image Fallback**: Uses `BENEFIT_IMAGE_SIZE` with offer image for sharing UI display

#### Ticket Model Integration

Tickets inherit offer sharing patterns with event-specific considerations:

**Ticket Sharing Inheritance**:
- Inherits all offer sharing behaviors and constraints
- Uses same single recipient limitation and transfer mode preference
- Maintains offer-style sharing record management
- Supports same cancellation mechanisms via sharing ID

#### Cross-Entity Sharing Differences

**Image Handling Variations**:
- **Cards**: Uses sophisticated image properties with front/back display support
- **Offers/Tickets**: Uses standardized benefit image size with single image source
- **UI Adaptation**: Share controller adapts image handling based on entity type

**Limit Enforcement Patterns**:
- **Cards**: Flexible recipient limits based on share mode and policy
- **Offers/Tickets**: Strict single recipient enforcement across all modes
- **UI Behavior**: Contact selection UI adapts to limit requirements automatically

### Data Relationships and Constraints

#### Primary Relationships

1. **Entity → Sharing**: One-to-many relationship (entities can have multiple shares)
2. **Sharing → SharingRecipient**: One-to-one relationship (each share has one recipient)
3. **Sharing → SharingWhen**: One-to-one relationship (each share has temporal tracking)
4. **Entity → EntitySharer**: One-to-one relationship (shared entities have sharer info)

#### Referential Integrity

- **Cascade Deletion**: When entities are deleted, associated sharing records are cleaned up
- **Orphan Prevention**: Sharing records maintain references to prevent orphaned data
- **Generation Tracking**: Share chains are tracked through generation counters
- **Temporal Consistency**: Timestamp ordering is enforced for lifecycle events

#### Indexing Strategy

- **masterId**: Indexed for efficient card-based queries
- **cardId**: Indexed for offer and reward sharing lookups
- **personId**: Indexed for user-based sharing queries
- **createdAt**: Indexed for temporal sorting and filtering

## Business Logic and Sharing Policies

### Share Policy Configuration

Share policies are defined at the CardMaster level and provide comprehensive control over sharing behavior. These policies determine what can be shared, how it can be shared, and to whom it can be shared.

#### Policy Structure

```typescript
interface SharePolicy {
  mode: string;                    // Share mode: 'invite', 'transfer', 'clone', 'subobject'
  toCardMasterId: string;          // Target CardMaster ID for shares
  toCardMasterIds: string[];       // Multiple target CardMaster IDs
  message: MessageTemplate[];      // Custom message templates
  enabled: boolean;                // Policy activation status
  constraints: PolicyConstraints;  // Business rule constraints
}

interface PolicyConstraints {
  maxRecipients: number;           // Maximum recipients per share
  maxGenerations: number;          // Maximum share chain depth
  expirationHours: number;         // Share expiration time
  requireRegistration: boolean;    // Recipient must be registered
  allowSelfShare: boolean;         // Allow sharing to self
}
```

#### Policy Resolution Logic

The sharing system resolves policies through a sophisticated hierarchical approach with specific implementation details:

**Card Share Message Generation**:
- Policy lookup: `sharePolicies.find(({ mode }) => mode === shareMode)`
- Target CardMaster resolution: supports both `toCardMasterId` (legacy) and `toCardMasterIds` (array)
- Message template priority: custom policy messages override default localized messages
- Fallback message generation: uses localized templates with brand, object, and slug interpolation
- Slug generation: last 6 characters of target CardMaster ID for message personalization

**Offer Share Message Generation**:
- Policy search anomaly: searches for 'subobject' mode regardless of actual share mode
- Hardcoded message template: always uses 'share.transfer.msg' localization key
- Brand and object name interpolation with offer-specific defaults
- Target CardMaster resolution with same fallback logic as cards

**Policy Missing Handling**:
- Warning logging: `$.watch('[ShareWidget]init', 'policy_missing', { masterId, mode })`
- Graceful continuation: sharing proceeds with default behavior when policy missing
- No blocking behavior: missing policies don't prevent sharing operations
- Default message fallback: uses system default messages when custom templates unavailable

**Share Mode Determination**:
1. **Entity-Level Configuration**: Uses entity's `shareModes` array if available
2. **First Available Mode**: Selects `shareModes[0]` as primary mode
3. **Type-Specific Defaults**: Cards default to 'invite', offers default to 'transfer'
4. **Policy Validation**: Ensures selected mode exists in CardMaster policies

### Validation Framework

#### Pre-Share Validation

Before any sharing operation, the system performs comprehensive validation:

**Network Connectivity Check**:
```javascript
if (!isOnline()) {
  Network.showRequired();
  return;
}
```

**Entity State Validation**:
- Entity must be in shareable state (active, not expired)
- Entity must not be already shared (for transfer mode)
- Entity must have valid sharing permissions
- Entity must meet business rule requirements

**Recipient Validation**:
- Mobile number format validation and cleansing
- Duplicate recipient detection within share request
- Recipient capability verification (can receive entity type)
- Existing relationship checks (prevent duplicate shares)

#### Share Mode Validation

Each share mode has specific validation requirements with implementation-specific behaviors:

**Invite Mode Validation**:
- Recipient must not already have the card type (triggers `hasCard` error if violated)
- CardMaster must support invite mode in `sharePolicies` configuration
- Invitation limits not exceeded (enforced at API level)
- Valid target CardMaster configuration with proper `toCardMasterId` or `toCardMasterIds`
- Network connectivity required: blocks operation if offline

**Transfer Mode Validation**:
- Sender must own the entity being transferred (ownership verification via `personId`)
- Entity must be in transferable state (active, not expired, not already shared)
- Single recipient requirement: strictly enforced (limit = 1) for offers and tickets
- Recipient profile resolution attempted but not required for operation to proceed
- Share cancellation support: maintains `sharingId` for cancellation operations

**Clone Mode Validation**:
- Entity must support cloning operations (CardMaster policy dependent)
- Cloning limits not exceeded (generation tracking prevents infinite chains)
- Recipient has compatible card type (CardMaster compatibility checking)
- Resource availability verification (backend enforcement)
- Multiple recipients supported for clone operations

**Subobject Mode Validation**:
- Parent-child relationship validity (CardMaster hierarchy enforcement)
- Subobject creation permissions (policy-based authorization)
- Target entity compatibility (CardMaster relationship validation)
- Business rule compliance (custom policy enforcement)
- Anomaly: offer message generation always searches for 'subobject' policy regardless of actual mode

**Cross-Mode Behavioral Differences**:
- **Contact Selection**: Cards support multiple contacts, offers/tickets limited to single contact
- **Profile Resolution**: All modes attempt profile lookup but handle failures gracefully
- **Message Generation**: Different localization keys and interpolation variables per mode
- **Cancellation Support**: All modes support cancellation via `shareCancel` API with `sharingId`

### Business Rule Engine

#### Share Limit Enforcement

The system enforces various sharing limits to prevent abuse:

**Per-Entity Limits**:
- Maximum shares per entity instance
- Time-based sharing rate limits
- Generation depth limits for share chains
- Recipient count limits per share operation

**Per-User Limits**:
- Daily/weekly/monthly sharing quotas
- Concurrent pending share limits
- Share frequency throttling
- Abuse detection and prevention

**Per-CardMaster Limits**:
- Brand-specific sharing policies
- Business rule enforcement
- Compliance requirement adherence
- Custom constraint validation

#### Expiration and Lifecycle Management

**Share Expiration Logic**:
```javascript
// Automatic expiration handling
const isExpired = (sharing) => {
  const { createdAt } = sharing;
  const expirationTime = moment(createdAt).add(policy.expirationHours, 'hours');
  return moment().isAfter(expirationTime);
};
```

**Lifecycle State Management**:
- Pending → Received → Accepted/Declined
- Automatic cleanup of expired shares
- Notification scheduling and delivery
- Status synchronization across devices

### Security Controls and Access Management

#### Authentication Requirements

All sharing operations require authenticated users:

- **Sender Authentication**: Must be logged in with valid session
- **Recipient Verification**: Contact validation and profile resolution
- **Entity Ownership**: Verification of sharing permissions
- **Session Validation**: Active session and token verification

#### Permission-Based Access Control

**Contact Access Permissions**:
```javascript
const status = getPermissions(CONTACTS);
if (status.allowed || status.limited) {
  // Proceed with contact access
} else {
  // Request permissions or show rationale
}
```

**Entity-Level Permissions**:
- Read permissions for entity access
- Share permissions for sharing operations
- Modify permissions for transfer operations
- Admin permissions for policy management

#### Fraud Prevention Measures

**Duplicate Detection**:
- Prevent sharing same entity to same recipient
- Detect and prevent circular sharing patterns
- Monitor for suspicious sharing patterns
- Rate limiting and abuse detection

**Validation Checks**:
- Mobile number verification and cleansing
- Person profile validation and resolution
- Entity state and ownership verification
- Business rule compliance checking

### Error Handling and Recovery

#### Error Classification

The sharing system categorizes errors for appropriate handling:

**Network Errors**:
- Offline state detection and handling
- Timeout management and retry logic
- Connection quality assessment
- Graceful degradation strategies

**Validation Errors**:
- Business rule violations
- Policy constraint failures
- Data integrity issues
- Permission denied scenarios

**System Errors**:
- Database operation failures
- Service unavailability
- Resource exhaustion
- Unexpected system states

#### Recovery Strategies

**Automatic Recovery**:
- Retry logic for transient failures
- Queue management for offline operations
- State synchronization on reconnection
- Background processing for pending operations

**User-Guided Recovery**:
- Clear error messaging and guidance
- Alternative action suggestions
- Manual retry options
- Support contact information

## Implementation-Specific Behavioral Nuances

### Share Controller State Management

The Share Controller implements sophisticated state management with specific behavioral patterns:

**Modal State Handling**:
- **Automatic Dismissal**: Share modal automatically dismisses on successful completion
- **State Preservation**: Maintains share state during modal transitions and navigation
- **Loading State Coordination**: Manages loading indicators across multiple async operations
- **Error State Recovery**: Preserves user selections during error recovery flows

**Contact Selection State**:
- **Search State Persistence**: Maintains search string during contact selection
- **Selection Limit Enforcement**: Dynamically updates UI based on recipient limits
- **Duplicate Prevention**: Tracks previously selected contacts to prevent duplicates
- **Self-Share Detection**: Real-time validation against user's own mobile number

### Message Generation Implementation Details

**Localization Key Resolution**:
- **Card Messages**: Uses `share.{mode}.msg` pattern with mode-specific keys
- **Offer Messages**: Hardcoded to use `share.transfer.msg` regardless of actual mode
- **Fallback Hierarchy**: `discount.one` → entity name → generic fallback
- **Variable Interpolation**: Supports `{brand}`, `{object}`, and `{slug}` variables

**Slug Generation Logic**:
- **Target CardMaster Slug**: Uses last 6 characters of target CardMaster ID
- **Personalization**: Provides unique identifier for message customization
- **Fallback Handling**: Graceful degradation when target CardMaster unavailable

### API Integration Behavioral Patterns

**Service Call Patterns**:
- **Conditional Loading**: Respects `hideLoading` parameter for nested operations
- **Error Propagation**: Maintains error context through service layer
- **Response Processing**: Handles both single and batch response formats
- **Timeout Management**: Implements service-specific timeout handling

**Retry and Fallback Logic**:
- **Network Retry**: Automatic retry for network-related failures
- **Service Degradation**: Graceful fallback when services unavailable
- **Partial Success Handling**: Processes mixed success/failure scenarios
- **Error Context Preservation**: Maintains detailed error information for debugging

### Contact System Integration Specifics

**Permission Handling Edge Cases**:
- **Limited Permission Support**: Handles iOS limited contact access gracefully
- **Permission Denial Recovery**: Provides manual entry option when contacts blocked
- **Background Permission Changes**: Adapts to permission changes during app lifecycle
- **Cross-Platform Differences**: Handles iOS/Android permission model differences

**Contact Data Processing**:
- **Phone Number Normalization**: Consistent formatting across different contact sources
- **Name Resolution Priority**: Contact name → profile name → phone number fallback
- **Image Caching**: Optimizes contact image loading for performance
- **Search Optimization**: Implements efficient contact filtering and search

### Error Handling Implementation Details

**Error Dialog Management**:
- **Timing Control**: 1-second delay before error presentation for better UX
- **Context Preservation**: Maintains error context for user decision making
- **Action Button Customization**: Dynamic button text based on error type
- **Recovery Flow Integration**: Seamless transition to recovery actions

**Specific Error Scenarios**:
- **HasCard Error**: Presents "Remind recipient?" dialog with Yes/No options
- **Network Errors**: Shows network requirement dialog with settings link
- **Timeout Errors**: Provides retry option with exponential backoff
- **Validation Errors**: Inline feedback with correction guidance

### Performance Optimization Implementations

**Lazy Loading Strategies**:
- **Contact List**: Loads contacts on-demand during share initiation
- **Profile Resolution**: Asynchronous profile lookup without blocking UI
- **Image Loading**: Progressive image loading for contact avatars
- **Policy Loading**: Deferred policy loading until share mode selection

**Caching Implementations**:
- **Contact Cache**: In-memory contact list caching for session duration
- **Permission Cache**: Caches permission status to reduce system calls
- **Profile Cache**: Temporary profile information caching
- **Policy Cache**: CardMaster policy caching for repeated operations

## User Experience Flows

### Overall Sharing User Journey

The sharing system provides a consistent, intuitive user experience across all entity types while accommodating the unique characteristics of each shareable entity.

```mermaid
graph TB
    subgraph "🎯 Share Initiation"
        USER[User]
        ENTITY[Select Entity]
        SHARE_BTN[Tap Share Button]
        POLICY_CHECK[Policy Validation]
    end

    subgraph "📱 Contact Selection"
        PERM_REQ[Request Contacts Permission]
        CONTACT_LOAD[Load Device Contacts]
        CONTACT_LIST[Display Contact List]
        SEARCH[Search & Filter]
        SELECT[Select Recipients]
    end

    subgraph "✅ Validation & Confirmation"
        VALIDATE[Validate Recipients]
        PROFILE_RES[Resolve Profiles]
        CONFIRM[Confirmation Dialog]
        FINAL_CHECK[Final Validation]
    end

    subgraph "🚀 Execution & Feedback"
        API_CALL[Execute Share API]
        NOTIFY[Send Notifications]
        SUCCESS[Success Feedback]
        ERROR[Error Handling]
    end

    subgraph "📊 State Management"
        UPDATE_UI[Update UI State]
        SYNC_DATA[Sync Local Data]
        TRACK[Analytics Tracking]
    end

    %% Flow connections
    USER --> ENTITY
    ENTITY --> SHARE_BTN
    SHARE_BTN --> POLICY_CHECK
    POLICY_CHECK --> PERM_REQ

    PERM_REQ --> CONTACT_LOAD
    CONTACT_LOAD --> CONTACT_LIST
    CONTACT_LIST --> SEARCH
    SEARCH --> SELECT

    SELECT --> VALIDATE
    VALIDATE --> PROFILE_RES
    PROFILE_RES --> CONFIRM
    CONFIRM --> FINAL_CHECK

    FINAL_CHECK --> API_CALL
    API_CALL --> NOTIFY
    NOTIFY --> SUCCESS
    API_CALL --> ERROR

    SUCCESS --> UPDATE_UI
    ERROR --> UPDATE_UI
    UPDATE_UI --> SYNC_DATA
    SYNC_DATA --> TRACK

    %% Styling with darker backgrounds and white text
    classDef initiation fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef contact fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef validation fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef execution fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef state fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff

    class USER,ENTITY,SHARE_BTN,POLICY_CHECK initiation
    class PERM_REQ,CONTACT_LOAD,CONTACT_LIST,SEARCH,SELECT contact
    class VALIDATE,PROFILE_RES,CONFIRM,FINAL_CHECK validation
    class API_CALL,NOTIFY,SUCCESS,ERROR execution
    class UPDATE_UI,SYNC_DATA,TRACK state
```

### Card Sharing Flow

Card sharing supports multiple modes with distinct user experiences for each mode:

```mermaid
graph TB
    subgraph "💳 Card Share Modes"
        CARD[Card Selected]
        MODE_CHECK{Share Mode?}
        INVITE_MODE[Invite Mode]
        TRANSFER_MODE[Transfer Mode]
        CLONE_MODE[Clone Mode]
    end

    subgraph "📧 Invite Flow"
        INV_CONTACT[Select Contacts]
        INV_MSG[Generate Invite Message]
        INV_SEND[Send Invitation]
        INV_TRACK[Track Registration]
    end

    subgraph "🔄 Transfer Flow"
        TRANS_SINGLE[Single Recipient Only]
        TRANS_VERIFY[Verify Ownership]
        TRANS_CONFIRM[Transfer Confirmation]
        TRANS_EXECUTE[Execute Transfer]
    end

    subgraph "📋 Clone Flow"
        CLONE_MULTI[Multiple Recipients OK]
        CLONE_POLICY[Check Clone Policy]
        CLONE_CREATE[Create Card Copies]
        CLONE_NOTIFY[Notify Recipients]
    end

    subgraph "📱 Recipient Experience"
        REC_NOTIFY[Receive Notification]
        REC_ACTION{Action Required?}
        REC_REGISTER[Registration Flow]
        REC_ACCEPT[Accept Share]
        REC_ACTIVATE[Activate Card]
    end

    %% Flow connections
    CARD --> MODE_CHECK
    MODE_CHECK -->|invite| INVITE_MODE
    MODE_CHECK -->|transfer| TRANSFER_MODE
    MODE_CHECK -->|clone| CLONE_MODE

    INVITE_MODE --> INV_CONTACT
    INV_CONTACT --> INV_MSG
    INV_MSG --> INV_SEND
    INV_SEND --> INV_TRACK

    TRANSFER_MODE --> TRANS_SINGLE
    TRANS_SINGLE --> TRANS_VERIFY
    TRANS_VERIFY --> TRANS_CONFIRM
    TRANS_CONFIRM --> TRANS_EXECUTE

    CLONE_MODE --> CLONE_MULTI
    CLONE_MULTI --> CLONE_POLICY
    CLONE_POLICY --> CLONE_CREATE
    CLONE_CREATE --> CLONE_NOTIFY

    INV_SEND --> REC_NOTIFY
    TRANS_EXECUTE --> REC_NOTIFY
    CLONE_NOTIFY --> REC_NOTIFY

    REC_NOTIFY --> REC_ACTION
    REC_ACTION -->|New User| REC_REGISTER
    REC_ACTION -->|Existing User| REC_ACCEPT
    REC_REGISTER --> REC_ACTIVATE
    REC_ACCEPT --> REC_ACTIVATE

    %% Styling with darker backgrounds and white text
    classDef card fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef invite fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef transfer fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef clone fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff
    classDef recipient fill:#d69e2e,stroke:#ed8936,stroke-width:2px,color:#ffffff

    class CARD,MODE_CHECK card
    class INVITE_MODE,INV_CONTACT,INV_MSG,INV_SEND,INV_TRACK invite
    class TRANSFER_MODE,TRANS_SINGLE,TRANS_VERIFY,TRANS_CONFIRM,TRANS_EXECUTE transfer
    class CLONE_MODE,CLONE_MULTI,CLONE_POLICY,CLONE_CREATE,CLONE_NOTIFY clone
    class REC_NOTIFY,REC_ACTION,REC_REGISTER,REC_ACCEPT,REC_ACTIVATE recipient
```

### Offer and Ticket Sharing Flow

Offers and tickets share similar sharing patterns with transfer-focused workflows:

```mermaid
graph TB
    subgraph "🎁 Offer/Ticket Selection"
        OFFER[Select Offer/Ticket]
        SHARE_CHECK{Shareable?}
        SHARE_MODE[Determine Share Mode]
        LIMIT_CHECK[Check Share Limits]
    end

    subgraph "👤 Recipient Selection"
        SINGLE_REC[Single Recipient Only]
        CONTACT_SEL[Contact Selection]
        PROFILE_CHECK[Profile Resolution]
        COMPAT_CHECK[Compatibility Check]
    end

    subgraph "🔄 Transfer Process"
        OWNERSHIP_VER[Verify Ownership]
        STATE_CHECK[Check Entity State]
        TRANSFER_CONF[Transfer Confirmation]
        EXECUTE_TRANS[Execute Transfer]
    end

    subgraph "📬 Notification & Delivery"
        SEND_NOTIF[Send Notification]
        TRACK_DELIVERY[Track Delivery]
        RECIPIENT_ACT[Recipient Action]
        COMPLETE[Transfer Complete]
    end

    subgraph "❌ Error Scenarios"
        NOT_SHAREABLE[Not Shareable]
        INVALID_REC[Invalid Recipient]
        TRANSFER_FAIL[Transfer Failed]
        RETRY_OPT[Retry Options]
    end

    %% Flow connections
    OFFER --> SHARE_CHECK
    SHARE_CHECK -->|Yes| SHARE_MODE
    SHARE_CHECK -->|No| NOT_SHAREABLE
    SHARE_MODE --> LIMIT_CHECK
    LIMIT_CHECK --> SINGLE_REC

    SINGLE_REC --> CONTACT_SEL
    CONTACT_SEL --> PROFILE_CHECK
    PROFILE_CHECK --> COMPAT_CHECK
    COMPAT_CHECK -->|Valid| OWNERSHIP_VER
    COMPAT_CHECK -->|Invalid| INVALID_REC

    OWNERSHIP_VER --> STATE_CHECK
    STATE_CHECK --> TRANSFER_CONF
    TRANSFER_CONF --> EXECUTE_TRANS
    EXECUTE_TRANS -->|Success| SEND_NOTIF
    EXECUTE_TRANS -->|Failure| TRANSFER_FAIL

    SEND_NOTIF --> TRACK_DELIVERY
    TRACK_DELIVERY --> RECIPIENT_ACT
    RECIPIENT_ACT --> COMPLETE

    NOT_SHAREABLE --> RETRY_OPT
    INVALID_REC --> RETRY_OPT
    TRANSFER_FAIL --> RETRY_OPT

    %% Styling with darker backgrounds and white text
    classDef selection fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef recipient fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef transfer fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef notification fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef error fill:#e53e3e,stroke:#f56565,stroke-width:2px,color:#ffffff

    class OFFER,SHARE_CHECK,SHARE_MODE,LIMIT_CHECK selection
    class SINGLE_REC,CONTACT_SEL,PROFILE_CHECK,COMPAT_CHECK recipient
    class OWNERSHIP_VER,STATE_CHECK,TRANSFER_CONF,EXECUTE_TRANS transfer
    class SEND_NOTIF,TRACK_DELIVERY,RECIPIENT_ACT,COMPLETE notification
    class NOT_SHAREABLE,INVALID_REC,TRANSFER_FAIL,RETRY_OPT error
```

## API Endpoints and Service Integration

### Core Sharing API Endpoints

The sharing system exposes RESTful API endpoints for all sharing operations, providing consistent interfaces across entity types with robust error handling and security measures.

#### Card Sharing Endpoints

**Share Card to Single Recipient**
```
POST /Cards/{id}/app/share
```

Request Body:
```typescript
{
  recipient: {
    mobile: string;        // Required: Cleansed mobile number
    name?: string;         // Optional: Recipient name
    email?: string;        // Optional: Email address
    personId?: string;     // Optional: Person ID if registered user
  };
  mode: ShareMode;         // Required: 'invite' | 'transfer' | 'clone'
  options?: {
    message?: string;      // Optional: Custom share message
    expiry?: number;       // Optional: Unix timestamp for expiration
  };
}
```

**Share Card to Multiple Recipients**
```
POST /Cards/{id}/app/shareToMany
```

Request Body:
```typescript
{
  recipients: Recipient[]; // Array of recipient objects
  mode: ShareMode;         // Share mode for all recipients
  options?: ShareOptions;  // Optional sharing configuration
}
```

**Cancel Card Share**
```
POST /Cards/{id}/app/share/cancel
```

Request Body:
```typescript
{
  sharingId: string;       // ID of the sharing record to cancel
}
```

#### Offer Sharing Endpoints

**Share Offer to Single Recipient**
```
POST /Offers/{id}/app/share
```

Request Body:
```typescript
{
  recipient: Recipient;    // Recipient information
  mode: 'transfer' | 'subobject'; // Offer share modes
  options?: {
    transferOwnership?: boolean; // Transfer ownership flag
    createCopy?: boolean;        // Create copy vs transfer
  };
}
```

**Share Offer to Multiple Recipients**
```
POST /Offers/{id}/app/shareToMany
```

Request Body:
```typescript
{
  recipients: Recipient[]; // Array of recipients
  mode: string;           // Share mode
  options?: {
    batchSize?: number;   // Batch processing size
    priority?: string;    // Processing priority
  };
}
```

**Cancel Offer Share**
```
POST /Offers/{id}/app/share/cancel
```

Request Body:
```typescript
{
  sharingId: string;      // Sharing record ID to cancel
}
```

### Service Layer Architecture

#### CardService Integration (`src/lib/common/services/card.js`)

The CardService provides the primary interface for card sharing operations:

```javascript
const CardService = {
  shareToMany: ({ id, recipients = {}, mode, options = {} }) =>
    call({
      ...API.shareToMany,
      routeParams: { id },
      body: { recipients, mode, options },
    }),

  shareCancel: ({ id, sharingId }) =>
    call({
      ...API.shareCancel,
      routeParams: { id },
      body: { sharingId },
    }),
};
```

**Key Features:**
- Standardized request/response handling
- Automatic retry logic for transient failures
- Timeout management with configurable limits
- Error classification and handling
- Request/response logging and monitoring

#### OfferService Integration (`src/lib/common/services/offer.js`)

Similar service patterns for offer sharing operations:

```javascript
const OfferService = {
  shareToMany: ({ id, recipients, mode, options = {} }) =>
    call({
      ...API.shareToMany,
      routeParams: { id },
      body: { recipients, mode, options },
    }),

  shareCancel: ({ id, sharingId }) =>
    call({
      ...API.shareCancel,
      routeParams: { id },
      body: { sharingId },
    }),
};
```

### External Platform Integrations

#### SMS Integration (`src/lib/common/utils/system.js`)

The sharing system integrates with native SMS capabilities for notification delivery:

```javascript
export const sms = (recipients, body) => new Promise((resolve, reject) => {
  const options = {
    recipients: recipients.map(r => `+${r}`),  // Format with country code
    body,                                      // Message content
    successTypes: ['sent', 'queued'],         // Success criteria
    allowAndroidSendWithoutReadPermission: true, // Android compatibility
  };

  const callback = (completed, cancelled, error) =>
    completed ? resolve() : reject(error || cancelled);

  SendSMS.send(options, callback);
});
```

**SMS Integration Features:**
- Cross-platform compatibility (iOS/Android)
- Automatic number formatting and validation
- Delivery status tracking
- Error handling and retry logic
- Permission management

#### Contact System Integration

Device contact integration for recipient selection:

```javascript
export const Contacts = {
  get: () => {
    const status = getPermissions(CONTACTS);

    if (status.allowed || status.limited) {
      return RNContacts.getAll()
        .catch(err => {
          $.watch('[ShareContacts]get', err);
          return [];
        });
    }
    return Promise.resolve([]);
  },

  getImage: (contactName) => {
    return Contacts.get().then(contacts => {
      const contact = contacts.find((c) => sameName(contactName, c));
      return contact?.thumbnailPath ? contact.thumbnailPath : '';
    });
  }
};
```

**Contact Integration Features:**
- Permission-based access control
- Contact image and metadata retrieval
- Search and filtering capabilities
- Privacy-compliant data handling
- Graceful degradation for permission denial

### Notification System Integration

#### Push Notification Integration

The sharing system integrates with Firebase Cloud Messaging for push notifications:

```javascript
const RemoteNotify = {
  init: async () => {
    const provider = await RemoteNotify.getProvider();
    RemoteNotify.provider = provider;
    provider.init();
  },

  handle: (notification, tapped, inApp) => {
    // Process sharing-related notifications
    const { data, nav } = notification;

    if (data.type === 'share') {
      // Handle share notification
      handleShareNotification(data, nav, tapped);
    }
  }
};
```

**Notification Features:**
- Multi-platform support (FCM, APNS, JPush)
- Background and foreground notification handling
- Deep linking integration for share actions
- Badge management and update coordination
- Analytics and tracking integration

#### Local Notification Integration

For time-based and location-based sharing notifications:

```javascript
const LocalNotify = {
  init: async () => {
    notifee.onForegroundEvent(Notifee.handle);
    notifee.onBackgroundEvent(Notifee.handle);

    if (DEVICE.ANDROID_MIN_8) {
      CHANNELS.forEach(channel => {
        if (channel.id) notifee.createChannel(channel);
      });
    }
  },

  schedule: (shareData, delay) => {
    // Schedule share reminder notifications
    const notification = createShareNotification(shareData);
    notifee.createTriggerNotification(notification, {
      type: TriggerType.TIMESTAMP,
      timestamp: Date.now() + delay,
    });
  }
};
```

### Data Flow Patterns

#### Share Request Data Flow

```
Client Request → Service Layer → API Gateway → Business Logic → Database → Response
```

1. **Client Request**: User initiates share through UI
2. **Service Layer**: CardService/OfferService processes request
3. **API Gateway**: Routes request to appropriate backend service
4. **Business Logic**: Validates policies and executes sharing logic
5. **Database**: Persists sharing records and updates entity state
6. **Response**: Returns success/error status to client

#### Share Notification Data Flow

```
Share Creation → Notification Service → External Platforms → Recipient Device → User Action
```

1. **Share Creation**: Backend creates sharing record
2. **Notification Service**: Generates appropriate notifications
3. **External Platforms**: Delivers via SMS, push, email
4. **Recipient Device**: Receives and displays notification
5. **User Action**: Recipient accepts/declines share

#### Share Synchronization Data Flow

```
Local Update → Sync Engine → Backend API → Other Devices → UI Update
```

1. **Local Update**: Share status changes locally
2. **Sync Engine**: Queues update for synchronization
3. **Backend API**: Processes and validates update
4. **Other Devices**: Receives synchronized state
5. **UI Update**: Updates interface across all devices

## Security and Permissions Framework

### Authentication Requirements

The sharing system implements comprehensive authentication and authorization controls to ensure secure sharing operations across all entity types.

#### User Authentication

**Session-Based Authentication**:
All sharing operations require authenticated user sessions with valid tokens:

```javascript
// Authentication validation before sharing
const validateSession = () => {
  const session = getCurrentSession();
  if (!session || !session.isValid()) {
    throw new Error('Authentication required');
  }
  return session;
};
```

**Multi-Factor Authentication Support**:
- Biometric authentication for sensitive sharing operations
- PIN/password verification for high-value transfers
- Device-based authentication tokens
- Session timeout and renewal mechanisms

#### Entity Ownership Verification

**Ownership Validation**:
```javascript
const verifyOwnership = (entityId, userId) => {
  const entity = getEntity(entityId);
  if (entity.personId !== userId) {
    throw new Error('Insufficient permissions');
  }
  return true;
};
```

**Delegation and Proxy Support**:
- Authorized delegate sharing capabilities
- Corporate account sharing permissions
- Family account sharing controls
- Temporary sharing authorization

### Permission Management System

#### Device Permissions

**Contact Access Permissions**:
The sharing system requires contact access for recipient selection:

```javascript
export const checkAndRequest = async (feature, options = {}) => {
  const { showRationale, type } = options;
  const status = await check(feature);

  if (status.allowed || status.limited) return true;
  if (showRationale) return showRationalDialog(feature, type);
  if (status.blocked || status.unavailable) return showDeniedDialog(feature);

  const res = await request(feature, type);
  return res;
};
```

**Permission States**:
- **Allowed**: Full contact access granted
- **Limited**: Restricted contact access
- **Denied**: No contact access (graceful degradation)
- **Blocked**: Permanently denied (show settings guidance)

#### Application-Level Permissions

**Feature-Based Permissions**:
- Share permission for each entity type
- Recipient limit permissions
- Share frequency permissions
- Administrative sharing permissions

**Role-Based Access Control**:
- User roles: Standard, Premium, Admin
- Entity-specific permissions
- Brand-specific sharing controls
- Geographic restriction compliance

### Security Controls and Validation

#### Input Validation and Sanitization

**Mobile Number Validation**:
```javascript
const validateMobileNumber = (number) => {
  const cleansed = MobileNumber.cleanse(number);
  if (!MobileNumber.isValid(cleansed)) {
    throw new ValidationError('Invalid mobile number format');
  }
  return cleansed;
};
```

**Recipient Validation**:
- Contact information sanitization
- Duplicate recipient detection
- Blacklist and whitelist checking
- Fraud pattern detection

#### Rate Limiting and Abuse Prevention

**Share Rate Limiting**:
```javascript
const rateLimiter = {
  checkLimit: (userId, entityType) => {
    const key = `share_${userId}_${entityType}`;
    const count = getShareCount(key, RATE_LIMIT_WINDOW);

    if (count >= MAX_SHARES_PER_WINDOW) {
      throw new RateLimitError('Share limit exceeded');
    }

    incrementShareCount(key);
    return true;
  }
};
```

**Abuse Detection Patterns**:
- Rapid successive sharing attempts
- Sharing to invalid or non-existent numbers
- Circular sharing pattern detection
- Suspicious recipient list patterns

#### Data Protection and Privacy

**Personal Data Handling**:
- Contact data encryption in transit and at rest
- Minimal data collection principles
- Automatic data purging for expired shares
- GDPR and privacy regulation compliance

**Secure Communication Channels**:
- TLS encryption for all API communications
- Certificate pinning for API endpoints
- Request signing and validation
- Replay attack prevention

### Access Control Matrix

#### Entity-Level Access Controls

| Entity Type | Read | Share | Transfer | Cancel | Admin |
|-------------|------|-------|----------|--------|-------|
| **Cards** | Owner, Delegates | Owner, Authorized | Owner Only | Owner, Admin | Admin Only |
| **Offers** | Owner, Recipients | Owner Only | Owner Only | Owner, Admin | Admin Only |
| **Tickets** | Owner, Recipients | Owner Only | Owner Only | Owner, Admin | Admin Only |
| **Rewards** | Owner Only | Limited | Limited | Owner Only | Admin Only |
| **Messages** | Owner, Recipients | Not Supported | Not Supported | Owner Only | Admin Only |

#### Share Mode Access Controls

| Share Mode | Authentication Level | Ownership Required | Recipient Validation |
|------------|---------------------|-------------------|---------------------|
| **Invite** | Standard | Yes | Contact Verification |
| **Transfer** | Enhanced | Yes | User Registration |
| **Clone** | Standard | Yes | Compatibility Check |
| **Subobject** | Standard | Yes | Relationship Validation |

### Security Monitoring and Auditing

#### Audit Trail Management

**Share Operation Logging**:
```javascript
const auditLog = {
  logShareOperation: (operation) => {
    const logEntry = {
      timestamp: new Date(),
      userId: operation.userId,
      entityId: operation.entityId,
      entityType: operation.entityType,
      shareMode: operation.mode,
      recipients: operation.recipients.map(r => ({ mobile: r.mobile })),
      result: operation.result,
      ipAddress: operation.ipAddress,
      deviceId: operation.deviceId
    };

    secureLog.write(logEntry);
  }
};
```

**Security Event Monitoring**:
- Failed authentication attempts
- Permission escalation attempts
- Unusual sharing patterns
- API abuse detection
- Data access violations

#### Incident Response Framework

**Automated Response Actions**:
- Account suspension for abuse patterns
- Rate limiting enforcement
- Notification to security team
- Automatic share cancellation for suspicious activity

**Manual Investigation Support**:
- Detailed audit trail access
- User behavior analysis tools
- Share pattern visualization
- Forensic data preservation

### Compliance and Regulatory Framework

#### Data Protection Compliance

**GDPR Compliance**:
- Right to data portability for sharing data
- Right to erasure for sharing records
- Data processing consent management
- Cross-border data transfer controls

**Regional Compliance**:
- Country-specific sharing restrictions
- Local data residency requirements
- Regulatory reporting capabilities
- Compliance audit support

#### Security Standards Adherence

**Industry Standards**:
- ISO 27001 security management
- SOC 2 Type II compliance
- PCI DSS for payment-related shares
- OWASP security guidelines

**Security Certifications**:
- Regular security assessments
- Penetration testing programs
- Vulnerability management
- Security awareness training

## Comprehensive Sharing System Architecture

### Complete System Architecture Diagram

This diagram illustrates the complete sharing system architecture, showing all components, data flows, and integration patterns:

```mermaid
graph TB
    subgraph "👤 User Interface Layer"
        UI_CARD[Card Share UI]
        UI_OFFER[Offer Share UI]
        UI_TICKET[Ticket Share UI]
        UI_CONTACT[Contact Selection]
        UI_CONFIRM[Confirmation Dialog]
    end

    subgraph "🎮 Controller Layer"
        SHARE_CTRL[Share Controller]
        CARD_CTRL[Card Controller]
        OFFER_CTRL[Offer Controller]
        TICKET_CTRL[Ticket Controller]
    end

    subgraph "🔧 Service Layer"
        CARD_SVC[Card Service]
        OFFER_SVC[Offer Service]
        PERSON_SVC[Person Service]
        NOTIFY_SVC[Notification Service]
    end

    subgraph "📡 API Gateway"
        API_CARD[Card Share API]
        API_OFFER[Offer Share API]
        API_PERSON[Person API]
        API_NOTIFY[Notification API]
    end

    subgraph "🧠 Business Logic Layer"
        POLICY_ENG[Policy Engine]
        VALID_ENG[Validation Engine]
        SECURITY_ENG[Security Engine]
        RATE_LIMIT[Rate Limiter]
    end

    subgraph "💾 Data Layer"
        REALM_DB[(Realm Database)]
        SHARING_MODEL[Sharing Models]
        ENTITY_MODEL[Entity Models]
        PERSON_MODEL[Person Models]
    end

    subgraph "🔐 Security Layer"
        AUTH[Authentication]
        PERM[Permissions]
        ENCRYPT[Encryption]
        AUDIT[Audit Logging]
    end

    subgraph "🌐 External Integrations"
        SMS_GATE[SMS Gateway]
        PUSH_NOTIF[Push Notifications]
        CONTACTS[Device Contacts]
        EMAIL_SVC[Email Service]
    end

    subgraph "📊 Monitoring & Analytics"
        METRICS[Share Metrics]
        ANALYTICS[Usage Analytics]
        ALERTS[Security Alerts]
        LOGS[System Logs]
    end

    %% UI to Controller connections
    UI_CARD --> SHARE_CTRL
    UI_OFFER --> SHARE_CTRL
    UI_TICKET --> SHARE_CTRL
    UI_CONTACT --> SHARE_CTRL
    UI_CONFIRM --> SHARE_CTRL

    %% Controller to Service connections
    SHARE_CTRL --> CARD_SVC
    SHARE_CTRL --> OFFER_SVC
    SHARE_CTRL --> PERSON_SVC
    SHARE_CTRL --> NOTIFY_SVC

    CARD_CTRL --> CARD_SVC
    OFFER_CTRL --> OFFER_SVC
    TICKET_CTRL --> OFFER_SVC

    %% Service to API connections
    CARD_SVC --> API_CARD
    OFFER_SVC --> API_OFFER
    PERSON_SVC --> API_PERSON
    NOTIFY_SVC --> API_NOTIFY

    %% API to Business Logic connections
    API_CARD --> POLICY_ENG
    API_OFFER --> POLICY_ENG
    API_CARD --> VALID_ENG
    API_OFFER --> VALID_ENG

    POLICY_ENG --> SECURITY_ENG
    VALID_ENG --> SECURITY_ENG
    SECURITY_ENG --> RATE_LIMIT

    %% Business Logic to Data connections
    POLICY_ENG --> REALM_DB
    VALID_ENG --> REALM_DB
    SECURITY_ENG --> REALM_DB

    REALM_DB --> SHARING_MODEL
    REALM_DB --> ENTITY_MODEL
    REALM_DB --> PERSON_MODEL

    %% Security Layer connections
    SHARE_CTRL --> AUTH
    SHARE_CTRL --> PERM
    API_CARD --> ENCRYPT
    API_OFFER --> ENCRYPT
    SECURITY_ENG --> AUDIT

    %% External Integration connections
    NOTIFY_SVC --> SMS_GATE
    NOTIFY_SVC --> PUSH_NOTIF
    NOTIFY_SVC --> EMAIL_SVC
    SHARE_CTRL --> CONTACTS

    %% Monitoring connections
    SHARE_CTRL --> METRICS
    API_CARD --> ANALYTICS
    API_OFFER --> ANALYTICS
    SECURITY_ENG --> ALERTS
    AUDIT --> LOGS

    %% Styling with darker backgrounds and white text
    classDef ui fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef controller fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef service fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef api fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef business fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff
    classDef data fill:#d69e2e,stroke:#ed8936,stroke-width:2px,color:#ffffff
    classDef security fill:#e53e3e,stroke:#f56565,stroke-width:2px,color:#ffffff
    classDef external fill:#38b2ac,stroke:#4fd1c7,stroke-width:2px,color:#ffffff
    classDef monitoring fill:#4a5568,stroke:#718096,stroke-width:2px,color:#ffffff

    class UI_CARD,UI_OFFER,UI_TICKET,UI_CONTACT,UI_CONFIRM ui
    class SHARE_CTRL,CARD_CTRL,OFFER_CTRL,TICKET_CTRL controller
    class CARD_SVC,OFFER_SVC,PERSON_SVC,NOTIFY_SVC service
    class API_CARD,API_OFFER,API_PERSON,API_NOTIFY api
    class POLICY_ENG,VALID_ENG,SECURITY_ENG,RATE_LIMIT business
    class REALM_DB,SHARING_MODEL,ENTITY_MODEL,PERSON_MODEL data
    class AUTH,PERM,ENCRYPT,AUDIT security
    class SMS_GATE,PUSH_NOTIF,CONTACTS,EMAIL_SVC external
    class METRICS,ANALYTICS,ALERTS,LOGS monitoring
```

### Data Flow Architecture

This diagram shows the detailed data flow patterns through the sharing system:

```mermaid
graph LR
    subgraph "📱 Client Side"
        USER_ACTION[User Share Action]
        CONTACT_SEL[Contact Selection]
        VALIDATION[Client Validation]
        UI_UPDATE[UI State Update]
    end

    subgraph "🔄 Request Processing"
        REQ_QUEUE[Request Queue]
        POLICY_CHECK[Policy Validation]
        SECURITY_CHECK[Security Validation]
        BIZ_LOGIC[Business Logic]
    end

    subgraph "💾 Data Operations"
        CREATE_SHARE[Create Sharing Record]
        UPDATE_ENTITY[Update Entity State]
        PERSIST_DATA[Persist to Database]
        SYNC_STATE[Sync State]
    end

    subgraph "📬 Notification Flow"
        GEN_NOTIF[Generate Notifications]
        ROUTE_NOTIF[Route Notifications]
        DELIVER_SMS[SMS Delivery]
        DELIVER_PUSH[Push Delivery]
    end

    subgraph "🎯 Recipient Processing"
        REC_RECEIVE[Recipient Receives]
        REC_ACTION[Recipient Action]
        ACCEPT_FLOW[Acceptance Flow]
        COMPLETE[Share Complete]
    end

    subgraph "🔄 Synchronization"
        SYNC_ENGINE[Sync Engine]
        CONFLICT_RES[Conflict Resolution]
        STATE_MERGE[State Merge]
        BROADCAST[Broadcast Updates]
    end

    %% Main flow connections
    USER_ACTION --> CONTACT_SEL
    CONTACT_SEL --> VALIDATION
    VALIDATION --> REQ_QUEUE

    REQ_QUEUE --> POLICY_CHECK
    POLICY_CHECK --> SECURITY_CHECK
    SECURITY_CHECK --> BIZ_LOGIC

    BIZ_LOGIC --> CREATE_SHARE
    CREATE_SHARE --> UPDATE_ENTITY
    UPDATE_ENTITY --> PERSIST_DATA
    PERSIST_DATA --> SYNC_STATE

    SYNC_STATE --> GEN_NOTIF
    GEN_NOTIF --> ROUTE_NOTIF
    ROUTE_NOTIF --> DELIVER_SMS
    ROUTE_NOTIF --> DELIVER_PUSH

    DELIVER_SMS --> REC_RECEIVE
    DELIVER_PUSH --> REC_RECEIVE
    REC_RECEIVE --> REC_ACTION
    REC_ACTION --> ACCEPT_FLOW
    ACCEPT_FLOW --> COMPLETE

    %% Synchronization flows
    PERSIST_DATA --> SYNC_ENGINE
    COMPLETE --> SYNC_ENGINE
    SYNC_ENGINE --> CONFLICT_RES
    CONFLICT_RES --> STATE_MERGE
    STATE_MERGE --> BROADCAST
    BROADCAST --> UI_UPDATE

    %% Styling with darker backgrounds and white text
    classDef client fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef processing fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef data fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef notification fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef recipient fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff
    classDef sync fill:#d69e2e,stroke:#ed8936,stroke-width:2px,color:#ffffff

    class USER_ACTION,CONTACT_SEL,VALIDATION,UI_UPDATE client
    class REQ_QUEUE,POLICY_CHECK,SECURITY_CHECK,BIZ_LOGIC processing
    class CREATE_SHARE,UPDATE_ENTITY,PERSIST_DATA,SYNC_STATE data
    class GEN_NOTIF,ROUTE_NOTIF,DELIVER_SMS,DELIVER_PUSH notification
    class REC_RECEIVE,REC_ACTION,ACCEPT_FLOW,COMPLETE recipient
    class SYNC_ENGINE,CONFLICT_RES,STATE_MERGE,BROADCAST sync
```

## Integration with Core Application Systems

### Cross-System Integration Patterns

The sharing system integrates seamlessly with other core application systems, maintaining consistency and leveraging shared infrastructure.

#### Card Management Integration

**Reference**: See [Card Management Documentation](./cards.md) for detailed card lifecycle information.

The sharing system integrates with the card management system through:
- **Card State Validation**: Ensures cards are in shareable states before allowing sharing operations
- **Card Master Policies**: Leverages CardMaster-level sharing policies and configurations
- **Card Lifecycle Events**: Triggers sharing-related events during card state transitions
- **Card Display Integration**: Provides consistent card visualization in sharing interfaces

#### Offer Management Integration

**Reference**: See [Offers Management Documentation](./offers.md) for comprehensive offer system details.

Integration patterns include:
- **Offer Transfer Logic**: Implements sophisticated offer ownership transfer mechanisms
- **Offer State Synchronization**: Maintains offer state consistency across sharing operations
- **Offer Expiration Handling**: Respects offer expiration rules during sharing processes
- **Offer Redemption Coordination**: Prevents sharing of already-redeemed offers

#### Ticket Management Integration

**Reference**: See [Tickets Documentation](./tickets.md) for event ticket system architecture.

Ticket sharing leverages:
- **Event Coordination**: Ensures ticket transfers maintain event integrity
- **Venue Integration**: Coordinates with venue management systems for ticket validation
- **Fraud Prevention**: Implements ticket-specific fraud prevention measures
- **Real-time Validation**: Provides real-time ticket validity checking

#### Message System Integration

**Reference**: See [Messages Documentation](./messages.md) for messaging system details.

Message integration includes:
- **Notification Coordination**: Leverages message system for share notifications
- **Template Management**: Uses message templates for sharing communications
- **Delivery Tracking**: Integrates with message delivery tracking systems
- **User Preference Respect**: Honors user notification preferences

### Performance and Optimization

#### Caching Strategies

**Contact Caching**:
- Device contact information cached for improved performance
- Contact image caching for faster UI rendering
- Permission status caching to reduce system calls
- Search result caching for improved responsiveness

**Policy Caching**:
- Share policy caching at CardMaster level
- Validation rule caching for faster processing
- Security policy caching for improved performance
- Rate limit status caching for abuse prevention

#### Optimization Techniques

**Lazy Loading**:
- Contact list lazy loading for large contact databases
- Share history lazy loading for improved initial load times
- Image lazy loading for contact avatars and entity images
- Policy lazy loading for unused entity types

**Batch Processing**:
- Multiple recipient processing in batches
- Notification delivery batching for efficiency
- Database operation batching for performance
- Sync operation batching for reduced network overhead

### Error Handling and Recovery

#### Comprehensive Error Management

**Sharing-Specific Error Codes**:
- **`hasCard`**: Recipient already has the card type being shared
- **`generationLimit`**: Share chain has reached maximum generation depth
- **`limit`**: Share limit exceeded (daily/total limits)
- **`mobileInvalid`**: Mobile number format validation failed
- **`offline`**: Network connectivity required for sharing operation
- **`timeout`**: Sharing operation timed out

**Special Error Handling Behaviors**:

**HasCard Error Recovery**:
- Presents user choice dialog: "Remind recipient?" with Yes/No options
- Yes option: sends SMS notification to recipient without creating share record
- No option: silently completes operation without further action
- Error message personalization: includes recipient name and failure count

**Timeout and Offline Handling**:
- Network connectivity check: `isOnline()` validation before share operations
- Automatic network dialog presentation for offline scenarios
- Timeout-specific error dialogs with retry options
- Graceful degradation: queues operations for later retry when possible

**Fallback Error Handling**:
- Generic error dialog for unrecognized error codes
- Watchdog logging: `$.watch('[ShareLib]share', 'fail without reason', recipient)`
- 1-second delay before error presentation for better UX
- Error context preservation: maintains recipient and failure details

**Contact Access Error Handling**:
- Permission denied: returns empty contact list without blocking operation
- Contact retrieval failure: logs error but continues with empty list
- Graceful degradation: allows manual number entry when contacts unavailable
- Silent error handling: `$.watch('[ShareContacts]get', err)` for monitoring

## Summary and Key Benefits

### Architectural Strengths

The Perkd sharing system demonstrates several key architectural strengths:

1. **Unified Architecture**: Consistent sharing patterns across all entity types while accommodating unique requirements
2. **Security-First Design**: Comprehensive security controls, authentication, and privacy protection
3. **Scalable Infrastructure**: Modular design supporting growth and new entity types
4. **User-Centric Experience**: Intuitive interfaces with sophisticated business logic hidden from users
5. **Integration Excellence**: Seamless integration with core application systems and external platforms

### Business Value Delivery

**Enhanced User Engagement**:
- Simplified sharing workflows increase user adoption
- Multi-entity sharing capabilities expand use cases
- Social features drive viral growth and user acquisition
- Personalized sharing experiences improve satisfaction

**Operational Efficiency**:
- Automated validation reduces manual intervention
- Comprehensive audit trails support compliance
- Rate limiting and abuse prevention protect system resources
- Monitoring and analytics enable data-driven optimization

**Security and Compliance**:
- Multi-layered security controls protect user data
- Comprehensive audit trails support regulatory compliance
- Privacy-by-design principles ensure data protection
- Industry-standard security practices maintain trust

### Critical Implementation Findings

Based on comprehensive codebase analysis, several critical implementation details were discovered that significantly impact sharing behavior:

**Policy Resolution Anomalies**:
- **Offer Message Generation Bug**: Offer sharing always searches for 'subobject' policy mode regardless of actual share mode, potentially causing incorrect message templates
- **Legacy Policy Support**: System supports both `toCardMasterId` (single) and `toCardMasterIds` (array) for backward compatibility
- **Missing Policy Graceful Handling**: When share policies are missing, system logs warnings but continues operation with default behavior

**Contact Processing Edge Cases**:
- **Numeric Name Handling**: When contact names are purely numeric, system attempts profile name resolution as fallback
- **Self-Share Prevention**: Real-time validation prevents users from sharing to their own mobile number with immediate error feedback
- **Multiple Phone Number Selection**: Contacts with multiple numbers trigger selection dialog with labeled options

**Error Handling Behavioral Nuances**:
- **HasCard Error Recovery**: Unique error handling presents "Remind recipient?" dialog allowing SMS notification without share record creation
- **Timeout Presentation Delay**: 1-second delay before error dialog presentation for improved user experience
- **Silent Error Tolerance**: Profile resolution and contact access errors don't block sharing operations

**API Integration Specifics**:
- **Conditional Loading States**: `hideLoading` parameter allows nested operations without conflicting loading indicators
- **Partial Success Processing**: System handles mixed success/failure scenarios in batch operations
- **Share Cancellation Mechanism**: Uses first sharing record's `sharingId` for cancellation operations

**State Management Complexities**:
- **Modal Auto-Dismissal**: Share modal automatically dismisses on successful completion
- **Search State Persistence**: Contact search string maintained during selection process
- **Selection Limit Enforcement**: Dynamic UI updates based on entity-specific recipient limits

### Future Enhancement Opportunities

**Advanced Features**:
- AI-powered recipient suggestions based on sharing patterns
- Advanced analytics for sharing behavior insights
- Integration with social media platforms for broader reach
- Blockchain-based sharing verification for high-value entities

**Performance Optimizations**:
- Real-time collaboration features for shared entities
- Advanced caching strategies for global deployment
- Edge computing integration for reduced latency
- Machine learning for fraud detection and prevention

**Platform Expansion**:
- Web platform sharing capabilities
- API exposure for third-party integrations
- White-label sharing solutions for partners
- Cross-platform sharing with other loyalty systems

**Implementation Improvements**:
- Fix offer message generation policy search anomaly
- Standardize error handling patterns across entity types
- Implement consistent loading state management
- Enhance policy missing scenarios with better fallbacks

This comprehensive sharing system architecture provides a robust foundation for secure, scalable, and user-friendly sharing operations while maintaining the flexibility needed for future enhancements and business requirements. The system successfully balances sophisticated business logic with intuitive user experiences, establishing Perkd as a leader in digital loyalty platform sharing capabilities.
