# Place Management System

## Overview

The Place Management System is a sophisticated location-intelligence platform that orchestrates the discovery, management, and contextual interaction with physical locations within the Perkd ecosystem. This system implements advanced business logic for location-based user experiences, intelligent place prompting, and seamless integration with loyalty card workflows. The architecture prioritizes user experience optimization through contextual place suggestions, sophisticated opening hours management, and intelligent regional filtering.

## Business Logic Architecture

The Place Management System implements sophisticated business rules that govern user interactions, data consistency, and performance optimization. These rules are designed to provide contextual, location-aware experiences while maintaining data integrity and system performance.

### Core Business Principles

#### Location Intelligence Strategy
The system employs a multi-faceted approach to location intelligence that balances user convenience with system performance:

1. **Contextual Place Discovery**: Places are discovered and presented based on user location, movement patterns, and card associations
2. **Intelligent Prompting**: Custom place creation is prompted only when specific business conditions are met to avoid user fatigue
3. **Regional Optimization**: Place data is filtered and synchronized based on user's geographical region to optimize performance
4. **Availability-Aware Presentation**: Places are presented with real-time availability status to enhance user decision-making

#### Data Consistency Framework
The system maintains data consistency through sophisticated synchronization and conflict resolution mechanisms:

1. **Local-First Operations**: All place operations function offline with eventual consistency
2. **Custom Place Ownership**: Only custom places created by users can be modified, ensuring data integrity
3. **Regional Synchronization**: Place data is synchronized based on regional boundaries to optimize bandwidth and storage
4. **Conflict Resolution**: Server-side conflict resolution ensures data consistency across multiple devices

### Core Components

- **Place Model** (`src/lib/common/models/Place.js`): Core data model implementing location intelligence and availability logic
- **Place Controller** (`src/controllers/Places.js`): Business logic coordination for user interactions and place lifecycle management
- **Place Widget** (`src/lib/widgets/Place.js`): Widget infrastructure providing card-integrated place management
- **Place Lists** (`src/lib/common/models/PlaceList.js`): Regional categorization and intelligent fallback mechanisms
- **Location Services** (`src/lib/common/location.js`): GPS coordination, distance calculation, and geospatial operations
- **Opening Hours Engine** (`src/lib/common/place.js`, `src/lib/common/utils/hours.js`): Sophisticated time-based availability and status determination
- **Custom Place Logic** (`src/lib/common/place.js`): Intelligent prompting and creation rules for user-generated places

### Integration Points

- **Card System**: Places are dynamically associated with loyalty cards through configurable `placeListIds` with intelligent fallback mechanisms
- **Widget Infrastructure**: Places are exposed as contextual widgets that adapt to card configuration and user location
- **Sync Engine**: Regional synchronization with remote place databases via `PlaceLists/app/sync` API with intelligent conflict resolution
- **Location Services**: Real-time GPS integration for proximity-based features, distance calculations, and contextual prompting
- **User Interface**: Comprehensive place management screens with location-aware sorting and filtering
- **Permission System**: Sophisticated location permission handling with graceful degradation for denied permissions

## Data Model Architecture

### Place Data Structure

The Place model represents physical locations with comprehensive metadata:

```typescript
interface Place {
  id: string;                    // Unique place identifier
  name: string;                  // Display name
  custom: boolean;               // User-created vs official place
  officialId?: string;           // Reference to official place if custom
  cardId: string;                // Associated loyalty card
  
  // Location Data
  geo: {
    type: "Point";
    coordinates: [number, number]; // [longitude, latitude]
  };
  lat: number;                   // Computed latitude for queries
  lng: number;                   // Computed longitude for queries
  
  // Address Information
  addressList: Address[];        // Structured address data
  
  // Business Information
  brand?: Brand;                 // Associated brand information
  openingHours?: OpeningHours;   // Operating hours configuration
  phoneList?: Phone[];           // Contact information
  urls?: URL[];                  // Web presence
  
  // Service Configuration
  pickup?: ServiceConfig;        // Pickup service availability
  deliver?: ServiceConfig;       // Delivery service availability
  dinein?: ServiceConfig;        // Dine-in service availability
  booking?: ServiceConfig;       // Booking service availability
  queuing?: ServiceConfig;       // Queue management availability
  
  // Settings and Metadata
  settingList?: Setting[];       // Place-specific configurations
  external?: ExternalData;       // Third-party system integrations
  timeZone?: string;             // Local timezone for hours calculation
  
  // Audit Fields
  createdAt: Date;
  modifiedAt: Date;
  deletedAt?: Date;
}
```

### Opening Hours System

The opening hours system provides sophisticated time-based availability logic:

```typescript
interface OpeningHours {
  periods: Period[];             // Regular weekly schedule
  specific?: SpecificHours[];    // Date-specific overrides (holidays, etc.)
}

interface Period {
  open: { day: number; time: string };   // Opening time (day 1-7, time HHMM)
  close: { day: number; time: string };  // Closing time
  busy?: { time: string; day: number };  // Peak hours indicator
}

interface SpecificHours {
  date: { year: number; month: number; day: number };
  periods: Period[];
}
```

### Place List Management

Places are organized through PlaceList entities that provide categorization and regional grouping:

```typescript
interface PlaceList {
  id: string;
  name: string;
  regions: string[];             // Country/region codes
  ids: string[];                 // Array of place IDs in this list
  tags?: string[];               // Categorization tags
}
```

## Sophisticated Business Logic Patterns

### Custom Place Creation Intelligence

The system implements sophisticated business rules for when and how to prompt users to create custom places, designed to maximize user value while minimizing prompt fatigue.

#### Intelligent Prompting Algorithm
The custom place prompting system evaluates multiple contextual factors to determine optimal prompting moments:

**Location Accuracy Requirements**:
- GPS accuracy must be better than the configured threshold (typically 65 meters)
- User must be stationary (speed below 0.5 m/s) to ensure accurate location capture
- Location must not be at user's personal addresses to avoid home/work prompts

**Temporal Constraints**:
- Minimum delay between prompts (typically 24 hours) to prevent user fatigue
- Prompts are suppressed during "Do Not Disturb" periods
- Time-based filtering prevents prompts during inappropriate hours

**Contextual Business Rules**:
- Cards with existing official places (placeListIds) skip custom place prompting to avoid confusion
- Managed cards (corporate/institutional) disable custom place creation entirely
- Cards with `noLocation` option bypass all location-based features
- Proximity to existing custom places (within 50 meters) suppresses new prompts

**User Behavior Analysis**:
- System tracks user's refusal patterns to adjust prompting frequency
- "Do Not Disturb" positions are cached to avoid repeated prompts at declined locations
- Personal place detection prevents prompts at user's home or work addresses

#### Place Lifecycle Management

**Creation Process**:
1. **Validation Phase**: Address validation is mandatory; places cannot be created without valid address data
2. **Coordinate Precision**: GPS coordinates are rounded to 6 decimal places for consistency and privacy
3. **Automatic Naming**: If no name is provided, system generates contextual names based on location and card association
4. **Official Place Replacement**: When users create custom places, any associated official places are removed from the sorted list to prevent duplication

**Editing Permissions**:
- Only custom places (user-created) can be edited or deleted
- Official places are read-only and display in view-only mode
- Editing custom places creates new versions rather than modifying official place data
- Place modifications trigger immediate local updates and background synchronization

**Deletion Logic**:
- Deletion is implemented as soft delete (setting `deletedAt` timestamp) for data recovery
- Deleted places are removed from sorted lists but retained in database for sync purposes
- Official place associations are restored when custom places are deleted

### Regional Intelligence and Fallback Mechanisms

The system implements sophisticated regional management to ensure place availability across different geographical contexts.

#### Regional Filtering Strategy
**Primary Regional Logic**:
- Places are primarily filtered by user's current country code
- Regional filtering optimizes performance by reducing dataset size
- Cross-border scenarios are handled through intelligent fallback mechanisms

**Fallback Hierarchy**:
1. **Current Region**: Places in user's current country are prioritized
2. **Default Region**: If no places exist in current region, system falls back to card's default region
3. **User Preference Regions**: System checks user's configured discovery regions in preference order
4. **Global Fallback**: If no regional places exist, system may present global options based on card configuration

**Performance Optimization**:
- Regional filtering occurs at the database query level to minimize memory usage
- Place lists are cached by region to improve response times
- Background synchronization is region-aware to optimize bandwidth usage

#### PlaceList Management Logic

**Configuration-Based Filtering**:
- Cards with `noDefaultPlaceList` option skip fallback mechanisms entirely
- PlaceList associations determine which places are visible for each card
- Multiple cards can share PlaceLists, enabling efficient place management across card portfolios

**Dynamic List Resolution**:
- PlaceList IDs are resolved to actual place collections at runtime
- Union operations combine multiple PlaceLists while eliminating duplicates
- List resolution respects regional boundaries and user preferences

### Opening Hours Business Intelligence

The opening hours system implements sophisticated time-based logic that handles complex business scenarios including late-night operations, timezone management, and special hours.

#### Status Determination Algorithm
**Real-Time Status Calculation**:
- **OPEN**: Currently accepting customers with more than 1 hour until closing
- **CLOSING**: Open but closing within 1 hour (provides urgency context)
- **CLOSED**: Currently closed with next opening time information
- **UNKNOWN**: No opening hours configured or timezone issues

**Late-Night Operation Handling**:
- Places closing after midnight are handled through day-spanning logic
- Close times before open times indicate overnight operations
- Time calculations account for day boundaries and timezone transitions

**Special Hours Support**:
- Date-specific hours override regular weekly schedules
- Holiday hours and special events are supported through specific period definitions
- Seasonal adjustments can be configured through the specific hours mechanism

#### Timezone Intelligence
**Automatic Timezone Detection**:
- Place timezones are automatically determined based on geographical coordinates
- Cross-timezone calculations ensure accurate status determination for traveling users
- Performance optimization assumes places in the same region share timezones

**Performance Optimization**:
- Status calculations are batched for multiple places to improve performance
- Timezone calculations are cached to avoid repeated computations
- Status updates are event-driven to maintain real-time accuracy

### Location-Based Sorting and Filtering

#### Distance-Based Intelligence
**Sorting Strategy**:
- When location permission is granted, places are sorted by proximity to user
- Places without valid coordinates are appended to the end of sorted lists
- Distance calculations use high-precision geospatial algorithms for accuracy

**Performance Optimization**:
- Spatial queries use bounding box calculations to limit search scope
- Distance calculations are cached and updated only when user location changes significantly
- Sorting operations are debounced to prevent excessive recalculation during movement

**Fallback Mechanisms**:
- When location permission is denied, places are sorted alphabetically by name
- Invalid coordinates are handled gracefully without breaking the sorting algorithm
- Network-based location services provide fallback when GPS is unavailable

### Opening Hours Business Logic

The opening hours system implements sophisticated time-based logic:

#### Status Determination
Places have real-time status calculation based on current time and configured hours:

- **OPEN**: Currently accepting customers
- **CLOSING**: Open but closing within 1 hour
- **CLOSED**: Currently closed
- **UNKNOWN**: No opening hours configured

#### Time Zone Handling
- Automatic timezone detection based on place location
- Cross-timezone calculation for accurate status determination
- Support for late-night operations spanning midnight

#### Special Hours Support
- Holiday and special event hour overrides
- Date-specific hour configurations
- Seasonal hour adjustments

### Location Intelligence

#### Distance and Proximity
The system provides comprehensive location-based features:

1. **Distance Calculation**: Accurate distance measurement using geolib
2. **Proximity Sorting**: Automatic sorting of places by distance from user
3. **ETA Calculation**: Integration with mapping services for travel time estimates
4. **Geofencing**: Location-based triggers and notifications

#### Regional Management
Places are organized by geographical regions:

1. **Country-Based Filtering**: Places filtered by user's current country
2. **Regional Fallbacks**: Intelligent fallback to nearby regions when local places unavailable
3. **Multi-Region Support**: Support for users traveling across regions

## User Experience Flows

### Intelligent Place Discovery Flow

The place discovery system implements sophisticated business logic that adapts to user context, location permissions, and card configurations to provide optimal user experiences.

```mermaid
graph TB
    subgraph "🎯 Context Analysis"
        A[User Opens Card] --> B{Card Configuration}
        B -->|Has PlaceLists| C[Load Official Places]
        B -->|Custom Only| D[Load Custom Places]
        B -->|No Location| E[Show All Places Alphabetically]
        C --> F{Location Permission?}
        D --> F
        F -->|Granted & Precise| G[Get Current Location]
        F -->|Granted & Imprecise| H[Network Location]
        F -->|Denied| I[Alphabetical Sort]
    end

    subgraph "📍 Location Intelligence"
        G --> J[Calculate Distances]
        H --> J
        J --> K[Apply Regional Filter]
        K --> L[Check Opening Status]
        L --> M[Sort by Proximity]
        M --> N{Show Open Only?}
        N -->|Yes| O[Filter Open Places]
        N -->|No| P[Show All Places]
        O --> Q[Display Contextual List]
        P --> Q
    end

    subgraph "� Dynamic Updates"
        Q --> R[Monitor Location Changes]
        R --> S{Significant Movement?}
        S -->|Yes| T[Recalculate Distances]
        S -->|No| U[Maintain Current Sort]
        T --> V[Update Place Order]
        V --> Q
        U --> Q
    end

    subgraph "🎯 User Actions"
        Q --> W[Select Place]
        Q --> X{Can Add Custom?}
        X -->|Yes| Y[Smart Place Prompt]
        X -->|No| Z[View Only Mode]
        W --> AA[Place Detail View]
        Y --> BB[Custom Place Creation]
    end

    style A fill:#1a1a2e,color:#ffffff
    style Q fill:#16213e,color:#ffffff
    style AA fill:#0f3460,color:#ffffff
    style BB fill:#e94560,color:#ffffff
```

### Intelligent Place Creation Flow

The place creation system implements sophisticated validation and business logic to ensure data quality while providing optimal user experience.

```mermaid
graph TB
    subgraph "🎯 Creation Context"
        A[Smart Place Prompt] --> B{Trigger Conditions}
        B -->|Location Accurate| C[Check Proximity Rules]
        B -->|Location Inaccurate| D[Manual Creation Only]
        C --> E{Near Existing Place?}
        E -->|Yes| F[Suppress Prompt]
        E -->|No| G[Show Creation Option]
        D --> H[Address Entry Required]
        G --> I[Request Location Permission]
    end

    subgraph "📍 Location Validation"
        I --> J{Permission Status}
        J -->|Granted & Precise| K[Show Interactive Map]
        J -->|Granted & Imprecise| L[Show Map with Warning]
        J -->|Denied| M[Address Form Only]
        K --> N[User Selects Location]
        L --> N
        N --> O[Validate Coordinates]
        O --> P{Within Accuracy Threshold?}
        P -->|Yes| Q[Reverse Geocoding]
        P -->|No| R[Request Manual Adjustment]
        R --> N
    end

    subgraph "📝 Data Validation"
        Q --> S[Pre-fill Address Data]
        M --> T[Manual Address Entry]
        H --> T
        S --> U[Address Validation]
        T --> U
        U --> V{Address Valid?}
        V -->|Yes| W[Enable Place Creation]
        V -->|No| X[Show Address Errors]
        X --> T
        W --> Y[Optional: Opening Hours]
        Y --> Z[Optional: Contact Info]
        Z --> AA[Form Completion]
    end

    subgraph "💾 Intelligent Persistence"
        AA --> BB{Data Quality Check}
        BB -->|High Quality| CC[Create with Confidence]
        BB -->|Low Quality| DD[Create with Warnings]
        CC --> EE[Check for Duplicates]
        DD --> EE
        EE --> FF{Duplicate Detected?}
        FF -->|Yes| GG[Merge or Replace Logic]
        FF -->|No| HH[Create New Place]
        GG --> II[Update Existing]
        HH --> JJ[Background Sync]
        II --> JJ
        JJ --> KK[Update Place Lists]
        KK --> LL[Refresh UI]
    end

    style A fill:#1a1a2e,color:#ffffff
    style K fill:#16213e,color:#ffffff
    style W fill:#0f3460,color:#ffffff
    style HH fill:#e94560,color:#ffffff
```

### Sophisticated Place Management Flow

The place management system implements complex business logic for place lifecycle management, permission validation, and data consistency.

```mermaid
graph TB
    subgraph "🏪 Context-Aware Interaction"
        A[Place List View] --> B[Select Place]
        B --> C{Place Ownership}
        C -->|Official Place| D[Read-Only Details]
        C -->|Custom Place| E{User Permissions}
        C -->|Shared Place| F[Limited Access]
        E -->|Owner| G[Full Edit Access]
        E -->|Viewer| H[View Only]
        D --> I[Show Official Data]
        F --> J[Show Shared Data]
        G --> K[Edit/Delete Options]
        H --> I
    end

    subgraph "✏️ Intelligent Editing"
        K --> L{Edit Action}
        L -->|Edit| M[Load Current State]
        L -->|Delete| N[Deletion Validation]
        M --> O[Validate Edit Permissions]
        O --> P{Can Modify?}
        P -->|Yes| Q[Show Edit Form]
        P -->|No| R[Permission Denied]
        Q --> S[Apply Business Rules]
        S --> T[Validate Changes]
        T --> U{Valid Update?}
        U -->|Yes| V[Save Changes]
        U -->|No| W[Show Validation Errors]
        W --> Q
    end

    subgraph "🗑️ Safe Deletion Logic"
        N --> X{Has Dependencies?}
        X -->|Yes| Y[Show Impact Warning]
        X -->|No| Z[Confirm Deletion]
        Y --> AA{User Confirms?}
        AA -->|Yes| BB[Cascade Delete]
        AA -->|No| CC[Cancel Operation]
        Z --> DD{User Confirms?}
        DD -->|Yes| EE[Soft Delete]
        DD -->|No| CC
        BB --> FF[Update References]
        EE --> FF
        FF --> GG[Background Sync]
        GG --> HH[Refresh UI]
    end

    subgraph "🔄 Data Consistency"
        V --> II[Update Local Database]
        II --> JJ[Queue Sync Operation]
        JJ --> KK{Network Available?}
        KK -->|Yes| LL[Immediate Sync]
        KK -->|No| MM[Queue for Later]
        LL --> NN[Conflict Resolution]
        MM --> OO[Background Retry]
        NN --> PP{Conflicts Detected?}
        PP -->|Yes| QQ[Merge Strategy]
        PP -->|No| RR[Update Complete]
        QQ --> SS[Apply Resolution]
        SS --> RR
        RR --> TT[Emit Change Events]
        TT --> UU[Update UI Components]
    end

    style A fill:#1a1a2e,color:#ffffff
    style I fill:#16213e,color:#ffffff
    style V fill:#0f3460,color:#ffffff
    style EE fill:#e94560,color:#ffffff
```

## Data Flow Architecture

### Sophisticated Synchronization Strategy

The place management system implements a multi-layered synchronization architecture that balances performance, consistency, and user experience through intelligent business logic.

#### Regional Synchronization Intelligence

**Regional Optimization Strategy**:
The system implements region-aware synchronization to optimize bandwidth usage and improve performance:

1. **Dynamic Region Detection**: User's current country code determines primary sync region
2. **Intelligent Fallback**: When no places exist in current region, system syncs fallback regions based on user preferences
3. **Bandwidth Optimization**: Only places relevant to user's region are synchronized to minimize data transfer
4. **Cross-Border Support**: Traveling users automatically trigger sync for new regions while maintaining cached data from previous regions

**Sync Prioritization Logic**:
- **Custom Places**: User-created places are prioritized for immediate synchronization
- **Official Places**: Official place updates are batched and synchronized during background operations
- **Regional Places**: Places in user's current region are synchronized more frequently than distant places
- **Cached Places**: Previously accessed places are maintained in local cache for offline access

#### Conflict Resolution Framework

**Business Rule Enforcement**:
The system implements sophisticated conflict resolution that respects business rules and user ownership:

1. **Custom Place Ownership**: Only the creating user can modify custom places, preventing unauthorized changes
2. **Official Place Protection**: Official places cannot be modified by users, ensuring data integrity
3. **Temporal Precedence**: Most recent modifications take precedence when conflicts occur
4. **Server Authority**: Server-side validation ensures business rules are enforced consistently

**Conflict Detection Mechanisms**:
- **Timestamp Comparison**: Modification timestamps determine conflict scenarios
- **Checksum Validation**: Data integrity is verified through checksum comparison
- **Field-Level Conflicts**: Individual field conflicts are resolved independently when possible
- **User Notification**: Users are notified when their changes conflict with server updates

#### Intelligent Sync Flow

```mermaid
graph TB
    subgraph "📱 Local Operations"
        A[User Action] --> B{Action Type}
        B -->|Custom Place| C[High Priority Queue]
        B -->|Official Place View| D[Low Priority Queue]
        C --> E[Immediate Local Update]
        D --> F[Cached Data Access]
        E --> G[Validate Business Rules]
        F --> H[Background Refresh]
    end

    subgraph "🔄 Intelligent Sync Engine"
        G --> I{Network Available}
        I -->|Yes| J[Priority-Based Sync]
        I -->|No| K[Queue for Later]
        J --> L[Regional Filtering]
        L --> M[Batch Operations]
        M --> N[Upload Changes]
        N --> O[Download Regional Updates]
        K --> P[Offline Mode]
        P --> Q[Local-Only Operations]
    end

    subgraph "☁️ Server-Side Processing"
        O --> R[PlaceLists API]
        R --> S[Business Rule Validation]
        S --> T{Validation Passed}
        T -->|Yes| U[Apply Changes]
        T -->|No| V[Return Errors]
        U --> W[Conflict Detection]
        W --> X{Conflicts Found}
        X -->|Yes| Y[Resolution Strategy]
        X -->|No| Z[Success Response]
        Y --> AA[Merge or Reject]
        AA --> Z
    end

    subgraph "🔄 Local Consistency"
        Z --> BB[Process Server Response]
        V --> CC[Handle Validation Errors]
        BB --> DD[Update Local Database]
        CC --> EE[User Notification]
        DD --> FF[Emit Change Events]
        FF --> GG[Update UI Components]
        GG --> HH[Refresh Place Lists]
        EE --> II[Retry or Cancel]
    end

    style A fill:#1a1a2e,color:#ffffff
    style J fill:#16213e,color:#ffffff
    style R fill:#0f3460,color:#ffffff
    style DD fill:#e94560,color:#ffffff
```

### Location Intelligence Framework

The location services system implements sophisticated business logic for location acquisition, processing, and integration with place management.

#### Permission-Aware Location Strategy

**Tiered Permission Management**:
The system implements a sophisticated permission model that adapts to different permission levels:

1. **Full Permission (Precise Location)**:
   - High-precision distance calculations
   - Proximity-based sorting
   - Custom place prompting
   - Real-time ETA calculations

2. **Limited Permission (Coarse Location)**:
   - Approximate distance calculations
   - Region-based filtering
   - Limited proximity features
   - Degraded but functional experience

3. **No Permission**:
   - Alphabetical place sorting
   - Manual location entry
   - Region-based filtering from user preferences
   - Graceful degradation of location features

**Permission Request Logic**:
- Permission requests are contextual and triggered only when needed
- System tracks permission denial patterns to avoid repeated prompts
- Educational UI explains benefits of location permission
- Graceful fallbacks maintain functionality regardless of permission state

#### Location Accuracy Business Rules

**Accuracy-Based Feature Activation**:
The system implements business rules that activate features based on location accuracy:

1. **High Accuracy (< 65 meters)**:
   - Custom place prompting enabled
   - Precise distance sorting
   - Detailed ETA calculations
   - Proximity-based notifications

2. **Medium Accuracy (65-500 meters)**:
   - Distance sorting with accuracy indicators
   - Approximate ETA calculations
   - No custom place prompting
   - Limited proximity features

3. **Low Accuracy (> 500 meters)**:
   - Region-based filtering only
   - No distance-based sorting
   - No custom place prompting
   - Fallback to manual location entry

**Movement Detection Logic**:
- Stationary detection (speed < 0.5 m/s) enables place prompting
- Movement detection triggers distance recalculation
- Significant location changes (> 100 meters) trigger place list resorting
- Continuous movement suppresses place prompting to avoid disruption

#### Sophisticated Location Processing

```mermaid
graph TB
    subgraph "📍 Multi-Source Location Acquisition"
        A[Location Request] --> B{Permission Level}
        B -->|Full & Precise| C[High Precision GPS]
        B -->|Limited| D[Network Location]
        B -->|Denied| E[Manual Location Entry]
        C --> F[Accuracy Validation]
        D --> F
        F --> G{Accuracy Threshold}
        G -->|High Accuracy| H[Full Feature Set]
        G -->|Medium Accuracy| I[Limited Features]
        G -->|Low Accuracy| J[Basic Features]
    end

    subgraph "🧠 Intelligent Processing"
        H --> K[Location Cache]
        I --> K
        J --> K
        K --> L[Movement Detection]
        L --> M{Movement Type}
        M -->|Stationary| N[Enable Place Prompts]
        M -->|Moving Slowly| O[Periodic Updates]
        M -->|Moving Quickly| P[Suppress Prompts]
        N --> Q[Proximity Analysis]
        O --> Q
        P --> Q
        Q --> R[Distance Calculation]
        R --> S[Spatial Indexing]
    end

    subgraph "🏪 Context-Aware Integration"
        S --> T[Place Distance Updates]
        T --> U[Dynamic Sorting]
        U --> V[ETA Calculation]
        V --> W[Opening Status Context]
        W --> X[Proximity Grouping]
        X --> Y[Regional Filtering]
        Y --> Z[Contextual UI Presentation]
    end

    subgraph "🔄 Optimization Mechanisms"
        Z --> AA[Debounced Updates]
        AA --> BB[Batch Processing]
        BB --> CC[Priority-Based Updates]
        CC --> DD[Memory Management]
        DD --> EE[Event Cleanup]
        EE --> FF[Performance Monitoring]
    end

    style A fill:#1a1a2e,color:#ffffff
    style Q fill:#16213e,color:#ffffff
    style Z fill:#0f3460,color:#ffffff
    style FF fill:#e94560,color:#ffffff
```

## Integration Patterns

### Card-Place Integration

Places are tightly integrated with the card system through several mechanisms:

#### Widget Integration
- Places are exposed as interactive widgets on card interfaces
- Widget configuration determines place display themes and behavior
- Real-time place count updates based on location and availability

#### PlaceList Configuration
- Cards reference specific PlaceLists through `placeListIds` configuration
- PlaceLists provide categorization and regional filtering
- Fallback mechanisms ensure place availability across regions

### Service Integration

Places integrate with various fulfillment services:

#### Fulfillment Services
- **Pickup**: In-store pickup availability and configuration
- **Delivery**: Delivery service areas and availability
- **Dine-in**: Table service and reservation capabilities
- **Booking**: Appointment and reservation systems
- **Queuing**: Queue management and wait time estimation

#### Payment Integration
- Place-specific payment method configuration
- Credential management for authenticated services
- Integration with payment processing systems

## Performance Optimization Architecture

### Sophisticated Performance Strategies

The place management system implements advanced performance optimization strategies that balance user experience with system efficiency through intelligent business logic.

#### Location-Based Performance Intelligence

**Spatial Query Optimization**:
The system implements sophisticated spatial query strategies that minimize computational overhead:

1. **Bounding Box Pre-filtering**: Geographic bounds are calculated to limit spatial queries to relevant areas
2. **Distance Threshold Optimization**: Queries are limited to configurable distance thresholds (typically 50-500 meters)
3. **Coordinate Precision Management**: GPS coordinates are rounded to 6 decimal places to balance accuracy with performance
4. **Spatial Index Utilization**: Database queries leverage lat/lng indexes for optimal query performance

**Distance Calculation Efficiency**:
- **Cached Distance Matrix**: Frequently calculated distances are cached to avoid repeated computation
- **Batch Distance Calculations**: Multiple distance calculations are batched for improved performance
- **Proximity Thresholds**: Distance calculations are skipped for places beyond relevance thresholds
- **Movement-Based Invalidation**: Distance cache is invalidated only when user moves significantly (> 100 meters)

#### Memory Management Intelligence

**Intelligent Caching Strategy**:
The system implements multi-layered caching that adapts to usage patterns:

1. **Hot Data Caching**: Frequently accessed places are maintained in memory cache
2. **Regional Data Partitioning**: Place data is partitioned by region to optimize memory usage
3. **LRU Eviction**: Least recently used places are evicted from cache when memory pressure increases
4. **Predictive Preloading**: Places likely to be accessed are preloaded based on user patterns

**Event System Optimization**:
- **Event Listener Lifecycle Management**: Event listeners are automatically cleaned up to prevent memory leaks
- **Debounced Event Processing**: Rapid-fire events are debounced to prevent excessive processing
- **Event Priority Queuing**: High-priority events (user interactions) are processed before background events
- **Batch Event Processing**: Multiple related events are batched for efficient processing

#### Synchronization Performance

**Intelligent Sync Scheduling**:
The system implements sophisticated sync scheduling that balances data freshness with performance:

1. **Priority-Based Sync**: Custom places are synchronized immediately while official places are batched
2. **Regional Sync Optimization**: Only places relevant to user's region are synchronized actively
3. **Background Sync Throttling**: Background sync operations are throttled to prevent battery drain
4. **Network-Aware Sync**: Sync behavior adapts to network conditions (WiFi vs cellular)

**Data Transfer Optimization**:
- **Incremental Sync**: Only changed data is transferred to minimize bandwidth usage
- **Compression**: Place data is compressed during transfer to reduce payload size
- **Batch Operations**: Multiple place operations are batched into single API calls
- **Conflict Minimization**: Optimistic locking reduces the likelihood of sync conflicts

#### UI Performance Intelligence

**Rendering Optimization**:
The system implements sophisticated UI optimization strategies:

1. **Virtual List Rendering**: Large place lists use virtual rendering to maintain smooth scrolling
2. **Image Lazy Loading**: Place images are loaded on-demand as they become visible
3. **Progressive Data Loading**: Place details are loaded progressively as users interact with the interface
4. **Animation Optimization**: UI animations are optimized for 60fps performance

**State Management Efficiency**:
- **Selective Re-rendering**: UI components re-render only when relevant data changes
- **State Normalization**: Place data is normalized to prevent unnecessary re-renders
- **Memoization**: Expensive calculations are memoized to avoid repeated computation
- **Background Processing**: Heavy operations are moved to background threads when possible

#### Database Performance

**Query Optimization Strategy**:
The system implements sophisticated database optimization:

1. **Index Strategy**: Composite indexes on frequently queried fields (lat, lng, cardId, deletedAt)
2. **Query Batching**: Multiple related queries are batched for improved performance
3. **Result Set Limiting**: Query results are limited to prevent memory overflow
4. **Prepared Statements**: Frequently used queries are prepared and cached

**Data Lifecycle Management**:
- **Soft Delete Strategy**: Deleted places are marked rather than removed to optimize sync performance
- **Data Archival**: Old place data is archived to maintain database performance
- **Index Maintenance**: Database indexes are maintained automatically for optimal performance
- **Vacuum Operations**: Database is periodically optimized to maintain performance

## Advanced Business Logic Patterns

### Edge Case Management

The place management system implements sophisticated logic to handle complex edge cases and ensure robust operation under various conditions.

#### Cross-Border and Travel Scenarios

**International Travel Logic**:
When users travel across international borders, the system implements intelligent adaptation strategies:

1. **Automatic Region Detection**: System detects country changes through GPS coordinates and adjusts place filtering accordingly
2. **Cross-Border Place Continuity**: Places near borders may appear in multiple regions to ensure continuity
3. **Currency and Language Adaptation**: Place information adapts to local currency and language preferences
4. **Roaming Data Optimization**: Sync behavior adapts to roaming conditions to minimize data usage

**Timezone Transition Handling**:
- **Dynamic Timezone Detection**: Place timezones are automatically detected based on coordinates
- **Opening Hours Recalculation**: Place status is recalculated when user crosses timezone boundaries
- **Temporal Consistency**: System maintains temporal consistency across timezone changes
- **Daylight Saving Time**: Automatic adjustment for daylight saving time transitions

#### Data Consistency Edge Cases

**Offline-Online Transition Logic**:
The system handles complex scenarios when transitioning between offline and online states:

1. **Conflict Resolution Priority**: User-created data takes precedence over server data in most conflict scenarios
2. **Partial Sync Recovery**: System recovers gracefully from partial sync failures
3. **Data Integrity Validation**: Comprehensive validation ensures data integrity after network reconnection
4. **User Notification Strategy**: Users are informed of sync conflicts and resolution outcomes

**Concurrent Modification Handling**:
- **Optimistic Locking**: System uses optimistic locking to handle concurrent modifications
- **Last-Writer-Wins Strategy**: Most recent modifications take precedence in conflict scenarios
- **Field-Level Merging**: Individual field conflicts are resolved independently when possible
- **User Choice Resolution**: Complex conflicts may require user intervention to resolve

#### Performance Edge Cases

**Large Dataset Management**:
When dealing with large numbers of places, the system implements sophisticated management strategies:

1. **Pagination Strategy**: Large place lists are paginated to maintain UI responsiveness
2. **Progressive Loading**: Place details are loaded progressively as needed
3. **Memory Pressure Handling**: System adapts to memory pressure by reducing cache sizes
4. **Background Processing**: Heavy operations are moved to background threads

**Network Condition Adaptation**:
- **Bandwidth Detection**: System adapts sync behavior based on available bandwidth
- **Retry Logic**: Failed operations are retried with exponential backoff
- **Graceful Degradation**: Features degrade gracefully under poor network conditions
- **Offline Mode Optimization**: System optimizes for offline operation when network is unavailable

### Security and Privacy Architecture

#### Location Privacy Framework

**Privacy-First Design**:
The system implements comprehensive privacy protection while maintaining functionality:

1. **Granular Permission Management**: Users can grant different levels of location access
2. **Data Minimization Principle**: Only necessary location data is collected and stored
3. **Temporal Data Limits**: Location history is automatically purged after configurable periods
4. **User Control Mechanisms**: Users maintain full control over location data sharing

**Secure Storage Implementation**:
- **Device-Level Encryption**: Location data is encrypted using device-specific keys
- **Secure Keychain Integration**: Sensitive data is stored in platform-specific secure storage
- **Memory Protection**: Location data in memory is protected against unauthorized access
- **Audit Trail Maintenance**: Comprehensive logging of location data access and modifications

#### Data Security Framework

**Access Control Implementation**:
The system implements sophisticated access control mechanisms:

1. **User-Based Permissions**: Place editing is restricted to authorized users only
2. **Role-Based Access**: Different user roles have different place management capabilities
3. **Audit Trail Logging**: All place modifications are logged for security and compliance
4. **Data Validation Pipeline**: Server-side validation ensures data integrity and security

**Encryption and Transport Security**:
- **End-to-End Encryption**: All place data is encrypted during transmission
- **Certificate Pinning**: API communications use certificate pinning for enhanced security
- **Token-Based Authentication**: Secure token-based authentication for API access
- **Data Integrity Verification**: Checksums and signatures verify data integrity

## Error Handling and Resilience Architecture

### Sophisticated Error Recovery Framework

The place management system implements a comprehensive error handling strategy that ensures robust operation under various failure conditions while maintaining optimal user experience.

#### Location Service Resilience

**Multi-Layered Location Fallback Strategy**:
The system implements a sophisticated fallback hierarchy for location services:

1. **Primary GPS Failure Handling**:
   - Automatic fallback to network-based location services
   - Graceful degradation to manual location entry when all services fail
   - User notification with clear explanation of reduced functionality
   - Cached location data utilization when real-time location is unavailable

2. **Permission Denial Recovery**:
   - Immediate adaptation to permission-denied scenarios
   - Educational UI explaining benefits of location permission
   - Alternative workflows that function without location data
   - Periodic re-prompting with contextual explanations

3. **Accuracy Degradation Handling**:
   - Dynamic feature adjustment based on location accuracy
   - User notification when accuracy affects functionality
   - Intelligent threshold management for different accuracy levels
   - Fallback to region-based filtering when precision is insufficient

**Location Service Timeout Management**:
- **Progressive Timeout Strategy**: Timeouts increase progressively to balance responsiveness with reliability
- **Background Recovery**: Failed location requests are retried in background
- **User Experience Continuity**: UI remains responsive during location service failures
- **Cached Data Utilization**: Previously obtained location data is used when services are unavailable

#### Synchronization Resilience Framework

**Network Failure Recovery Strategy**:
The system implements sophisticated network failure handling:

1. **Offline Operation Continuity**:
   - Full functionality maintained during network outages
   - Local data modifications queued for later synchronization
   - User notification of offline status with clear expectations
   - Automatic sync resumption when network connectivity returns

2. **Partial Sync Failure Recovery**:
   - Granular retry mechanisms for individual failed operations
   - Intelligent batching to optimize retry efficiency
   - Conflict detection and resolution during recovery
   - User notification of sync status and any required actions

3. **Data Corruption Detection and Recovery**:
   - Comprehensive data validation during sync operations
   - Automatic recovery from corrupted local data
   - Server-side validation to prevent corruption propagation
   - User notification and guidance for manual recovery when needed

**Conflict Resolution Intelligence**:
- **Automatic Resolution Strategies**: Most conflicts are resolved automatically using business rules
- **User Intervention Protocols**: Complex conflicts are escalated to users with clear resolution options
- **Data Preservation**: All conflicting data is preserved to prevent data loss
- **Audit Trail Maintenance**: Complete audit trail of conflict resolution decisions

#### User Experience Error Handling

**Graceful Degradation Patterns**:
The system implements sophisticated degradation strategies that maintain core functionality:

1. **Feature-Level Degradation**:
   - Non-essential features are disabled gracefully when errors occur
   - Core place management functionality remains available
   - Clear user communication about reduced functionality
   - Automatic feature restoration when errors are resolved

2. **Performance Degradation Handling**:
   - Automatic adaptation to performance constraints
   - Progressive feature reduction under resource pressure
   - User notification of performance-related limitations
   - Background optimization to restore full functionality

3. **Data Availability Degradation**:
   - Cached data utilization when fresh data is unavailable
   - Clear indication of data freshness and limitations
   - Progressive data loading as connectivity improves
   - User control over data refresh attempts

**Error Communication Strategy**:
- **Contextual Error Messages**: Error messages are tailored to user context and technical level
- **Actionable Guidance**: Users receive clear guidance on how to resolve issues
- **Progressive Disclosure**: Technical details are available for users who need them
- **Recovery Assistance**: System provides assistance for error recovery when possible

## Business Logic Summary

### Key Architectural Decisions

The place management system's architecture reflects several critical business decisions that prioritize user experience, data integrity, and system performance:

#### User Experience Prioritization
1. **Contextual Intelligence**: The system prioritizes contextual relevance over raw data completeness, ensuring users see the most relevant places for their current situation
2. **Progressive Enhancement**: Features enhance progressively based on available permissions and data quality, ensuring core functionality remains available under all conditions
3. **Intelligent Prompting**: Custom place prompting is carefully balanced to provide value without causing user fatigue through sophisticated timing and context analysis

#### Data Integrity Framework
1. **Ownership Model**: Clear ownership boundaries between official and custom places ensure data integrity while enabling user customization
2. **Regional Consistency**: Regional filtering and fallback mechanisms ensure consistent place availability across geographical boundaries
3. **Conflict Resolution**: Sophisticated conflict resolution prioritizes user data while maintaining system consistency

#### Performance Architecture
1. **Location-Aware Optimization**: All performance optimizations consider location context, from caching strategies to sync prioritization
2. **Graceful Degradation**: System performance degrades gracefully under resource constraints while maintaining core functionality
3. **Predictive Optimization**: System anticipates user needs through pattern analysis and preloads relevant data

### Business Rule Enforcement

The system enforces several critical business rules that ensure consistent operation:

1. **Custom Place Ownership**: Only users who create custom places can modify them, preventing unauthorized changes
2. **Official Place Protection**: Official places cannot be modified by users, ensuring data consistency across the platform
3. **Regional Compliance**: Place data respects regional boundaries and local regulations through intelligent filtering
4. **Permission Respect**: All location-based features respect user permission choices and degrade gracefully when permissions are denied

## Related Documentation

- [Application Architecture](./app-architecture.md) - Overall system architecture and design patterns
- [Card Management](./cards.md) - Card system integration and place associations
- [Widget System](./widgets.md) - Widget infrastructure and place widget implementation
- [Location Services](./location.md) - Location and mapping services integration
- [Sync Architecture](./sync.md) - Data synchronization patterns and conflict resolution
- [Opening Hours](./opening-hours.md) - Sophisticated time-based availability logic
- [Performance Optimization](./performance.md) - System performance strategies and optimization

## API Reference

### Core Place Model Methods

**Place Discovery and Filtering**:
- `Place.findByCard(card, options)` - Find places associated with a card with regional filtering
- `Place.findByCoords(coords, options)` - Find places near coordinates with distance and type filtering
- `Place.findByCards(cards, options)` - Batch find places for multiple cards with optimization
- `Place.addStatus(places)` - Add real-time opening status to place collections

**Place Lifecycle Management**:
- `Place.create(data)` - Create new place record with validation and business rule enforcement
- `Place.update(data)` - Update existing place with ownership validation
- `Place.beforePersist(data)` - Pre-persistence validation and coordinate processing

**Spatial and Temporal Queries**:
- `Place.findByCoords(coords, options)` - Spatial queries with bounding box optimization
- `PlaceInstance.isAvailable(fulfillment)` - Check place availability for specific fulfillment types
- `PlaceInstance.distance` - Calculate distance from user's current position

### Place Controller Business Logic

**User Interface Coordination**:
- `Places.view(containerId, placeId)` - Display place management interface with context awareness
- `Places.add(callbacks)` - Add new custom place with permission validation
- `Places.edit(placeId, callbacks)` - Edit existing place with ownership verification
- `Places.delete(place)` - Delete custom place with soft delete implementation

**Data Management**:
- `Places.loadPlaces(card)` - Load places for card with regional filtering and performance optimization
- `Places.getDataAndSort(places, preload, openOnly)` - Sort and filter places with business logic
- `Places.refreshData()` - Refresh place data with intelligent caching

### Location Intelligence Services

**Distance and Proximity**:
- `Distance.between(from, to)` - High-precision distance calculation between coordinates
- `Distance.bound(center, distance)` - Calculate geographic bounds for spatial queries
- `Distance.nearest(from, list, radius)` - Find nearest places within specified radius
- `Distance.format(meters)` - Format distance for user display with localization

**Position Management**:
- `Position.get()` - Get current user position with permission handling
- `Position.last()` - Retrieve last known position with caching
- `Position.geoToPosition(geo)` - Convert GeoJSON to position format

**ETA and Navigation**:
- `ETA.get(from, to)` - Calculate estimated travel time with multiple transport modes
- `ETA.mode(distance)` - Determine optimal transport mode based on distance
- `ETA.tooFar(eta)` - Determine if destination is too far for practical travel

### Opening Hours Intelligence

**Status Determination**:
- `OpeningHours.detail(place, options)` - Comprehensive opening status with next opening times
- `OpeningHours.isOpen(hours, from, to)` - Check if place is open during specified time range
- `OpeningHours.periods(hours)` - Extract structured periods from opening hours configuration

**Time Management**:
- `Hours.isOpen(from, to)` - Instance-based opening status checking
- `Hours.current` - Get current active period
- `Hours.next` - Get next opening period with intelligent scheduling
