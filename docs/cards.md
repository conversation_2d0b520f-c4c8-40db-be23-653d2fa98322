# Card Management System

## Table of Contents
- [Overview](#overview)
- [Architecture](#architecture)
- [Card List Component](#card-list-component)
- [Card Details Component](#card-details-component)
- [Widget Integration](#widget-integration)
- [Service Integrations](#service-integrations)
- [Data Flow and State Management](#data-flow-and-state-management)
- [User Experience Patterns](#user-experience-patterns)
- [Implementation Guidelines](#implementation-guidelines)

## Overview

The Card Management system is the core feature of the Perkd application, providing comprehensive digital wallet functionality for loyalty cards, gift cards, and membership cards. The system emphasizes user experience flow optimization and sophisticated data management patterns while maintaining seamless integration with the broader application ecosystem.

The card management system consists of two primary components: the Card List interface for browsing and organizing cards, and the Card Details interface for individual card interaction and management. Both components are deeply integrated with the Widget system for extensible functionality and various application services for comprehensive feature delivery.

```mermaid
graph TB
    subgraph "🎯 Card Management Architecture"
        style CL fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        style CD fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        style WS fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        style SI fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        
        CL[Card List Component<br/>src/containers/Card/List.js<br/>src/controllers/cardList.js]
        CD[Card Details Component<br/>src/containers/Card/Detail.js<br/>src/controllers/CardDetail.js]
        WS[Widget System<br/>Integration Layer]
        SI[Service Integrations<br/>Events, Payments, Actions]
    end

    subgraph "🔧 Data Layer"
        style CM fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff
        style CS fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff
        style WD fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff
        
        CM[Card Models<br/>src/lib/models/Card.js<br/>src/lib/cards.js]
        CS[Card Services<br/>src/lib/common/services/card.js]
        WD[Widget Data<br/>src/lib/widgets/]
    end

    subgraph "🎨 UI Components"
        style BC fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff
        style SC fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff
        style CC fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff
        
        BC[BigCardListItem<br/>components/Card/]
        SC[SmallCardListItem<br/>components/Card/]
        CC[Card Component<br/>lib/common/components/Card/]
    end

    CL --> CM
    CD --> CM
    CL --> BC
    CL --> SC
    CD --> CC
    CD --> WS
    WS --> WD
    CM --> CS
    CD --> SI
    CL --> SI
```

## Architecture

### Core Components

The Card Management system follows a layered architecture with clear separation of concerns:

#### Presentation Layer
- **Card List Container** (`src/containers/Card/List.js`): Main list interface with search, filtering, and sorting
- **Card Detail Container** (`src/containers/Card/Detail.js`): Individual card view with widget integration
- **Card Components** (`src/lib/common/components/Card/`): Reusable card UI components

#### Business Logic Layer
- **Card List Controller** (`src/controllers/cardList.js`): List state management and operations
- **Card Detail Controller** (`src/controllers/CardDetail.js`): Detail view orchestration and widget coordination
- **Card Actions** (`src/lib/common/actions/card.js`): User interaction handlers

#### Data Layer
- **Card Models** (`src/lib/models/Card.js`): Realm database models and business logic
- **Card Services** (`src/lib/common/services/card.js`): API integration and remote operations
- **Card Utilities** (`src/lib/cards.js`): Core card operations and utilities

### Design Patterns

#### Event-Driven Communication
```javascript
// Card lifecycle events
$.Event.emit(EVENT.Card.new, { masterId });
$.Event.emit(EVENT.Card.updated, { cardId, changes });
$.Event.emit(EVENT.Card.view, { id, masterId });

// Cross-component communication
$.Event.on(EVENT.Card.updated, this.onCardUpdated, this);
```

#### Registry Pattern for Data Access
```javascript
// Centralized model access
const card = _.Card.findById(cardId);
const cards = _.Card.list();

// Instance management
const cardController = Instances.CardDetail;
```

## Card List Component

The Card List component provides the primary interface for browsing, organizing, and managing the user's card collection. It implements sophisticated filtering, sorting, and search capabilities while maintaining optimal performance for large card collections.

### Architecture and Data Flow

```mermaid
graph TB
    subgraph "📱 Card List User Interface"
        style UI fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        style TB fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        style FL fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        
        UI[List Container<br/>src/containers/Card/List.js]
        TB[Top Bar<br/>Search & View Controls]
        FL[FlatList<br/>Card Items Rendering]
    end

    subgraph "🎛️ Controller Layer"
        style CLC fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff
        style SM fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff
        style FM fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff
        
        CLC[CardList Controller<br/>src/controllers/cardList.js]
        SM[Sort Management<br/>Custom, Brand, Location]
        FM[Filter Management<br/>Search, Categories]
    end

    subgraph "💾 Data Management"
        style CD fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff
        style CC fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff
        style PS fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff
        
        CD[Card Data<br/>Realm Models]
        CC[Category Cache<br/>Performance Layer]
        PS[Preference Storage<br/>User Settings]
    end

    UI --> CLC
    TB --> SM
    TB --> FM
    FL --> CD
    CLC --> CD
    SM --> PS
    FM --> CC
```

### Key Features

#### Multi-View Support
- **Big Card View**: Large card thumbnails with detailed information display
- **Small Card View**: Compact list format for efficient browsing
- **Dynamic View Switching**: Seamless transition between view modes with state preservation

#### Advanced Sorting
- **Custom Sort**: User-defined card order with drag-and-drop reordering
  - **iOS Implementation**: Direct persistence of user-defined arrangement via drag-and-drop interface
  - **Android Implementation**: Uses view frequency-based sorting through `sortByViews` function where more frequently viewed cards appear higher in the list
- **Brand Sort**: Alphabetical organization by card brand/issuer (implemented in `sortByBrand` function)
- **Location Sort**: Proximity-based ordering using geolocation services (requires location permissions and falls back to custom sort when location is unavailable)
- **View-Based Sort**: Frequency-based ordering by card usage patterns with automatic preference persistence

#### Search and Filtering
- **Real-time Search**: Instant filtering by card name, brand, or custom keywords
- **Category Filtering**: Organization by card type (loyalty, gift, membership, etc.)
- **AI-Powered Categorization**: Advanced categorization system using natural language processing
  - Uses `CardCategorizer` class to analyze card names, brand names, and tags. The `cardList.js` controller uses this service for batch categorization.
  - The `getCardCategory` utility function is used for synchronous, single-card categorization in the `CardDetail.js` controller.
  - Supports master category tags from server with fallback to pattern matching
  - Performs batch categorization via `categorizeBatch` method for efficiency
  - Implements caching with TTL (Time To Live) for performance optimization
  - Maintains category mappings for custom taxonomies
- **Performance Optimization**: Debounced search with efficient text matching algorithms

### State Management

The Card List maintains sophisticated state management for optimal user experience:

```javascript
// Core state structure
this.data = {
    search: {
        searching: false,
        keyword: '',
    },
    list: {
        scrolling: false,
        dragging: false,
        offset: { y: 0 },
        refreshing: false,
        viewableItems: [],
    },
    container: {
        animating: false,
        recoverTopBar: false,
        recoverTabs: true,
    }
};
```

#### Performance Optimizations
- **Lazy Loading**: Progressive card rendering with configurable batch sizes
- **View Recycling**: Efficient FlatList implementation with item layout caching
- **Memory Management**: Automatic cleanup of off-screen card components
- **Background Processing**: Asynchronous sorting and filtering operations

### User Interactions

#### Interactive Features
- **Pull-to-Refresh**: Custom header with sort order selection buttons and edit mode toggle
- **Header Messages**: Context-aware information display managed by HeaderMessage controller
- **Drag and Drop**: Custom implementation for card reordering with visual feedback and haptic feedback
- **Card Animations**: Card fly-out and fly-in animations with shared element transitions
- **Edit Mode**: Manual card reordering interface with changes persisted to preferences

#### Navigation Patterns
- **Card Selection**: Tap to navigate to Card Details with smooth transitions
- **Pull-to-Refresh**: Manual data synchronization with visual feedback
- **Infinite Scroll**: Seamless browsing of large card collections
- **Gesture Support**: Swipe gestures for quick actions and navigation

#### Accessibility Features
- **VoiceOver Support**: Comprehensive screen reader compatibility
- **Dynamic Type**: Automatic font scaling for accessibility preferences
- **High Contrast**: Support for accessibility display modes
- **Keyboard Navigation**: Full keyboard accessibility for external keyboards

### Platform-Specific Differences

#### Android
- **Custom Sort**: The "Custom Sort" feature on Android is based on view frequency. The `sortByViews` function in `src/controllers/cardList.js` sorts cards based on how frequently they are viewed, with more frequently viewed cards appearing higher in the list.

#### iOS
- **Custom Sort**: The "Custom Sort" feature on iOS allows for direct manipulation of the card order via drag-and-drop. The user's custom arrangement is persisted directly.

## Card Details Component

The Card Details component provides the primary interface for individual card interaction, displaying card information, managing card state, and coordinating widget functionality. It serves as the central hub for all card-related user actions and integrations.

### Architecture and Flow Management

```mermaid
graph TB
    subgraph "🎴 Card Detail Interface"
        style CDI fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        style CC fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        style WA fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        
        CDI[Detail Container<br/>src/containers/Card/Detail.js]
        CC[Card Component<br/>Visual Card Display]
        WA[Widget Area<br/>Action Buttons]
    end

    subgraph "🎯 Controller Orchestration"
        style CDC fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff
        style SM fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff
        style WM fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff
        
        CDC[CardDetail Controller<br/>src/controllers/CardDetail.js]
        SM[Step Management<br/>Card Flow States]
        WM[Widget Management<br/>Dynamic Widget Loading]
    end

    subgraph "📊 State and Data"
        style CS fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff
        style WD fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff
        style ES fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff
        
        CS[Card State<br/>Flow Management]
        WD[Widget Data<br/>Dynamic Content]
        ES[Event System<br/>Cross-Component Communication]
    end

    CDI --> CDC
    CC --> CS
    WA --> WM
    CDC --> SM
    SM --> CS
    WM --> WD
    CDC --> ES
```

### Card Flow and Step Management

The Card Details component implements a sophisticated step-based flow system that guides users through different card states and interactions:

#### Step Types and Transitions
- **Registration Steps**: New card onboarding and profile completion
- **Payment Steps**: Transaction processing and payment method selection
- **Usage Steps**: Active card utilization and feature access
- **Done Steps**: Completed card states with full functionality access

#### Step Implementation
```javascript
// Step initialization and management
this.step = Step[step] ? new Step[step](cardId, this) : new Step[DEFAULT](step);

// Step transitions based on card flow
if (flow && flow.steps) {
    const step = flow.steps[flow.at];
    if (this.step.at !== step) {
        this.step.destroy();
        this.step = new Step[step](cardId, this) : new Step[DEFAULT](step);
    }
}
```

### Card Display and Interaction

#### Visual Card Component
The card display system provides realistic card visualization with interactive features:

- **Front/Back Views**: Automatic card flipping with touch gesture support
- **Barcode Display**: Dynamic barcode rendering (QR codes, barcodes, etc.)
**Date Display**: The back of the card shows the join and expiry dates, with expired dates clearly highlighted
- **Image Management**: Custom card images with fallback handling
- **Animation Support**: Smooth transitions and visual feedback
- **Rotate Mode for Landscape Viewing**: When the device is turned, the card auto-rotates to landscape for horizontal layouts, with a smooth scale-and-rotate animation. Rotation can be programmatically locked or unlocked by the controller
- **Stored Value Display**: Cards with stored value (e.g., gift cards) show the remaining balance on the front for quick reference

#### User Interaction Patterns
- **Touch Gestures**: Tap to flip, swipe for navigation
- **Long Press Actions**: Context menus and advanced options
- **Gesture Recognition**: Platform-specific gesture handling (iOS/Android)
- **Accessibility Support**: VoiceOver and TalkBack integration

#### Card Management

he Card Details component lets users manage their cards—edit details or delete them from their wallet.

-   **Edit Card**: Tapping "Edit" button opens a form where users can update card info (e.g., name, number, expiry for physical cards; name, mobile, email for digital cards).
-   **Delete Card**: Deletion is done via the edit form, where a "Delete" button is provided—ensuring a deliberate, two-step process.

#### Card Sharing and Transfer

Users can share or transfer cards directly from the Card Details view via the app’s integrated sharing system

-   **Sharing Modes**:
    -   **Invite**: Sends a signup invite for a new card (default mode)
    -   **Transfer**: Moves card ownership (e.g., gift cards with balance) to another user
    -   **Clone**: Creates a duplicate card (e.g., loyalty cards) for shared use
-   **User Flow**:
    1.  Start from the Share widget under the card details
    2.  Select contacts
    3.  Choose the method of notifying recipients and confirm

For more detailed information on the sharing architecture, policies, and security, see the [Sharing System Documentation](sharing.md).

### Data Presentation

#### Card Information Display
- **Dynamic Content**: Real-time data updates from multiple sources
- **Conditional Rendering**: Context-aware information display
- **Localization Support**: Multi-language content presentation
- **Responsive Layout**: Adaptive design for different screen sizes

#### State-Driven UI Updates
```javascript
// Real-time card updates
onCardUpdate({ ids }) {
    if (ids.includes(card.id)) {
        this.refreshWidgets().then(({ fresh = [] }) => {
            this.step.init();
            Object.assign(this.props, { widgets: this.widgets, step: this.step });
            Navigation.updateProps(this.componentId, this.props);
            this.emit('changed');
        });
    }
}
```

### Context Awareness

The Card Details component adapts functionality based on multiple context factors:

#### Location Integration
- **Spot Checking**: Integration with location services via `getSpot()` method
- **Proximity Features**: Location-based card functionality and widget availability
- **Geofencing**: Automatic card activation based on location proximity
- **Fallback Handling**: Graceful degradation when location services are unavailable

#### Card State Adaptation
- **Dynamic Functionality**: Responds to different card states (active, inactive, transferred)
- **Flow-Based Features**: Adapts available actions based on current card flow step
- **Conditional Widget Display**: Shows/hides widgets based on card state and user context
- **Real-time Updates**: Automatic adaptation to card state changes

#### User Context Integration
- **Global Context System**: Uses application-wide context for personalization
- **Preference Adaptation**: Adapts interface based on user preferences and settings
- **Accessibility Context**: Automatic adaptation for accessibility needs
- **Device Context**: Platform-specific optimizations and feature availability

### Navigation Patterns

#### Seamless Navigation Flow
- **Deep Linking**: Direct navigation to specific cards and widgets
- **Stack Management**: Proper navigation hierarchy maintenance
- **State Restoration**: Automatic state recovery during navigation
- **Transition Animations**: Smooth visual transitions between views

#### Widget Navigation Integration
- **Widget Opening**: Seamless transition from card to widget views
- **Back Navigation**: Consistent return-to-card functionality
- **Modal Overlays**: Context-preserving overlay presentations
- **Cross-Widget Navigation**: Direct navigation between related widgets

## Widget Integration

The Card Management system is deeply integrated with the Widget system to provide extensible functionality and customizable user experiences. This integration enables dynamic feature delivery and contextual user interactions.

### Widget Architecture in Card Context

```mermaid
graph TB
    subgraph "🎯 Widget Integration Layer"
        style WF fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        style WM fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        style WR fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff

        WF[Widget Factory<br/>Dynamic Widget Creation]
        WM[Widget Manager<br/>Lifecycle Coordination]
        WR[Widget Renderer<br/>UI Integration]
    end

    subgraph "🔧 Card-Widget Communication"
        style CC fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff
        style WC fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff
        style EC fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff

        CC[Card Controller<br/>Widget Orchestration]
        WC[Widget Context<br/>Card Data Access]
        EC[Event Coordination<br/>Cross-System Communication]
    end

    subgraph "📊 Widget Types in Cards"
        style OW fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff
        style RW fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff
        style MW fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff
        style SW fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff

        OW[Offer Widgets<br/>Promotional Content]
        RW[Reward Widgets<br/>Loyalty Features]
        MW[Message Widgets<br/>Communication]
        SW[Shop Widgets<br/>Commerce Integration]
    end

    WF --> CC
    WM --> WC
    WR --> EC
    CC --> OW
    CC --> RW
    CC --> MW
    CC --> SW
```

### Widget Lifecycle Management

#### Dynamic Widget Loading
The card system dynamically loads and manages widgets based on card state, user context, and business rules:

```javascript
// Widget refresh and lifecycle management
refreshWidgets() {
    const { card, widgets: current } = this,
        { widgets: definitions = [] } = card.master,
        widgetData = _.WidgetData.findByCardId(card.id);

    return card.credentials(WIDGETS).then(credentials => {
        const fresh = WidgetFactory(card.id, definitions, this, widgetData, credentials);
        this.widgets.push(...fresh);
        this.widgets.sort((a, b) => a.order - b.order);
        return { fresh };
    });
}
```

#### Widget State Synchronization
- **Card State Updates**: Widgets automatically respond to card state changes
- **Data Synchronization**: Real-time widget data updates from external sources
- **Visibility Management**: Context-aware widget visibility and availability
- **Performance Optimization**: Lazy loading and efficient widget lifecycle management

### Extension Points for Custom Card Behaviors

#### Widget Configuration System
- **Registry-Based Configuration**: Centralized widget definitions with override capabilities
- **Platform Adaptation**: iOS and Android specific widget customizations
- **Regional Customization**: Country and locale-specific widget variations
- **Dynamic Configuration**: Runtime configuration based on user context and preferences

#### Custom Widget Development
```javascript
// Custom widget implementation
export default class CustomCardWidget extends Widget {
    init() {
        // Widget initialization with card context
        const { card, master } = this;
        this.updateVisible();
    }

    updateVisible() {
        // Dynamic visibility based on card state
        const { card } = this;
        this.visible = card.atStep === 'done' && card.isActive;
        if (!init) this.update();
    }

    open(containerId) {
        // Widget interaction handling
        return new Promise(resolve => {
            // Custom widget logic
            resolve();
        });
    }
}
```

### Data Flow Between Widgets and Card Management

#### Widget Data Access Patterns
- **Direct Card Access**: Widgets have direct access to card properties and state
- **Model Integration**: Seamless integration with card-related database models
- **Context Sharing**: Shared context between widgets and card controllers
- **Event Propagation**: Widget events propagate to card management system

#### Cross-Reference: Widget System Documentation
For comprehensive widget system architecture, lifecycle management, and implementation patterns, see [Widget System Documentation](widgets.md). Key integration points include:

- **Widget Factory Integration** (widgets.md#widget-factory): Dynamic widget creation for card contexts
- **Widget Lifecycle Management** (widgets.md#widget-lifecycle): Complete lifecycle coordination with card states
- **Data Widget Patterns** (widgets.md#data-widget-class): Specialized widgets for card-related data management
- **Event System Integration** (widgets.md#event-system-integration): Cross-system event coordination patterns

## Service Integrations

The Card Management system integrates with multiple application services to provide comprehensive functionality. These integrations enable seamless user experiences across different feature domains while maintaining clean architectural boundaries.

### Events System Integration

The card system participates extensively in the application's event-driven architecture for real-time communication and state synchronization.

```mermaid
graph TB
    subgraph "📡 Event System Integration"
        style ES fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        style CE fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        style AE fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff

        ES[Event System<br/>src/lib/common/appEvent.js]
        CE[Card Events<br/>Lifecycle & State Changes]
        AE[Analytics Events<br/>User Behavior Tracking]
    end

    subgraph "🎯 Card Event Types"
        style CNE fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff
        style CUE fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff
        style CVE fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff
        style CRE fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff

        CNE[Card Creation<br/>EVENT.Card.new]
        CUE[Card Updates<br/>EVENT.Card.updated]
        CVE[Card Views<br/>EVENT.Card.view]
        CRE[Card Registration<br/>EVENT.Card.register]
    end

    subgraph "📊 Event Consumers"
        style UI fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff
        style AN fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff
        style SY fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff

        UI[UI Components<br/>Real-time Updates]
        AN[Analytics<br/>Behavior Tracking]
        SY[Sync Services<br/>Data Synchronization]
    end

    ES --> CE
    ES --> AE
    CE --> CNE
    CE --> CUE
    CE --> CVE
    CE --> CRE
    CNE --> UI
    CUE --> UI
    CVE --> AN
    CRE --> SY
```

#### Event Emission Patterns
```javascript
// Card lifecycle events
$.Event.emit(EVENT.Card.new, { masterId: master.id });
$.Event.emit(EVENT.Card.updated, { cardId, changes });
$.Event.emit(EVENT.Card.view, { id, masterId });
$.Event.emit(EVENT.Card.register, { id: card.id, masterId });

// Widget interaction events
$.Event.emit(EVENT.Widget.view, { cardId, name, key, kind });
$.Event.emit(EVENT.Widget.checkin, { cardId, mode, spot });
```

#### Event Consumption and Response
- **UI Synchronization**: Real-time UI updates based on card state changes
- **Analytics Integration**: Automatic user behavior tracking and analytics
- **Cross-Feature Communication**: Coordination with offers, rewards, and messaging systems
- **Background Synchronization**: Triggered data sync operations

### Payments Integration

The card system provides comprehensive payment processing capabilities through multiple payment providers and methods.

#### Payment Service Architecture
```javascript
// Payment processing integration
CardService.pay({
    masterId,
    personId,
    payments: [{ method: 'applepay', provider: 'stripe', source: paymentSource }],
    order: { amount, currency, description, metadata },
    options: { cardId, masterId }
});

// Order management
CardService.orderPay({
    masterId,
    personId,
    type: 'immediate',
    payments,
    order,
    options: { booking, reservation }
});
```

#### Payment Method Support
- **Mobile Payments**: Apple Pay, Google Pay integration
- **Digital Wallets**: Alipay, LinePay, and regional payment methods
- **Card Payments**: Credit/debit card processing through Stripe and other providers
- **Stored Value**: Gift card and prepaid card balance management

#### Payment Flow Integration
- **Step-Based Payments**: Integration with card flow steps for payment processing
- **Idempotency Support**: Duplicate payment prevention with idempotency keys
- **Error Handling**: Comprehensive error handling and retry mechanisms
- **Transaction Tracking**: Complete transaction lifecycle management

### Actions System Integration

The card system integrates with the application's action system for handling user interactions and business operations.

#### Action Types and Handlers
```javascript
// Card-specific actions
export const Actions = {
    request: (data) => Card.request(data.masterId),
    add: (data) => Card.add(data.masterId, undefined, undefined, undefined, undefined, data.options),
    qualified: (data) => _.CardMaster.qualified(data.masterId),
    clockin: async (data, cardmaster, card) => Cards.clockIn(data, { cardId: card.id })
};
```

#### Action Processing Flow
- **Context Resolution**: Automatic card and context resolution for actions
- **Permission Checking**: User permission and qualification validation
- **Business Logic Execution**: Core business operation processing
- **Result Handling**: Success/error response management and user feedback

### Engage System Integration

The card system integrates with the Engage system for contextual user engagement and notification delivery.

#### Engagement Patterns
```javascript
// Card-based engagement
Engage.Card.request(masterId, reason, options, show);

// Contextual engagement triggers
if (masterIds) {
    const tickets = _.Offer.findActiveTickets(masterIds, cardId, cardMasterIds);
    if (tickets.length) {
        Engage.ticket({ ids: ticketIds }, { spot, through: data.through });
    }
}
```

#### Engagement Types
- **Card Discovery**: Proactive card recommendation and discovery
- **Offer Delivery**: Contextual offer presentation based on card usage
- **Reward Notifications**: Loyalty point updates and reward availability
- **Location-Based Engagement**: Proximity-triggered card interactions

#### Cross-Reference: Application Architecture
For detailed information about the overall application architecture and service integration patterns, see [Application Architecture Documentation](app-architecture.md). Key integration points include:

- **Event System Architecture** (app-architecture.md#event-driven-architecture): Core event system patterns
- **Service Layer Integration** (app-architecture.md#business-logic-layer): Service integration architecture
- **Navigation System** (app-architecture.md#navigation-system): Deep linking and navigation patterns
- **Data Synchronization** (app-architecture.md#data-layer): Cross-service data synchronization patterns

## Data Flow and State Management

The Card Management system implements sophisticated data flow patterns and state management strategies to ensure consistent user experiences and optimal performance across all card-related operations.

### Data Flow Architecture

```mermaid
graph TB
    subgraph "🌐 External Data Sources"
        style API fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        style CS fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        style WS fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff

        API[Card Service API<br/>Remote Card Data]
        CS[Content Services<br/>Images & Assets]
        WS[Widget Services<br/>Dynamic Content]
    end

    subgraph "💾 Local Data Layer"
        style RM fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff
        style CC fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff
        style PS fill:#1a202c,stroke:#2d3748,stroke-width:2px,color:#ffffff

        RM[Realm Models<br/>Card & CardMaster]
        CC[Cache Layer<br/>Images & Metadata]
        PS[Preference Storage<br/>User Settings]
    end

    subgraph "🎯 Application State"
        style CL fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff
        style CD fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff
        style WM fill:#553c9a,stroke:#6b46c1,stroke-width:2px,color:#ffffff

        CL[Card List State<br/>Sorting & Filtering]
        CD[Card Detail State<br/>Flow & Widgets]
        WM[Widget State<br/>Dynamic Content]
    end

    API --> RM
    CS --> CC
    WS --> WM
    RM --> CL
    RM --> CD
    CC --> CL
    PS --> CL
    WM --> CD
```

### State Management Patterns

#### Card List State Management
The Card List maintains complex state for optimal user experience:

```javascript
// Centralized state management
const CACHED = {
    order: SORT.CUSTOM,
    view: VIEW.BIG,
    cards: [],
    customSortIds: [],
    categoryFilters: new Set(),
    categoryCache: new Map(),
    permitLocation: false,
    preciseLocation: true
};

// State synchronization
CardList.sortBy(order).then(({ cards, order }) => {
    CACHED.cards = cards;
    CACHED.order = order;
    this.cardsUpdated(cards, animated);
});
```

#### Card Detail State Management
Card Details implement step-based state management with widget coordination:

```javascript
// Step-based state transitions
if (flow && flow.steps) {
    const step = flow.steps[flow.at];
    if (this.step.at !== step) {
        this.step.destroy();
        this.step = new Step[step](cardId, this);
        this.step.init();
    }
}

// Widget state synchronization
this.refreshWidgets().then(({ fresh }) => {
    Object.assign(this.props, { widgets: this.widgets, step: this.step });
    Navigation.updateProps(this.componentId, this.props);
    this.emit('changed');
});
```

### Data Synchronization Strategies

#### Real-Time Data Updates
- **Event-Driven Updates**: Automatic UI updates based on data change events
- **Optimistic Updates**: Immediate UI feedback with background synchronization
- **Conflict Resolution**: Handling concurrent data modifications
- **Offline Support**: Local data persistence with sync queue management

#### Performance Optimization
- **Lazy Loading**: Progressive data loading based on user interaction
- **Caching Strategies**: Multi-level caching for images, metadata, and computed data
- **Background Sync**: Non-blocking data synchronization operations
- **Memory Management**: Efficient memory usage with automatic cleanup

## User Experience Patterns

The Card Management system implements sophisticated user experience patterns designed to provide intuitive, efficient, and delightful interactions across all card-related features.

### Navigation and Transition Patterns

#### Seamless Navigation Flow
```mermaid
graph LR
    subgraph "📱 Navigation Flow"
        style CL fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        style CD fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        style WV fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
        style CF fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff

        CL[Card List<br/>Browse & Search]
        CD[Card Detail<br/>View & Interact]
        WV[Widget View<br/>Feature Access]
        CF[Card Form<br/>Edit & Register]
    end

    CL -->|Tap Card| CD
    CD -->|Tap Widget| WV
    CD -->|Edit Action| CF
    WV -->|Back| CD
    CF -->|Save| CD
    CD -->|Back| CL
```

#### Transition Animations
- **Card Fly Animations**: Smooth card transitions between list and detail views
- **Widget Transitions**: Seamless navigation between card and widget interfaces
- **Form Transitions**: Contextual form presentation with state preservation
- **Loading States**: Progressive loading indicators and skeleton screens

### Interaction Design Patterns

#### Touch and Gesture Interactions
- **Card Flipping**: Intuitive front/back card navigation with touch gestures
- **Drag and Drop**: Custom card ordering with visual feedback
- **Pull to Refresh**: Manual data synchronization with haptic feedback
- **Long Press Actions**: Context menus and advanced options access

#### Accessibility and Inclusive Design
- **VoiceOver Integration**: Comprehensive screen reader support
- **Dynamic Type Support**: Automatic font scaling for accessibility
- **High Contrast Mode**: Enhanced visibility for accessibility needs
- **Keyboard Navigation**: Full keyboard accessibility support

### Error Handling and Recovery

The Card Management system has a robust error handling strategy to ensure a smooth user experience, even when things go wrong.

- **Network Errors**: The `cardList.js` controller will display cached data when the network is unavailable. The `CardService` in `src/lib/common/services/card.js` implements a retry mechanism with exponential backoff for failed API requests.
- **Data Validation**: The `CardDetail.js` controller validates user input in the edit form and displays user-friendly error messages.
- **Graceful Degradation**: If a card's image fails to load, a fallback placeholder is displayed. If a widget fails to load, the rest of the card details are still displayed.

## Implementation Guidelines

- **Concurrency Management**: The `CardDetail.js` controller uses a `Semaphore` to prevent race conditions during complex asynchronous operations, such as widget refreshing and step transitions. This ensures that only one critical operation is executing at a time, which helps to maintain data consistency and prevent unexpected behavior.

### Development Best Practices

#### Component Architecture
- **Separation of Concerns**: Clear separation between presentation, business logic, and data layers. For example, the `BigCardListItem.js` component is a presentational component that receives all its data and callbacks via props.
- **Reusable Components**: Modular component design for maintainability and consistency. The `CardFront` component, for example, is used in both the `BigCardListItem` and the `CardDetail` components.
- **Performance Optimization**: Efficient rendering with React Native best practices. The `BigCardListItem` component is a `PureComponent` to prevent unnecessary re-renders.
- **Testing Strategy**:
  - **Unit Tests**: The business logic in the controllers and services is tested with Jest. For example, the sorting and filtering logic in `cardList.js` is unit tested with mock data.
  - **Integration Tests**: The interaction between the controllers, components, and services is tested with React Native Testing Library.
  - **E2E Tests**: The end-to-end user flows are tested with Detox.

#### Data Management
- **Model Design**: Consistent Realm model patterns with proper relationships
- **Service Integration**: Clean API integration with error handling and retry logic
- **State Management**: Predictable state updates with event-driven architecture
- **Caching Strategy**: Multi-level caching for optimal performance

### Integration Patterns

#### Widget Integration
```javascript
// Widget integration example
export default class CardWidget extends Widget {
    constructor(definition, owner, data, credentials) {
        super(definition, owner, data, credentials);
        this.card = this.owner.card;
        this.cardId = this.card?.id;
    }

    init() {
        this.updateVisible();
        this.buildIcon();
    }

    updateVisible() {
        // Context-aware visibility logic
        this.visible = this.card.atStep === 'done' && this.card.isActive;
        if (!init) this.update();
    }
}
```

#### Service Integration
```javascript
// Service integration pattern
const cardOperation = async (cardId, operation) => {
    try {
        const result = await CardService[operation](cardId);
        $.Event.emit(EVENT.Card.updated, { cardId, operation });
        return result;
    } catch (error) {
        $.Event.emit(EVENT.Card.error, { cardId, operation, error });
        throw error;
    }
};
```

### Performance Considerations

#### Optimization Strategies
- **Component Recycling**: Card components are recycled and references cached in `CardList.components`
- **Widget Lazy Loading**: Widgets are loaded on-demand using the preload pattern, coordinated by Card controller
- **Image Optimization**: Card images are cached and optimized for display with automatic compression
- **Resource Management**: Proper cleanup through destroy methods for both card and widget resources
- **View Caching**: View components are reused where possible to minimize memory allocation
- **Batch Processing**: Operations like categorization are performed in batches for efficiency
- **Debounced Updates**: State changes are debounced to prevent excessive renders
- **Hardware Acceleration**: Optimized transitions with shared element animations using hardware acceleration
- **Memory Management**: Efficient component lifecycle management with automatic cleanup
- **Background Processing**: Non-blocking operations with proper threading
- **Network Optimization**: Request batching and intelligent caching

#### Monitoring and Analytics
- **Performance Metrics**: Real-time performance monitoring and alerting
- **User Analytics**: Comprehensive user behavior tracking and analysis
- **Error Reporting**: Automatic error reporting and analysis
- **A/B Testing**: Feature experimentation and optimization

This comprehensive documentation provides a complete overview of the Card Management system's architecture, implementation patterns, and integration strategies. The system's emphasis on user experience flow and data flow optimization ensures scalable, maintainable, and delightful card management functionality within the Perkd application ecosystem.
```
<environment_details>
# VSCode Visible Files
docs/cards.md

# VSCode Open Tabs
docs/shop.md
docs/commerce.md
docs/cards.md

# Current Time
15/07/2025, 7:32:01 pm (Asia/Singapore, UTC+8:00)

# Context Window Usage
295,737 / 1,048.576K tokens used (28%)

# Current Mode
ACT MODE
</environment_details>

</final_file_content>

IMPORTANT: For any future changes to this file, use the final_file_content shown above as your reference. This content reflects the current state of the file, including any auto-formatting (e.g., if you used single quotes but the formatter converted them to double quotes). Always base your SEARCH/REPLACE operations on this final version to ensure accuracy.

<environment_details>
# VSCode Visible Files
docs/cards.md

# VSCode Open Tabs
docs/shop.md
docs/commerce.md
docs/cards.md

# Current Time
15/07/2025, 7:33:53 pm (Asia/Singapore, UTC+8:00)

# Context Window Usage
315,814 / 1,048.576K tokens used (30%)

# Current Mode
ACT MODE
</environment_details>
