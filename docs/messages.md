# Messages Management System

## Overview

The Messages Management System is a sophisticated communication platform within the Perkd application that enables rich, interactive messaging between brands and users. Built on a robust webview-based architecture, the system supports personalized, multilingual content delivery with comprehensive lifecycle management and seamless integration with the card-based loyalty ecosystem.

## Architecture Components

### Core Models and Data Structure

The messages system is built around a comprehensive data model that supports rich content delivery and lifecycle management:

````javascript
class Message extends Realm.Object {}
Message.schema = {
    name: 'Message',
    primaryKey: 'id',
    properties: {
        id: 'string',
        sender: 'MessageSender',
        subject: { type: 'string', default: '' },
        preview: { type: 'string?', default: '' },
        kind: 'string?',
        cover: 'MessageCoverWebview',
        body: 'MessageBodyWebview',
        personalize: { type: 'string?', default: '{}' },
        globalize: 'MessageGlobalize',
        activeTime: 'date?',
        purgeTime: 'date?',
        options: { type: 'string?', default: '{}' },
        when: 'MessageWhen',
        localStates: { type: 'string?', default: '{}' },
        visible: { type: 'bool?', default: true },
    }
};
````

### Message Components Architecture

The system employs a modular component architecture with specialized classes for different aspects of message handling:

#### Message Model (`src/lib/models/Message.js`)
- **Core Business Logic**: Implements message lifecycle management, resource fetching, and data operations
- **Event Integration**: Extends Emitter for real-time communication with UI components
- **Resource Management**: Handles downloading and caching of message assets (HTML, images, JavaScript)
- **Query Optimization**: Provides efficient database queries for message retrieval and filtering

#### Message Widget (`src/lib/widgets/Message.js`)
- **Widget Integration**: Extends DataWidget to integrate with the card-based widget system
- **Kind Filtering**: Automatically manages message types to prevent conflicts between widgets
- **Event Handling**: Listens for message updates and deletions to maintain UI consistency
- **Badge Management**: Integrates with the badge system for unread message indicators

#### Message Controller (`src/controllers/Message.js`)
- **UI Coordination**: Manages the interaction between message data and UI components
- **Cover Management**: Orchestrates message cover rendering and interaction
- **State Management**: Tracks message viewing states (fresh, seen, viewed)
- **Navigation Integration**: Handles screen transitions and modal presentations

### Rich Content System

The messages system supports rich, interactive content through a sophisticated webview-based architecture built on a shared content rendering foundation:

#### Content System Integration

Messages leverage a comprehensive content system that provides shared rendering capabilities across the application:

````javascript
export default class ContentMessage extends Content {
    constructor(type, card, master, webview, personalize, language, owner, localStates) {
        super(type, card, master, webview, personalize, language, owner);
        this.data = localStates;
    }

    open(componentId) {
        super.open(componentId);
        _.Message.on(EVENT.Message.updated, this.onDataChanged, this);
    }
}
````

#### Shared Content Architecture
- **Base Content Class**: Messages extend the foundational Content class for consistent rendering
- **ContentMessage Specialization**: Specialized content handling for message-specific features
- **Local State Management**: Persistent storage of user interactions and preferences
- **Event-Driven Updates**: Real-time content updates through message event system
- **Cross-System Compatibility**: Shared rendering capabilities with other content systems

#### Cover Webview (`src/lib/models/Message/CoverWebview.js`)
- **Preview Rendering**: Displays message previews with rich HTML content
- **Resource Localization**: Supports language-specific resource paths
- **Performance Optimization**: Implements efficient resource loading and caching

#### Body Webview (`src/lib/models/Message/BodyWebview.js`)
- **Full Content Display**: Renders complete message content with interactive elements
- **Personalization Support**: Integrates personalized data into message content
- **Cross-Platform Compatibility**: Ensures consistent rendering across iOS and Android

#### Webview Integration
````javascript
<WebView
    ref={(w) => { this.webview = w; }}
    containerStyle={{ backgroundColor: 'transparent' }}
    onMessage={this.onMessage}
    onLoadEnd={this.onLoad}
    style={[ style, { height: coverHeight, opacity: 0.99 }]}
    originWhitelist={[ '*' ]}
    allowFileAccess
    allowingReadAccessToURL={`file://${TemporaryDirectoryPath}`}
    allowUniversalAccessFromFileURLs
    allowsInlineMediaPlayback
    allowsFullscreenVideo
    source={{ html, baseUrl: `file://${TemporaryDirectoryPath}` }}
    scrollEnabled={false}
    cacheEnabled={false}
/>
````

## User Experience Flow

### Message Lifecycle States

The system implements a comprehensive state management system that tracks user interaction with messages:

1. **Received**: Message is delivered to the user's device
2. **Seen**: Message appears in the user's viewport (for messages with body content)
3. **Viewed**: User actively interacts with the message (for messages without body) or views the full content

### Message Discovery and Presentation

Messages are presented to users through multiple pathways:

#### Widget-Based Discovery
- **Card Integration**: Messages appear as widgets within loyalty cards
- **Badge Indicators**: Unread message counts displayed on card widgets
- **Contextual Presentation**: Messages filtered by card context and user preferences

#### List-Based Viewing
- **Chronological Organization**: Messages grouped by date with human-readable timestamps
- **Section Headers**: Clear temporal organization for easy navigation
- **Progressive Loading**: Efficient loading of message covers with lazy body loading

### Interaction Patterns

#### Cover Interaction
````javascript
read(messageId) {
    const self = this,
        NOW = newDate(),
        cover = self.covers.find(c => c.key === messageId),
        message = self.messages.find(m => m.id === messageId),
        { id, subject, when, templateId, cardId } = message,
        { viewed } = when || {},
        event = { id, cardId, subject, when: { ...when }, templateId, first: !viewed };

    self.startReading = NOW;
    cover.view();

    if (!viewed) {
        _.Message.update({ id, when: { viewed: NOW } });
        event.when.viewed = NOW;
    }
    $.Event.emit(EVENT.Message.view, event);
}
````

#### Body Viewing
- **Modal Presentation**: Full message content displayed in dedicated modal screens
- **Reading Time Tracking**: System tracks time spent reading message content
- **Interaction Analytics**: Comprehensive event tracking for user engagement analysis

## Data Flow Architecture

### Message Synchronization

The system implements a sophisticated synchronization mechanism that ensures message consistency across devices:

#### Resource Management
````javascript
static fetchResources(options = {}) {
    const NOW = newDate(),
        { notDeleted, notPurged } = QUERY,
        priority = options.all
            ? [ 'shared', 'js', 'html', 'others' ]
            : [ 'shared', 'js', 'html' ],
        query = `${notDeleted} && ${notPurged}`,
        messages = Message.find(query, NOW),
        resources = { shared: {}, js: {}, html: {}, others: {} };

    return promiseMap(messages, msg => {
        const { id, cover, body, globalize } = msg,
            { default: language } = globalize || {};

        return Promise.all([
            (cover) && _.MessageCoverWebview.files(id, cover, resources, language),
            (body) && _.MessageBodyWebview.files(id, body, resources, language),
        ]);
    }, CONCURRENCY);
}
````

#### Event-Driven Updates
The system uses a comprehensive event system for real-time updates:

- **Message Received**: Triggered when new messages arrive
- **Message Seen**: Fired when messages enter the user's viewport
- **Message Viewed**: Emitted when users interact with message content
- **Message Body Viewed**: Tracked when users view full message content

### Integration with Card System

Messages are tightly integrated with the card-based architecture:

#### Widget Integration
````javascript
export default class Message extends DataWidget {
    constructor(definition, owner, data = {}, credentials) {
        super(definition, owner, data, credentials);

        const { key, param } = this,
            exclude = this.master.widgets
                .filter(w => w.param.model === MESSAGE)
                .reduce((kinds, w) => kinds.concat(w.param.kinds), []);

        if (!param.kinds && !!exclude.length) {
            const { id: cardId } = this.card,
                kinds = _.Message.kinds(cardId).filter(k => !!k && !exclude.includes(k));

            Object.assign(param, { kinds: kinds.length ? kinds : [ MESSAGE ] });
        }
    }
}
````

#### Badge System Integration
- **Unread Counting**: Automatic calculation of unread message counts
- **Real-time Updates**: Badge counts update immediately when messages are viewed
- **Kind-based Filtering**: Support for different message types with separate badge counts

### Header Notification System

The messages system integrates with a sophisticated header notification system that provides global message awareness:

````javascript
init() {
    return Persist.get(PERSIST.App.latestVersion).then(latestVersion => {
        const upgrade = latestVersion ? !Version.meet(latestVersion) : false,
            notice = _.Notify.unreads(),
            unreads = [
                ...(_.Message.unreads().filter(m => m.kind !== 'receipt').map(i => getProps(MESSAGE, i))),
            ],
            sorted = unreads.sort((a, b) => (b.createdAt || b.modifiedAt) - (a.createdAt || a.modifiedAt));
    });
}
````

#### Header Integration Features
- **Global Badge Display**: Unread message counts displayed in application header
- **Quick Access**: Header notifications provide direct access to recent messages
- **Firebase Integration**: Seamless integration with FCM for push notifications
- **Cross-Card Aggregation**: Messages from all cards aggregated in header display
- **Real-time Updates**: Header badges update immediately when message states change

#### Navigation Integration
- **Deep Linking**: Direct navigation to specific messages via URL schemes
- **Context Preservation**: Navigation maintains card and message context
- **Animation Support**: Smooth transitions with MessageFly animation system
- **Tab Management**: Intelligent tab locking during message navigation

## Internationalization and Personalization

### Multilingual Support

The messages system provides comprehensive internationalization support:

#### Globalization Architecture
````javascript
import Globalize from 'models/shared/Globalize';

class MessageGlobalize extends Globalize.Model {
    static className = 'MessageGlobalize';
}
````

#### Language-Specific Resource Paths
- **Localized Content**: HTML and resource files organized by language
- **Fallback Mechanism**: Graceful fallback to default language when translations unavailable
- **Dynamic Language Switching**: Support for runtime language changes

### Personalization Features

#### Content Personalization
- **Dynamic Data Injection**: Personalized data merged into message content
- **User Context Integration**: Messages adapted based on user preferences and behavior
- **Card-Specific Customization**: Content tailored to specific loyalty card contexts

#### Local State Management
- **Persistent Preferences**: User-specific message preferences stored locally
- **Interaction History**: Tracking of user interactions for improved personalization
- **Context-Aware Delivery**: Messages delivered based on user context and location

## Performance Optimization

### Resource Management

The system implements sophisticated performance optimization strategies:

#### Caching Strategy
- **Multi-Level Caching**: Memory, disk, and network caching layers
- **Resource Prioritization**: Critical resources loaded first (shared, JS, HTML)
- **Lazy Loading**: Message bodies loaded only when requested

#### Memory Management
- **Cover Lifecycle**: Efficient creation and destruction of message covers
- **Resource Cleanup**: Automatic cleanup of message resources when deleted
- **Background Processing**: Resource downloading performed in background threads

### UI Performance

#### Rendering Optimization
- **Virtualized Lists**: Efficient rendering of large message lists using SectionList
- **Progressive Loading**: Covers loaded progressively as they enter viewport
- **Animation Optimization**: Hardware-accelerated animations for smooth interactions
- **Lazy Content Loading**: Message bodies loaded only when requested by user interaction

#### State Management
- **Efficient Updates**: Minimal re-renders through intelligent state management
- **Event Batching**: Multiple state changes batched for performance
- **Memory-Conscious Design**: Careful management of component lifecycle
- **Pagination Support**: Large message collections handled through pagination

#### Advanced Performance Features
- **Selective Data Loading**: Only necessary message data loaded based on user context
- **Resource Cleanup**: Automatic cleanup of message resources during navigation transitions
- **Background Processing**: Resource downloading and processing moved to background threads
- **Caching Strategies**: Multi-level caching for rendered content and resources

## Security and Privacy

### Content Security

The messages system implements comprehensive security measures:

#### Webview Security
- **Origin Restrictions**: Controlled access to external resources
- **Content Sanitization**: Safe rendering of user-generated content
- **File Access Controls**: Restricted file system access for security

#### Data Protection
- **Encrypted Storage**: Sensitive message data encrypted at rest
- **Secure Transmission**: All message content transmitted over encrypted channels
- **Privacy Compliance**: GDPR and privacy regulation compliance

### User Privacy

#### Data Minimization
- **Selective Tracking**: Only necessary interaction data collected
- **Retention Policies**: Automatic purging of expired message data
- **User Control**: Users can delete messages and control data retention

### Error Handling and Recovery

The Messages Management system has a robust error handling strategy to ensure a smooth user experience, even when things go wrong.

- **Network Errors**: The `Message.js` model will retry fetching resources with exponential backoff if the network is unavailable.
- **Data Validation**: The `Message.js` model validates incoming data against the schema and logs any errors.
- **Graceful Degradation**: If a message's cover or body fails to load, a fallback placeholder is displayed.

## Integration Patterns

### Widget System Integration

Messages integrate seamlessly with the broader widget ecosystem:

#### DataWidget Extension
- **Model Binding**: Automatic integration with Message model
- **Badge Management**: Sophisticated badge counting and notification handling
- **Event Integration**: Real-time updates through event system

#### Controller Coordination
- **Lifecycle Management**: Proper initialization and cleanup of message controllers
- **State Synchronization**: Consistent state management across components
- **Navigation Integration**: Seamless integration with app navigation system

### Event System Integration

The messages system is deeply integrated with the application's event architecture:

#### Event Definitions
````json
"Message": {
    "updated": "updated",
    "deleted": "deleted",
    "new": "message.new",
    "received": "message.received",
    "seen": "message.seen",
    "view": "message.view",
    "usageCover": "message.cover.usage",
    "viewBody": "message.body.view",
    "usageBody": "message.body.usage"
}
````

#### Analytics Integration
- **Comprehensive Tracking**: All user interactions tracked for analytics
- **Performance Metrics**: Message loading and rendering performance monitored
- **User Engagement**: Detailed engagement metrics for message optimization

## Technical Implementation Details

### File System Organization

Messages are organized in a structured file system hierarchy:

```
messages/
├── {messageId}/
│   ├── html/
│   │   ├── {language}/
│   │   │   └── content.html
│   │   └── cover.html
│   └── resources/
│       ├── {language}/
│       │   ├── images/
│       │   └── styles/
│       └── shared/
```

### Resource Loading Strategy

The system implements a sophisticated resource loading strategy:

1. **Priority Loading**: Shared resources loaded first
2. **Language-Specific Loading**: Localized resources loaded based on user language
3. **Concurrent Processing**: Multiple messages processed concurrently with controlled concurrency
4. **Error Handling**: Graceful handling of resource loading failures

### State Persistence

Message states are persisted across app sessions:

#### Local State Management
- **Viewing States**: Persistent tracking of message viewing states
- **User Preferences**: Message-specific user preferences stored locally
- **Interaction History**: Complete history of user interactions with messages

#### Synchronization
- **Bidirectional Sync**: Local changes synchronized with server
- **Conflict Resolution**: Intelligent handling of synchronization conflicts
- **Offline Support**: Full functionality available offline with sync on reconnection

## Sophisticated Business Logic and Rules

The messages system implements complex business logic that governs sophisticated scenarios and edge cases beyond basic message management.

### Message Lifecycle Business Rules

#### State Transition Logic
The system implements sophisticated state management with complex conditional logic governing message progression:

**Automatic State Progression:**
- **Received State Assignment**: Messages automatically receive a `received` timestamp when first persisted to the database, but only if they don't already have one, ensuring idempotent state management
- **Seen vs Viewed Logic**: Messages with body content follow a two-step progression (seen → viewed), while messages without body content skip directly to viewed state when they enter the viewport
- **Receipt Message Exclusion**: Messages with `kind: 'receipt'` are excluded from standard lifecycle tracking and badge counting, implementing special handling for transactional messages
- **Slide-in Message Handling**: When multiple messages are processed simultaneously, slide-in animations are detected and those messages bypass normal seen/viewed event emission to prevent duplicate analytics

**Conditional State Updates:**
- **First-time Interaction Tracking**: The system tracks whether a state change represents the user's first interaction with a message, enabling sophisticated analytics and personalization
- **Batch State Processing**: Multiple message state updates are batched together for performance, with atomic database operations ensuring consistency
- **Reading Time Measurement**: For messages with body content, the system tracks precise reading duration from initial interaction to body closure

#### Resource Lifecycle Management

**Resource Versioning and Cleanup:**
- **Version-based Cleanup**: When a message's `rsrcUpdated` timestamp changes, the system automatically removes old cached resources before downloading new versions
- **Purge Time Enforcement**: Messages with `purgeTime` set are automatically removed from local storage after the specified date, with background cleanup processes ensuring storage efficiency
- **Orphaned Resource Handling**: When messages are deleted, their associated file system resources are automatically cleaned up to prevent storage bloat

### Widget Integration Business Logic

#### Kind-based Filtering Algorithm
The message widget system implements sophisticated conflict resolution and automatic type assignment:

**Automatic Kind Assignment:**
- **Exclusion-based Filtering**: When multiple message widgets exist on the same card, the system automatically calculates which message kinds each widget should handle to prevent conflicts
- **Dynamic Kind Discovery**: If no explicit kinds are specified, the widget queries the database for all available message kinds for the card and filters out those already claimed by other widgets
- **Fallback Behavior**: If no specific kinds are available after filtering, the widget defaults to handling generic 'message' type content

**Widget Conflict Resolution:**
- **Master Widget Coordination**: The system maintains a registry of which widgets handle which message types, ensuring no message type is handled by multiple widgets simultaneously
- **Card Context Awareness**: Message filtering respects card context, ensuring messages only appear on widgets associated with their originating card
- **State Synchronization**: Widget state changes are coordinated across all message widgets to maintain consistency

### Badge and Notification Business Rules

#### Sophisticated Unread Counting Logic
The badge system implements complex aggregation rules with multiple layers of filtering:

**Multi-level Filtering:**
- **Active Card Validation**: Unread counts only include messages from cards that are currently active and not in transferring state
- **Widget Availability Check**: Messages are only counted if their associated card has a widget capable of displaying that message type
- **Kind-specific Aggregation**: Badge counts are calculated separately for each message kind, allowing granular notification management
- **Cross-card Aggregation**: Header notifications aggregate unread counts across all cards while maintaining individual card context

**Conditional Badge Logic:**
- **Receipt Message Exclusion**: Receipt-type messages are filtered out of standard unread counts but included in specialized receipt handling
- **Master Card Grouping**: In header notifications, messages are grouped by card master to provide consolidated brand-level notifications
- **Time-based Filtering**: Only messages that haven't been viewed are included in unread counts, with precise timestamp-based filtering

#### Notification Triggering Conditions

**Event-driven Notification Logic:**
- **First-time Interaction Detection**: Notifications distinguish between first-time and repeat interactions, enabling different notification strategies
- **Context-aware Delivery**: Notification delivery respects user context, app state, and device capabilities
- **Batch Notification Processing**: Multiple notifications are batched and processed together to prevent notification spam

### Resource Management Policies

#### Sophisticated Caching Strategies
The system implements multi-level caching with intelligent prioritization:

**Priority-based Resource Loading:**
- **Resource Type Prioritization**: Resources are loaded in priority order: shared resources first, then JavaScript, then HTML, and finally other assets
- **Concurrency Management**: Resource downloads are limited to 10 concurrent operations to balance performance with system stability
- **Language-specific Caching**: Resources are cached separately for each language, with intelligent fallback to default language when translations are unavailable

**Performance-based Optimization:**
- **Device Performance Adaptation**: Resource loading strategies adapt based on device performance characteristics (high/mid/low performance tiers)
- **Background Processing**: Resource downloads are moved to background threads when the app is not in the foreground
- **Selective Loading**: Only critical resources are loaded by default, with optional "all" mode for comprehensive resource fetching

#### Cache Expiration and Cleanup Policies

**Intelligent Cache Management:**
- **Header-based Expiration**: Cache expiration is determined by HTTP Cache-Control headers, with automatic cleanup when resources expire
- **Storage Limit Management**: Cache size is monitored and managed to prevent excessive storage usage
- **Cleanup Coordination**: Resource cleanup is coordinated with message deletion to ensure no orphaned resources remain

### Content Personalization Business Rules

#### Dynamic Data Injection Logic
The system implements sophisticated content personalization with complex data merging:

**Context-aware Personalization:**
- **User Context Integration**: Personalization data includes user profile information, card context, location data, and behavioral history
- **Real-time Data Merging**: Personalization data is merged into message content at render time, ensuring always-current information
- **Fallback Content Handling**: When personalization data is unavailable, the system gracefully falls back to default content without breaking the user experience

**Local State Management:**
- **Persistent User Preferences**: User interactions and preferences are stored locally and persist across app sessions
- **Cross-message State Sharing**: Local states can be shared between related messages to maintain consistent user experience
- **Privacy-conscious Storage**: Sensitive personalization data is encrypted and stored securely

### Security and Validation Rules

#### Content Security Policies
The system implements comprehensive security measures for rich content:

**Webview Security Enforcement:**
- **Origin Restriction**: Webview content is restricted to specific origins and file system paths to prevent security vulnerabilities
- **Content Sanitization**: All message content is validated and sanitized before rendering to prevent XSS attacks
- **Resource Access Control**: File system access is limited to designated temporary directories with automatic cleanup

**Data Validation Rules:**
- **Input Sanitization**: All user inputs and message data are validated and sanitized before processing
- **Schema Validation**: Message data must conform to strict schemas before being accepted into the system
- **Permission Enforcement**: User permissions are checked before allowing access to sensitive message features

## Summary

The Messages Management System represents a sophisticated, feature-rich communication platform that seamlessly integrates with the Perkd application's card-based architecture. Through its comprehensive support for rich content, multilingual delivery, personalization, and performance optimization, the system provides an engaging and efficient messaging experience that enhances user engagement with loyalty programs.

Key architectural strengths include:

- **Rich Content Support**: Webview-based architecture enabling interactive, multimedia messages
- **Seamless Integration**: Deep integration with card system and widget architecture
- **Performance Optimization**: Multi-level caching and efficient resource management
- **Internationalization**: Comprehensive multilingual support with localized content delivery
- **User Experience**: Intuitive interaction patterns with comprehensive state management
- **Security**: Robust security measures protecting user data and content integrity

## Implementation Guidelines

When working with the messages management system, developers should follow these established patterns and best practices:

### Architecture Patterns

#### Widget Extension
- **Extend DataWidget**: Use the Message Widget as a foundation for specialized message functionality
- **Controller Delegation**: Delegate complex business logic to the Message Controller rather than embedding in widgets
- **Model-Based State**: Leverage Message model methods for consistent state and status management
- **Event-Driven Communication**: Use the event system for cross-component communication and updates

#### Content System Integration
- **ContentMessage Base**: Extend ContentMessage for custom message content rendering
- **Shared Rendering**: Leverage shared content rendering capabilities for consistency
- **Local State Management**: Use the local states system for persistent user preferences
- **Resource Management**: Follow established patterns for resource loading and cleanup

### Best Practices

#### Message Handling
- **Consistent Controllers**: Use the Message controller for all message-related business logic
- **Proper Lifecycle**: Implement proper cleanup in widget and controller lifecycle methods
- **State Synchronization**: Maintain consistency between local and remote message states
- **Error Handling**: Follow established error handling patterns for message operations

#### Performance Optimization
- **Lazy Loading**: Load message content only when needed by user interaction
- **Resource Caching**: Implement appropriate caching strategies for message resources
- **Memory Management**: Properly manage component lifecycle to prevent memory leaks
- **Background Processing**: Use background threads for resource-intensive operations

#### User Experience
- **Notification Best Practices**: Follow platform guidelines for message notifications
- **Accessibility**: Ensure message content is accessible to all users
- **Responsive Design**: Support different screen sizes and orientations
- **Offline Support**: Provide graceful degradation when network is unavailable

#### Security Considerations
- **Content Validation**: Validate all message content before rendering
- **Secure Storage**: Use appropriate storage mechanisms for sensitive message data
- **Permission Management**: Respect user privacy preferences and permissions
- **Input Sanitization**: Sanitize all user inputs in message interactions

### Testing Strategy
  - **Unit Tests**: The business logic in the controllers and models is tested with Jest. For example, the message state transitions in `Message.js` are unit tested with mock data.
  - **Integration Tests**: The interaction between the controllers, components, and services is tested with React Native Testing Library.
  - **E2E Tests**: The end-to-end user flows for viewing and interacting with messages are tested with Detox.

### Integration Guidelines

#### Card System Integration
- **Widget Configuration**: Properly configure message widgets within card definitions
- **Badge Management**: Integrate with the badge system for unread indicators
- **Context Awareness**: Respect card context when displaying messages
- **State Coordination**: Coordinate message states with card lifecycle

#### Navigation Integration
- **Deep Linking**: Support deep linking to specific messages
- **Context Preservation**: Maintain navigation context during message interactions
- **Animation Consistency**: Use consistent animations for message transitions
- **Back Navigation**: Implement proper back navigation handling

This architecture provides a solid foundation for sophisticated brand-to-user communication while maintaining the flexibility needed for future enhancements and evolving business requirements.

## Architecture Diagrams

### Messages System Architecture

The following diagram illustrates the comprehensive architecture of the messages management system and its integration with the broader application ecosystem:

```mermaid
graph TB
    subgraph "📱 User Interface Layer"
        A[Message List Screen<br/>src/containers/Message/List.js]
        B[Message Cover Component<br/>src/components/Message/messageCover.js]
        C[Message Widget<br/>src/lib/widgets/Message.js]
        D[WebView Rendering<br/>Rich Content Display]
    end

    subgraph "🎯 Controller Layer"
        E[Message Controller<br/>src/controllers/Message.js]
        F[Cover Controller<br/>src/controllers/Cover.js]
        G[Body Controller<br/>src/controllers/Body.js]
        H[Widget Controller<br/>DataWidget Extension]
        I1[Header Message Controller<br/>src/controllers/headerMessage.js]
        J1[Content Message Controller<br/>src/controllers/ContentMessage.js]
    end

    subgraph "💾 Data Model Layer"
        I[Message Model<br/>src/lib/models/Message.js]
        J[Message Cover Webview<br/>src/lib/models/Message/CoverWebview.js]
        K[Message Body Webview<br/>src/lib/models/Message/BodyWebview.js]
        L[Message Globalize<br/>src/lib/models/Message/Globalize.js]
        M[Message When<br/>src/lib/models/Message/When.js]
        N[Message Sender<br/>src/lib/models/Message/Sender.js]
    end

    subgraph "🔧 Infrastructure Layer"
        O[Message Service<br/>src/lib/common/services/message.js]
        P[Messages Library<br/>src/lib/messages.js]
        Q[File System<br/>Resource Management]
        R[Event System<br/>Real-time Updates]
    end

    subgraph "🌐 External Integration"
        S[Backend APIs<br/>Message Sync]
        T[Local Storage<br/>Realm Database]
        U[File Resources<br/>HTML/CSS/Images]
        V[Card System<br/>Widget Integration]
        W1[Firebase FCM<br/>Push Notifications]
        X1[Header System<br/>Global Notifications]
    end

    %% UI Layer Connections
    A --> E
    B --> F
    C --> H
    D --> B

    %% Controller Layer Connections
    E --> I
    F --> J
    G --> K
    H --> C
    I1 --> X1
    J1 --> F
    J1 --> G

    %% Data Model Connections
    I --> J
    I --> K
    I --> L
    I --> M
    I --> N

    %% Infrastructure Connections
    E --> O
    E --> P
    I --> Q
    I --> R

    %% External Integration
    O --> S
    I --> T
    J --> U
    K --> U
    C --> V
    I1 --> W1
    I1 --> X1

    %% Cross-layer Integration
    R --> A
    R --> C
    Q --> D

    %% Styling with darker backgrounds and white text
    classDef ui fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef controller fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef data fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef infrastructure fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff
    classDef external fill:#d69e2e,stroke:#ed8936,stroke-width:2px,color:#ffffff

    class A,B,C,D ui
    class E,F,G,H,I1,J1 controller
    class I,J,K,L,M,N data
    class O,P,Q,R infrastructure
    class S,T,U,V,W1,X1 external
```

### Message User Experience Flow

This diagram illustrates the complete user journey and data flow for message interactions:

```mermaid
graph TB
    subgraph "👤 User Entry Points"
        USER[User]
        CARD[Card View]
        WIDGET[Message Widget]
        NOTIFICATION[Push Notification]
        DEEPLINK[Deep Link]
    end

    subgraph "🎯 Message Discovery"
        BADGE[Badge Indicator<br/>Unread Count]
        LIST[Message List<br/>Chronological View]
        FILTER[Message Filtering<br/>By Kind/Type]
        SECTION[Section Grouping<br/>Date-based Organization]
    end

    subgraph "📖 Message Interaction"
        COVER[Message Cover<br/>Preview Display]
        PREVIEW[Preview Image<br/>Fallback Content]
        WEBVIEW[WebView Content<br/>Rich HTML Rendering]
        BODY[Message Body<br/>Full Content View]
    end

    subgraph "📊 State Management"
        RECEIVED[Received State<br/>Message Delivered]
        SEEN[Seen State<br/>In Viewport]
        VIEWED[Viewed State<br/>User Interaction]
        BODYVIEWED[Body Viewed<br/>Full Content Read]
    end

    subgraph "💾 Data Flow"
        LOCALDB[(Local Database<br/>Message Storage)]
        SYNC[Sync Engine<br/>State Updates]
        CACHE[Resource Cache<br/>HTML/Images/CSS]
        BACKEND[(Backend APIs<br/>Message Delivery)]
    end

    subgraph "🔔 Event System"
        EVENTS[Event Emission<br/>User Actions]
        ANALYTICS[Analytics Tracking<br/>Engagement Metrics]
        BADGES[Badge Updates<br/>Real-time Counts]
        NOTIFICATIONS[Local Notifications<br/>Message Alerts]
    end

    %% User Entry Flow
    USER --> CARD
    USER --> NOTIFICATION
    USER --> DEEPLINK
    CARD --> WIDGET

    %% Discovery Flow
    WIDGET --> BADGE
    WIDGET --> LIST
    LIST --> FILTER
    LIST --> SECTION

    %% Interaction Flow
    SECTION --> COVER
    COVER --> PREVIEW
    COVER --> WEBVIEW
    WEBVIEW --> BODY

    %% State Progression
    COVER --> RECEIVED
    COVER --> SEEN
    WEBVIEW --> VIEWED
    BODY --> BODYVIEWED

    %% Data Flow
    COVER --> LOCALDB
    BODY --> LOCALDB
    LOCALDB --> SYNC
    SYNC --> BACKEND
    WEBVIEW --> CACHE

    %% Event Flow
    VIEWED --> EVENTS
    BODYVIEWED --> EVENTS
    EVENTS --> ANALYTICS
    EVENTS --> BADGES
    EVENTS --> NOTIFICATIONS

    %% Feedback Loops
    BADGES --> WIDGET
    NOTIFICATIONS --> USER
    SYNC --> COVER

    %% Styling with darker backgrounds and white text
    classDef entry fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef discovery fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef interaction fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef state fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef data fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff
    classDef events fill:#4a5568,stroke:#718096,stroke-width:2px,color:#ffffff

    class USER,CARD,WIDGET,NOTIFICATION,DEEPLINK entry
    class BADGE,LIST,FILTER,SECTION discovery
    class COVER,PREVIEW,WEBVIEW,BODY interaction
    class RECEIVED,SEEN,VIEWED,BODYVIEWED state
    class LOCALDB,SYNC,CACHE,BACKEND data
    class EVENTS,ANALYTICS,BADGES,NOTIFICATIONS events
```

### Message Content Architecture

This diagram shows the rich content system and resource management architecture:

```mermaid
graph TB
    subgraph "📄 Content Structure"
        MSG[Message Instance<br/>Core Data Model]
        COVER[Cover Content<br/>Preview HTML]
        BODY[Body Content<br/>Full HTML]
        RESOURCES[Resource Files<br/>Images/CSS/JS]
    end

    subgraph "🌍 Internationalization"
        GLOBALIZE[Globalization Model<br/>Language Support]
        LANG[Language Detection<br/>User Locale]
        FALLBACK[Fallback System<br/>Default Language]
        LOCALIZED[Localized Resources<br/>Language-specific Files]
    end

    subgraph "🎨 Personalization"
        PERSONALIZE[Personalization Data<br/>User-specific Content]
        CONTEXT[User Context<br/>Card/Location Data]
        DYNAMIC[Dynamic Content<br/>Runtime Data Injection]
        STATES[Local States<br/>User Preferences]
    end

    subgraph "📁 File System"
        STRUCTURE[Directory Structure<br/>messages/{id}/]
        HTML[HTML Files<br/>html/{language}/]
        ASSETS[Asset Files<br/>resources/{language}/]
        SHARED[Shared Resources<br/>Common Assets]
    end

    subgraph "⚡ Performance"
        PRIORITY[Priority Loading<br/>Critical Resources First]
        CONCURRENT[Concurrent Processing<br/>Parallel Downloads]
        CACHING[Multi-level Caching<br/>Memory/Disk/Network]
        LAZY[Lazy Loading<br/>On-demand Content]
    end

    subgraph "🔒 Security"
        SANDBOX[WebView Sandbox<br/>Isolated Execution]
        VALIDATION[Content Validation<br/>Safe Rendering]
        ENCRYPTION[Data Encryption<br/>Secure Storage]
        ACCESS[Access Control<br/>File Permissions]
    end

    %% Content Flow
    MSG --> COVER
    MSG --> BODY
    COVER --> RESOURCES
    BODY --> RESOURCES

    %% Internationalization Flow
    MSG --> GLOBALIZE
    GLOBALIZE --> LANG
    LANG --> FALLBACK
    FALLBACK --> LOCALIZED

    %% Personalization Flow
    MSG --> PERSONALIZE
    PERSONALIZE --> CONTEXT
    CONTEXT --> DYNAMIC
    DYNAMIC --> STATES

    %% File System Organization
    RESOURCES --> STRUCTURE
    STRUCTURE --> HTML
    STRUCTURE --> ASSETS
    ASSETS --> SHARED

    %% Performance Optimization
    RESOURCES --> PRIORITY
    PRIORITY --> CONCURRENT
    CONCURRENT --> CACHING
    CACHING --> LAZY

    %% Security Implementation
    HTML --> SANDBOX
    ASSETS --> VALIDATION
    STATES --> ENCRYPTION
    STRUCTURE --> ACCESS

    %% Cross-cutting Concerns
    LOCALIZED --> HTML
    LOCALIZED --> ASSETS
    DYNAMIC --> HTML
    CONTEXT --> PERSONALIZE

    %% Styling with darker backgrounds and white text
    classDef content fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef i18n fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef personalization fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef filesystem fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff
    classDef performance fill:#d69e2e,stroke:#ed8936,stroke-width:2px,color:#ffffff
    classDef security fill:#4a5568,stroke:#718096,stroke-width:2px,color:#ffffff

    class MSG,COVER,BODY,RESOURCES content
    class GLOBALIZE,LANG,FALLBACK,LOCALIZED i18n
    class PERSONALIZE,CONTEXT,DYNAMIC,STATES personalization
    class STRUCTURE,HTML,ASSETS,SHARED filesystem
    class PRIORITY,CONCURRENT,CACHING,LAZY performance
    class SANDBOX,VALIDATION,ENCRYPTION,ACCESS security
```
