# Error Codes Reference

For available actions, see [Actions Reference](./Actions.md).
For event handling, see [Events Reference](./Events.md).

## Common Error Codes

### NETWORK_ERROR
Connection failed or timed out.

### VALIDATION_ERROR
Invalid parameters provided.

### PERMISSION_DENIED
Action requires permissions that were not granted.

## Action-Specific Errors

### Shopping Actions
- INVALID_ITEMS
- OUT_OF_STOCK
- PAYMENT_REQUIRED

### Card Actions
- CARD_NOT_FOUND
- CARD_ALREADY_ACTIVATED
- INVALID_CARD_STATE

## Error Handling Example
```javascript
try {
    await $perkd.do('action');
} catch (error) {
    switch (error.code) {
        case 'NETWORK_ERROR':
            showOfflineMessage();
            break;
        case 'PERMISSION_DENIED':
            requestPermission();
            break;
        default:
            showError(error);
    }
}
``` 