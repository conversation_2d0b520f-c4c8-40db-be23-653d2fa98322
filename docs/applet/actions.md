# Perkd Applet Actions Reference

This document lists all actions available through the AppBridge SDK. All actions are called using `$perkd.do(action, params)`. For example:
```javascript
$perkd.do('app.navto', { route: [{ card: { id: '123' } }] });
```

## Table of Contents

1. [App Actions](#app-actions)
2. [Bag Actions](#bag-actions)
3. [Card Actions](#card-actions)
4. [Communication Actions](#communication-actions)
5. [Engagement Actions](#engagement-actions)
6. [File Actions](#file-actions)
7. [Form Actions](#form-actions)
8. [Interaction Actions](#interaction-actions)
9. [Location Actions](#location-actions)
10. [Media Actions](#media-actions)
11. [Message Actions](#message-actions)
12. [Network Actions](#network-actions)
13. [NFC Actions](#nfc-actions)
14. [Offer Actions](#offer-actions)
15. [Order Actions](#order-actions)
16. [Payment Actions](#payment-actions)
17. [Permissions Actions](#permissions-actions)
18. [Remote Actions](#remote-actions)
19. [Reward Actions](#reward-actions)
20. [Shop Actions](#shop-actions)
21. [Sync Actions](#sync-actions)
22. [System Actions](#system-actions)
23. [Track Actions](#track-actions)
24. [Web Actions](#web-actions)
25. [Widget Actions](#widget-actions)
26. [Window Actions](#window-actions)

## app
[View all app actions](../actions/app.md)

- `navto`
  Navigates to a specific route in the application. Accepts route array for navigation path and supports card, widget, and offer navigation.

- `colorscheme`
  Gets the current app color scheme (light/dark mode). Uses device appearance settings and updates dynamically.

- `sync`
  Syncs all application data with the server. Updates cache, cards, settings and handles app events.

- `update`
  Opens the relevant app store for updates. Supports iOS App Store and Google Play.

## bag
[View all bag actions](../actions/bag.md)

- `additems`
  Adds items to a shopping bag with quantities and options. Emits `Bag.addItems` event on success.

- `configitems`
  Configures items before adding to bag. Shows configuration modal for user input.

- `updateitems`
  Updates existing items in the shopping bag. Emits `Bag.updateItems` event on success.

- `removeitems`
  Removes items from the shopping bag. Emits `Bag.removeItems` event on success.

- `configbundle`
  Configures bundle items before adding to bag. Shows bundle configuration modal.

- `get`
  Get current shopping bag contents and state.

- `from`
  Load shopping bag from a specific source or template.

- `open`
  Open the shopping bag view with current contents.

- `use`
  Apply shopping bag template or preset configuration.

- `build`
  Build a new shopping bag with specified configuration.

## card
[View all card actions](../actions/card.md)

- `request`
  Request a new loyalty card. Requires card master ID and validates user qualification.

- `add`
  Add cards to the user's wallet. Supports single or multiple cards with principal card relationships.

- `qualified`
  Check if user qualifies for a specific card based on card master ID.

- `clockin`
  Record a clock-in event for time-based cards. Supports location-based check-ins with TTL.

- `clockout`
  Record a clock-out event for time-based cards. Validates clock-in status and calculates duration.

## communicate
[View all communicate actions](../actions/communicate.md)

- `call`
  Make a phone call to a specified number. Automatically formats phone number.

- `email`
  Send an email using device's email client. Supports template variables and CC/BCC.

- `sms`
  Send SMS messages to one or more recipients. Supports template variables.

- `chat`
  Open a chat application or web chat interface. Supports deep linking with web fallback.

## engage
[View all engage actions](../actions/engage.md)

- `action`
  Execute a generic engagement action. Handles Do Not Disturb (DND) state.

- `applet`
  Launch an interactive applet with custom HTML content. Supports resource loading and styling.

- `dialog`
  Show a dialog with customizable content and actions. Locks app's DND state.

- `optionsdialog`
  Show a dialog with selectable options for user choice.

- `updateapp`
  Show an app update prompt. Handles platform-specific app store navigation.

- `rateapp`
  Show an app rating prompt using native platform rating interface.

- `checkin`
  Perform a location-based check-in with optional ticket validation.

## file
[View all file actions](../actions/file.md)

- `share`
  Share content using system share sheet. Supports file contents and URLs.

- `download`
  Download a file from URL to app's temporary directory. Creates directories as needed.

- `write`
  Write content to a file in app's storage. Supports custom paths and encodings.

## form
[View all form actions](../actions/form.md)

- `validate`
  Validate form data against JSON Schema. Supports custom rules and localized errors.

- `openinghours`
  Configure business opening hours with visual picker interface.

- `showpicker`
  Show picker modal for date, time, or duration selection. Supports multiple display modes.

## interact
[View all interact actions](../actions/interact.md)

- `bottommodal`
  Show a bottom modal with custom HTML content. Supports pull-to-close behavior.

- `showloading`
  Show a loading indicator overlay. Manages loading state to prevent duplicates.

- `hideloading`
  Hide the currently displayed loading indicator overlay.

- `notify`
  Schedule a local notification. Supports title, subtitle, body, and image.

- `toast`
  Show a toast message. Supports custom position and auto-dismissal.

- `vibrate`
  Trigger device haptic feedback. Supports different patterns with vibration fallback.

- `dialog`
  Show a dialog with customizable title and buttons. Supports different styles.

- `inputdialog`
  Show a dialog with text input field. Supports custom placeholder and validation.

- `playsound`
  Play a predefined sound effect. Supports various sound categories.

## location
[View all location actions](../actions/location.md)

- `navigate`
  Navigate to a location using device's navigation system. Supports multiple map providers.

- `current`
  Get current location information including country details and geocoded data.

- `distance`
  Calculate distance between two points. Returns distance in meters.

- `duration`
  Calculate travel duration between points. Returns duration in seconds and distance.

- `nearestplaces`
  Find nearest places within radius. Returns places sorted by distance.

## media
[View all media actions](../actions/media.md)

- `barcode`
  Generate and display a barcode or QR code. Supports multiple formats and styling.

- `scan`
  Scan barcodes and QR codes using device camera. Supports multiple formats.

- `addcalendar`
  Add an event to device calendar. Supports start/end dates and custom colors.

- `takephoto`
  Open camera to take a photo. Supports quality settings and flash control.

- `choosephoto`
  Open photo picker for gallery selection. Supports single/multiple selection.

## message
[View all message actions](../actions/message.md)

- `request`
  Request message issuance from CRM via card master. Requires master ID and template ID.

## network
[View all network actions](../actions/network.md)

- `info`
  Get current network connection information. Includes connection type and status.

## nfc
[View all nfc actions](../actions/nfc.md)

- `read`
  Read data from NFC tag. Supports content decoding and QR fallback.

- `write`
  Write data to NFC tag. Supports text and URI records.

- `setpassword`
  Set password on NFC tag. Supports multiple tag types.

- `removepassword`
  Remove password protection from NFC tag. Requires current password.

## offer
[View all offer actions](../actions/offer.md)

- `request`
  Request offer issuance from CRM. Requires master ID and offer master ID.

- `redeem`
  Redeem offers with optional actions. Supports item matching and order redemption.

- `share`
  Share an offer with other users. Supports multiple sharing modes.

## order
[View all order actions](../actions/order.md)

- `at`
  Check in and navigate to store front. Integrates with location services.

- `card`
  Place order for card or membership. Supports various pricing and payment options.

- `products`
  Place order for products. Supports multiple payment methods and fulfillment types.

- `cancel`
  Cancel an order. Handles payment intents and cleanup.

## pay
[View all pay actions](../actions/pay.md)

- `select`
  Select payment methods. Supports multiple providers and platform-specific methods.

- `request`
  Request a payment. Supports multiple currencies and payment methods.

- `params`
  Get payment parameters for specific methods and providers.

- `preview`
  Show payment preview modal with cost breakdown.

- `pay`
  Process payment using selected method. Handles authentication flows.

- `send`
  Send money to another card. Supports transfer limits and validation.

- `addmoney`
  Show add money modal for topping up balance. Supports multiple methods.

- `requestpay`
  Request payment from others. Supports multiple payment methods.

- `cancel`
  Cancel a payment. Handles cleanup across different providers.

- `setup`
  Configure payment methods. Handles platform-specific setup flows.

- `applepay`
  Process Apple Pay payments. Supports merchant validation.

- `alipay`
  Process Alipay payments. Supports multiple providers.

- `safepay`
  Process SafePay payments. Handles URL scheme-based flows.

- `linepay`
  Process LINE Pay payments. Supports test/production environments.

- `tng`
  Process Touch 'n Go payments. Supports multiple providers.

## permissions
[View all permissions actions](../actions/permissions.md)

- `check`
  Check current permission status. Returns detailed status information.

- `request`
  Check and request permission if not granted. Handles platform-specific types.

## remote
[View all remote actions](../actions/remote.md)

- `api`
  Make remote API call. Includes card context and spot information.

- `connect`
  Create WebSocket connection. Handles connection lifecycle and reconnection.

- `send`
  Send data through WebSocket. Supports string and object messages.

- `close`
  Close WebSocket connection. Handles proper termination.

- `clean`
  Clean up WebSocket connections for specific source.

## reward
[View all reward actions](../actions/reward.md)

- `request`
  Request reward issuance from CRM. Handles reward state transitions.

## shop
[View all shop actions](../actions/shop.md)

- `brand`
  Open brand search or details view. Supports filtering and analytics.

- `products`
  Fetch and display product details. Handles caching and preloading.

- `likebrand`
  Manage brand favorite status. Updates user preferences.

## sync
[View all sync actions](../actions/sync.md)

- `all`
  Performs a complete synchronization of all data types including cache, card masters, settings, actions, app events, places, and images.

- `cache`
  Synchronizes cache data between local and remote storage, handling both up and down sync operations.

## system
[View all system actions](../actions/system.md)

- `settings`
  Open device's system settings for the application.

- `time`
  Get current system time in milliseconds.

- `toclipboard`
  Copy text to device's clipboard.

- `fromclipboard`
  Retrieve text from device's clipboard.

- `openapp`
  Open another application using URI schemes.

- `download`
  Download content from URL to temporary directory.

- `share`
  Share content using system share sheet.

## track
[View all track actions](../actions/track.md)

- `log`
  Log message with optional topic and priority.

- `event`
  Track application event with associated data.

- `watch`
  Watch for errors with optional topic and priority.

## web
[View all web actions](../actions/web.md)

- `open`
  Open web URL with optional context. Supports multiple opening modes.

- `search`
  Trigger web search event. Emits applet search event.

## widget
[View all widget actions](../actions/widget.md)

- `data`
  Manage widget data persistence and state. Handles synchronization.

## window
[View all window actions](../actions/window.md)

- `close`
  Close current window or modal view. Handles cleanup and transitions.

## Error Handling & Best Practices

### Error Pattern
All actions follow a standard error pattern:
```javascript
{
    code: 'ERROR_CODE',
    message: 'Human readable message',
    details: {
        // Additional error context
    }
}
```

### Common Error Codes
- `NETWORK_ERROR` - Network connectivity issues
- `VERSION_MISMATCH` - App version incompatibility
- `PERMISSION_DENIED` - Required permission not granted
- `INVALID_PARAMETER` - Invalid action parameters
- `NOT_SUPPORTED` - Action not supported on current platform

### Development Guidelines

1. **Error Handling**
   - Always implement proper error handling
   - Show user-friendly error messages
   - Implement retry logic where appropriate

2. **Network Awareness**
   - Check network connectivity before actions
   - Implement offline fallbacks where possible
   - Cache responses when appropriate

3. **Security**
   - Validate all input parameters
   - Handle sensitive data appropriately
   - Follow platform security guidelines

4. **Performance**
   - Minimize action chaining
   - Implement proper loading states
   - Cache results when possible
