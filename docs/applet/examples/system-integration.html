<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover">
    <meta name="format-detection" content="telephone=no" />
    
    <!-- App<PERSON>ridge will inject these constants -->
    <script>
        window.$const = <%- constants %>;
    </script>
    
    <!-- Include AppBridge -->
    <script src="<%- PATH.SHARED.RSRC %>appbridge-1.0.min.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 20px;
            padding: 0;
        }
        .button {
            background: #007AFF;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            border: none;
            margin: 10px 0;
            width: 100%;
            font-size: 16px;
        }
        .update-prompt {
            display: none;
            background: #FFE4E4;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
            text-align: center;
        }
        .update-prompt.visible {
            display: block;
        }
    </style>
</head>
<body>
    <div id="updatePrompt" class="update-prompt">
        <h3>Update Required</h3>
        <p>Please update your app to the latest version to continue.</p>
        <button class="button" onclick="openAppStore()">Update Now</button>
    </div>

    <button class="button" onclick="openProductLink()">Open Product in Browser</button>

    <script>
        // Constants
        const MIN_APP_VERSION = '1.0.0'; // Set your minimum required version
        const PRODUCT_URL = 'https://perkd.com/product/123';
        
        // Initialize the applet
        $perkd.do('init').then(() => {
            checkAppVersion();
        }).catch(error => {
            console.error('Initialization failed:', error);
        });

        // Version checking
        function checkAppVersion() {
            const currentVersion = window.$const.APP.VERSION;
            if (compareVersions(currentVersion, MIN_APP_VERSION) < 0) {
                document.getElementById('updatePrompt').classList.add('visible');
            }
        }

        // Version comparison utility
        function compareVersions(v1, v2) {
            const parts1 = v1.split('.').map(Number);
            const parts2 = v2.split('.').map(Number);
            
            for (let i = 0; i < 3; i++) {
                if (parts1[i] > parts2[i]) return 1;
                if (parts1[i] < parts2[i]) return -1;
            }
            return 0;
        }

        // Open product in system browser
        async function openProductLink() {
            try {
                await $perkd.do('web.open', {
                    url: PRODUCT_URL
                });
            } catch (error) {
                console.error('Failed to open URL:', error);
            }
        }

        // Open appropriate app store
        function openAppStore() {
            const platform = window.$const.DEVICE.OS;
            // You can provide custom store links per platform and store type
            const storeLinks = {
                ios: {
                    apple: 'https://apps.apple.com/app/perkd/id123456789'
                },
                android: {
                    google: 'https://play.google.com/store/apps/details?id=com.perkd.app'
                }
            };
            
            $perkd.do('app.update', storeLinks).catch(error => {
                console.error('Failed to open store:', error);
            });
        }
    </script>
</body>
</html> 