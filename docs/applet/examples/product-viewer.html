<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover">
    <meta name="format-detection" content="telephone=no" />
    
    <!-- AppB<PERSON> will inject these constants -->
    <script>
        window.$const = <%- constants %>;
    </script>
    
    <!-- Include AppBridge -->
    <script src="<%- PATH.SHARED.RSRC %>appbridge-1.0.min.js"></script>
    
    <style>
        :root {
            --base-font-size: <%- UI.baseFontSize %>px;
            --primary-color: <%- UI.button.primary.background %>;
            --primary-text: <%- UI.button.primary.text %>;
            --disabled-color: <%- UI.button.disabled.background %>;
            --disabled-text: <%- UI.button.disabled.text %>;
        }
        
        body {
            font-size: var(--base-font-size);
            padding-top: <%- DEVICE.STATUS_BAR_HEIGHT %>px;
        }
        
        .container {
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .product {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .product-image {
            width: 100%;
            height: 300px;
            background: #f5f5f5;
            border-radius: 8px;
            margin-bottom: 15px;
            object-fit: cover;
        }
        
        .product-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0 0 10px;
        }
        
        .product-price {
            font-size: 20px;
            color: #007AFF;
            margin: 0 0 15px;
        }
        
        .product-description {
            color: #666;
            margin: 0 0 20px;
            line-height: 1.5;
        }
        
        .size-selector {
            margin-bottom: 20px;
        }
        
        .size-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .size-options {
            display: flex;
            gap: 10px;
        }
        
        .size-option {
            padding: 8px 16px;
            border: 2px solid #ddd;
            border-radius: 6px;
            cursor: pointer;
        }
        
        .size-option.selected {
            border-color: #007AFF;
            color: #007AFF;
        }
        
        .quantity {
            margin-bottom: 20px;
        }
        
        .quantity-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .quantity-btn {
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 50%;
            background: #007AFF;
            color: white;
            font-size: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .quantity-value {
            font-size: 18px;
            min-width: 40px;
            text-align: center;
        }
        
        .add-to-bag {
            width: 100%;
            padding: 15px;
            background: var(--primary-color);
            color: var(--primary-text);
            border: none;
            border-radius: <%- UI.button.borderRadius %>px;
            font-size: var(--base-font-size);
            font-weight: 500;
            cursor: pointer;
        }
        
        .add-to-bag:disabled {
            background: var(--disabled-color);
            color: var(--disabled-text);
            cursor: not-allowed;
        }
        
        .toast {
            position: fixed;
            bottom: calc(20px + <%- DEVICE.NAV_BAR_HEIGHT %>px);
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 12px 24px;
            border-radius: 24px;
            font-size: 14px;
            display: none;
        }
        
        .offline-banner {
            background: #FF3B30;
            color: white;
            text-align: center;
            padding: 10px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            transform: translateY(-100%);
            transition: transform 0.3s ease;
            padding-top: calc(10px + <%- DEVICE.HAS_NOTCH ? DEVICE.STATUS_BAR_HEIGHT : 0 %>px);
        }
        
        .offline-banner.visible {
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <div class="offline-banner" id="offlineBanner">
        You are currently offline
    </div>
    
    <div class="container">
        <div class="product">
            <img class="product-image" src="<%- PATH.IMAGES %>product.jpg" alt="Product Image">
            <h1 class="product-title">Premium T-Shirt</h1>
            <div class="product-price">$29.99</div>
            <p class="product-description">
                Ultra-soft cotton blend t-shirt with a modern fit. 
                Perfect for everyday wear with exceptional comfort and style.
            </p>
            
            <div class="size-selector">
                <label class="size-label">Select Size</label>
                <div class="size-options" id="sizeOptions">
                    <div class="size-option" data-size="S">S</div>
                    <div class="size-option" data-size="M">M</div>
                    <div class="size-option" data-size="L">L</div>
                    <div class="size-option" data-size="XL">XL</div>
                </div>
            </div>
            
            <div class="quantity">
                <label class="quantity-label">Quantity</label>
                <div class="quantity-controls">
                    <button class="quantity-btn" id="decreaseBtn">-</button>
                    <span class="quantity-value" id="quantityValue">1</span>
                    <button class="quantity-btn" id="increaseBtn">+</button>
                </div>
            </div>
            
            <button class="add-to-bag" id="addToBag">Add to Bag</button>
        </div>
    </div>
    
    <div class="toast" id="toast"></div>

    <script>
        class ProductViewer {
            constructor() {
                this.productId = 'tshirt-premium-001';
                this.selectedSize = null;
                this.quantity = 1;
                this.isOnline = true;
                this.constants = window.$const;
                
                // Use constants for configuration
                this.maxQuantity = this.constants.UI.maxQuantity || 10;
                this.imageBasePath = this.constants.PATH.IMAGES;
                
                this.loadSavedState();
            }
            
            async init() {
                await $perkd.do('init');
                this.setupEventListeners();
                this.updateAddToBagButton();
            }
            
            loadSavedState() {
                if ($data.lastProduct === this.productId) {
                    this.selectedSize = $data.lastSize;
                    this.quantity = $data.lastQuantity || 1;
                }
            }
            
            saveState() {
                $data.lastProduct = this.productId;
                $data.lastSize = this.selectedSize;
                $data.lastQuantity = this.quantity;
                $data.save();
            }
            
            setupEventListeners() {
                // Size selection
                const sizeOptions = document.getElementById('sizeOptions');
                sizeOptions.addEventListener('click', (e) => {
                    const option = e.target.closest('.size-option');
                    if (option) {
                        this.handleSizeSelect(option);
                    }
                });
                
                // Quantity controls
                document.getElementById('decreaseBtn').addEventListener('click', () => {
                    this.updateQuantity(Math.max(1, this.quantity - 1));
                });
                
                document.getElementById('increaseBtn').addEventListener('click', () => {
                    this.updateQuantity(Math.min(10, this.quantity + 1));
                });
                
                // Add to bag
                document.getElementById('addToBag').addEventListener('click', () => {
                    this.addToBag();
                });
                
                // Network status
                window.addEventListener('network.changed', ({ detail }) => {
                    this.handleConnectivity(detail.isInternetReachable);
                });
                
                // Restore saved size selection
                if (this.selectedSize) {
                    const option = document.querySelector(`[data-size="${this.selectedSize}"]`);
                    if (option) {
                        this.handleSizeSelect(option, false);
                    }
                }
                
                // Restore saved quantity
                this.updateQuantity(this.quantity, false);
            }
            
            handleSizeSelect(option, save = true) {
                // Remove previous selection
                document.querySelectorAll('.size-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // Add new selection
                option.classList.add('selected');
                this.selectedSize = option.dataset.size;
                
                if (save) {
                    this.saveState();
                }
                
                this.updateAddToBagButton();
            }
            
            updateQuantity(value, save = true) {
                this.quantity = Math.min(this.maxQuantity, Math.max(1, value));
                document.getElementById('quantityValue').textContent = this.quantity;
                
                if (save) {
                    this.saveState();
                }
                
                this.updateAddToBagButton();
            }
            
            updateAddToBagButton() {
                const button = document.getElementById('addToBag');
                const isValid = this.selectedSize && this.quantity > 0 && this.isOnline;
                button.disabled = !isValid;
            }
            
            async addToBag() {
                if (!this.selectedSize || !this.isOnline) return;
                
                try {
                    await $perkd.do('bag.additems', {
                        items: [{
                            id: this.productId,
                            quantity: this.quantity,
                            options: {
                                size: this.selectedSize
                            }
                        }]
                    });
                    
                    this.showToast('Added to bag');
                } catch (error) {
                    this.showToast('Failed to add to bag');
                }
            }
            
            handleConnectivity(isOnline) {
                this.isOnline = isOnline;
                document.getElementById('offlineBanner').classList.toggle('visible', !isOnline);
                this.updateAddToBagButton();
            }
            
            showToast(message) {
                const toast = document.getElementById('toast');
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(() => {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            updateUI() {
                // Use constants for image paths
                const productImage = document.querySelector('.product-image');
                productImage.src = `${this.imageBasePath}product.jpg`;
                
                // Use constants for currency formatting if available
                const priceElement = document.querySelector('.product-price');
                const formatter = new Intl.NumberFormat(this.constants.UI.locale || 'en-US', {
                    style: 'currency',
                    currency: this.constants.UI.currency || 'USD'
                });
                priceElement.textContent = formatter.format(29.99);
            }
        }

        // Initialize
        const viewer = new ProductViewer();
        viewer.init().then(() => {
            viewer.updateUI();
        });
    </script>
</body>
</html> 