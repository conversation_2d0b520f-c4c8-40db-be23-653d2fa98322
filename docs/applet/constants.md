# Perkd Applet Constants Reference

This document lists all constants available through `window.$const` in applets.

## Device Information (DEVICE)

```javascript
const { DEVICE } = window.$const;

// Device type and platform
DEVICE.OS           // 'ios' or 'android'
DEVICE.IOS         // true if iOS
DEVICE.ANDROID     // true if Android
DEVICE.IS_IPAD     // true if iPad
DEVICE.IS_SIMULATOR // true if running in simulator

// Device dimensions
DEVICE.WIDTH       // Screen width
DEVICE.HEIGHT      // Screen height
DEVICE.PIXEL_RATIO // Device pixel ratio

// Device features
DEVICE.HAS_NOTCH   // Whether device has a notch
DEVICE.IOS_DYNAMIC_ISLAND // Whether device has Dynamic Island

// Device metrics
DEVICE.STATUS_BAR_HEIGHT  // Height of status bar
DEVICE.NAV_BAR_HEIGHT    // Height of navigation bar
DEVICE.TAB_BAR_HEIGHT    // Height of tab bar
DEVICE.NOTCH_HEIGHT     // Height of notch area
DEVICE.MIN_TOP_SPACE    // Minimum top spacing
DEVICE.MIN_BOTTOM_SPACE // Minimum bottom spacing

// Device identification
DEVICE.ID          // Unique device ID
DEVICE.NAME        // Device name
DEVICE.BRAND       // Device brand
DEVICE.MODEL       // Device model

// App information
DEVICE.APP = {
    ID: 'me.perkd',           // App bundle ID
    VERSION: '1.0.0',         // App version
    TEST: false,              // Whether in test mode
    ENV: 'prod'              // Environment ('dev' or 'prod')
}

// Performance metrics
DEVICE.PERFORMANCE = {
    high: true,   // High-end device
    mid: false,   // Mid-range device
    low: false    // Low-end device
}
```

## UI Constants (UI)

```javascript
const { UI } = window.$const;

// Typography
UI.baseFontSize    // Base font size for the app
UI.FONT_SIZE = {
    h1: 24,
    h2: 20,
    body1: 16,
    body2: 14,
    caption: 12
}

// Spacing
UI.SPACING = {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32
}

// Colors
UI.COLOR = {
    primary: '#FFB300',
    accent: '#954700',
    background: '#FFFFFF',
    text: '#000000'
}

// Button styles
UI.button = {
    default: {
        backgroundColor: '#FFB300',
        textColor: '#FFFFFF',
        borderRadius: 8
    },
    outline: {
        backgroundColor: 'transparent',
        borderColor: '#FFB300',
        textColor: '#FFB300'
    }
}
```

## Path Constants (PATH)

```javascript
const { PATH } = window.$const;

// File system paths
PATH.SHARED        // Path to shared resources
PATH.APPLETS       // Path to applets directory
PATH.MESSAGES      // Path to messages directory

// Storage paths
PATH.IMAGES        // Path to images directory
PATH.CARDS         // Path to cards directory
PATH.CACHE         // Path to cache directory

// Temporary paths
PATH.TMP_FOLDER    // Path to temporary directory
PATH.NOTIFICATIONS // Path to notifications directory
```

## Language Constants (LANGUAGES)

```javascript
const { LANGUAGES } = window.$const;

// Available languages
LANGUAGES.SUPPORTED = [
    'en',              // English
    'zh-Hans',         // Simplified Chinese
    'zh-Hant',         // Traditional Chinese
    'zh-Hant-HK',      // Traditional Chinese (HK)
    'ja',              // Japanese
    'ko',              // Korean
    'id',              // Bahasa Indonesia
    'ms'               // Bahasa Melayu
]

LANGUAGES.DEFAULT = 'en'
LANGUAGES.DOUBLE_BYTE = ['zh-Hans', 'zh-Hant', 'zh-Hant-HK', 'ja', 'ko']
```

## Best Practices

1. **Device-Specific Logic**
   ```javascript
   function setupLayout() {
       const { DEVICE } = window.$const;
       
       // Handle notch
       const topPadding = DEVICE.HAS_NOTCH 
           ? DEVICE.NOTCH_HEIGHT 
           : DEVICE.STATUS_BAR_HEIGHT;
           
       // Handle platform differences
       const buttonHeight = DEVICE.IOS ? 44 : 48;
       
       return { topPadding, buttonHeight };
   }
   ```

2. **Responsive Design**
   ```javascript
   function calculateLayout() {
       const { DEVICE, UI } = window.$const;
       
       // Max width for tablets
       const containerWidth = Math.min(DEVICE.WIDTH, 900);
       
       // Scale font size
       const fontSize = UI.baseFontSize * DEVICE.FONT_SCALE;
       
       return { containerWidth, fontSize };
   }
   ```

3. **Path Resolution**
   ```javascript
   function getResourcePath(filename) {
       const { PATH } = window.$const;
       return `${PATH.SHARED}/resources/${filename}`;
   }
   ```

4. **Language Handling**
   ```javascript
   function isDoubleByteLanguage(lang) {
       const { LANGUAGES } = window.$const;
       return LANGUAGES.DOUBLE_BYTE.includes(lang);
   }
   ```

## Notes

1. Constants are read-only and should not be modified.
2. Some constants may be platform-specific.
3. Values may vary between devices and OS versions.
4. Always check for existence before using optional constants. 