# Events Reference

For action-related errors, see [Error Codes](./Errors.md).
For available actions, see [Actions Reference](./Actions.md).

## Core Events

### app.pause
Fired when the app goes to background.

### app.resume
Fired when the app comes to foreground.

### app.active
App became active.

### app.inactive
App became inactive.

### app.background
App entered background state.

## Data Events

### data.changed
Fired when the data layer is updated.

### bag.changed
Fired when the shopping bag is updated.

## Navigation Events

- `applet.loaded` - Applet finished loading
- `applet.load.error` - Error loading applet
- `applet.usage` - Applet usage tracked

## Card Events

- `card.created` - New card created
- `card.updated` - Card details updated
- `card.deleted` - Card removed
- `card.view` - Card viewed
- `card.exit` - Card view exited
- `card.clockIn` - Card clock-in recorded

## Shopping Events

- `bag.items.add` - Items added to bag
- `bag.items.update` - Bag items updated
- `bag.items.remove` - Items removed from bag
- `bag.checkout` - Checkout initiated

## Widget Events

- `widget.view` - Widget viewed
- `widget.exit` - Widget exited
- `widget.arrive` - User arrived at location
- `widget.leave` - User left location
- `widget.checkin` - User checked in
- `widget.checkout` - User checked out

## Network Events

- `network.changed` - Network connectivity changed
- `network.online` - Network became available

## Payment Events

- `payment.callback` - Payment callback received
- `payment.method.added` - Payment method added
- `payment.method.removed` - Payment method removed

## Event Data Structure

Events follow a consistent structure with data passed in the `detail` property:

```javascript
window.addEventListener('card.view', function(event) {
    const {
        id,          // Card ID
        number,      // Card number
        masterId,    // Card master ID
        brand,       // Brand info
        tenant      // Tenant info
    } = event.detail;
});
```

## Common Event Properties

Most events include these common properties in their detail:

- `cardId` - Related card ID
- `masterId` - Related card master ID
- `source` - Event source/trigger
- `timestamp` - Event timestamp

## Best Practices

1. **Event Cleanup**
   ```javascript
   // Add listener
   const handler = (event) => {
       // Handle event
   };
   window.addEventListener('event.name', handler);
   
   // Remove when done
   window.removeEventListener('event.name', handler);
   ```

2. **Error Handling**
   ```javascript
   window.addEventListener('applet.load.error', function(event) {
       const { code, message } = event.detail;
       // Handle error appropriately
   });
   ```

3. **Event Debouncing**
   ```javascript
   let timeout;
   window.addEventListener('data.changed', function(event) {
       clearTimeout(timeout);
       timeout = setTimeout(() => {
           // Handle data change
       }, 250);
   });
   ```

## Event Categories

### User Interaction Events
- `widget.usage` - Widget interaction tracked
- `widget.view` - Widget viewed
- `widget.exit` - Widget exited

### Location Events
- `location.country.changed` - User's country changed
- `location.position.changed` - User's position updated
- `location.spot.changed` - User entered/left a spot

### Communication Events
- `message.received` - New message received
- `message.seen` - Message marked as seen
- `notify.received` - Push notification received

### Commerce Events
- `offer.view` - Offer viewed
- `offer.redeem` - Offer redeemed
- `reward.view` - Reward viewed
- `reward.received` - Reward received

### System Events
- `permissions.changed` - App permissions changed
- `network.changed` - Network status changed
- `sync.all` - Full sync completed
- `sync.cache` - Cache sync completed 