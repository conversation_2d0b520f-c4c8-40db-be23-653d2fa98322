
# Perkd Application Architecture

## Overview

Perkd is a comprehensive React Native mobile application designed for customer loyalty and rewards management. The application follows a sophisticated multi-layered architecture that emphasizes modularity, scalability, and maintainability. This document provides a detailed technical analysis of the application's architecture, design patterns, and component interactions with a focus on user experience flow and data flow.

## Technology Stack

### Core Technologies
- **Framework**: React Native 0.67.5 with React 17.0.2
- **Language**: JavaScript with TypeScript support (TypeScript 5.7.3)
- **Navigation**: React Native Navigation 7.27.1 (Wix Navigation)
- **Database**: Realm 10.24.0 for local data persistence
- **Storage**: AsyncStorage for simple key-value persistence
- **State Management**: Custom event-driven architecture with centralized registry
- **UI Components**: React Native Elements 3.4.3 with custom component library
- **Networking**: Axios 1.6.5 with custom service layer
- **Testing**: Jest 28.0.3 for unit testing, Detox 19.7.1 for E2E testing

### Key Dependencies
- **Firebase**: Push notifications and analytics (@react-native-firebase/*)
- **Payments**: Multiple payment providers (Stripe, Apple Pay, Google Pay)
- **Location**: Geolocation services and mapping (react-native-maps)
- **Camera**: Barcode scanning and image processing
- **Biometrics**: Fingerprint and face recognition authentication
- **Animations**: Lottie animations and React Native Reanimated

## Architectural Patterns

### 1. Layered Architecture

The application follows a clear layered architecture with well-defined separation of concerns:

#### Presentation Layer
- **Containers** (`src/containers/`): Screen-level components that manage navigation and high-level state
- **Components** (`src/components/`): Reusable UI components with minimal business logic
- **Styles** (`src/styles/`): Centralized styling and theming system

#### Business Logic Layer
- **Controllers** (`src/controllers/`): Coordinate between UI and data layers, manage screen-specific logic
- **Actions** (`src/lib/common/actions/`): Handle user interactions and business operations
- **Services** (`src/lib/common/services/`): External API integrations and service communications

#### Data Layer
- **Models** (`src/lib/models/`): Realm database models and data structures
- **Sync** (`src/lib/common/sync.js`): Data synchronization between local and remote
- **Persistence** (`src/lib/common/persist.js`): Local storage management

#### Infrastructure Layer
- **Common** (`src/lib/common/`): Shared utilities, constants, and core functionality
- **Registry** (`src/lib/common/registry.js`): Centralized model and instance management
- **Navigator** (`src/lib/navigator.js`): Navigation system and routing

### 2. Event-Driven Architecture

The application uses a sophisticated event system for decoupled communication:

```javascript
// Event emission
$.Event.emit(EVENT.Card.updated, { cardId, changes });

// Event listening
$.Event.on(EVENT.Card.updated, this.onCardUpdated, this);
```

Events are organized by domain (Card, Shop, Place, etc.) and enable:
- Loose coupling between components
- Real-time UI updates
- Cross-feature communication
- Analytics and tracking integration

### 3. Registry Pattern

A centralized registry manages all models and instances:

```javascript
// Model access
const card = _.Card.findById(cardId);

// Instance management
const cardInstance = Instances.Card;
```

This pattern provides:
- Consistent data access across the application
- Lazy loading of models
- Centralized dependency management
- Type safety and intellisense support

## Core Modules and Responsibilities

### Navigation System
The application uses a sophisticated navigation system built on react-native-navigation:

- **Tab Navigation**: Five main tabs (Cards, Discover, Shop, Profile, Preferences)
- **Stack Navigation**: Hierarchical screen navigation within tabs
- **Modal System**: Overlay screens for focused interactions
- **Deep Linking**: URL scheme support for external navigation (`perkd://`)
- **Dynamic Loading**: Lazy loading of screen bundles for performance optimization

**Key Components:**
- `src/lib/navigator.js`: Core navigation logic and utilities
- `src/lib/loader.js`: Dynamic screen loading and registration
- `src/styles/navigator.js`: Navigation styling and themes

### Data Management
The data layer provides comprehensive data management capabilities through a sophisticated multi-tiered storage architecture:

#### Multi-Tiered Storage Architecture

The application uses a specialized storage architecture to meet various requirements for data persistence, performance, and security:

| Storage Layer | Technology | Primary Use | Security Level |
|---------------|------------|-------------|----------------|
| **Key-Value Store** | AsyncStorage | App configuration, preferences | Standard |
| **Object Database** | Realm | Structured application data | Standard |
| **Secure Storage** | Keychain API | Credentials, tokens, payment info | Encrypted |
| **In-Memory Cache** | JavaScript objects | Temporary data, performance optimization | Transient |

#### Data Access Layers

**Persistence Layer** (`src/lib/common/persist.js`):
- Manages simple key-value storage needs using AsyncStorage
- Handles application settings, user preferences, and session state
- Provides type-aware serialization/deserialization
- Implements memory caching for frequently accessed values
- Used for configuration that doesn't require relational queries

**Database Layer** (`src/lib/common/db.js`, `src/lib/common/storage.js`):
- Manages structured data using Realm database (current schema: 278+)
- Defines schemas for all application models with relationships
- Supports primary keys, complex queries, and transactions
- Provides an abstraction layer for database operations
- Enables automated schema migrations and versioning

**Secure Storage Layer** (`src/lib/common/credentials.js`):
- Handles sensitive information using device-specific secure storage
- Stores credentials, tokens, and payment information
- Organized by domain (App, CardMaster, Card, Payment)
- Provides encrypted storage inaccessible to other applications
- Leverages the cryptography service for data encryption/decryption

**Synchronization Layer** (`src/lib/common/sync.js`):
- Coordinates data between local storage and remote services
- Manages background synchronization scheduling
- Handles conflict resolution and offline capabilities
- Implements optimistic updates for better user experience
- Provides intelligent sync strategies based on data priority

#### Storage Architecture Benefits

This multi-layered approach provides several advantages:

1. **Appropriate Storage Selection**: Uses the right storage mechanism for each data type
   - Transactional data → Realm Database
   - Configuration → AsyncStorage
   - Sensitive data → Secure Keychain

2. **Performance Optimization**: Each storage type is optimized for its specific use case
   - Fast access to simple settings via AsyncStorage
   - Efficient queries for complex data via Realm
   - Cached access for frequently used data

3. **Security**: Different security levels for different data sensitivity
   - Highest security for credentials and payment data
   - Standard security for regular application data

4. **Maintainability**: Clean separation of concerns with specialized APIs for each storage type

**Key Models:**
- `Person`: User profile and authentication data
- `Card`: Digital loyalty cards with metadata
- `CardMaster`: Card templates and business rules
- `Offer`: Promotional offers and campaigns
- `Reward`: Loyalty rewards and redemption
- `Place`: Location and merchant data
- `Message`: In-app messaging and notifications

### User Experience Flow
The application provides a rich user experience through several key systems:

#### Engagement System
- **Contextual Interactions**: Smart prompts based on user context and location
- **Progressive Disclosure**: Information revealed based on user journey
- **Personalization**: Tailored content based on user preferences and behavior
- **Gamification**: Points, badges, and achievement systems

#### Notification System
- **Push Notifications**: Remote notifications via Firebase
- **Local Notifications**: Time-based and location-based alerts
- **In-App Messages**: Contextual messaging within the application
- **Badge Management**: App icon badge updates for pending items

#### Location Services
- **GPS Tracking**: Real-time location tracking for proximity features
- **Geofencing**: Location-based triggers and notifications
- **Place Detection**: Automatic merchant and venue recognition
- **Navigation Integration**: Integration with Maps applications

### Business Logic Architecture

#### Card Management
The core business logic revolves around digital loyalty card management:

- **Card Lifecycle**: Registration, activation, usage, and expiration
- **Card Sharing**: Multi-user card sharing with permission management
- **Card Categories**: Automatic categorization using machine learning
- **Card Validation**: Real-time validation of card numbers and barcodes

#### Offer and Reward System
- **Offer Discovery**: Personalized offer recommendations
- **Redemption Flow**: Secure offer and reward redemption process
- **Inventory Management**: Real-time availability tracking
- **Fraud Prevention**: Security measures for redemption validation

#### Payment Processing
- **Multiple Providers**: Support for various payment methods
- **Secure Transactions**: PCI-compliant payment processing
- **Wallet Integration**: Apple Pay and Google Pay support
- **Transaction History**: Comprehensive payment tracking

## Data Flow Architecture

### User Experience Flow

1. **App Launch**:
   - Authentication check and biometric verification
   - Data synchronization and cache warming
   - Location permission and GPS initialization
   - Engagement system activation

2. **Card Discovery**:
   - Location-based card recommendations
   - Barcode scanning for card registration
   - Manual card entry with validation
   - Card categorization and organization

3. **Offer Interaction**:
   - Contextual offer presentation
   - Offer details and terms display
   - Redemption process with validation
   - Transaction completion and confirmation

4. **Reward Management**:
   - Points accumulation tracking
   - Reward availability checking
   - Redemption process execution
   - History and analytics display

### Data Synchronization Flow

1. **Local Changes**: User actions create local data modifications
2. **Change Tracking**: Changes are queued for synchronization
3. **Background Sync**: Periodic sync uploads changes to server
4. **Conflict Resolution**: Server resolves conflicts and returns updates
5. **Local Update**: Local database is updated with server changes
6. **Event Notification**: UI components are notified of data changes

### Navigation and Screen Flow

1. **Route Request**: Navigation request with target screen and parameters
2. **Route Resolution**: Navigator resolves route to screen configuration
3. **Screen Loading**: Dynamic loading of required screen bundle
4. **Component Mounting**: Screen component is mounted with props
5. **Controller Initialization**: Screen controller is initialized and bound
6. **Data Loading**: Required data is loaded and bound to UI components

## Error Handling Architecture

The Perkd application implements a comprehensive error handling strategy that ensures robust operation and excellent user experience:

### Error Classification

The application categorizes errors into distinct types for appropriate handling:

- **Network Errors**: Connection issues, timeouts, API failures
- **Authentication Errors**: Invalid credentials, expired tokens
- **Validation Errors**: Invalid user input, format violations
- **Application Errors**: Internal logic failures
- **External Service Errors**: Third-party API failures

### Error Handling Patterns

#### Centralized Error Definitions
- Error codes and messages centralized in `src/lib/common/Errors.json`
- Consistent error format across the application with over 200 defined error types
- Localized error messages for user display
- Organized by feature domain (Account, Actions, Card, Payment, etc.)

#### Error Propagation
- Errors are wrapped and enriched as they propagate up the call stack
- Error transformation utilities in `src/lib/common/utils/core.js`
- Error context is preserved for debugging and analytics
- Standardized error format enables consistent handling

#### User-Facing Error Handling
- Network errors are handled consistently with retry options
- Authentication errors trigger re-authentication flows
- Validation errors provide clear, actionable feedback to users
- Critical errors are logged for analytics and monitoring

### Error Recovery Strategies

- **Graceful Degradation**: Application continues to function with reduced capabilities
- **Automatic Retry**: Network operations with exponential backoff
- **Offline Fallback**: Local data access when network is unavailable
- **User Notification**: Clear communication of error states and recovery options

## Security Architecture

### Authentication and Authorization
- **JWT Tokens**: Secure token-based authentication with automatic refresh
- **Biometric Authentication**: Fingerprint and face recognition support
- **Session Management**: Secure session handling with timeout management
- **Multi-factor Authentication**: Support for additional security layers

### Data Protection
- **Encryption**: Sensitive data encryption at rest using AES encryption
- **Secure Storage**: Keychain (iOS) and Keystore (Android) for sensitive data
- **Network Security**: Certificate pinning and TLS encryption
- **Privacy Compliance**: GDPR and privacy regulation compliance

### Cryptography Service

The Cryptography Service (`src/lib/common/utils/crypto.js`) is a core security component that provides:

- **Encryption/Decryption**: AES encryption for sensitive data with random IV generation
- **Hash Functions**: SHA1, SHA256, MD5 for data integrity verification
- **Token Management**: JWT token handling, encoding, and validation
- **Secure Random**: Random number generation for security operations
- **Base64 Encoding**: Secure encoding/decoding utilities
- **Digital Signatures**: HMAC-SHA256 for request signing and verification

This service supports both the Secure Storage Layer and Network Security implementations, ensuring consistent cryptographic operations across the application.

### Permission System
- Fine-grained permission management in `src/lib/common/permissions.js`
- Support for platform-specific permission models (iOS/Android)
- Permission tracking and request handling
- Event-based permission state monitoring

### Security Patterns
- **Input Validation**: Comprehensive input sanitization and validation
- **SQL Injection Prevention**: Parameterized queries and ORM protection
- **XSS Prevention**: Content sanitization and CSP implementation
- **Access Control**: Role-based access control for sensitive operations

## Internationalization Architecture

Perkd implements a robust internationalization (i18n) system to support multiple languages and locales across diverse markets:

### Architecture Components

- **Translation System**: Based on `i18n-js` and `react-native-localize`
- **Language Detection**: Automatic detection of device locale with fallback support
- **Fallback System**: Graceful fallback to supported languages
- **Resource Management**: Language resources organized by locale in `src/lib/i18n/[locale].json`

### Supported Languages

The application supports 8 languages across major markets:

- **English (en)**: Primary language and fallback
- **Simplified Chinese (zh-Hans)**: Mainland China market
- **Traditional Chinese (zh-Hant)**: Taiwan and general Traditional Chinese
- **Hong Kong Traditional Chinese (zh-Hant-HK)**: Hong Kong specific localization
- **Japanese (ja)**: Japan market
- **Korean (ko)**: South Korea market
- **Bahasa Melayu (ms)**: Malaysia market
- **Bahasa Indonesia (id)**: Indonesia market

### Implementation Details

#### Translation Access
- Localized string access via `$.L()` helper function
- Automatic formatting of dates, times, and numbers based on locale
- Special handling for double-byte languages in UI layouts
- Dynamic language switching with model data translation

#### Language Detection and Fallback
- Unicode-based language detection for user-generated content
- Intelligent fallback system using i18next language utilities
- Support for regional variants (e.g., zh-Hant-HK)
- Graceful degradation to English for unsupported languages

#### Styling Adaptations
- Text sizing adjustments based on language characteristics
- Special handling for CJK (Chinese, Japanese, Korean) languages
- Font scaling considerations for different character sets
- Layout adjustments for text expansion/contraction

#### Dynamic Content Translation
- Model-based translation system for dynamic content
- Globalized data structures with language-specific variants
- Automatic translation of CardMaster, Offer, Reward, Message, and Place data
- Efficient language change operations for large datasets

### Performance Considerations

- Translation resources are loaded on demand
- Caching of translated strings for performance
- Optimized language change operations (951 masters processed in ~494ms)
- Memory-efficient storage of multilingual content

## Performance Optimization

The Perkd application employs comprehensive performance optimization strategies across multiple dimensions:

### Launch Optimization

The application implements a sophisticated staged initialization process in `src/perkd.js`:

- **Performance-Critical Components Loaded First**: Essential screens and navigation loaded immediately
- **Non-Essential Operations Deferred**: Background tasks scheduled after core functionality
- **Device Capability Detection**: Performance parameters adjusted based on device capabilities
- **Staged Service Initialization**: Services started in priority order to minimize blocking

**Launch Sequence:**
1. Core application restoration and state initialization
2. Main screen loading and navigation setup
3. Essential services (Events, Actions, UrlScheme, Sync)
4. Background services (Reminders, Badges, RemoteNotify, Engage)
5. Secondary operations (Indexing, Context, housekeeping)

### Memory Management

- **Memory Footprint Monitoring**: Active monitoring and optimization of memory usage
- **Image Scaling**: Dynamic image scaling based on device capabilities
- **Resource Cleanup**: Automatic cleanup during navigation transitions
- **Background Task Scheduling**: Prevents UI blocking through intelligent task scheduling
- **Lazy Loading**: Components and screens loaded on demand
- **Bundle Splitting**: Code splitting for reduced initial load time

### Data Access Optimization

- **Multi-Layered Caching Strategy**: Memory, disk, and network caching layers
- **Prefetching**: Likely-needed data loaded proactively
- **Efficient Database Queries**: Optimized Realm queries using indexes
- **Throttled Synchronization**: Reduces resource usage through intelligent sync timing
- **Background Fetch**: Data synchronization during background execution

### UI Performance

- **Component Memoization**: Expensive renders cached and reused
- **Virtualized Lists**: Efficient rendering of large data sets
- **Image Optimization**: WebP format support and lazy loading with react-native-fast-image
- **Animation Optimization**: Hardware-accelerated animations using native drivers
- **Responsive Scaling**: Dynamic scaling utilities for different screen sizes

### Background Processing

- **Background Fetch Configuration**: Intelligent background synchronization
- **Intensive Operation Offloading**: CPU-intensive tasks moved to background threads
- **Smart Request Batching**: Network requests combined for efficiency
- **Device-Aware Concurrency**: Concurrency limits adjusted based on device capabilities
- **Housekeeping Operations**: Automated cleanup and maintenance tasks

### Network Optimization

- **Request Batching**: Multiple API requests combined for efficiency
- **Caching Strategy**: Multi-level caching (memory, disk, network)
- **Offline Queue**: Queued operations for offline scenarios
- **Data Compression**: Gzip compression for reduced bandwidth usage
- **Connection Management**: Intelligent connection pooling and reuse

### Styling Performance

The styling system (`src/styles/styles.js`) implements performance-optimized responsive design:

- **Calculation Caching**: Scaling calculations cached to avoid repeated computation
- **Responsive Design**: Scaling utilities adapt to different screen sizes efficiently
- **Font Scale Optimization**: Intelligent font scaling with device-specific limits
- **Language-Aware Sizing**: Optimized text sizing for different languages

## Integration Architecture

### External Services
- **Firebase Services**: Push notifications, analytics, and crash reporting
- **Payment Providers**: Stripe, Apple Pay, Google Pay, and regional providers
- **Maps Integration**: Google Maps and Apple Maps for location services
- **Social Integration**: Facebook SDK for social features
- **Analytics**: Custom analytics and user behavior tracking

### Backend Communication
- **REST APIs**: RESTful service communication with standardized endpoints
- **WebSocket**: Real-time communication for live features
- **File Upload**: Secure file and image upload with progress tracking
- **Error Handling**: Comprehensive error handling and retry mechanisms

## Feature-Based Organization

The codebase is organized by feature domains for better maintainability:

### Core Features
- **Card**: Digital loyalty card management and lifecycle
- **Discover**: Card discovery and recommendation engine
- **Shop**: E-commerce functionality and product catalog
- **Profile**: User profile management and preferences
- **Preference**: Application settings and user preferences

### Supporting Features
- **Bag**: Shopping cart and checkout functionality
- **Offer**: Promotional offers and campaign management
- **Reward**: Loyalty rewards and redemption system
- **Message**: In-app messaging and communication
- **Payment**: Payment processing and transaction management
- **Place**: Location and merchant management
- **Scan**: Barcode and QR code scanning functionality

### System Features
- **Reception**: User onboarding and authentication flows
- **Notify**: Notification management and delivery
- **Engage**: User engagement and interaction system
- **Share**: Social sharing and referral functionality
- **Ticket**: Support ticket and help system

## Development Patterns

### Code Organization
- **Feature Modules**: Self-contained feature modules with clear boundaries
- **Shared Components**: Reusable UI components across features
- **Service Layer**: Abstracted service layer for external integrations
- **Utility Functions**: Common utilities and helper functions

### Testing Strategy
- **Unit Testing**: Jest-based unit testing for business logic
- **Integration Testing**: Component integration testing
- **E2E Testing**: Detox-based end-to-end testing
- **Performance Testing**: Performance monitoring and optimization

### Deployment Architecture
- **Code Push**: Over-the-air updates for JavaScript changes
- **App Store Distribution**: Native app distribution through app stores
- **Environment Management**: Multiple environment support (dev, staging, production)
- **CI/CD Pipeline**: Automated build and deployment processes

## Implementation Patterns

The Perkd application follows established architectural patterns that promote maintainability, testability, and scalability:

### Component Patterns

- **Presentation/Container Pattern**: Components are structured following clear separation between presentation and logic
- **Single Responsibility**: Each component focuses on a specific responsibility
- **Props-Based Communication**: Data passing and callback functions handled through props
- **Stateless Components**: Presentation components remain stateless where possible
- **Reusability**: Components designed for reuse across different features

### Controller Patterns

- **Singleton Pattern**: Controllers use singleton pattern with exposed methods
- **Centralized Business Logic**: Business logic is centralized in controllers rather than scattered across components
- **State Management**: Controllers manage their own state and communicate with services
- **Event Integration**: Controllers both consume events and use the Actions layer
- **Service Coordination**: Controllers orchestrate interactions between different services

### Service Patterns

- **Domain-Specific Services**: Services implement specific functionality domains
- **Platform Abstractions**: Common abstractions for platform capabilities
- **Stateless Design**: Services are stateless where possible for better testability
- **Interface Consistency**: Consistent interfaces across different service types

### Data Flow Patterns

The application follows a unidirectional data flow pattern:

1. **Data Services** (in `src/lib/common/services`) fetch and process data
2. **Controllers** access services, transform data, and manage state
3. **Components** receive data from controllers via props
4. **User Interactions** trigger callbacks defined in controllers
5. **State Updates** are managed by controllers and propagated to components

This pattern provides predictable data flow that's easier to debug and maintain.

## Developer Guidelines

When extending or modifying the Perkd application, developers should follow these established patterns:

### Code Organization

- **Follow Directory Structure**: Place new components in the appropriate feature directory
- **Shared Utilities**: Place shared utilities in the `src/lib/common` library
- **Feature Focus**: Keep controllers focused on specific feature domains
- **Separation of Concerns**: Maintain clear boundaries between layers

### Navigation

- **Centralized Navigation**: Use the navigation system in `src/lib/navigator.js`
- **Established Patterns**: Follow established patterns for screen transitions
- **Proper Registration**: Register new screens properly in the navigation system
- **Deep Link Support**: Consider deep linking requirements for new screens

### Styling

- **Scaling Utilities**: Use the scaling utilities for responsive designs
- **Established Patterns**: Follow the established style patterns in `src/styles/styles.js`
- **Internationalization**: Consider internationalization when styling text
- **Performance**: Use cached scaling calculations for performance

### State Management

- **Minimal Component State**: Keep component state minimal
- **Controller-Based State**: Use controllers for complex state management
- **Persistence Considerations**: Consider state persistence needs when designing features
- **Event-Driven Updates**: Use the event system for cross-component communication

### Testing

- **Controller Independence**: Test controllers independently of UI components
- **Mock Data**: Test UI components with mock data and callbacks
- **Integration Mocks**: Test integration points with appropriate mocks
- **Performance Testing**: Include performance considerations in testing strategy

### Security

- **Input Validation**: Implement comprehensive input validation
- **Secure Storage**: Use appropriate storage layers for different data sensitivity levels
- **Error Handling**: Follow established error handling patterns
- **Permission Management**: Properly handle platform permissions

This architecture provides a robust, scalable foundation for a complex mobile loyalty application while maintaining flexibility for future enhancements and business requirements.

## Architecture Diagrams

### Layered Architecture Overview

The following diagram illustrates the high-level architectural layers and their relationships:

```mermaid
graph TB
    subgraph "📱 Presentation Layer"
        A[Containers/Screens<br/>src/containers/]
        B[Components<br/>src/components/]
        C[Styles & Themes<br/>src/styles/]
    end

    subgraph "🎯 Business Logic Layer"
        D[Controllers<br/>src/controllers/]
        E[Actions<br/>src/lib/common/actions/]
        F[Services<br/>src/lib/common/services/]
    end

    subgraph "💾 Data Layer"
        G[Models<br/>src/lib/models/]
        H[Sync Engine<br/>src/lib/common/sync.js]
        I[Persistence<br/>src/lib/common/persist.js]
        J[Realm Database<br/>Local Storage]
    end

    subgraph "🔧 Infrastructure Layer"
        K[Registry<br/>src/lib/common/registry.js]
        L[Navigator<br/>src/lib/navigator.js]
        M[Common Utilities<br/>src/lib/common/]
        N[Event System<br/>Global Events]
    end

    subgraph "🌐 External Layer"
        O[Firebase<br/>Push Notifications]
        P[Payment Providers<br/>Stripe, Apple Pay, etc.]
        Q[Backend APIs<br/>REST/WebSocket]
        R[Device Services<br/>Camera, Location, etc.]
    end

    %% Presentation Layer Connections
    A --> D
    B --> A
    C --> A
    C --> B

    %% Business Logic Layer Connections
    D --> E
    D --> F
    E --> G
    F --> Q

    %% Data Layer Connections
    G --> H
    G --> I
    H --> J
    I --> J
    H --> Q

    %% Infrastructure Layer Connections
    K --> G
    L --> A
    M --> D
    M --> E
    M --> F
    N --> A
    N --> D

    %% External Layer Connections
    F --> O
    F --> P
    F --> R

    %% Styling with darker backgrounds and white text
    classDef presentation fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef business fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef data fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef infrastructure fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff
    classDef external fill:#d69e2e,stroke:#ed8936,stroke-width:2px,color:#ffffff

    class A,B,C presentation
    class D,E,F business
    class G,H,I,J data
    class K,L,M,N infrastructure
    class O,P,Q,R external
```

### Logical Feature Architecture

This diagram shows how functional components are organized into logical modules:

```mermaid
graph TB
    subgraph "🏠 Core Application"
        APP[Perkd Main App<br/>src/perkd.js]
        NAV[Navigation System<br/>src/lib/navigator.js]
        REG[Registry & Models<br/>src/lib/common/registry.js]
    end

    subgraph "💳 Card Management"
        CARD[Card Module<br/>Digital Loyalty Cards]
        CARDLIST[Card List<br/>Card Discovery & Organization]
        CARDMASTER[Card Master<br/>Card Templates & Rules]
        CARDSCAN[Card Scanning<br/>Barcode & QR Recognition]
    end

    subgraph "🎁 Offers & Rewards"
        OFFER[Offer Module<br/>Promotional Campaigns]
        REWARD[Reward Module<br/>Loyalty Points & Redemption]
        ENGAGE[Engagement System<br/>Contextual Interactions]
        NOTIFY[Notification System<br/>Push & Local Alerts]
    end

    subgraph "🛒 Commerce"
        SHOP[Shop Module<br/>E-commerce Platform]
        BAG[Shopping Bag<br/>Cart & Checkout]
        PAY[Payment System<br/>Multiple Payment Methods]
        ORDER[Order Management<br/>Transaction Processing]
    end

    subgraph "📍 Location & Discovery"
        PLACE[Place Module<br/>Merchant & Venue Data]
        DISCOVER[Discovery Engine<br/>Personalized Recommendations]
        LOCATION[Location Services<br/>GPS & Geofencing]
        MAP[Map Integration<br/>Navigation & Directions]
    end

    subgraph "👤 User Management"
        PROFILE[User Profile<br/>Personal Information]
        PREF[Preferences<br/>Settings & Configuration]
        AUTH[Authentication<br/>Security & Biometrics]
        RECEPTION[Onboarding<br/>Registration Flow]
    end

    %% Core connections
    APP --> NAV
    APP --> REG
    NAV --> CARD
    NAV --> SHOP
    NAV --> PROFILE
    NAV --> DISCOVER
    NAV --> PREF

    %% Card Management connections
    CARD --> CARDLIST
    CARD --> CARDMASTER
    CARD --> CARDSCAN
    CARDLIST --> DISCOVER

    %% Offers & Rewards connections
    OFFER --> ENGAGE
    REWARD --> ENGAGE
    ENGAGE --> NOTIFY
    CARD --> OFFER
    CARD --> REWARD

    %% Commerce connections
    SHOP --> BAG
    BAG --> PAY
    PAY --> ORDER
    OFFER --> SHOP

    %% Location & Discovery connections
    PLACE --> LOCATION
    DISCOVER --> PLACE
    LOCATION --> MAP
    CARD --> PLACE

    %% User Management connections
    PROFILE --> AUTH
    PROFILE --> PREF
    AUTH --> RECEPTION

    %% Styling with darker backgrounds and white text
    classDef core fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef card fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef offer fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef commerce fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef location fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff
    classDef user fill:#d69e2e,stroke:#ed8936,stroke-width:2px,color:#ffffff

    class APP,NAV,REG core
    class CARD,CARDLIST,CARDMASTER,CARDSCAN card
    class OFFER,REWARD,ENGAGE,NOTIFY offer
    class SHOP,BAG,PAY,ORDER commerce
    class PLACE,DISCOVER,LOCATION,MAP location
    class PROFILE,PREF,AUTH,RECEPTION user
```

### User Experience & Data Flow

This diagram illustrates the complete user journey and data flow through the application:

```mermaid
graph TB
    subgraph "👤 User Journey Entry Points"
        USER[User]
        LAUNCH[App Launch]
        DEEPLINK[Deep Link]
        NOTIFICATION[Push Notification]
        SCAN[QR/Barcode Scan]
    end

    subgraph "🎯 User Experience Flow"
        AUTH[Authentication<br/>Biometric/PIN]
        ONBOARD[Onboarding<br/>First-time Setup]
        DASHBOARD[Main Dashboard<br/>Tab Navigation]
        CONTEXT[Context Engine<br/>Personalization]
    end

    subgraph "💳 Card Experience"
        CARDDISC[Card Discovery<br/>Location-based]
        CARDREG[Card Registration<br/>Scan/Manual Entry]
        CARDVIEW[Card Viewing<br/>Digital Wallet]
        CARDUSE[Card Usage<br/>Show/Redeem]
    end

    subgraph "🎁 Engagement Experience"
        OFFERDISC[Offer Discovery<br/>Personalized]
        OFFERDET[Offer Details<br/>Terms & Conditions]
        OFFERRED[Offer Redemption<br/>In-store/Online]
        REWARDTRACK[Reward Tracking<br/>Points & Progress]
    end

    subgraph "🛒 Shopping Experience"
        BROWSE[Product Browse<br/>Catalog Navigation]
        CART[Shopping Cart<br/>Item Management]
        CHECKOUT[Checkout Flow<br/>Payment Processing]
        ORDERTRACK[Order Tracking<br/>Fulfillment Status]
    end

    subgraph "💾 Data Flow Layer"
        LOCALDB[(Local Database<br/>Realm)]
        SYNC[Sync Engine<br/>Bidirectional]
        CACHE[Cache Layer<br/>Performance]
        BACKEND[(Backend APIs<br/>REST/WebSocket)]
    end

    %% User Entry Flow
    USER --> LAUNCH
    USER --> DEEPLINK
    USER --> NOTIFICATION
    USER --> SCAN

    %% Authentication & Onboarding Flow
    LAUNCH --> AUTH
    AUTH --> ONBOARD
    AUTH --> DASHBOARD
    ONBOARD --> DASHBOARD

    %% Main Navigation Flow
    DASHBOARD --> CARDVIEW
    DASHBOARD --> BROWSE
    DASHBOARD --> OFFERDISC

    %% Card Experience Flow
    CARDDISC --> CARDREG
    CARDREG --> CARDVIEW
    CARDVIEW --> CARDUSE
    SCAN --> CARDREG

    %% Engagement Flow
    OFFERDISC --> OFFERDET
    OFFERDET --> OFFERRED
    OFFERRED --> REWARDTRACK
    CARDUSE --> REWARDTRACK

    %% Shopping Flow
    BROWSE --> CART
    CART --> CHECKOUT
    CHECKOUT --> ORDERTRACK
    OFFERRED --> CHECKOUT

    %% Context & Personalization
    CONTEXT --> CARDDISC
    CONTEXT --> OFFERDISC

    %% Data Flow Connections
    CARDVIEW --> LOCALDB
    OFFERDISC --> LOCALDB
    BROWSE --> LOCALDB

    LOCALDB --> SYNC
    SYNC --> BACKEND
    SYNC --> CACHE
    CACHE --> CARDVIEW
    CACHE --> OFFERDISC

    %% Deep Link Handling
    DEEPLINK --> CARDVIEW
    DEEPLINK --> OFFERDET
    DEEPLINK --> BROWSE

    %% Notification Actions
    NOTIFICATION --> OFFERDET
    NOTIFICATION --> CARDVIEW
    NOTIFICATION --> ORDERTRACK

    %% Styling with darker backgrounds and white text
    classDef entry fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef ux fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef card fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef engagement fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef shopping fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff
    classDef data fill:#4a5568,stroke:#718096,stroke-width:2px,color:#ffffff

    class USER,LAUNCH,DEEPLINK,NOTIFICATION,SCAN entry
    class AUTH,ONBOARD,DASHBOARD,CONTEXT ux
    class CARDDISC,CARDREG,CARDVIEW,CARDUSE card
    class OFFERDISC,OFFERDET,OFFERRED,REWARDTRACK engagement
    class BROWSE,CART,CHECKOUT,ORDERTRACK shopping
    class LOCALDB,SYNC,CACHE,BACKEND data
```

## Summary

The Perkd application represents a sophisticated mobile loyalty platform built on React Native with a well-architected, modular design. The architecture emphasizes:

- **Separation of Concerns**: Clear layered architecture with distinct responsibilities
- **Scalability**: Modular feature-based organization supporting growth
- **Performance**: Multi-level caching and optimization strategies
- **User Experience**: Contextual, personalized interactions with smooth data flow
- **Security**: Comprehensive security measures for sensitive data and transactions
- **Maintainability**: Clean code organization and consistent patterns

This architecture provides a solid foundation for a complex mobile application while maintaining the flexibility needed for future enhancements and business requirements.
