# Engage System Architecture

## Table of Contents
- [Overview](#overview)
- [Architecture Overview](#architecture-overview)
- [Core Components](#core-components)
- [Engagement Types](#engagement-types)
- [Business Logic and Rules](#business-logic-and-rules)
- [Integration Points](#integration-points)
- [Performance Considerations](#performance-considerations)
- [User Experience Flows](#user-experience-flows)
- [Technical Implementation](#technical-implementation)
- [Advanced Features](#advanced-features)
- [Development Guidelines](#development-guidelines)
- [Troubleshooting](#troubleshooting)

## Overview

The Engage system is a sophisticated user engagement framework that proactively presents contextual interactions to users based on their activity, location, and available content. It serves as the primary mechanism for delivering personalized notifications, promotional content, and interactive experiences within the Perkd application. The system coordinates across multiple data sources to present timely, relevant engagements while maintaining a smooth user experience through careful timing and conflict management.

The Engage system operates as a centralized orchestrator that processes engagement candidates from various sources (Cards, Offers, Rewards, Actions), applies business rules and eligibility filters, and presents them through appropriate UI components. It ensures users are informed about important opportunities without disrupting their current workflow through sophisticated Do Not Disturb (DND) integration and semaphore-based concurrency control.

The engagement process follows a defined sequence to ensure proper timing and user experience:

1. **Detection**: Subsystems (<PERSON>, Offer, Reward, Action) identify content requiring user attention by implementing the `nextEngage(cardId)` method
2. **Queuing**: Engagements are prioritized in a queue based on timing and importance
3. **Presentation**: The appropriate engagement UI is presented to the user (pBox, dialog, applet, ticket)
4. **Interaction**: User interacts with the engagement through defined actions
5. **Resolution**: Engagement is marked as handled, with appropriate state updates and cleanup

## Architecture Overview

The Engage system follows a service-oriented architecture with clear separation between data collection, business logic processing, and presentation layers:

### High-Level Data Flow

The engagement system processes data through a structured pipeline from collection to presentation:

```mermaid
graph TD
    ES[Engagement System] --> DC[Data Collection]
    ES --> F[Filtering & Rules]
    ES --> P[Presentation]

    DC --> D[Data Sources]
    D --> CD[Card Data]
    D --> OD[Offer Data]
    D --> RD[Reward Data]
    D --> AD[Action Data]

    F --> R[Rules Engine]
    R --> T[Timing Rules]
    R --> E[Eligibility Rules]
    R --> N[Notification State]

    P --> PS[Presentation Styles]
    PS --> PB[pBox - Visual Overlays]
    PS --> AP[Applet - Interactive Content]
    PS --> TK[Ticket - Carousel Display]
    PS --> DG[Dialog - User Prompts]

    %% Styling with darker backgrounds and white text
    classDef system fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef data fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef rules fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef presentation fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff

    class ES system
    class DC,D,CD,OD,RD,AD data
    class F,R,T,E,N rules
    class P,PS,PB,AP,TK,DG presentation
```

### Detailed System Architecture

```mermaid
graph TB
    subgraph "🎯 Engagement Orchestration"
        ENG["Engage Service<br>src/lib/common/engage.js"]
        SEM["Semaphore System<br>Concurrency Control"]
        DND["DND Integration<br>Conflict Prevention"]
        QUEUE["Engagement Queue<br>Priority Management"]
    end

    subgraph "📊 Data Sources"
        CARD["Card Model<br>nextEngage()"]
        OFFER["Offer Model<br>nextEngage()"]
        REWARD["Reward Model<br>nextEngage()"]
        ACTION["Action Model<br>nextEngage()"]
    end

    subgraph "🎨 Presentation Layer"
        PBOX["PBox Component<br>Visual Overlays"]
        APPLET["Applet Component<br>Interactive Content"]
        TICKET["Ticket Component<br>Carousel Display"]
        DIALOG["Dialog Component<br>User Prompts"]
    end

    subgraph "🔧 Supporting Systems"
        OVERLAY["Overlay System<br>UI Management"]
        NOTIFY["Notification Tracking<br>State Management"]
        EVENT["Event System<br>Communication"]
        NAV["Navigation System<br>Route Handling"]
    end

    %% Data Flow
    CARD --> ENG
    OFFER --> ENG
    REWARD --> ENG
    ACTION --> ENG

    %% Orchestration Flow
    ENG --> SEM
    ENG --> DND
    ENG --> QUEUE
    ENG --> NOTIFY

    %% Presentation Flow
    ENG --> PBOX
    ENG --> APPLET
    ENG --> TICKET
    ENG --> DIALOG

    %% Supporting Systems
    PBOX --> OVERLAY
    APPLET --> OVERLAY
    TICKET --> OVERLAY
    DIALOG --> OVERLAY
    ENG --> EVENT
    PBOX --> NAV

    %% Styling with darker backgrounds and white text
    classDef orchestration fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef data fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef presentation fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef supporting fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff

    class ENG,SEM,DND,QUEUE orchestration
    class CARD,OFFER,REWARD,ACTION data
    class PBOX,APPLET,TICKET,DIALOG presentation
    class OVERLAY,NOTIFY,EVENT,NAV supporting
```

## Core Components

### Engagement Service

The central `Engage` service (`src/lib/common/engage.js`) coordinates all engagement activities:

**State Management:**
- `Engage.active`: Controls overall system activation
- `Engage.engaging`: Tracks current engagement state
- `Engage.queued`: Array of pending engagements during locked periods
- `Engage.attempted`: Array of previously attempted engagement IDs
- `Engage.componentIds`: Maps engagement IDs to UI component instances
- `Engage.PENDING`: Tracks pending engagement promises for resolution

**Core Methods:**
- `Engage.next(cardId, includeCards)`: Retrieves and processes next engagement
- `Engage.do(action, data, saved)`: Executes specific engagement actions
- `Engage.pBox(data)`: Displays visual overlay engagements
- `Engage.applet(data)`: Presents interactive applet content
- `Engage.ticket(data)`: Shows ticket carousel interfaces
- `Engage.dialog(data)`: Displays user prompt dialogs

### Concurrency Control

The system uses multiple mechanisms to prevent UI conflicts and ensure smooth user experience:

**Semaphore System:**
- `Engage.semaphore`: Prevents multiple simultaneous engagements
- Automatic locking/releasing during engagement lifecycle
- Queue management for pending engagements during locked periods

**DND Integration:**
- `$.DND.lock(cardId, exclusive, context)`: Prevents interruptions during critical operations
- `$.DND.release()`: Releases locks when engagements complete
- Context tracking for debugging and state management
- Automatic cleanup on engagement completion or failure

### Notification State Tracking

The system prevents redundant notifications through sophisticated state tracking:

**Notified Class Implementation:**
- TTL-based tracking with 5-minute expiration windows
- Engagement registry mapping IDs to notification states
- Model-specific notification checking logic
- Automatic cleanup of expired notification states

**Model Integration:**
- Each model implements `notified(timestamp)` methods
- Automatic notification state updates on engagement completion
- Cross-model notification coordination for related content

## Engagement Types

### PBox Engagements

PBox (Promotion Box) engagements display visually rich overlays for highlighting new content:

**Card PBox:**
- Notifies users of new cards requiring acceptance/decline
- Supports registration flow integration with animated transitions
- Handles card master discovery and registration eligibility
- Provides accept/decline/view action buttons

**Offer PBox:**
- Presents new promotional offers and deals
- Displays offer imagery with scaling and animation support
- Integrates with offer redemption and navigation flows
- Supports batch offer presentation for related content

**Reward PBox:**
- Highlights earned rewards and loyalty achievements
- Shows stamp amounts and progress visualization
- Integrates with reward redemption and tracking systems
- Provides view/acknowledge action options

**Implementation Details:**
- Component: `src/containers/Engage/PBox.js`
- Shared element animations for smooth transitions
- Automatic image downloading and caching
- Responsive scaling for different screen sizes
- Language-aware text sizing and layout

### Applet Engagements

Applet engagements present interactive web-based content within native containers:

**Features:**
- WebView-based interactive content rendering
- JavaScript bridge for native app communication
- Resource preloading and caching for performance
- Secure content isolation with controlled access

**Implementation:**
- Component: `src/containers/Engage/Applet.js`
- Bridge: `src/common/applet/Bridge.js`
- Supports both local HTML content and remote URI loading
- Event-driven communication between web content and native app
- Automatic cleanup and resource management

### Ticket Engagements

Ticket engagements use carousel-based presentation for multiple related items:

**Features:**
- Carousel interface for multiple ticket presentation
- Stack and parallax display modes based on content type
- Auto-check-in functionality for eligible tickets
- Focus-based rendering optimization for performance

**Implementation:**
- Component: `src/containers/Engage/Ticket.js`
- Carousel component with gesture support
- Automatic ticket filtering and grouping
- Real-time ticket state updates and synchronization

### Dialog Engagements

Dialog engagements provide structured prompts requiring user decisions:

**Types:**
- Confirmation dialogs for action verification
- Options dialogs for multiple choice selections
- Rate app dialogs for app store rating requests
- Custom dialogs with configurable buttons and actions

**Features:**
- Platform-native dialog styling and behavior
- Configurable button styles (default, cancel, destructive)
- Automatic DND locking during dialog presentation
- Promise-based resolution for action handling

## Business Logic and Rules

### Engagement Eligibility Framework

The system implements a sophisticated multi-layered eligibility framework designed to maximize user engagement while preventing notification fatigue and ensuring business value delivery.

#### Strategic Priority Hierarchy

The engagement system employs a carefully designed priority hierarchy that reflects business priorities and user experience optimization:

**Queue-First Processing:** The system prioritizes manually queued engagements to ensure critical business communications reach users immediately. This supports urgent promotional campaigns, system announcements, and time-sensitive opportunities that require immediate user attention.

**Card-Centric Engagement Strategy:** When no queued engagements exist, the system prioritizes card-related engagements because cards represent the foundational relationship between users and merchants. New card registrations, acceptance requirements, and card lifecycle events take precedence as they establish the basis for all future engagement opportunities.

**Reward-Before-Offer Logic:** Rewards are prioritized over offers because they represent earned value that users have already accumulated. This business rule ensures users are immediately notified of achievements and available redemptions, which drives satisfaction and loyalty program engagement.

**Offer Engagement Timing:** Offers are processed after rewards to ensure promotional content doesn't overshadow earned benefits. This sequencing maximizes the perceived value of the loyalty program while still delivering timely promotional opportunities.

**Action-Based Recovery:** Actions serve as the engagement system's recovery mechanism, handling postponed engagements and ensuring no business-critical communications are permanently lost due to temporary system states.

#### Sophisticated Eligibility Filtering

**Card Lifecycle State Management:** The system implements complex business rules around card states that directly impact engagement eligibility. Cards must be in active states with proper registration completion before related engagements can be triggered. This prevents user confusion and ensures engagements are only shown when users can actually act upon them.

**Temporal Business Rules:** Engagement timing follows sophisticated business logic that considers not just technical availability but user behavior patterns. The system prevents background engagements to avoid notification spam, implements cooldown periods based on content type and user response patterns, and coordinates timing across multiple engagement sources to prevent overwhelming users.

**Cross-Model Coordination Rules:** The system implements sophisticated coordination between different content types to ensure coherent user experiences. For example, when multiple offers exist for the same card, they are batched and presented together rather than as separate interruptions. Similarly, reward notifications are coordinated with related offer presentations to create cohesive value propositions.

#### Performance-Driven Business Rules

**Attempted Engagement Tracking:** The system maintains sophisticated tracking of previously attempted engagements to prevent infinite retry loops while ensuring legitimate opportunities aren't lost. This business rule balances persistence in delivering value with respect for user preferences and system stability.

**DND Queue Integration:** The Do Not Disturb system integration reflects a business understanding that user experience quality directly impacts engagement effectiveness. Rather than simply blocking engagements, the system intelligently queues them for appropriate timing, ensuring business value is preserved while respecting user context.

**Resource Optimization Logic:** The system implements business rules that optimize resource usage based on engagement value. High-value engagements receive priority resource allocation, while lower-priority content is processed efficiently to maintain system responsiveness for critical business communications.

### Advanced Business Logic Patterns

#### Member-Only Content Strategy

The system implements sophisticated business logic for member-only content that balances exclusivity with user acquisition:

**Membership Validation Logic:** When users attempt to access member-only content without proper card registration, the system doesn't simply block access. Instead, it intelligently presents card registration opportunities, converting potential frustration into acquisition opportunities. This business rule recognizes that exclusive content creates desire, and the system leverages this psychological principle to drive card registrations.

**Discoverable Card Master Logic:** The system evaluates card master discoverability to determine whether to show registration prompts or redirect to discovery interfaces. This business rule ensures users are only presented with actionable opportunities while maintaining the exclusivity value of member-only content.

**Registration Eligibility Assessment:** The system implements complex business rules around registration eligibility that consider user qualification status, existing card relationships, and business policies. This prevents user frustration from impossible registration attempts while maximizing conversion opportunities for qualified users.

#### Card Lifecycle Business Intelligence

**Active Card Validation:** The system implements sophisticated business rules that define "active" cards based on multiple criteria including registration status, management type, and temporal validity. This business logic ensures engagements are only triggered when users can meaningfully interact with the content, preventing confusion and maintaining trust in the system.

**Registration Flow Optimization:** The business logic recognizes that card registration represents a critical conversion moment and implements special handling to optimize this experience. The system coordinates multiple touchpoints, manages complex state transitions, and ensures seamless user experiences during registration processes.

**Managed vs. Non-Managed Card Logic:** The system implements different business rules for managed and non-managed cards, recognizing that these represent different business relationships and user expectations. Managed cards receive more sophisticated lifecycle management, while non-managed cards follow simpler patterns that respect their physical nature.

#### Error Recovery as Business Strategy

**Silent Discard Strategy:** When content has already been shown to users, the system silently discards duplicate attempts rather than showing error messages. This business rule recognizes that user experience quality is more important than technical completeness, and prevents user annoyance from system-level retry logic.

**User Feedback for Consumed Actions:** When users attempt to use already-consumed actions, the system provides clear feedback rather than silent failure. This business rule maintains user trust by providing transparency about why actions cannot be completed.

**Intelligent Postponement Logic:** Rather than losing engagement opportunities when the system is busy, the business logic automatically postpones engagements to the Action model for later retry. This ensures business value is preserved while respecting system capacity constraints.

**Recursive Engagement Triggering:** The system implements business logic that automatically triggers up to three additional engagements after completing one, recognizing that user attention represents a valuable business opportunity that should be maximized while users are actively engaged with the application.

## Integration Points

### Strategic Integration Architecture

#### Card System Business Integration

The engagement system's integration with the card management system reflects sophisticated business logic designed to optimize the customer acquisition and retention funnel:

**Card Discovery Business Strategy:** The system implements intelligent card discovery that goes beyond simple notification to create compelling acquisition experiences. When new cards become available, the engagement system evaluates user qualification status, existing card relationships, and business policies to determine the optimal presentation strategy. This business logic ensures that card discovery feels like valuable opportunity rather than promotional spam.

**Registration Flow Optimization:** The system coordinates complex registration flows that balance user experience with business requirements. This includes managing multi-step registration processes, handling qualification verification, and coordinating with external systems while maintaining engagement momentum. The business logic recognizes that registration represents a critical conversion moment that requires careful orchestration.

**Card Lifecycle Value Management:** The system implements sophisticated business rules around card lifecycle events that maximize customer lifetime value. This includes timing acceptance prompts for optimal conversion, coordinating decline handling to preserve relationships, and managing card state transitions to maintain user engagement throughout the customer journey.

#### Promotional Content Business Logic

**Offer Orchestration Strategy:** The system implements intelligent offer presentation that considers user context, offer value, and business objectives. Rather than simply showing available offers, the business logic evaluates offer relevance, user qualification, and optimal timing to maximize conversion probability while maintaining user experience quality.

**Batch Presentation Logic:** When multiple related offers exist, the system implements sophisticated batching logic that creates coherent value propositions rather than fragmented communications. This business rule recognizes that users respond better to comprehensive offers that tell complete stories rather than disconnected promotional fragments.

**Reward Recognition Strategy:** The system prioritizes reward notifications because they represent earned value that drives loyalty program engagement. The business logic ensures that users are immediately aware of achievements and available redemptions, understanding that reward recognition creates positive emotional associations that drive future engagement.

**Cross-Content Coordination:** The system implements business rules that coordinate between offers and rewards to create compelling combined value propositions. This includes presenting related offers alongside earned rewards, coordinating expiration timing, and sequencing content to build engagement momentum rather than creating decision paralysis.

### Notification Intelligence and State Management

#### Sophisticated Notification State Tracking

The system implements advanced notification state management that serves critical business functions beyond simple duplicate prevention:

**Time-Based Notification Windows:** The system uses 5-minute notification windows not as arbitrary technical constraints but as carefully calibrated business rules that balance engagement persistence with user experience quality. This timing reflects user behavior research showing that shorter windows miss legitimate retry scenarios while longer windows create notification fatigue that reduces overall engagement effectiveness.

**Cross-Model Notification Coordination:** The system implements sophisticated notification state tracking that coordinates across different content types to prevent overwhelming users while ensuring important opportunities aren't lost. This business logic recognizes that users have limited attention capacity and implements intelligent coordination to maximize the value of each engagement opportunity.

**Engagement Registry Intelligence:** The system maintains detailed engagement registries that track not just what was shown but when, how, and with what result. This business intelligence enables sophisticated optimization of future engagement strategies, including timing adjustments, content personalization, and channel optimization based on historical performance data.

#### Business-Driven Cooldown Mechanisms

**Content-Type Specific Cooldowns:** The system implements different cooldown periods for different content types based on their business value and user impact. High-value opportunities like reward redemptions receive shorter cooldowns to ensure users don't miss time-sensitive benefits, while promotional offers receive longer cooldowns to prevent promotional fatigue.

**User Response Pattern Learning:** The system analyzes user response patterns to dynamically adjust cooldown periods, implementing business logic that recognizes individual user preferences and optimizes engagement frequency accordingly. This personalization ensures that high-engagement users receive more frequent opportunities while respecting the preferences of users who prefer less frequent communications.

**Context-Aware Cooldown Adjustment:** The system implements intelligent cooldown adjustments based on user context, location, and activity patterns. This business logic recognizes that user receptivity to engagements varies based on circumstances and adjusts timing accordingly to maximize conversion probability while maintaining relationship quality.

### Edge Case Management and Business Resilience

#### Sophisticated Error Classification and Business Impact

The system implements a sophisticated error classification framework that treats different error types according to their business impact and user experience implications:

**Silent Business Logic Failures:** When content has already been shown to users (alreadyNotified errors), the system implements silent discard logic that prioritizes user experience over technical completeness. This business rule recognizes that duplicate notifications create negative user experiences that can damage brand relationships, making silent handling the optimal business strategy even if it means some technical events go unlogged.

**User-Facing Business Logic Errors:** When users attempt to use consumed actions (actionUsed errors), the system provides clear feedback rather than silent failure. This business rule maintains user trust by providing transparency about system state while preventing user frustration from unexplained failures. The system recognizes that user understanding of system limitations is preferable to mysterious failures.

**System Capacity Management:** When the system is locked due to DND or semaphore constraints, the business logic automatically postpones engagements rather than losing them entirely. This approach ensures that business value is preserved even during high-load periods, recognizing that delayed engagement delivery is preferable to lost engagement opportunities.

**Background State Business Protection:** When the app is in background state, the system rejects engagement attempts entirely rather than queuing them. This business rule reflects understanding that background interruptions create negative user experiences that can damage long-term engagement effectiveness, making immediate rejection preferable to delayed delivery.

#### Business Continuity and Recovery Patterns

**Automatic Retry Logic:** The system implements sophisticated retry logic through the Action model that ensures business-critical engagements eventually reach users even after temporary failures. This business rule recognizes that some engagements represent significant business value that justifies persistent delivery attempts while respecting user experience constraints.

**Graceful Degradation Strategy:** When engagements fail due to resource constraints or system issues, the system implements graceful degradation that maintains core functionality while preserving business value. This includes fallback presentation modes, simplified content delivery, and alternative engagement channels that ensure users still receive important communications.

**State Recovery and Cleanup:** The system implements comprehensive state recovery that ensures failed engagements don't leave the system in inconsistent states that could impact future business operations. This includes automatic semaphore cleanup, DND lock release, and component state reset that maintains system reliability for subsequent engagement opportunities.

### Business Intelligence and Optimization Patterns

#### Engagement Performance Analytics

The system implements sophisticated analytics patterns that provide business intelligence for continuous optimization:

**Conversion Funnel Analysis:** The system tracks engagement presentation, user interaction, and conversion completion to provide detailed funnel analysis that enables business optimization. This includes measuring engagement effectiveness across different content types, timing strategies, and user segments to identify optimization opportunities.

**User Behavior Pattern Recognition:** The system analyzes user engagement patterns to identify behavioral segments and optimize engagement strategies accordingly. This business intelligence enables personalized engagement approaches that improve conversion rates while maintaining user satisfaction.

**Content Performance Optimization:** The system tracks the performance of different engagement types, content formats, and presentation strategies to continuously optimize business results. This includes A/B testing capabilities, performance benchmarking, and automated optimization recommendations based on historical data.

#### Business Rule Evolution and Learning

**Dynamic Business Rule Adjustment:** The system implements learning algorithms that adjust business rules based on performance data and user feedback. This enables continuous optimization of engagement strategies without requiring manual intervention, ensuring that business rules evolve to maintain optimal performance as user behavior and market conditions change.

**Predictive Engagement Optimization:** The system uses historical data and user behavior patterns to predict optimal engagement timing, content selection, and presentation strategies. This business intelligence enables proactive optimization that improves engagement effectiveness while reducing user experience friction.

**Cross-Model Performance Correlation:** The system analyzes performance correlations across different engagement types to identify synergistic opportunities and optimize cross-content coordination. This business intelligence enables sophisticated engagement orchestration that maximizes overall business value rather than optimizing individual engagement types in isolation.

### Navigation System Integration

Seamless integration with the application's navigation system:

**Route Coordination:**
- Deep link support for engagement targets
- Animated transitions with shared elements
- Navigation state preservation during engagements
- Back button handling and flow management

**Screen Integration:**
- Widget-based content presentation
- Screen-specific engagement contexts
- Modal and overlay coordination
- Tab navigation preservation

## Performance Optimization and Business Impact

### Strategic Performance Design

The engagement system's performance optimizations are driven by specific business requirements and user experience goals rather than purely technical considerations:

#### Business-Driven Resource Management

**Memory Optimization for Engagement Quality:** The system implements sophisticated memory management specifically designed to ensure engagement presentations are smooth and professional. Component instance tracking and cleanup prevent memory leaks that could cause engagement stuttering or delays, recognizing that poor performance during engagement moments directly impacts conversion rates and brand perception.

**Image Caching Strategy:** The system implements intelligent image caching that prioritizes engagement-related imagery because visual presentation quality directly impacts user response rates. Pre-downloading and caching engagement images ensures that promotional content, reward imagery, and card visuals load instantly, maintaining the emotional impact that drives user action.

**WebView Resource Management:** For applet-based engagements, the system implements careful resource management that balances interactive capability with performance. This business logic recognizes that interactive engagements can drive higher conversion rates but only if they perform smoothly and don't degrade the overall app experience.

#### User Experience Performance Thresholds

**Background State Business Logic:** The system implements strict background detection not just for technical reasons but because background engagements represent poor user experience that can damage brand relationships. The business logic recognizes that interrupting users when they're not actively using the app creates negative associations with the brand and implements automatic prevention of such scenarios.

**Deferred Processing Strategy:** The system uses deferred processing specifically to ensure that engagement presentations never block user interactions with core app functionality. This business rule prioritizes user experience continuity over engagement immediacy, understanding that frustrated users are less likely to respond positively to promotional content.

**Parallel Resource Loading:** The system implements parallel loading of engagement resources because engagement timing is critical to business success. The business logic recognizes that delays in engagement presentation can result in missed conversion opportunities, especially for time-sensitive offers or location-based promotions.

#### Performance Thresholds and Business Justification

**Database Query Optimization:** The system implements specific performance thresholds for engagement queries (typically under 5ms for card queries, under 100ms for complex reward calculations) because engagement discovery speed directly impacts user experience quality. These thresholds are set based on user perception studies that show engagement delays beyond these limits create noticeable user experience degradation.

**Recursive Engagement Limits:** The system limits recursive engagement triggering to three levels specifically to balance engagement opportunity maximization with system performance. This business rule recognizes that while user attention represents valuable business opportunity, system responsiveness must be maintained to preserve overall user experience quality.

**Semaphore and Concurrency Management:** The system implements sophisticated concurrency control not just to prevent technical conflicts but because overlapping engagements create poor user experiences that reduce conversion effectiveness. The business logic ensures that users receive coherent, sequential engagement experiences that feel intentional rather than chaotic.

### Concurrency Optimization

**Queue Management:**
- Intelligent queuing during locked periods
- Priority-based engagement ordering
- Batch processing for related engagements
- Recursive engagement limiting (max 3 levels)

**State Synchronization:**
- Efficient notification state tracking
- Minimal database writes for state updates
- Event-driven UI updates for real-time sync
- Optimistic updates for better responsiveness

## User Experience Flows

### Primary Engagement Flow

The typical user engagement follows this sequence:

```mermaid
sequenceDiagram
    participant U as User
    participant A as App
    participant E as Engage System
    participant D as DND System
    participant M as Model Layer
    participant UI as UI Component

    A->>E: Trigger engagement check
    E->>D: Check DND status
    alt DND Available
        E->>M: Query nextEngage()
        M-->>E: Return engagement data
        E->>D: Lock DND
        E->>UI: Present engagement
        UI->>U: Display content
        U->>UI: User interaction
        UI->>E: Handle action
        E->>M: Update notification state
        E->>D: Release DND
        E->>A: Trigger next engagement
    else DND Locked
        E->>M: Create postponed action
        E-->>A: Return null (postponed)
    end
```

### Error Recovery Flow

When engagements encounter errors, the system follows specific recovery patterns:

```mermaid
graph TB
    START[Engagement Attempt]
    CHECK{Error Type?}
    ALREADY[Already Notified]
    USED[Action Used]
    DND_ERR[DND Locked]
    OTHER[Other Error]
    
    DISCARD[Discard Silently]
    FEEDBACK[Show User Feedback]
    POSTPONE[Create Action for Retry]
    LOG[Log and Monitor]
    
    CLEANUP[Cleanup Resources]
    NEXT[Trigger Next Engagement]
    
    START --> CHECK
    CHECK -->|alreadyNotified| ALREADY
    CHECK -->|actionUsed| USED
    CHECK -->|DND| DND_ERR
    CHECK -->|other| OTHER
    
    ALREADY --> DISCARD
    USED --> FEEDBACK
    DND_ERR --> POSTPONE
    OTHER --> LOG
    
    DISCARD --> CLEANUP
    FEEDBACK --> CLEANUP
    POSTPONE --> CLEANUP
    LOG --> CLEANUP
    
    CLEANUP --> NEXT

    %% Styling with darker backgrounds and white text
    classDef default fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef error fill:#e53e3e,stroke:#c53030,stroke-width:2px,color:#ffffff
    classDef success fill:#38a169,stroke:#2f855a,stroke-width:2px,color:#ffffff
    
    class CHECK,ALREADY,USED,DND_ERR,OTHER error
    class CLEANUP,NEXT success
```

## Technical Implementation

### Data Flow Architecture

The Engage system processes data through multiple layers with specific responsibilities:

**Collection Layer:**
- Model-specific `nextEngage()` implementations
- Business rule application and filtering
- Priority-based candidate selection
- Cross-model coordination for related content

**Processing Layer:**
- Engagement object creation and validation
- Resource preparation and downloading
- Component selection and configuration
- Error handling and recovery logic

**Presentation Layer:**
- UI component instantiation and management
- User interaction capture and processing
- Animation and transition coordination
- Cleanup and resource release

### Configuration and Extensibility

The system supports configuration and extension through several mechanisms:

**Model Integration:**
- Implement `nextEngage(cardId)` static method
- Return standardized engagement objects
- Handle notification state tracking
- Coordinate with related models

**UI Component Extension:**
- Register new engagement types in `Engage.do()`
- Implement component lifecycle methods
- Handle user interactions and callbacks
- Integrate with overlay and navigation systems

**Business Rule Customization:**
- Extend eligibility checking logic
- Customize priority and ordering rules
- Implement domain-specific error handling
- Configure timing and cooldown parameters

## Advanced Features

### Contextual Intelligence and Business Optimization

#### Location-Driven Business Logic

The system implements sophisticated location-based business logic that goes beyond simple proximity detection to create meaningful business value:

**Merchant Proximity Intelligence:** The system recognizes that physical proximity to merchants represents peak conversion opportunity and implements special business rules for location-triggered engagements. When users are near participating locations, the system prioritizes relevant offers and rewards, understanding that immediate actionability dramatically increases engagement value.

**Geofencing Business Strategy:** Rather than simply triggering notifications based on location, the system implements intelligent geofencing that considers user behavior patterns, visit frequency, and historical engagement data. This business logic ensures location-based engagements feel helpful rather than intrusive, maintaining the delicate balance between relevance and privacy.

**Context-Aware Timing Optimization:** The system implements business rules that optimize engagement timing based on location context. For example, meal-related offers are prioritized during appropriate dining hours when users are near restaurants, while retail offers are emphasized during shopping periods. This contextual intelligence maximizes conversion probability by aligning business opportunities with user intent.

#### Behavioral Intelligence Framework

**Engagement Pattern Recognition:** The system analyzes user engagement patterns to optimize future interactions, implementing business logic that learns from user behavior to improve relevance over time. This includes understanding preferred engagement types, optimal timing windows, and content preferences that drive higher conversion rates.

**Cross-Session Coordination:** The business logic recognizes that user relationships with brands extend across multiple app sessions and implements sophisticated coordination to maintain engagement continuity. This ensures that engagement sequences feel natural and progressive rather than repetitive or disconnected.

**Adaptive Frequency Management:** The system implements intelligent business rules that adjust engagement frequency based on user response patterns and engagement success rates. High-responding users may receive more frequent engagements, while users showing engagement fatigue receive reduced frequency to maintain long-term relationship health.

#### Content Intelligence and Business Value

**Related Content Orchestration:** The system implements sophisticated business logic that groups related content for batch presentation, recognizing that coherent value propositions are more effective than fragmented communications. This includes coordinating offers with related rewards, grouping complementary promotions, and sequencing content to build compelling narratives.

**Expiration-Aware Business Logic:** The system prioritizes time-sensitive content based on business value and user impact, implementing rules that ensure high-value opportunities are presented before expiration while managing user attention efficiently. This business logic balances urgency with user experience quality.

**Dynamic Availability Filtering:** The system implements real-time business logic that filters content based on current availability, user eligibility, and business constraints. This ensures users only see actionable opportunities, maintaining trust and preventing frustration from unavailable offers or expired promotions.

### Advanced UI Patterns

**Shared Element Animations:**
- Seamless transitions from engagement to detail views
- Element-based animation coordination with navigation
- Performance-optimized animation rendering
- Cross-screen animation state management

**Responsive Design:**
- Dynamic scaling based on device capabilities
- Language-aware layout adjustments for CJK languages
- Accessibility support with proper contrast and sizing
- Orientation-aware layout optimization

**Progressive Enhancement:**
- Graceful degradation for limited device capabilities
- Adaptive image quality based on network conditions
- Fallback UI patterns for unsupported features
- Performance-aware feature enablement

### Security and Privacy

**Data Protection:**
- Engagement data encryption for sensitive content
- Secure storage of user interaction history
- Privacy-compliant tracking and analytics
- User consent management for personalization features

**Content Security:**
- Applet content sandboxing and validation
- Secure communication channels for interactive content
- Input validation and sanitization for user data
- Protection against malicious engagement content

## Integration Patterns

### Event-Driven Architecture

The Engage system leverages the application's event system for loose coupling:

**Event Emission:**
- `EVENT.Engage.pbox`: PBox engagement presentation
- `EVENT.Engage.applet`: Applet engagement activation
- `EVENT.Engage.completed`: Engagement completion notification
- `EVENT.Engage.error`: Engagement error reporting

**Event Consumption:**
- Model update events for real-time engagement refresh
- Navigation events for engagement context management
- User interaction events for engagement triggering
- System state events for engagement eligibility

### Service Integration

**Analytics Integration:**
- Engagement presentation tracking
- User interaction analytics
- Conversion rate monitoring
- Performance metrics collection

**Notification Integration:**
- Push notification coordination with engagements
- Local notification scheduling for time-based content
- Badge count management for pending engagements
- Cross-platform notification consistency

## Development Guidelines

### Adding New Engagement Types

To add a new engagement type to the system:

1. **Define Engagement Object Structure:**
```javascript
{
  object: 'engage',
  action: 'pBox',
  engageId: cardId,
  data: {
    object: 'yourObject',
    id: itemId,
    cardId: cardId,
    // Additional properties
  }
}
```

2. **Implement Model Integration:**
```javascript
// In your model class
static nextEngage(cardId) {
  // Find items requiring user attention
  const candidates = this.find(eligibilityQuery, currentTime);

  // Check notification state
  const unnotified = candidates.filter(item => !item.when.notified);

  // Return engagement object
  return unnotified.length ? {
    object: 'engage',
    action: 'pBox', // or 'applet', 'ticket', 'dialog'
    engageId: cardId,
    data: { object: 'yourObject', id: unnotified[0].id, cardId }
  } : null;
}
```

3. **Add Preparation Logic:**
```javascript
// In src/lib/common/engage.js prepare() function
case YOUR_OBJECT: {
  // Extract needed properties
  const { id, title, message } = param;
  // Build UI properties
  // Return props for UI rendering
  return {
    object: YOUR_OBJECT,
    id,
    title,
    message,
    // Additional UI properties
  };
}
```

4. **Implement UI Component:**
- Create component in `src/containers/Engage/`
- Handle user interactions and callbacks
- Integrate with overlay system
- Implement proper cleanup and resource management

### Best Practices

**Performance:**
- Minimize database queries in `nextEngage()` implementations
- Use efficient indexing for engagement candidate queries
- Implement proper component cleanup and resource release
- Cache frequently accessed engagement data

**User Experience:**
- Respect user preferences and Do Not Disturb states
- Provide clear action options and feedback
- Implement smooth animations and transitions
- Handle edge cases gracefully with appropriate fallbacks

**Maintainability:**
- Follow established patterns for consistency
- Document business rules and eligibility criteria
- Implement comprehensive error handling
- Use proper logging for debugging and monitoring

**DND System Integration:**
- Always integrate with the DND system properly by using `$.DND.lock()` and `$.DND.release()`
- Ensure proper cleanup in error scenarios to prevent DND locks
- Use appropriate context information for debugging DND issues
- Coordinate with semaphore system for comprehensive conflict prevention

## Business-Focused Troubleshooting

### Business Impact Analysis

When engagement issues occur, the troubleshooting approach should prioritize business impact assessment and user experience preservation:

#### Engagement Delivery Failures

**Business Impact Assessment:** When engagements fail to appear, the primary concern is identifying lost business opportunities and user experience degradation. This includes assessing whether high-value promotions are being missed, whether user acquisition flows are being interrupted, and whether loyalty program engagement is being compromised.

**User Experience Preservation:** Troubleshooting should prioritize maintaining user experience quality over technical completeness. This means ensuring that failed engagements don't leave users in confusing states, that error recovery doesn't create additional user friction, and that system reliability is maintained for subsequent engagement opportunities.

**Business Continuity Verification:** When engagement systems experience issues, verification should focus on ensuring that critical business functions continue to operate. This includes confirming that card registration flows remain functional, that reward redemption processes continue to work, and that essential user communications are still being delivered.

#### Performance Impact on Business Metrics

**Conversion Rate Impact Analysis:** Performance issues should be evaluated based on their impact on engagement conversion rates and user response patterns. This includes measuring whether slow engagement loading affects user interaction rates, whether system delays impact promotional effectiveness, and whether performance issues correlate with reduced user engagement.

**User Experience Quality Metrics:** Troubleshooting should include assessment of user experience quality metrics such as engagement completion rates, user satisfaction indicators, and system responsiveness measurements that directly impact business outcomes.

**Business Rule Effectiveness Monitoring:** Performance issues should be evaluated for their impact on business rule effectiveness, including whether timing constraints are being met, whether priority hierarchies are being respected, and whether business logic is executing as intended.

### Business-Oriented Debugging Strategies

#### Business Logic Verification

**Engagement Eligibility Analysis:** Debugging should include comprehensive analysis of engagement eligibility logic to ensure business rules are being applied correctly. This includes verifying that card requirements are being enforced appropriately, that user qualification logic is functioning correctly, and that content availability rules are being respected.

**Priority Hierarchy Validation:** Troubleshooting should verify that engagement priority hierarchies are functioning as designed, ensuring that high-value business opportunities are being prioritized correctly and that user experience optimization is being maintained.

**Cross-Model Coordination Assessment:** Debugging should include analysis of cross-model coordination to ensure that business logic dependencies are being satisfied and that engagement orchestration is creating coherent user experiences.

#### User Experience Impact Assessment

**Engagement Flow Continuity:** Troubleshooting should verify that engagement flows maintain continuity and coherence from the user perspective, ensuring that business objectives are being met while preserving user experience quality.

**Error Recovery User Impact:** Debugging should assess how error recovery mechanisms impact user experience, ensuring that business continuity measures don't create additional user friction or confusion.

**Performance Threshold Compliance:** Troubleshooting should verify that performance thresholds are being met to ensure that business objectives for user experience quality are being satisfied.

## Business Architecture Summary

This comprehensive engagement architecture represents a sophisticated business intelligence system that goes far beyond simple notification delivery to create a strategic customer engagement platform. The system's design reflects deep understanding of user psychology, business optimization principles, and customer relationship management best practices.

### Strategic Business Value

The engagement system serves as a critical business asset that drives customer acquisition, retention, and lifetime value optimization through intelligent, contextual user interactions. Its sophisticated business logic ensures that every engagement opportunity is maximized while maintaining the user experience quality that preserves long-term customer relationships.

### Architectural Business Intelligence

The system's architecture embodies sophisticated business intelligence that balances multiple competing objectives: maximizing engagement opportunities while preventing user fatigue, delivering timely business communications while respecting user context, and optimizing conversion rates while maintaining system performance and reliability.

### Future Business Evolution

The engagement system's flexible architecture and learning capabilities position it to evolve with changing business requirements and user expectations. Its sophisticated analytics and optimization frameworks enable continuous improvement of business outcomes while maintaining the architectural integrity needed for long-term scalability and reliability.

This architecture provides a robust foundation for sophisticated user engagement that delivers measurable business value through increased conversion rates, improved customer satisfaction, and optimized customer lifetime value, while maintaining the flexibility needed for future business evolution and market adaptation.
