# Perkd App: Sign Up & Login Flow Design Documentation

## Table of Contents

1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [User Journey Flows](#user-journey-flows)
4. [Detailed Flow Documentation](#detailed-flow-documentation)
5. [Screen Components](#screen-components)
6. [Special Features](#special-features)
7. [State Management](#state-management)
8. [Security & Verification](#security--verification)
9. [Integration Points](#integration-points)
10. [Known Conflicts & Areas for Refinement](#known-conflicts--areas-for-refinement)

## Overview

The Perkd app's Reception system orchestrates user authentication, onboarding, and account setup processes. It manages the complete journey from first-time sign up to returning user authentication through well-defined flows and screens. The system uses a mobile-first approach, leveraging the user's mobile number as the primary identifier and maintains user progress through persistent state management.

**Key Characteristics:**
- Mobile number as primary identifier
- WhatsApp/SMS verification for authentication
- Support for pre-issued membership cards (Startup Cards)
- Persistent state management across app sessions
- Seamless handoff to main app (Party system)

## System Architecture

```mermaid
graph TD
    subgraph "Reception System"
        A[Reception Controller]
        B[State Management]
        C[Flow Navigator]
    end
    
    subgraph "Core Services"
        D[Person Service]
        E[Analytics & Monitoring]
        F[Navigation Service]
        G[Persistence Service]
    end
    
    subgraph "External Systems"
        H[WhatsApp/SMS Gateway]
        I[Card Library Service]
        J[Party System]
    end
    
    A <-->|Profile data| D
    A -->|Events & errors| E
    A <-->|Screen management| F
    A <-->|State storage| G
    B <--> G
    C <--> F
    
    A <-->|Verification| H
    A <-->|Card data| I
    A -->|Handoff on completion| J
```

## User Journey Flows

The Reception system handles four primary user scenarios:

```mermaid
graph TD
    A[App Launch] --> B{User Status Check}
    
    B -->|New User| C[Sign Up Flow]
    B -->|Returning User| D[Login Flow]
    B -->|Incomplete Profile| E[Profile Completion Flow]
    B -->|Missing Permissions| F[Permission Request Flow]
    
    C --> G["authenticate → agree → verify → profile → welcome → restore → startup → requestpush → done"]
    D --> H["restore → requestpush → done"]
    E --> I["profile → done"]
    F --> J["requestpush → done"]
    
    G --> K[Party System]
    H --> K
    I --> K
    J --> K
    
    style K fill:#e1f5fe,stroke:#01579b,stroke-width:2px
```

### Flow Steps Definition

| Step | Purpose | All Flows | Sign Up | Login | Profile | Push |
|------|---------|-----------|---------|-------|---------|------|
| authenticate | Initial authentication | ✓ | ✓ | ✓ | - | - |
| agree | Terms acceptance | ✓ | ✓ | - | - | - |
| verify | Phone verification | ✓ | ✓ | - | - | - |
| profile | User profile setup | ✓ | ✓ | - | ✓ | - |
| welcome | Onboarding introduction | ✓ | ✓ | - | - | - |
| restore | Data restoration | ✓ | ✓ | ✓ | - | - |
| startup | Startup card processing | ✓ | ✓ | - | - | - |
| requestpush | Permission requests | ✓ | ✓ | ✓ | - | ✓ |
| done | Flow completion | ✓ | ✓ | ✓ | ✓ | ✓ |

## Detailed Flow Documentation

### Sign Up Flow

```mermaid
graph TD
    A[Start Screen] --> B{User Enters Mobile & Taps Sign Up};
    B --> C{Is Number Registered?};
    
    C -->|No| D[Join Perkd Screen];
    D --> E[Mobile Number Verification];
    E --> F{Startup Card Check};
    
    F -->|Has Card| G[Setting up Card Library];
    G --> H[Card Pop Up];
    H --> I{Accept/Decline Card?};
    
    I -->|Accept| J{Register Card?};
    J -->|Register| K[Register Card Process];
    K --> L[Cards Screen - Party System];
    
    J -->|Cancel| M[Profile Screen];
    I -->|Decline| M;
    
    F -->|No Card| N[Profile Screen];
    M --> O[Setting up Card Library];
    N --> O;
    O --> P[Welcome Screen];
    P --> Q[Restore Process];
    Q --> R[Push Permission Request];
    R --> L;

    C -->|Yes| S[Redirect to Login Flow];
    S --> L;
    
    style L fill:#f9f,stroke:#333,stroke-width:2px;
    style S fill:#e1f5fe,stroke:#01579b,stroke-width:2px;
```

#### Step-by-Step Sign Up Process

1. **Start Screen (authenticate)**
   - User selects country code and enters mobile number
   - Validation of mobile number format
   - Option to choose "Sign Up" or "Login"

2. **Join Perkd Screen (agree)**
   - Display terms of service and privacy policy
   - User must agree to terms before proceeding
   - Links to full terms and privacy policy documents

3. **Mobile Number Verification (verify)**
   - Verification code sent via WhatsApp (preferred) or SMS.
   - User enters received 5-digit verification code.
   - Rate limiting on resend: 60-second delay and max attempt limit.
   - Resend option available after timeout, with choices for WhatsApp, SMS, or Voice Call.

   ##### **Detailed Verification Sub-Flow**

   This diagram illustrates the complete user journey for the verification step, including error handling and alternative paths.

   ```mermaid
      graph TD
      subgraph "High-Level Verification Flow"
         A[Start: Verify Screen Loads] --> B[Code is sent & 60s resend timer begins];
         
         B --> C[UI is waiting for user action];

         C --> D[User enters code];
         D --> E{Is Code Valid?};
         E -->|Yes| F[Success: Continue to Next Step];
         E -->|No| G[Show Error];
         G --> C;

         C --> H[User clicks 'resend' if timer expired];
         H --> I{Max Resend Attempts Reached?};
         I -->|No| J[User chooses resend method];
         J --> B;
         I -->|Yes| K[Show 'Max attempts reached' & disable resend];

         A --> L["change mobile"];
         L --> M[Return to Mobile Entry Screen];
      end

      style F fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px;
      style K fill:#ffebee,stroke:#c62828,stroke-width:1px;
   ```

4. **Startup Card Check**
   - System checks for pre-issued cards linked to mobile number
   - Branches flow based on card existence

5. **Profile Setup (profile)**
   - Collection of user information:
     - Given Name
     - Family Name  
     - Gender
     - Birth Date
   - Data validation and persistence

6. **Welcome Screen (welcome)**
   - Introduction to app features
   - Onboarding content presentation

7. **Restore Process (restore)**
   - Download and sync user settings
   - Fetch card library data
   - Progress tracking display

8. **Push Permission Request (requestpush)**
   - Request notification permissions
   - Explain benefits of notifications
   - Handle permission grant/denial

### Login Flow

```mermaid
graph TD
    A[Start Screen] --> B[Login Screen]
    B --> C{Login Success?}
    
    C -->|Success| D[Restore Cards Process]
    D --> E[Push Permission Check]
    E --> F[Cards Screen - Party System]
    
    C -->|Failure| G[Login Error Feedback]
    G --> H{Action Choice}
    
    H -->|Retry| B
    H -->|Forgot Password| I[Receive New Password via Whatsapp/SMS/Email]
    H -->|Sign Up| J[Sign Up Flow]
    
    I --> |Retry|B
    
    style F fill:#f9f,stroke:#333,stroke-width:2px
```

#### Step-by-Step Login Process

1. **Login Screen (authenticate)**
   - User enters mobile number and password
   - Password visibility toggle
   - "Forgotten password" option
   - "Sign up new account" option

2. **Authentication Validation**
   - Credential verification against stored data
   - Error handling for invalid credentials

3. **Restore Cards Process (restore)**
   - Sync previously added cards
   - Restore user preferences and settings
   - Display restoration progress

4. **Push Permission Check (requestpush)**
   - Verify current notification permission status
   - Request permissions if not granted

## Screen Components

### Core Screens

| Screen | User-Facing Name | Purpose | Key Elements |
|--------|------------------|---------|--------------|
| **Start** | Welcome Screen | Authentication entry point | Country selector, mobile input, sign up/login buttons, biometric option |
| **Agree** | Join Perkd | Terms acceptance | Terms display, privacy policy link, agreement checkbox |
| **Verify** | Verification | Phone verification | Code input, resend option, voice call alternative |
| **Profile** | Profile Setup | User information collection | Name fields, gender, birth date, validation |
| **Welcome** | Welcome to Perkd | Feature introduction | Onboarding content, feature highlights |
| **Restore** | Setting Up | Data restoration | Progress indicator, card library setup |
| **Startup** | New Card Available | Startup card processing | Card display, accept/decline options |
| **PushRequest** | Stay Updated | Permission management | Permission benefits, allow/skip options |
| **Account** | My Profile | Profile management | Profile editing, security settings |

### Screen Relationships

```mermaid
graph TD
    A[Start] --> B[Agree]
    A --> C[Login Screen]
    
    B --> D[Verify]
    C --> E[Restore]
    
    D --> F{Startup Card?}
    F -->|Yes| G[Startup]
    F -->|No| H[Profile]
    
    G --> I{Card Action}
    I -->|Accept & Register| J[Cards Screen]
    I -->|Accept & Cancel| H
    I -->|Decline| H
    
    H --> K[Welcome]
    K --> E
    E --> L[PushRequest]
    L --> J
    
    style J fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
```

## Special Features

### Startup Card System

The Startup Card feature optimizes onboarding for users with pre-issued cards:

```mermaid
graph TD
    A[Mobile Number Verified] --> B[Check Card Database]
    B -->|Card Found| C[Download Card Data]
    B -->|No Card| D[Standard Profile Flow]
    
    C --> E[Display Card Pop-up]
    E --> F{User Decision}
    
    F -->|Accept| G[Card Registration Form]
    F -->|Decline| H[Standard Profile Flow]
    
    G --> I{Registration Action}
    I -->|Complete| J[Skip Profile - Direct to Cards]
    I -->|Cancel| H
    
    H --> K[Manual Profile Entry]
    K --> L[Card Library Setup]
    L --> M[Continue Flow]
    
    style J fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
```

**Key Benefits:**
- Reduces onboarding friction
- Pre-populates profile information
- Immediate card availability
- Maintains user choice (accept/decline)

### Dynamic Profile Insertion

The system intelligently handles incomplete user profiles:

- **Detection**: After restoration, system checks profile completeness
- **Insertion**: Dynamically adds profile step to any flow if data is missing
- **Validation**: Ensures all users have complete profiles before main app access
- **Flexibility**: Works across all user journey types

### Biometric Authentication

For returning users with enabled biometric authentication:
- **Touch ID/Face ID**: Quick access option on start screen
- **Fallback**: Standard login available if biometric fails
- **Security**: Biometric data stored securely on device

## State Management

The Reception system maintains persistent state across app sessions:

### Stored State Data

```mermaid
graph TD
    A[Reception State] --> B[Registration Details]
    A --> C[Verification Status]
    A --> D[Flow Progress]
    A --> E[User Preferences]
    
    B --> F[Mobile Number]
    B --> G[Country Code]
    B --> H[Terms Acceptance]
    
    C --> I[Verification Method]
    C --> J[Last Verification Time]
    C --> K[Rate Limit Status]
    
    D --> L[Current Flow]
    D --> M[Current Step]
    D --> N[Completed Steps]
    
    E --> O[Startup Card Info]
    E --> P[Notification Permissions]
    E --> Q[Biometric Settings]
```

### State Persistence Lifecycle

1. **App Launch**: Check notification permissions and restore saved state
2. **Progress Tracking**: Update state after each completed step
3. **Session Management**: Maintain state across app backgrounding/foregrounding
4. **Completion**: Clear Reception state on successful handoff to Party system

## Security & Verification

### Multi-Factor Authentication

The system implements several security layers:

**Primary Authentication:**
- Mobile number verification via SMS/WhatsApp
- Rate limiting (60-second delays)
- Multiple verification methods (SMS, voice call)

**Secondary Authentication:**
- Password creation and validation
- Biometric authentication (Touch ID/Face ID)
- Device-specific security features

**Security Controls:**
- Back navigation prevention during critical flows
- Session timeout handling
- Secure credential storage

### Verification Process

```mermaid
sequenceDiagram
    participant U as User
    participant A as App
    participant G as Gateway
    participant V as Verification Service
    
    U->>A: Enter mobile number
    A->>V: Request verification code
    V->>G: Send via WhatsApp/SMS
    G->>U: Deliver verification code
    U->>A: Enter code
    A->>V: Validate code
    V->>A: Validation result
    
    Note over A,V: 60-second rate limit
    Note over V,G: Fallback to SMS if WhatsApp fails
```

## Integration Points

### Party System Handoff

The Reception system completes by transitioning control to the Party system:

1. **Completion Check**: All required steps completed successfully
2. **State Transfer**: Authentication context passed to Party
3. **Initialization**: Party system initializes core controllers and services
4. **UI Transition**: Navigation to main app interface (Cards Screen)

### External Service Integration

**Person Service:**
- Profile data management
- User authentication
- Data validation and persistence

**Card Library Service:**
- Pre-issued card detection
- Card data retrieval
- Card registration processing

**Analytics & Monitoring:**
- Flow completion tracking
- Error logging and reporting
- Performance monitoring

## Known Conflicts & Areas for Refinement

### Documentation Conflicts Identified

1. **Flow Sequence Differences:**
   - **Conflict**: Document 1 shows "Register Card" step after card acceptance, Document 2 doesn't detail this step
   - **Impact**: Unclear what happens during card registration process
   - **Recommendation**: Define complete card registration workflow

2. **Welcome Screen Placement:**
   - **Conflict**: Document 2 mentions Welcome screen in flow, Document 1 doesn't include it
   - **Impact**: Inconsistent onboarding experience definition
   - **Recommendation**: Clarify Welcome screen content and placement

3. **Profile Flow Variations:**
   - **Conflict**: Multiple profile completion paths with different triggers
   - **Impact**: Complex user experience with unclear branching logic
   - **Recommendation**: Standardize profile completion requirements

### Areas Requiring Product Decision

1. **Startup Card User Experience:**
   - Should users be able to modify pre-populated profile data?
   - What happens if card registration fails?
   - How to handle multiple cards for one user?

2. **Verification Method Priority:**
   - WhatsApp vs SMS preference logic
   - Fallback mechanisms for verification failures
   - International number handling

3. **Error Recovery Flows:**
   - Network failure handling during critical steps
   - App crash recovery mechanisms
   - Partial completion scenarios

### Technical Implementation Gaps

1. **Performance Considerations:**
   - Loading states during card library setup
   - Optimization for slow network conditions
   - Memory management during flow transitions

2. **Accessibility Requirements:**
   - Screen reader compatibility
   - Color contrast standards
   - Text size adaptation

3. **Analytics Integration:**
   - Flow completion metrics
   - Drop-off point identification
   - User behavior tracking

---

*This document represents the current state of the Perkd app's sign up and login flows as of the documentation review. It should be updated as the system evolves and conflicts are resolved.*
