# Storage Schemas

## Overview

This document provides comprehensive documentation of all storage schemas used throughout the Perkd application. The schemas are organized by storage type and include detailed information about data models, relationships, constraints, and validation rules that ensure data integrity and consistency across the application.

## Schema Organization

### Schema Categories

The application employs schemas across multiple storage layers, each optimized for specific data characteristics and access patterns:

- **Realm Object Schemas**: Complex relational data with object relationships
- **AsyncStorage Schemas**: Simple key-value configuration and preference data
- **Secure Storage Schemas**: Credential and sensitive information structures
- **File System Schemas**: Media asset organization and metadata structures
- **Cache Schemas**: Temporary data structures for performance optimization

### Schema Versioning

**Current Schema Version**: 278+  
**Migration Strategy**: Automated schema migrations with rollback capabilities  
**Backward Compatibility**: Maintains compatibility with previous schema versions during migration periods

## Core Entity Schemas

### Person Schema

**Primary Entity**: User profile and personal information management

```mermaid
erDiagram
    Person {
        string id PK
        string familyName
        string givenName
        string fullName
        string alias
        string gender
        int ethnicity
        int religion
        date createdAt
        date modifiedAt
        date deletedAt
    }
    
    Person ||--|| Name : has
    Person ||--o{ PersonDate : contains
    Person ||--o{ PersonPhone : contains
    Person ||--o{ PersonEmail : contains
    Person ||--o{ PersonAddress : contains
    Person ||--|| ProfileImage : has
    Person ||--|| PersonBrands : has
    Person ||--|| PersonProducts : has
    
    Name {
        string id PK
        string familyName
        string givenName
        string fullName
        string order
    }
    
    PersonDate {
        string id PK
        string type
        date date
    }
    
    PersonPhone {
        string id PK
        string type
        string number
        string countryCode
    }
    
    PersonEmail {
        string id PK
        string type
        string email
    }
    
    PersonAddress {
        string id PK
        string type
        string street
        string city
        string state
        string country
        string postalCode
    }
```

**Validation Rules**:
- **Name Validation**: Names cannot be symbol-only, emoji-only, or number-only
- **Email Validation**: Must conform to RFC 5322 email format standards
- **Phone Validation**: Country-specific phone number format validation
- **Age Validation**: Configurable minimum age requirements for registration

**Business Logic**:
- **Name Order Management**: Supports cultural name ordering preferences (given-family vs family-given)
- **Contact Deduplication**: Prevents duplicate contact information across different types
- **Privacy Controls**: Granular privacy settings for different personal information categories
- **Data Inheritance**: Personal information can be inherited by related entities (Cards, etc.)

### Card Schema

**Primary Entity**: Loyalty card and membership management

```mermaid
erDiagram
    Card {
        string id PK
        string number
        string barcode
        string barcodeType
        string displayName
        date startTime
        date endTime
        int imageIndex
        string forms
        string formData
        date createdAt
        date modifiedAt
        date deletedAt
        date hiddenAt
        string personId FK
        string masterId FK
        string principalCardId FK
        bool _upload
        bool _refresh
        string _model
    }
    
    Card ||--|| CardMaster : belongs_to
    Card ||--|| Person : belongs_to
    Card ||--o| Card : principal_card
    Card ||--|| CustomImage : has
    Card ||--|| CardMasterImage : uses
    Card ||--o{ Sharing : has
    Card ||--|| StoredValue : has
    Card ||--o{ CardWidget : contains
    
    CardMaster {
        string id PK
        string name
        string description
        string[] barcodeTypes
        string[] numberFormats
        string[] numberPatterns
        string[] barcodePatterns
        string minAppVersion
        string details
        string forms
        date createdAt
        date modifiedAt
        date deletedAt
    }
    
    StoredValue {
        string id PK
        string currency
        double balance
        double pendingBalance
        date lastUpdated
    }
    
    Sharing {
        string id PK
        string type
        string recipientId
        date sharedAt
        date expiresAt
    }
```

**Validation Rules**:
- **Card Number Validation**: Pattern-based validation using CardMaster-defined patterns
- **Barcode Validation**: Format-specific barcode validation (Code128, QR, etc.)
- **Date Validation**: Start time must be before end time, expiry dates must be future dates
- **Master Relationship**: Must reference valid CardMaster with compatible features

**Business Logic**:
- **Card Lifecycle**: Supports issued, active, expired, and suspended states
- **Principal Card Relationships**: Enables card hierarchies for family accounts
- **Form Data Management**: Dynamic form data based on CardMaster requirements
- **Sharing Policies**: Configurable sharing rules based on CardMaster policies
- **Stored Value Integration**: Optional stored value balance tracking and management

### Offer Schema

**Primary Entity**: Promotional offers and rewards management

```mermaid
erDiagram
    Offer {
        string id PK
        string name
        string description
        string kind
        string type
        date startTime
        date endTime
        string templateId
        string personId FK
        string cardId FK
        string merchantId FK
        date createdAt
        date modifiedAt
        date deletedAt
        bool _refresh
        string _model
    }
    
    Offer ||--o{ OfferCode : has
    Offer ||--o{ OfferImage : has
    Offer ||--|| OfferDiscount : has
    Offer ||--|| Redemption : has
    Offer ||--|| OfferSharer : has
    Offer ||--|| OfferGlobalize : has
    Offer ||--|| OfferWhen : has
    
    OfferCode {
        string id PK
        string code
        string type
        int usageLimit
        int usageCount
        date expiresAt
    }
    
    OfferDiscount {
        string id PK
        string type
        double value
        double minPurchase
        double maxDiscount
        string currency
    }
    
    Redemption {
        string id PK
        string status
        date redeemedAt
        string location
        double amount
        string currency
    }
```

**Validation Rules**:
- **Time Validation**: Start time must be before end time, both must be valid dates
- **Discount Validation**: Discount values must be positive, percentage discounts ≤ 100%
- **Code Validation**: Offer codes must be unique within their scope and format
- **Usage Limits**: Usage count cannot exceed defined usage limits

**Business Logic**:
- **Offer Lifecycle**: Supports draft, active, expired, and cancelled states
- **Redemption Tracking**: Comprehensive redemption history and analytics
- **Geographic Restrictions**: Location-based offer availability and validation
- **Personalization**: User-specific offer targeting and customization
- **Multi-Channel Support**: Offers can be redeemed across multiple touchpoints

### Place Schema

**Primary Entity**: Location and venue management

```mermaid
erDiagram
    Place {
        string id PK
        string type
        string name
        date startTime
        date endTime
        string external
        date createdAt
        date modifiedAt
        date deletedAt
        bool _refresh
        string _model
    }
    
    Place ||--|| PlaceBrand : has
    Place ||--|| PlaceGeometry : has
    Place ||--|| PlaceLocale : has
    Place ||--o{ PlaceUrl : has
    Place ||--|| PlaceTag : has
    Place ||--o{ PlaceImage : has
    Place ||--o{ PlacePhone : has
    Place ||--o{ PlaceAddress : has
    
    PlaceGeometry {
        string id PK
        double latitude
        double longitude
        double accuracy
        string viewport
    }
    
    PlaceLocale {
        string id PK
        string language
        string country
        string timezone
    }
    
    PlaceAddress {
        string id PK
        string street
        string city
        string state
        string country
        string postalCode
        string formattedAddress
    }
```

**Validation Rules**:
- **Coordinate Validation**: Latitude (-90 to 90), longitude (-180 to 180)
- **Address Validation**: Country-specific address format validation
- **Timezone Validation**: Valid IANA timezone identifiers
- **URL Validation**: Proper URL format for place-related links

**Business Logic**:
- **Geofencing**: Automatic location-based triggers and notifications
- **Operating Hours**: Complex operating hours with holiday and exception handling
- **Multi-Language Support**: Localized place information for different languages
- **External Integration**: Integration with third-party location services (Google Places, etc.)
- **Place Hierarchies**: Support for place relationships (chain stores, franchises)

## Configuration Schemas

### AsyncStorage Configuration Schema

**User Preferences Structure**:
```typescript
interface UserPreferences {
  reminders: {
    card: { days: number };
    offer: { days: number };
  };
  discover: {
    cards: boolean;
    regions: string[];
  };
  card: {
    view: 'big' | 'small';
    order: 'custom' | 'alphabetical' | 'recent';
    sort: string[];
  };
  bioAuth: boolean;
  scan: boolean;
}
```

**Application Settings Structure**:
```typescript
interface ApplicationSettings {
  sync: {
    timeout: number;
    fetchModels: string[];
    syncUpModels: string[];
    backgroundFetch: {
      enabled: boolean;
      minInterval: number;
      timeout: number;
    };
  };
  region: {
    max: number;
    common: string[];
  };
  track: {
    log: boolean;
    to: string;
  };
}
```

**Validation Rules**:
- **Timeout Values**: Must be positive integers within reasonable ranges
- **Model Names**: Must reference valid Realm model names
- **Region Codes**: Must be valid ISO 3166-1 alpha-2 country codes
- **Boolean Flags**: Strict boolean validation with default fallbacks

## Secure Storage Schemas

### Credential Storage Structure

**Domain-Based Organization**:
```typescript
interface CredentialDomains {
  app: {
    [provider: string]: {
      token: string;
      refreshToken?: string;
      expiresAt: Date;
      scope?: string[];
    };
  };
  cardmaster: {
    [masterId: string]: {
      [credentialType: string]: {
        username?: string;
        password?: string;
        apiKey?: string;
        certificate?: string;
      };
    };
  };
  card: {
    [cardId: string]: {
      [credentialType: string]: any;
    };
  };
  payment: {
    [paymentId: string]: {
      [credentialType: string]: {
        token: string;
        last4?: string;
        expiryMonth?: number;
        expiryYear?: number;
        brand?: string;
      };
    };
  };
}
```

**Security Policies**:
- **Encryption**: All credentials encrypted using platform-specific secure storage
- **Access Control**: Biometric authentication required for sensitive operations
- **Expiration**: Automatic credential expiration and rotation policies
- **Audit Trail**: Comprehensive logging of credential access and modifications

## File System Schemas

### Media Asset Organization

**Directory Structure Schema**:
```
DocumentDirectory/
├── x.realm                     # Primary database file
├── x.realm.management         # Database management metadata
├── images/                    # User-uploaded images
│   ├── profile/              # Profile images
│   ├── cards/                # Custom card images
│   └── shared/               # Shared media assets
├── cards/                    # Card-specific assets
└── tmp/                      # Temporary files

CacheDirectory/
├── images/                   # Cached remote images
│   ├── public/              # Public cached images
│   └── private/             # Private cached images
└── shopimages/              # E-commerce product images

TemporaryDirectory/
├── shared/                  # Shared temporary resources
│   └── rsrc/               # Resource files
├── notifications/          # Notification assets
└── Downloads/             # Downloaded files
```

**File Naming Conventions**:
- **Image Files**: `{type}_{id}_{timestamp}.{extension}`
- **Cache Files**: `{hash}.{extension}` (MD5 on iOS, SHA256 on Android)
- **Temporary Files**: `tmp_{uuid}_{timestamp}.{extension}`

**Metadata Schema**:
```typescript
interface FileMetadata {
  path: string;
  size: number;
  mimeType: string;
  createdAt: Date;
  lastAccessed: Date;
  checksum: string;
  isPrivate: boolean;
  expiresAt?: Date;
}
```

## Data Integrity Policies

### Validation Framework

**Field-Level Validation**:
- **Type Validation**: Strict type checking for all schema fields
- **Format Validation**: Regular expression and custom format validators
- **Range Validation**: Numeric and date range constraints
- **Relationship Validation**: Foreign key integrity and cascade rules

**Business Rule Validation**:
- **Cross-Field Validation**: Complex validation rules spanning multiple fields
- **Temporal Validation**: Time-based business rule enforcement
- **State Validation**: Entity state transition validation
- **Permission Validation**: User permission and access control validation

### Data Consistency Rules

**Transaction Management**:
- **ACID Compliance**: All database operations maintain ACID properties
- **Optimistic Locking**: Prevents concurrent modification conflicts
- **Rollback Capabilities**: Automatic rollback on validation failures
- **Batch Operations**: Efficient bulk operations with transaction boundaries

**Synchronization Integrity**:
- **Conflict Resolution**: Intelligent conflict resolution strategies
- **Version Control**: Maintains data version history for conflict resolution
- **Delta Tracking**: Efficient change tracking for synchronization
- **Consistency Checks**: Regular data consistency validation and repair

## Advanced Schema Patterns

### Relationship Management

**Complex Relationships**: The schema architecture supports sophisticated relationship patterns that reflect real-world business logic:

**One-to-Many Relationships**:
- **Person → Cards**: A person can have multiple loyalty cards
- **CardMaster → Cards**: A card master template can generate multiple card instances
- **Place → Offers**: A location can host multiple promotional offers
- **Card → Widgets**: Cards can contain multiple interactive widgets

**Many-to-Many Relationships**:
- **Cards ↔ Places**: Cards can be valid at multiple places, places can accept multiple card types
- **Offers ↔ Cards**: Offers can apply to multiple cards, cards can have multiple applicable offers
- **Person ↔ Places**: People can have relationships with multiple places (favorites, recent visits)

**Hierarchical Relationships**:
- **Principal Card Relationships**: Family account structures with primary and secondary cards
- **Place Hierarchies**: Chain stores, franchises, and location groupings
- **Offer Inheritance**: Offers that inherit properties from parent templates

**Self-Referential Relationships**:
- **Card Sharing**: Cards can be shared with other cards in family structures
- **Place Networks**: Places can reference related or nearby locations
- **Offer Chains**: Sequential offers that reference previous or next offers in a campaign

### Dynamic Schema Patterns

**Form Schema Management**: The application implements dynamic form schemas that adapt based on CardMaster requirements:

```typescript
interface DynamicFormSchema {
  cardMasterId: string;
  version: string;
  fields: {
    [fieldName: string]: {
      type: 'string' | 'number' | 'date' | 'boolean' | 'select' | 'multiselect';
      required: boolean;
      validation: {
        pattern?: string;
        minLength?: number;
        maxLength?: number;
        min?: number;
        max?: number;
        options?: string[];
      };
      display: {
        label: string;
        placeholder?: string;
        helpText?: string;
        conditional?: {
          field: string;
          value: any;
          operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
        };
      };
    };
  };
}
```

**Widget Schema Flexibility**: Supports dynamic widget schemas for extensible card functionality:

```typescript
interface WidgetSchema {
  id: string;
  type: 'balance' | 'points' | 'offers' | 'qr' | 'barcode' | 'custom';
  configuration: {
    [key: string]: any;
  };
  display: {
    position: number;
    size: 'small' | 'medium' | 'large';
    style: {
      backgroundColor?: string;
      textColor?: string;
      borderRadius?: number;
    };
  };
  data: {
    source: 'local' | 'remote' | 'computed';
    endpoint?: string;
    computation?: string;
    cachePolicy: {
      ttl: number;
      strategy: 'cache_first' | 'network_first' | 'cache_only' | 'network_only';
    };
  };
}
```

### Globalization Schema

**Multi-Language Support**: Comprehensive internationalization schema supporting multiple languages and regions:

```typescript
interface GlobalizationSchema {
  entityId: string;
  entityType: 'CardMaster' | 'Offer' | 'Place' | 'Message';
  localizations: {
    [languageCode: string]: {
      name?: string;
      description?: string;
      shortDescription?: string;
      terms?: string;
      customFields?: {
        [fieldName: string]: string;
      };
    };
  };
  defaultLanguage: string;
  supportedLanguages: string[];
  fallbackStrategy: 'default' | 'english' | 'none';
}
```

**Regional Customization**: Support for region-specific business rules and display preferences:

```typescript
interface RegionalSchema {
  regionCode: string;
  currency: {
    code: string;
    symbol: string;
    decimalPlaces: number;
    thousandsSeparator: string;
    decimalSeparator: string;
  };
  dateFormat: {
    short: string;
    long: string;
    time: string;
  };
  addressFormat: {
    fields: string[];
    required: string[];
    validation: {
      [fieldName: string]: string;
    };
  };
  phoneFormat: {
    countryCode: string;
    nationalPrefix: string;
    format: string;
    validation: string;
  };
}
```

## Validation Framework

### Comprehensive Validation Rules

**Field-Level Validation**: Sophisticated validation framework supporting multiple validation types:

**String Validation**:
- **Length Constraints**: Minimum and maximum length validation
- **Pattern Matching**: Regular expression pattern validation
- **Character Set Validation**: Allowed/disallowed character validation
- **Encoding Validation**: UTF-8 and special character handling

**Numeric Validation**:
- **Range Validation**: Minimum and maximum value constraints
- **Precision Validation**: Decimal place and precision requirements
- **Currency Validation**: Currency-specific formatting and validation
- **Mathematical Validation**: Custom mathematical constraint validation

**Date and Time Validation**:
- **Range Validation**: Past, present, and future date constraints
- **Business Day Validation**: Working day and holiday awareness
- **Age Validation**: Minimum and maximum age calculations
- **Timezone Validation**: Timezone-aware date validation

**Complex Object Validation**:
- **Nested Object Validation**: Recursive validation for complex objects
- **Array Validation**: Element count and content validation
- **Conditional Validation**: Field validation based on other field values
- **Cross-Field Validation**: Validation rules spanning multiple fields

### Business Rule Validation

**Entity State Validation**: Ensures entities maintain valid states throughout their lifecycle:

```typescript
interface StateValidationRules {
  entity: string;
  states: {
    [stateName: string]: {
      allowedTransitions: string[];
      requiredFields: string[];
      validationRules: {
        [fieldName: string]: any;
      };
      businessRules: {
        rule: string;
        condition: string;
        message: string;
      }[];
    };
  };
}
```

**Temporal Validation**: Time-based business rule enforcement:
- **Validity Periods**: Ensures entities are only active during valid time periods
- **Expiration Handling**: Automatic state transitions based on expiration dates
- **Scheduling Validation**: Validates scheduled operations and events
- **Timezone Handling**: Proper timezone handling for global operations

**Permission-Based Validation**: Validates operations based on user permissions and context:
- **Role-Based Validation**: Validates operations based on user roles
- **Context-Aware Validation**: Validation rules that change based on context
- **Dynamic Permission Validation**: Real-time permission validation
- **Audit Trail Validation**: Ensures proper audit trail maintenance

### Data Sanitization

**Input Sanitization**: Comprehensive input sanitization to prevent security vulnerabilities:

**XSS Prevention**:
- **HTML Encoding**: Automatic HTML entity encoding for user input
- **Script Tag Removal**: Removal of potentially dangerous script tags
- **Attribute Sanitization**: Sanitization of HTML attributes
- **URL Validation**: Validation and sanitization of user-provided URLs

**SQL Injection Prevention**:
- **Parameterized Queries**: Use of parameterized queries for all database operations
- **Input Validation**: Strict input validation before database operations
- **Escape Sequence Handling**: Proper handling of escape sequences
- **Query Validation**: Validation of dynamic query construction

**Data Format Sanitization**:
- **Phone Number Normalization**: Standardization of phone number formats
- **Email Normalization**: Email address normalization and validation
- **Address Standardization**: Address format standardization
- **Name Normalization**: Proper name capitalization and formatting

## Performance Optimization Schemas

### Indexing Strategy

**Primary Indexes**: Strategic primary key selection for optimal performance:
- **UUID vs Sequential**: UUID primary keys for distributed systems
- **Composite Keys**: Multi-field primary keys for complex relationships
- **Natural vs Surrogate**: Business logic considerations for key selection

**Secondary Indexes**: Performance-optimized secondary indexes:
- **Query Pattern Analysis**: Index selection based on common query patterns
- **Composite Indexes**: Multi-field indexes for complex queries
- **Partial Indexes**: Conditional indexes for subset queries
- **Covering Indexes**: Indexes that include all required query fields

**Index Maintenance**: Automated index maintenance and optimization:
- **Index Usage Monitoring**: Tracking index usage and effectiveness
- **Automatic Index Creation**: Dynamic index creation based on query patterns
- **Index Cleanup**: Removal of unused or ineffective indexes
- **Performance Impact Analysis**: Analysis of index impact on write performance

### Query Optimization

**Query Pattern Optimization**: Optimized query patterns for common operations:

```typescript
interface OptimizedQueryPatterns {
  entityType: string;
  commonQueries: {
    [queryName: string]: {
      pattern: string;
      indexes: string[];
      estimatedPerformance: {
        averageTime: number;
        worstCaseTime: number;
        memoryUsage: number;
      };
      optimizationTips: string[];
    };
  };
}
```

**Lazy Loading Strategies**: Efficient data loading patterns:
- **Relationship Lazy Loading**: Load related objects only when accessed
- **Pagination**: Efficient pagination for large datasets
- **Virtual Scrolling**: Virtual scrolling for large lists
- **Progressive Loading**: Progressive loading of complex objects

**Caching Integration**: Schema-aware caching strategies:
- **Entity-Level Caching**: Caching complete entities with automatic invalidation
- **Query Result Caching**: Caching query results with dependency tracking
- **Computed Property Caching**: Caching expensive computed properties
- **Cross-Entity Caching**: Caching relationships between entities

## Cross-Reference Integration

### Related Documentation

- **[Storage Architecture](./storage.md)**: Comprehensive storage layer architecture and design patterns
- **[Application Architecture](./app-architecture.md)**: Overall system architecture and component relationships
- **[Data Synchronization](./sync.md)**: Detailed synchronization strategies and conflict resolution
- **[Security Implementation](./security.md)**: Security policies and encryption strategies
- **[Performance Optimization](./performance.md)**: Performance tuning and monitoring strategies

### Schema Evolution

**Migration Strategies**: Comprehensive schema migration framework:

**Backward Compatibility**: Maintains compatibility during schema transitions:
- **Graceful Degradation**: Older app versions continue to function with new schemas
- **Field Addition**: New optional fields don't break existing functionality
- **Default Value Handling**: Proper default values for new required fields
- **Deprecation Warnings**: Clear deprecation warnings for removed fields

**Incremental Updates**: Gradual schema evolution without breaking changes:
- **Phased Rollouts**: Gradual schema changes across user base
- **Feature Flags**: Schema changes controlled by feature flags
- **A/B Testing**: Schema changes tested with subset of users
- **Rollback Capabilities**: Quick rollback of problematic schema changes

**Data Preservation**: Ensures no data loss during schema migrations:
- **Data Transformation**: Automatic data transformation during migrations
- **Backup Creation**: Automatic backups before schema changes
- **Validation Checks**: Post-migration data validation
- **Recovery Procedures**: Data recovery procedures for failed migrations

**Version Management**: Comprehensive version tracking for all schema changes:
- **Schema Versioning**: Detailed version tracking for all schema components
- **Migration Scripts**: Automated migration scripts for schema updates
- **Testing Framework**: Comprehensive testing for schema changes and migrations
- **Documentation Updates**: Automatic documentation updates for schema changes
- **Change Approval**: Formal approval process for schema modifications
