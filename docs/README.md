# Perkd Application

<!-- This document has been verified against the codebase - Last updated: February 27, 2024 -->

## Table of Contents
- [Overview](#overview)
- [Architecture](#architecture)
- [Core Components](#core-components)
  - [Cards](#cards)
  - [Offers](#offers)
  - [Rewards](#rewards)
  - [Notifications](#notifications)
  - [Messages](#messages)
  - [Payments](#payments)
  - [Engagement](#engagement)
- [Cross-Cutting Concerns](#cross-cutting-concerns)
- [Getting Started](#getting-started)
- [Developer Guidelines](#developer-guidelines)
- [Additional Resources](#additional-resources)

## Overview

Perkd is a comprehensive mobile loyalty and rewards platform that enables businesses to engage with their customers through digital loyalty cards, personalized offers, rewards, and targeted communications. The platform is designed to enhance customer retention and drive engagement through a feature-rich, modular architecture that adapts to various merchant needs and regional requirements.

Key features of the Perkd platform include:

- **Digital Loyalty Cards**: Virtual cards that replace traditional paper or plastic loyalty cards
- **Multi-payment Integration**: Support for various payment methods across different regions
- **Personalized Offers**: Customized promotional offers delivered to users based on preferences and behaviors
- **Rewards Programs**: Configurable loyalty programs with points, tiers, and redemption options
- **Rich Notifications**: Multi-channel notification system to keep users informed of new offers and rewards
- **Merchant Dashboard**: Backend tools for merchants to manage their loyalty programs
- **Cross-platform Support**: Consistent experience across iOS and Android devices

The platform is built using React Native with a modular architecture that emphasizes separation of concerns and reusability.

## Architecture

Perkd follows a modular architecture centered around the Card as the primary organizational unit:

```mermaid
graph TD
    User[User] --> CardList[Card List]
    CardList --> CardController[Card Controller]
    CardController --> CardModel[Card Model]
    CardController --> WidgetFramework[Widget Framework]
    CardController --> NavigationSystem[Navigation System]
    
    WidgetFramework --> OfferWidget[Offer Widget]
    WidgetFramework --> RewardWidget[Reward Widget]
    WidgetFramework --> MessageWidget[Message Widget]
    WidgetFramework --> ShareWidget[Share Widget]
    WidgetFramework --> PaymentWidget[Payment Widget]
    
    OfferWidget --> OfferController[Offer Controller]
    RewardWidget --> RewardController[Reward Controller]
    MessageWidget --> MessageController[Message Controller]
    ShareWidget --> ShareController[Share Controller]
    PaymentWidget --> PaymentController[Payment Controller]
    
    CardModel --> SyncService[Sync Service]
    OfferController --> OfferModel[Offer Model]
    RewardController --> RewardModel[Reward Model]
    MessageController --> MessageModel[Message Model]
    PaymentController --> PaymentService[Payment Service]
    
    NotifyController[Notify Controller] --> User
    EngageController[Engage Controller] --> User
    
    subgraph "Core Infrastructure"
        SyncService
        EventSystem[Event System]
        APILayer[API Layer]
    end
    
    OfferModel --> SyncService
    RewardModel --> SyncService
    MessageModel --> SyncService
    
    OfferController -.-> EventSystem
    RewardController -.-> EventSystem
    MessageController -.-> EventSystem
    CardController -.-> EventSystem
    NotifyController -.-> EventSystem
    EngageController -.-> EventSystem
    
    SyncService --> APILayer
    APILayer --> Backend[Backend Services]
```

Key architectural elements include:

- **Card-Centric Design**: Cards serve as the primary organizing principle
- **Widget Framework**: Extensible system for dynamically loading and displaying content within cards
- **Controller Pattern**: Business logic separated into controller classes
- **Event System**: Decoupled component communication via events
- **Navigation Framework**: Flexible navigation system supporting various presentation modes
- **Modular Services**: Service-oriented approach for cross-cutting functionality

For a detailed architecture overview, see the [Architecture Documentation](Architecture.md).

## Core Components

Perkd is composed of several core components that work together to provide a cohesive user experience.

### Cards

The Card component is the central feature of Perkd, providing users with digital access to their loyalty cards, membership passes, and rewards. It manages the display, organization, and functionality of users' cards.

Key features:
- Multiple view modes (Big Card and Small Card views)
- Sorting and categorization capabilities
- Widget framework for extended functionality
- Card lifecycle management

[Learn more about Cards](Card.md)

### Offers

The Offer component manages promotional offers, discounts, vouchers, and tickets, enabling merchants to create engagement opportunities with users.

Key features:
- Multiple offer types (discounts, vouchers, tickets)
- Redemption flow with security measures
- Offer sharing capabilities
- Status tracking and lifecycle management

[Learn more about Offers](Offer.md)

### Rewards

The Rewards Management System provides a comprehensive loyalty framework for managing customer rewards, stamp cards, point collections, and multi-level engagement programs. It enables merchants to create structured reward programs with progressive levels, customizable stamps, and completion incentives.

Key features:
- Multi-level loyalty programs with progressive engagement
- Flexible stamp collection mechanisms (purchase-based, visit-based, action-based)
- Automatic offer issuance upon level completion
- Real-time progress tracking and visual feedback
- Comprehensive transaction system with audit trails
- Integration with shopping, notification, and card systems

[Learn more about Rewards](rewards.md)

### Notifications

The Notify component provides comprehensive notification management, enabling both local and remote notifications to alert users of important events.

Key features:
- Remote (push) notification support
- Local notification scheduling
- Notification center for history
- Banner notifications for in-app alerts

[Learn more about Notifications](Notify.md)

### Messages

The Message component enables in-app communications between merchants and users, supporting various content types and user interactions.

Key features:
- Message categorization and organization
- Rich content display
- Read status tracking
- Integration with the widget framework

[Learn more about Messages](Message.md)

### Payments

The Payment component enables secure and flexible payment processing throughout the application, supporting multiple payment methods and providers.

Key features:
- Support for credit/debit cards and digital wallets
- Integration with multiple payment providers (Stripe, MyPAY, etc.)
- Stored value payment options
- Secure credential management

[Learn more about Payments](payments.md)

### Engagement

The Engage component proactively notifies users of important information and available actions, filtering engagement candidates through eligibility rules.

Key features:
- Multiple engagement presentation types
- Priority-based queuing
- Integration with multiple content sources
- Do Not Disturb (DND) integration

[Learn more about Engagement](engages.md)

## Cross-Cutting Concerns

Several components address cross-cutting concerns across the application:

- **Authentication**: Secure user authentication and session management
- **Localization**: Multi-language support and region-specific content
- **Analytics**: User behavior tracking and performance metrics
- **Security**: Data encryption, biometric authentication, and secure storage
- **Performance**: Optimized loading, caching, and resource management
- **Accessibility**: Features to support users with different abilities

## Getting Started

For developers new to the Perkd codebase:

1. Review the [Architecture Documentation](Architecture.md) for a system overview
2. Explore the core component documentation to understand specific functionality
3. Familiarize yourself with the [directory structure](#architecture) and organization
4. Follow the development setup guide in the project root README
5. Refer to specific component documentation when working on features

## Developer Guidelines

When contributing to the Perkd codebase:

- Follow the established patterns for each component
- Maintain separation between models, controllers, and views
- Use the event system for component communication
- Implement proper cleanup in destruction methods
- Write comprehensive tests for new functionality
- Document APIs using the standard format

## Additional Resources

- [API Documentation](api/)
- [Action System Reference](actions/)
- [Applet Framework Guide](applet/)
- [Reception](reception.md)
- [Party](party.md) 