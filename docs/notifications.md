# Perkd Notification Infrastructure Design

## Overview

The Perkd notification infrastructure serves as a comprehensive communication backbone that enables real-time user engagement across multiple channels and platforms. This sophisticated system orchestrates the delivery of contextual, personalized notifications through push notifications, local alerts, in-app banners, and notification center management. The infrastructure integrates deeply with the application's core systems including cards, offers, rewards, engagement engine, and navigation to provide seamless user experiences that drive loyalty program participation and business value.

The notification system follows a multi-layered architecture that emphasizes reliability, performance, and user experience while supporting complex business logic for personalized engagement strategies. It handles everything from simple promotional alerts to sophisticated location-based triggers and cross-device synchronization.

## Technology Stack

### Core Notification Technologies
- **Cross-Platform Framework**: Notifee 7.8.2 for unified notification handling
- **iOS Push Notifications**: Apple Push Notification Service (APNs) via @react-native-community/push-notification-ios
- **Android Push Notifications**: Firebase Cloud Messaging (FCM) via @react-native-firebase/messaging
- **Chinese Android Support**: JPush for devices without Google services
- **Local Notifications**: Notifee with timestamp-based triggers
- **Badge Management**: Platform-specific badge count management
- **Sound Integration**: Custom sound playback via react-native-sound

### Integration Dependencies
- **Actions System**: Deep integration with the centralized action execution framework
- **Engagement Engine**: Contextual engagement triggers and user journey orchestration
- **Navigation System**: Deep linking and route-based notification handling
- **Sync Engine**: Background synchronization triggered by notifications
- **Analytics**: Comprehensive tracking and user behavior analysis
- **Permissions**: Platform-specific permission management and request handling

## Architectural Patterns

### 1. Multi-Provider Architecture

The notification system employs a sophisticated provider pattern that abstracts platform-specific implementations while maintaining a unified API:

#### Provider Abstraction Layer
- **Base Provider** (`src/lib/common/notify/providers/base.js`): Common functionality and interface definition
- **Platform-Specific Providers**: Specialized implementations for each notification service
- **Dynamic Provider Selection**: Runtime provider selection based on platform and region
- **Unified Handler Interface**: Consistent notification handling across all providers

#### Provider Implementations
- **APNs Provider** (`src/lib/common/notify/providers/apns.js`): iOS-specific implementation with rich notification support
- **FCM Provider** (`src/lib/common/notify/providers/fcm.js`): Android Firebase Cloud Messaging integration
- **JPush Provider** (`src/lib/common/notify/providers/jpush.js`): Chinese Android market support

### 2. Event-Driven Notification Processing

The system uses a sophisticated event-driven architecture for notification lifecycle management:

```javascript
// Notification handling pipeline
function handle(notification, tapped, initial) {
    // 1. Parse and validate notification data
    // 2. Execute synchronization if required
    // 3. Display banner for foreground notifications
    // 4. Trigger engagement flows
    // 5. Execute associated actions
    // 6. Handle navigation routing
}
```

### 3. Layered Integration Architecture

The notification infrastructure integrates with multiple application layers:

- **Presentation Layer**: Banner notifications, notification center UI, badge management
- **Business Logic Layer**: Action execution, engagement triggers, navigation routing
- **Data Layer**: Notification persistence, sync coordination, analytics tracking
- **Infrastructure Layer**: Provider management, permission handling, device registration

## Core Components and Responsibilities

### Notification Service (`src/lib/common/notify.js`)

The central notification service coordinates all notification activities and serves as the primary interface for the rest of the application:

**Key Responsibilities:**
- **Initialization Management**: Coordinates startup of local and remote notification systems
- **Provider Orchestration**: Manages platform-specific provider selection and lifecycle
- **Notification Processing**: Handles incoming notifications through unified processing pipeline
- **State Management**: Tracks notification delivery, handling, and user interactions
- **Integration Coordination**: Orchestrates interactions with Actions, Engagement, and Navigation systems

**Core Functions:**
- `init()`: Initializes both local and remote notification systems
- `handle()`: Processes notifications through the complete lifecycle pipeline
- `RemoteNotify`: Manages push notification registration, delivery, and handling
- `LocalNotify`: Handles local notification scheduling and management
- `Badge`: Manages application badge counts across platforms

### Provider System

The provider system implements platform-specific notification handling while maintaining a consistent interface:

#### Base Provider (`src/lib/common/notify/providers/base.js`)
- **Registration Management**: Handles notification permission requests and device token management
- **Token Management**: Coordinates device token storage and synchronization
- **Common Interface**: Provides consistent API for all platform-specific implementations

#### Platform-Specific Implementations

**APNs Provider** (`src/lib/common/notify/providers/apns.js`):
- **iOS Integration**: Uses `PushNotificationIOS` from React Native Community
- **Rich Notifications**: Handles notification attachments for rich notifications with images and media
- **Foreground Presentation**: Manages foreground presentation options (list, badge, banner, sound)
- **Badge Management**: iOS-specific badge count management through platform APIs
- **Registration Handling**: Manages device token registration and permission requests

**FCM Provider** (`src/lib/common/notify/providers/fcm.js`):
- **Firebase Integration**: Uses Firebase Cloud Messaging for standard Android devices
- **Notification Channels**: Implements notification channels for Android 8+ devices
- **Big Picture Style**: Handles big picture style for image notifications
- **Custom Styling**: Custom icons, importance levels, and notification appearance
- **Background Handling**: Efficient background message handling and processing

**JPush Provider** (`src/lib/common/notify/providers/jpush.js`):
- **Chinese Market Support**: Special implementation for devices without Google services
- **Custom Parsing**: Custom parsing for JPush-specific notification format
- **Alternative Registration**: Alternative registration flow for device tokens
- **Fallback Mechanism**: Provides notification delivery when FCM is unavailable

### Notification Center and UI Components

The notification center provides a comprehensive interface for users to review and manage their notification history:

#### Notification Widget (`src/lib/widgets/Notify.js`)
- **Data Management**: Retrieves and manages notification data from the Action model
- **UI Coordination**: Coordinates with the Notify controller for user interactions
- **Lifecycle Management**: Handles widget initialization, updates, and cleanup

#### Notification Controller (`src/controllers/Notify.js`)
- **User Interactions**: Manages notification tapping, removal, and bulk operations
- **Action Execution**: Coordinates action execution when users interact with notifications
- **Navigation Integration**: Handles navigation routing from notification interactions

#### UI Components (`src/containers/Notify/Widget.js`)
- **Swipe Interactions**: Implements swipe-to-delete functionality for individual notifications
- **Visual Presentation**: Renders notification content with logos, text, and metadata
- **Accessibility Support**: Provides accessible interaction patterns for notification management

### Badge Management System (`src/lib/badges.js`)

The badge management system provides sophisticated badge counting and synchronization:

**Card-Level Badges:**
- **Individual Card Tracking**: Maintains unread counts for each loyalty card
- **Aggregation Logic**: Calculates total unread counts across all cards
- **Cache Management**: Implements efficient caching for badge calculations

**Application-Level Badges:**
- **Platform Integration**: Updates app icon badge counts on iOS
- **Persistence**: Maintains badge state across app launches
- **Synchronization**: Coordinates badge updates with notification delivery

## Notification Types and Delivery Mechanisms

### Remote Notifications (Push Notifications)

Remote notifications provide real-time communication from the server to user devices:

#### Delivery Pipeline
1. **Server Generation**: Backend systems generate notification payloads
2. **Provider Routing**: Notifications are routed through appropriate push notification services
3. **Device Delivery**: Platform-specific services deliver notifications to devices
4. **App Reception**: Application receives notifications through provider-specific handlers
5. **Processing Pipeline**: Notifications are processed through the unified handling system

#### Notification Structure
```javascript
{
  title: 'Notification Title',
  subtitle: 'Optional Subtitle',
  body: 'Notification message body',
  image: 'https://example.com/image.jpg',
  sound: 'notification.wav',
  data: {
    action: { object: 'card', action: 'view', data: { cardId: '123' } },
    nav: { route: ['Cards', 'CardDetail'], engage: { object: 'offer', param: 'offerId' } },
    options: { banner: true, sync: 'cache' }
  }
}
```

#### Platform-Specific Features

**iOS Rich Notifications (APNs)**:
- **Rich Media Support**: Support for images, videos, and interactive elements
- **Notification Attachments**: Automatic download and attachment of images to notifications
- **Foreground Presentation**: Configurable presentation options for foreground notifications
- **Badge Management**: Native iOS badge count management and synchronization
- **Sound Integration**: Custom notification sounds with iOS-specific audio handling

````javascript
// iOS notification configuration
if (IOS) {
    const foregroundPresentationOptions = {
        list: true,
        badge: true,
        banner: true,
        sound: !!sound,
    };

    notification.ios = { foregroundPresentationOptions };

    if (sound) notification.ios.sound = sound;

    // Rich notification attachment handling
    if (image) {
        const imagePath = await Download.image(image);
        if (imagePath) {
            const id = generateId(),
                extension = File.extension(imagePath),
                newPath = `${PATHS.NOTIFICATIONS}/${id}.${extension}`;

            await File.copy(imagePath, newPath)
                .then(() => { notification.ios.attachments = [{ url: newPath }]; });
        }
    }
}
````

**Android Notification Channels (FCM)**:
- **Channel Organization**: Organized notification categories with user-configurable settings
- **Importance Levels**: Configurable importance levels for different notification types
- **Big Picture Style**: Support for large image notifications with custom styling
- **Custom Icons**: Custom notification icons and branding elements
- **Sound and Vibration**: Platform-specific sound and vibration patterns

````javascript
// Android notification configuration
if (ANDROID) {
    notification.android = {
        channelId: sound ? ALERTS : PERKD,
        smallIcon: 'notify_icon',
        largeIcon: 'ic_launcher',
        importance: AndroidImportance.HIGH,
        color: COLOR.accent,
        pressAction: { id: 'default' },
    };

    // Big picture style for images
    if (image) {
        const isPrivate = Image.isPrivate(image),
            picture = isPrivate ? File.path(await Download.image(image)) : image;

        if (picture) {
            Object.assign(notification.android, {
                largeIcon: picture,
                style: {
                    type: AndroidStyle.BIGPICTURE,
                    largeIcon: null,
                    picture,
                },
            });
        }
    }
}
````

**Chinese Android Compatibility (JPush)**:
- **Alternative Delivery**: Alternative delivery mechanisms for devices without Google services
- **Custom Parsing**: JPush-specific notification format parsing and handling
- **Registration Flow**: Alternative device token registration for Chinese market
- **Fallback Support**: Seamless fallback when primary notification services are unavailable

### Local Notifications

Local notifications provide time-based and event-driven alerts generated by the application:

#### Scheduling Mechanisms
- **Timestamp Triggers**: Schedule notifications for specific future times using Notifee's TriggerType.TIMESTAMP
- **Recurring Patterns**: Support for repeating notification schedules
- **Conditional Logic**: Smart scheduling based on user behavior and preferences

#### Implementation Details
The `LocalNotify` object provides comprehensive local notification management:

````javascript
export const LocalNotify = {
    init: async () => {
        notifee.onForegroundEvent(Notifee.handle);
        notifee.onBackgroundEvent(Notifee.handle);

        if (DEVICE.ANDROID_MIN_8) {
            CHANNELS.forEach(channel => {
                if (channel.id) notifee.createChannel(channel);
            });
        }
    },

    schedule: ({ title, subtitle, body, image, data = {}, date }) => {
        const NOW = new Date().getTime();

        createNotification({ title, subtitle, body, image, data: { data: JSON.stringify(data) } }).then(notification => {
            const timestamp = date.getTime();

            if (NOW < timestamp) {
                const trigger = { type: TriggerType.TIMESTAMP, timestamp };
                notifee.createTriggerNotification(notification, trigger);
            }
        });
    }
};
````

#### Scheduled Notification Management
The system provides comprehensive functions for managing scheduled notifications:

- **`LocalNotify.getScheduled()`**: Retrieves all scheduled notifications with parsed data
- **`LocalNotify.cancel(ids)`**: Cancels specific notifications by ID array
- **`LocalNotify.cancelAll()`**: Cancels all scheduled notifications
- **`LocalNotify.removeDelivered(ids)`**: Removes delivered notifications from notification center
- **`LocalNotify.removeAllDelivered()`**: Removes all delivered notifications

````javascript
getScheduled: () => notifee.getTriggerNotifications()
    .then(notifications => {
        const scheduled = notifications.filter(notification => Object.keys(notification || {}).length);

        return scheduled.map(({ notification }) =>
            ({ ...notification, data: JSON.parse(notification?.data?.data || '{}') }));
    }),

cancel: (ids) => notifee.cancelTriggerNotifications(ids),

cancelAll: () => notifee.cancelAllNotifications(),

removeDelivered: (ids) => notifee.cancelDisplayedNotifications(ids),

removeAllDelivered: () => notifee.getDisplayedNotifications()
    .then(notifications => RemoteNotify.removeDelivered(notifications.map(n => n.id))),
````

#### Use Cases
- **Reminder Notifications**: Card usage reminders and offer expiration alerts
- **Location-Based Triggers**: Proximity-based notifications for relevant offers
- **Engagement Prompts**: Periodic engagement to maintain user activity
- **Time-Based Alerts**: Scheduled notifications for specific events or deadlines

### In-App Banner Notifications

Banner notifications provide immediate feedback when the app is in the foreground:

#### Display Logic
Banner notifications appear when specific conditions are met during notification processing:

````javascript
// Banner display conditions: app in foreground, banner option enabled, not tapped
if (options.banner && inApp && !tapped) {
    const bannerData = { data: JSON.stringify(merge({}, data, { options: { banner: false } })) };

    createNotification({ title, subtitle, body, image, sound, data: bannerData }).then(notify => {
        notifee.displayNotification(notify);

        // play sound on Android
        if (ANDROID && sound) Sound.play(sound.split('.')[0]);
    });
}
````

#### Implementation Features
- **Foreground Detection**: Automatically displays banners when app is active (`AppState.currentState !== BACKGROUND`)
- **Conditional Display**: Appears when `options.banner && inApp && !tapped` conditions are met
- **Sound Integration**: Plays notification sound on Android via `Sound.play()`
- **Data Preservation**: Contains reference to original notification data for user interaction handling
- **Non-Intrusive Design**: Overlays that don't interrupt user workflow
- **Interactive Elements**: Tap-to-action functionality for immediate engagement

#### Visual Design
- **Consistent Styling**: Matches application design language and theming
- **Rich Content**: Support for images, custom styling, and branding
- **Animation Support**: Smooth entrance and exit animations
- **Platform Adaptation**: Uses `notifee.displayNotification()` for cross-platform consistency

### Notification Center

The notification center provides a centralized location for notification history and management:

#### Implementation Architecture
The notification center is implemented through a coordinated system of controllers, widgets, and data models:

````javascript
export default class Notify {
    constructor(param, callbacks = {}) {
        const self = this;

        self.param = param;
        self.data = _.Action.findNotify();  // Retrieve notification data
        self.callbacks = callbacks;

        _.Action.on(EVENT.updated, self.onUpdated, self);
        _.Action.on(EVENT.deleted, self.onUpdated, self);
    }

    doAction(id, item, cloak) {
        const { action: notifyAction } = item.data?.data || {},
            { object = '', action = '' } = notifyAction;

        if (Actions.exists(object.toLowerCase(), action.toLowerCase())) {
            popToRoot(undefined, { animations: Animation.Stack.fadeOut }).then(() => {
                Actions.do(notifyAction);
            });
        }
    }

    remove(id) {
        return _.Action.update({ id, deletedAt: newDate() });
    }

    clearAll() {
        const NOW = newDate(),
            toUpdate = this.data.map(notice => ({ id: notice.id, deletedAt: NOW }));

        return _.Action.update(toUpdate);
    }
}
````

#### Data Source Integration
The notification center uses the Action model to retrieve and manage notification data:

````javascript
static findNotify() {
    const self = this,
        { notDeleted, notPurged } = QUERY,
        query = `object = "${ENGAGE}" && action = "${NOTIFY}" && ${notDeleted} && ${notPurged} && personId = "${_.Person.id}" ${SORT}`;

    return self.find(query, newDate());
}
````

#### Features
- **Historical View**: Complete history of received notifications via `_.Action.findNotify()`
- **Bulk Operations**: Clear all notifications via `clearAll()` method
- **Individual Management**: Remove specific notifications via `remove(id)` method
- **Action Replay**: Re-execute notification actions via `doAction()` method
- **Real-time Updates**: Automatic updates when notification data changes
- **Widget Integration**: Seamless integration with NotifyWidget UI component

## Data Flow Architecture

### User Experience Flow

The notification system supports multiple user journey entry points and interaction patterns:

```mermaid
graph TB
    subgraph "📱 Notification Entry Points"
        PUSH[Push Notification]
        LOCAL[Local Notification]
        BANNER[In-App Banner]
        CENTER[Notification Center]
    end

    subgraph "🎯 Processing Pipeline"
        RECEIVE[Notification Reception]
        PARSE[Data Parsing & Validation]
        SYNC[Sync Coordination]
        ENGAGE[Engagement Triggers]
    end

    subgraph "💳 Business Logic Integration"
        CARDS[Card System Integration]
        OFFERS[Offer & Reward Integration]
        ACTIONS[Action Execution]
        NAV[Navigation Routing]
    end

    subgraph "📊 User Experience Outcomes"
        CARDVIEW[Card Viewing]
        OFFERREDEEM[Offer Redemption]
        SHOPPING[Shopping Flow]
        ENGAGEMENT[User Engagement]
    end

    %% Entry Point Flow
    PUSH --> RECEIVE
    LOCAL --> RECEIVE
    BANNER --> RECEIVE
    CENTER --> ACTIONS

    %% Processing Flow
    RECEIVE --> PARSE
    PARSE --> SYNC
    SYNC --> ENGAGE

    %% Business Logic Flow
    ENGAGE --> CARDS
    ENGAGE --> OFFERS
    ENGAGE --> ACTIONS
    ACTIONS --> NAV

    %% Outcome Flow
    CARDS --> CARDVIEW
    OFFERS --> OFFERREDEEM
    NAV --> SHOPPING
    ACTIONS --> ENGAGEMENT

    %% Styling with darker backgrounds and white text
    classDef entry fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef processing fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef business fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef outcome fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff

    class PUSH,LOCAL,BANNER,CENTER entry
    class RECEIVE,PARSE,SYNC,ENGAGE processing
    class CARDS,OFFERS,ACTIONS,NAV business
    class CARDVIEW,OFFERREDEEM,SHOPPING,ENGAGEMENT outcome
```

### Notification Processing Pipeline

The notification processing pipeline ensures reliable and consistent handling of all notification types:

1. **Reception**: Notifications are received through platform-specific providers
2. **Parsing**: Notification data is parsed and validated for required fields
3. **Deduplication**: System prevents duplicate processing using notification IDs
4. **Synchronization**: Optional data synchronization is triggered based on notification flags
5. **Banner Display**: Foreground banners are displayed for active app sessions
6. **Engagement Processing**: Engagement flows are triggered based on notification content
7. **Action Execution**: Associated actions are executed when users interact with notifications
8. **Navigation Routing**: Deep linking and navigation routing is handled for notification taps

### Integration Data Flow

The notification system integrates with multiple application components through well-defined data flow patterns:

#### Actions System Integration
- **Action Payload**: Notifications carry action definitions for execution
- **Execution Context**: Actions are executed with appropriate context and parameters
- **Result Handling**: Action results are processed and may trigger additional flows

#### Engagement System Integration
- **Engagement Triggers**: Notifications can trigger sophisticated engagement flows
- **Context Awareness**: Engagement flows receive notification context for personalization
- **Flow Coordination**: Multiple engagement flows can be chained from single notifications

#### Navigation System Integration
- **Route Definitions**: Notifications carry navigation route information
- **Deep Linking**: Support for complex deep linking scenarios
- **State Management**: Navigation state is properly managed during notification handling

## Business Logic Architecture

### Badge Management Business Logic

The notification infrastructure implements sophisticated badge management that serves as a critical business intelligence and user engagement mechanism. The badge system operates on multiple levels with complex business rules that balance user awareness with experience quality.

#### Card-Level Badge Calculation Rules

The badge calculation system implements multi-dimensional counting that reflects business priorities and user value:

**Valid vs Unread Distinction**: The system distinguishes between "valid" items (available for user interaction) and "unread" items (requiring user attention), enabling nuanced user experience management. This distinction allows the business to surface urgent items while maintaining awareness of available opportunities.

**Cross-Component Aggregation**: Badge counts aggregate across offers, rewards, messages, and widget data with specific business rules for each component type. This comprehensive approach ensures users have complete visibility into available value across their loyalty program portfolio.

**Active Card Filtering**: Badge calculations only include items from "active" cards, implementing complex business rules that consider:
- **Managed Card Status**: Cards with managed features require registration completion before contributing to badge counts
- **Digital Activation**: Cards must be digitally active (not expired, not hidden, properly registered) to generate badge notifications
- **Transfer State**: Cards in transfer states are excluded from badge calculations to prevent user confusion during state transitions

#### Attention Flag Business Rules

The system implements sophisticated attention flag logic that drives user engagement strategies:

**Expiration-Based Attention**: Cards approaching or past expiration automatically trigger attention flags, ensuring users are aware of time-sensitive loyalty program benefits before they lose value.

**Registration-Required Attention**: Cards requiring registration but not yet completed trigger attention flags, driving completion of onboarding flows that unlock full program benefits.

**Digital Activation Attention**: Cards that are not digitally active (due to various business rule violations) trigger attention flags, guiding users toward resolution actions that restore full functionality.

#### Cross-Platform Badge Synchronization

The badge management system implements business rules for consistent user experience across platforms:

**iOS Badge Count Management**: The system maintains iOS app icon badge counts through platform-specific APIs, ensuring users have immediate visibility into available actions even when the app is closed.

**Persistence Strategy**: Badge counts are persisted in UserDefaults to maintain business continuity across app launches, ensuring users don't lose awareness of pending actions due to technical interruptions.

**Real-Time Updates**: Badge counts update in real-time as users interact with offers, rewards, and messages, providing immediate feedback that reinforces engagement behaviors.

#### Performance-Optimized Badge Calculation

The badge system implements performance optimization strategies that balance accuracy with user experience:

**Cached Badge States**: Badge calculations are cached per card to avoid expensive recalculation, with intelligent cache invalidation based on data model changes.

**Event-Driven Updates**: Badge recalculation is triggered only by relevant data model events (card creation/update/deletion, offer/reward/message changes), minimizing unnecessary computation.

**Batch Processing**: Badge updates are processed in batches to optimize performance during bulk data operations while maintaining user experience responsiveness.

### Notification Categorization and Prioritization

The system implements sophisticated business logic for notification categorization and delivery prioritization:

#### Notification Categories
- **Transactional**: Order confirmations, payment receipts, account changes
- **Promotional**: Offers, rewards, special campaigns
- **Engagement**: Usage reminders, feature announcements, tips
- **System**: App updates, maintenance notifications, security alerts

#### Priority Levels
- **Critical**: Security alerts, payment issues, account problems
- **High**: Time-sensitive offers, expiring rewards, urgent reminders
- **Normal**: Regular promotional content, engagement prompts
- **Low**: General announcements, feature tips, optional content

### Advanced Personalization and Targeting Business Logic

The notification system implements sophisticated personalization and targeting mechanisms that optimize business value delivery while respecting user preferences and context.

#### Card Portfolio-Based Relevance Filtering

The system implements intelligent filtering based on user's specific loyalty card portfolio, ensuring notifications deliver maximum business value:

**Active Card Filtering**: Notifications are only delivered for cards that are currently active and digitally functional. This business rule ensures users don't receive notifications for cards they cannot use, preventing frustration and maintaining engagement quality.

**Widget-Based Relevance**: The system checks card master widget configurations to determine notification relevance (`master.hasWidget(undefined, key)`), ensuring notifications align with the specific features and capabilities available for each user's cards.

**Transfer State Awareness**: Cards in transfer states are excluded from notification targeting, preventing user confusion during card lifecycle transitions and ensuring notifications remain actionable.

#### User Preference Integration and Compliance

The notification system implements comprehensive user preference management that balances business objectives with user autonomy:

**PreferenceReminders Model**: The system maintains detailed user preferences for card and offer reminders through the PreferenceReminders data model, allowing granular control over notification categories and timing.

**Opt-out Compliance**: The system implements strict compliance with user opt-out preferences across different notification categories, ensuring business communications respect user choices while maintaining opportunities for value delivery.

**Channel Preference Honoring**: Users can specify preferred notification channels, and the system respects these preferences while ensuring critical business communications still reach users through appropriate channels.

#### Dynamic Content Adaptation

The notification system implements intelligent content adaptation that optimizes engagement effectiveness:

**Context-Aware Content**: Notification content adapts based on current user context, including app state, location, time of day, and recent user activity patterns, ensuring maximum relevance and engagement potential.

**Card-Specific Messaging**: Notification content is tailored to the specific characteristics and capabilities of user's loyalty cards, ensuring messaging is relevant and actionable for their particular program participation.

**Engagement History Integration**: The system considers user's historical engagement patterns when crafting notification content, optimizing for communication styles and timing that have proven effective for individual users.

#### Behavioral Segmentation and Targeting

The system implements sophisticated behavioral analysis that drives targeted notification strategies:

**Usage Pattern Analysis**: The system analyzes app usage patterns and engagement history to optimize notification timing and content for individual users, maximizing the likelihood of positive engagement.

**Loyalty Program Participation**: Targeting considers user's loyalty program participation level and tier status, ensuring notifications are appropriate for their current engagement level and available benefits.

**Geographic and Temporal Targeting**: The system considers user location and timing preferences to optimize notification delivery for maximum relevance and minimal disruption.

#### Business Value Optimization Through Personalization

The personalization system implements business rules designed to maximize value delivery:

**Opportunity Prioritization**: When multiple notification opportunities exist, the system prioritizes based on business value, user preference, and engagement likelihood, ensuring the most valuable opportunities receive priority attention.

**Cross-Card Optimization**: The system optimizes notifications across user's entire card portfolio, preventing notification fatigue while ensuring comprehensive coverage of available opportunities.

**Engagement Quality Focus**: Personalization rules prioritize engagement quality over quantity, recognizing that meaningful interactions drive better business outcomes than high-frequency low-value communications.

### User Experience Protection Business Logic

The notification infrastructure implements sophisticated user experience protection mechanisms through the Do Not Disturb (DND) system, which serves as a critical business rule engine for maintaining user experience quality while preserving engagement opportunities.

#### Context-Aware Notification Blocking

The DND system implements intelligent blocking rules that protect user experience during critical application flows:

**Engagement Flow Protection**: When users are actively engaged in critical flows (card registration, offer redemption, payment processing), the DND system blocks interrupting notifications to prevent user confusion and flow abandonment. This business rule recognizes that interrupting high-value user actions reduces conversion rates and overall user satisfaction.

**Navigation State Awareness**: The system checks DND lock status before executing navigation routing from notifications (`if (nav && tapped && !$.DND.locked() && !eng)`), ensuring that users aren't jarred out of their current context by notification-triggered navigation. This preserves user intent and reduces abandonment of in-progress tasks.

**Exclusive Lock Management**: The DND system supports exclusive locks that prevent any interruptions during critical operations, with automatic cleanup mechanisms that ensure locks don't persist indefinitely and block legitimate user interactions.

#### Location-Based Experience Optimization

The DND system implements location-aware business rules that adapt notification behavior based on user context:

**Personal Place Recognition**: The system maintains awareness of user's personal locations (home, work) and adapts notification behavior accordingly, reducing notification frequency in private spaces where users may prefer less interruption.

**DND Position Registry**: The system maintains a registry of locations where users have previously indicated preference for reduced notifications, learning from user behavior to improve future experience quality.

**Proximity-Based Adaptation**: Location-based DND rules consider proximity to known locations, implementing buffer zones that account for GPS accuracy and user movement patterns.

#### Business Continuity Through DND Management

The DND system implements business rules that ensure user experience protection doesn't compromise business value delivery:

**Automatic Lock Release**: DND locks are automatically released when engagements complete or fail, ensuring that temporary protection doesn't become permanent blocking that reduces business value delivery.

**Context Tracking**: The system maintains detailed context information for DND locks, enabling debugging and optimization of protection rules while ensuring business operations remain transparent.

**Queue Management**: Rather than simply blocking notifications during DND periods, the system intelligently queues them for appropriate timing, ensuring business value is preserved while respecting user context.

#### Engagement Quality Assurance

The DND system implements business rules specifically designed to maintain engagement quality:

**Flow Completion Priority**: The system prioritizes completion of in-progress user flows over new notification delivery, recognizing that successful completion of current actions is more valuable than starting new ones.

**User Intent Preservation**: DND rules are designed to preserve user intent and context, ensuring that notification delivery enhances rather than disrupts the user's current objectives.

**Experience Continuity**: The system maintains experience continuity by preventing jarring transitions and context switches that could reduce user satisfaction and engagement effectiveness.

### Business Rules and Logic

The notification system implements complex business rules that govern notification delivery and behavior:

#### Delivery Rules
- **Frequency Limits**: Maximum notifications per day/week/month per category
- **Quiet Hours**: Respect user-defined quiet hours and do-not-disturb settings
- **Channel Preferences**: Honor user preferences for notification channels
- **Opt-out Compliance**: Respect user opt-out preferences for specific categories

#### Content Rules
- **Relevance Filtering**: Only deliver notifications relevant to user's cards and interests
- **Expiration Handling**: Automatic cleanup of expired notification content
- **Duplicate Prevention**: Prevent duplicate notifications for the same content
- **Context Awareness**: Adapt notification content based on current user context

### Notification Intelligence and State Management

The notification system implements advanced intelligence mechanisms that optimize user engagement while preventing notification fatigue through sophisticated state tracking and behavioral analysis.

#### Time-Based Notification Windows

The system employs carefully calibrated time-based business rules that balance engagement persistence with user experience quality:

**5-Minute Engagement Windows**: The notification system uses 5-minute windows for engagement tracking, a duration determined through user behavior research that balances legitimate retry scenarios with notification fatigue prevention. This timing reflects the business understanding that shorter windows miss valid user interaction patterns while longer windows create engagement fatigue that reduces overall effectiveness.

**Expiration-Based Cleanup**: Notification states automatically expire and clean up after their business-relevant timeframes, ensuring the system doesn't accumulate stale state that could affect future engagement decisions.

**Window Overlap Management**: The system manages overlapping notification windows intelligently, preventing multiple notifications for the same engagement opportunity while allowing legitimate separate engagements to proceed.

#### Sophisticated Duplicate Prevention

The notification system implements multi-layered duplicate prevention that serves critical business functions:

**Reception Tracking**: The RECEIVED array tracks notification delivery to prevent duplicate processing of the same notification payload, ensuring users don't receive multiple instances of the same business communication.

**Handling State Management**: The HANDLED array prevents duplicate handling of user interactions, ensuring that single user actions don't trigger multiple business processes that could create confusion or system inconsistency.

**ID-Based Deduplication**: The system uses notification IDs for precise deduplication, allowing legitimate similar notifications while preventing exact duplicates that would degrade user experience.

#### Engagement Registry and Retry Logic

The system maintains sophisticated tracking of engagement attempts that serves critical business intelligence functions:

**Attempted Engagement Tracking**: The system tracks previously attempted engagements to prevent infinite retry loops while ensuring legitimate opportunities aren't lost. This business rule balances persistence in delivering value with respect for user preferences and system stability.

**Model-Specific Notification Logic**: Different data models (offers, rewards, messages) implement specific notification checking logic that reflects their unique business characteristics and user interaction patterns.

**Intelligent Retry Strategies**: The system implements intelligent retry strategies that consider user behavior patterns, engagement history, and business value to optimize retry timing and frequency.

#### Business Intelligence Through State Tracking

The notification state management system provides critical business intelligence capabilities:

**Engagement Effectiveness Measurement**: By tracking notification delivery, user interaction, and engagement completion, the system provides insights into notification effectiveness and user engagement patterns.

**User Behavior Analysis**: State tracking enables analysis of user notification interaction patterns, informing optimization of notification timing, content, and delivery strategies.

**System Performance Monitoring**: State management provides visibility into notification system performance, enabling identification of bottlenecks and optimization opportunities that affect business value delivery.

## Performance Optimization

### Notification Delivery Optimization

The system implements comprehensive performance optimizations for reliable and efficient notification delivery:

#### Caching Strategies
- **Image Caching**: Notification images are downloaded and cached for offline display
- **Content Caching**: Notification content is cached to reduce processing overhead
- **Template Caching**: Notification templates are cached for rapid rendering

#### Background Processing
- **Efficient Parsing**: Optimized notification parsing to minimize processing time
- **Batch Operations**: Bulk notification operations for improved efficiency
- **Resource Management**: Careful management of system resources during notification processing

#### Memory Management
- **Cleanup Procedures**: Automatic cleanup of notification resources and temporary files
- **Memory Monitoring**: Active monitoring of memory usage during notification processing
- **Resource Pooling**: Efficient reuse of resources across notification operations

### User Experience Optimization

The system prioritizes user experience through various optimization strategies:

#### Response Time Optimization
- **Immediate Feedback**: Instant visual feedback for user interactions
- **Progressive Loading**: Progressive loading of notification content and images
- **Smooth Animations**: Hardware-accelerated animations for notification transitions

#### Battery Life Considerations
- **Efficient Scheduling**: Optimized scheduling to minimize battery impact
- **Background Limits**: Intelligent limits on background notification processing
- **Power-Aware Operations**: Adaptation of notification behavior based on device power state

## Security Architecture

### Data Protection and Privacy

The notification system implements comprehensive security measures to protect user data and privacy:

#### Data Encryption
- **In-Transit Encryption**: All notification data is encrypted during transmission
- **At-Rest Encryption**: Sensitive notification data is encrypted when stored locally
- **Key Management**: Secure key management for encryption operations

#### Privacy Protection
- **Data Minimization**: Only necessary data is included in notification payloads
- **Anonymization**: Personal data is anonymized where possible
- **Consent Management**: User consent is properly managed for notification delivery

#### Access Control
- **Permission Validation**: Strict validation of notification permissions
- **Token Security**: Secure management of device tokens and authentication credentials
- **Audit Logging**: Comprehensive logging of notification operations for security monitoring

### Threat Mitigation

The system implements various measures to mitigate security threats:

#### Input Validation
- **Payload Validation**: Strict validation of notification payloads to prevent injection attacks
- **Content Sanitization**: Sanitization of notification content to prevent XSS attacks
- **Size Limits**: Enforcement of size limits to prevent denial-of-service attacks

#### Authentication and Authorization
- **Token Validation**: Validation of device tokens and authentication credentials
- **Permission Checks**: Verification of permissions before notification delivery
- **Rate Limiting**: Rate limiting to prevent abuse and spam

## Error Handling Architecture

### Comprehensive Error Management

The notification system implements robust error handling to ensure reliable operation:

#### Error Classification
- **Network Errors**: Connection failures, timeouts, service unavailability
- **Permission Errors**: Missing or revoked notification permissions
- **Validation Errors**: Invalid notification data or malformed payloads
- **System Errors**: Device limitations, storage issues, processing failures

#### Error Recovery Strategies
- **Automatic Retry**: Intelligent retry mechanisms with exponential backoff
- **Graceful Degradation**: Fallback mechanisms when primary notification channels fail
- **User Communication**: Clear communication of error states to users
- **Logging and Monitoring**: Comprehensive error logging for debugging and monitoring

#### Resilience Patterns
- **Circuit Breaker**: Protection against cascading failures in notification delivery
- **Timeout Management**: Appropriate timeouts to prevent hanging operations
- **Resource Limits**: Limits on resource usage to prevent system overload
- **Health Monitoring**: Continuous monitoring of notification system health

## Developer Guidelines

### Implementation Best Practices

When extending or modifying the notification infrastructure, developers should follow these established patterns:

#### Notification Design Principles
- **User-Centric Design**: Always prioritize user experience and value
- **Contextual Relevance**: Ensure notifications are relevant to user context and interests
- **Respectful Timing**: Respect user preferences for notification timing and frequency
- **Clear Value Proposition**: Clearly communicate the value of each notification

#### Technical Implementation Guidelines
- **Provider Abstraction**: Use the provider abstraction layer for platform-specific functionality
- **Error Handling**: Implement comprehensive error handling for all notification operations
- **Performance Monitoring**: Monitor performance impact of notification operations
- **Security Compliance**: Follow security best practices for data handling and transmission

#### Testing Strategies
- **Cross-Platform Testing**: Test notification functionality across all supported platforms
- **Edge Case Testing**: Test edge cases including network failures and permission changes
- **Performance Testing**: Test notification performance under various load conditions
- **User Experience Testing**: Test the complete user experience flow for notifications

### Integration Patterns

#### Adding New Notification Types
1. **Define Notification Schema**: Create clear schema for new notification types
2. **Implement Business Logic**: Add business logic for notification generation and targeting
3. **Update UI Components**: Modify UI components to handle new notification types
4. **Test Integration**: Thoroughly test integration with existing systems

#### Extending Provider Support
1. **Implement Provider Interface**: Create new provider implementing the base provider interface
2. **Add Platform Detection**: Update platform detection logic for new provider
3. **Configure Provider Selection**: Update provider selection logic
4. **Test Provider Functionality**: Comprehensive testing of new provider functionality

## Architecture Diagrams

### Notification Infrastructure Overview

The following diagram illustrates the complete notification infrastructure architecture and component relationships:

```mermaid
graph TB
    subgraph "🌐 External Notification Services"
        APNS[Apple Push Notification Service<br/>iOS Devices]
        FCM[Firebase Cloud Messaging<br/>Android Devices]
        JPUSH[JPush Service<br/>Chinese Android]
        BACKEND[Backend Notification API<br/>Server-Generated Notifications]
    end

    subgraph "📱 Device-Level Notification Providers"
        APNS_PROV[APNs Provider<br/>src/lib/common/notify/providers/apns.js]
        FCM_PROV[FCM Provider<br/>src/lib/common/notify/providers/fcm.js]
        JPUSH_PROV[JPush Provider<br/>src/lib/common/notify/providers/jpush.js]
        BASE_PROV[Base Provider<br/>Common Functionality]
    end

    subgraph "🎯 Core Notification Engine"
        NOTIFY_SERVICE[Notification Service<br/>src/lib/common/notify.js]
        REMOTE_NOTIFY[RemoteNotify<br/>Push Notification Handler]
        LOCAL_NOTIFY[LocalNotify<br/>Local Notification Scheduler]
        NOTIFEE[Notifee Integration<br/>Cross-Platform Handler]
    end

    subgraph "🎨 User Interface Layer"
        BANNER[Banner Notifications<br/>Foreground Display]
        NOTIFY_CENTER[Notification Center<br/>History & Management]
        BADGE_MGR[Badge Management<br/>App Icon Badges]
        NOTIFY_WIDGET[Notification Widget<br/>src/lib/widgets/Notify.js]
    end

    subgraph "🔧 Business Logic Integration"
        ACTIONS[Actions System<br/>Action Execution]
        ENGAGE[Engagement Engine<br/>User Journey Triggers]
        NAV[Navigation System<br/>Deep Linking & Routing]
        SYNC[Sync Engine<br/>Data Synchronization]
    end

    subgraph "💾 Data & Storage Layer"
        ACTION_MODEL[Action Model<br/>Notification Persistence]
        PREFERENCES[User Preferences<br/>Notification Settings]
        ANALYTICS[Analytics Tracking<br/>Notification Metrics]
        CACHE[Notification Cache<br/>Performance Optimization]
    end

    %% External Service Connections
    BACKEND --> APNS
    BACKEND --> FCM
    BACKEND --> JPUSH

    %% Provider Connections
    APNS --> APNS_PROV
    FCM --> FCM_PROV
    JPUSH --> JPUSH_PROV
    APNS_PROV --> BASE_PROV
    FCM_PROV --> BASE_PROV
    JPUSH_PROV --> BASE_PROV

    %% Core Engine Connections
    BASE_PROV --> NOTIFY_SERVICE
    NOTIFY_SERVICE --> REMOTE_NOTIFY
    NOTIFY_SERVICE --> LOCAL_NOTIFY
    NOTIFY_SERVICE --> NOTIFEE

    %% UI Layer Connections
    NOTIFY_SERVICE --> BANNER
    NOTIFY_SERVICE --> NOTIFY_CENTER
    NOTIFY_SERVICE --> BADGE_MGR
    NOTIFY_CENTER --> NOTIFY_WIDGET

    %% Business Logic Connections
    NOTIFY_SERVICE --> ACTIONS
    NOTIFY_SERVICE --> ENGAGE
    NOTIFY_SERVICE --> NAV
    NOTIFY_SERVICE --> SYNC

    %% Data Layer Connections
    ACTIONS --> ACTION_MODEL
    NOTIFY_CENTER --> ACTION_MODEL
    BADGE_MGR --> PREFERENCES
    NOTIFY_SERVICE --> ANALYTICS
    BANNER --> CACHE

    %% Styling with darker backgrounds and white text
    classDef external fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef provider fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef core fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef ui fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef business fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff
    classDef data fill:#d69e2e,stroke:#ed8936,stroke-width:2px,color:#ffffff

    class APNS,FCM,JPUSH,BACKEND external
    class APNS_PROV,FCM_PROV,JPUSH_PROV,BASE_PROV provider
    class NOTIFY_SERVICE,REMOTE_NOTIFY,LOCAL_NOTIFY,NOTIFEE core
    class BANNER,NOTIFY_CENTER,BADGE_MGR,NOTIFY_WIDGET ui
    class ACTIONS,ENGAGE,NAV,SYNC business
    class ACTION_MODEL,PREFERENCES,ANALYTICS,CACHE data
```

### Notification Processing Flow

This diagram shows the complete notification processing pipeline from reception to user interaction:

```mermaid
sequenceDiagram
    participant Server as Backend Server
    participant PNS as Push Notification Service<br/>(APNs/FCM/JPush)
    participant Device as Device OS
    participant Provider as Notification Provider
    participant Engine as Notification Engine
    participant Actions as Actions System
    participant Engage as Engagement Engine
    participant UI as User Interface
    participant User as User

    %% Notification Generation and Delivery
    Server->>PNS: Send notification payload
    PNS->>Device: Deliver push notification
    Device->>Provider: Notification received
    Provider->>Engine: Handle notification

    %% Processing Pipeline
    Engine->>Engine: Parse notification data
    Engine->>Engine: Check for duplicates

    alt Sync Required
        Engine->>Actions: Trigger data sync
        Actions-->>Engine: Sync complete
    end

    alt App in Foreground
        Engine->>UI: Display banner notification
        UI->>User: Show in-app banner
    else App in Background
        Engine->>Device: Display system notification
        Device->>User: Show system notification
    end

    %% User Interaction
    User->>UI: Tap notification
    UI->>Engine: Process user interaction

    alt Has Engagement Flow
        Engine->>Engage: Trigger engagement
        Engage->>User: Show engagement content
    end

    alt Has Action
        Engine->>Actions: Execute action
        Actions->>UI: Update interface
        UI->>User: Show action result
    end

    alt Has Navigation
        Engine->>UI: Navigate to route
        UI->>User: Show target screen
    end

    %% Styling with darker background
    %%{init: {'theme':'dark'}}%%
```

### User Experience Flow Diagram

This diagram illustrates the various user experience paths through the notification system:

```mermaid
graph TB
    subgraph "📱 Notification Entry Points"
        PUSH_ENTRY[Push Notification Tap<br/>System Notification]
        BANNER_ENTRY[Banner Tap<br/>In-App Notification]
        CENTER_ENTRY[Notification Center<br/>Historical Access]
        LOCAL_ENTRY[Local Notification<br/>Scheduled Alert]
    end

    subgraph "🎯 Notification Processing"
        PARSE_DATA[Parse Notification Data<br/>Extract Actions & Navigation]
        CHECK_CONTEXT[Check User Context<br/>App State & Location]
        VALIDATE_PERMS[Validate Permissions<br/>User Preferences]
    end

    subgraph "💳 Card-Related Flows"
        CARD_VIEW[View Card Details<br/>Digital Wallet Display]
        CARD_SCAN[Scan New Card<br/>Registration Flow]
        CARD_SHARE[Share Card<br/>Social Sharing]
        CARD_REMIND[Card Reminders<br/>Usage Prompts]
    end

    subgraph "🎁 Offer & Reward Flows"
        OFFER_VIEW[View Offer Details<br/>Terms & Conditions]
        OFFER_REDEEM[Redeem Offer<br/>In-Store/Online]
        REWARD_TRACK[Track Rewards<br/>Points & Progress]
        REWARD_REDEEM[Redeem Rewards<br/>Loyalty Benefits]
    end

    subgraph "🛒 Commerce Flows"
        SHOP_BROWSE[Browse Products<br/>Catalog Navigation]
        SHOP_CART[Add to Cart<br/>Shopping Management]
        SHOP_CHECKOUT[Checkout Flow<br/>Payment Processing]
        ORDER_TRACK[Order Tracking<br/>Fulfillment Status]
    end

    subgraph "🎮 Engagement Flows"
        ENGAGE_SURVEY[User Surveys<br/>Feedback Collection]
        ENGAGE_TIPS[Feature Tips<br/>App Education]
        ENGAGE_SOCIAL[Social Features<br/>Community Engagement]
        ENGAGE_GAMIFY[Gamification<br/>Achievements & Badges]
    end

    subgraph "⚙️ System Flows"
        SETTINGS[App Settings<br/>Preferences Management]
        SUPPORT[Customer Support<br/>Help & Tickets]
        UPDATES[App Updates<br/>Feature Announcements]
        SECURITY[Security Alerts<br/>Account Protection]
    end

    %% Entry Point Routing
    PUSH_ENTRY --> PARSE_DATA
    BANNER_ENTRY --> PARSE_DATA
    CENTER_ENTRY --> PARSE_DATA
    LOCAL_ENTRY --> PARSE_DATA

    %% Processing Flow
    PARSE_DATA --> CHECK_CONTEXT
    CHECK_CONTEXT --> VALIDATE_PERMS

    %% Flow Routing Based on Content
    VALIDATE_PERMS --> CARD_VIEW
    VALIDATE_PERMS --> OFFER_VIEW
    VALIDATE_PERMS --> SHOP_BROWSE
    VALIDATE_PERMS --> ENGAGE_SURVEY
    VALIDATE_PERMS --> SETTINGS

    %% Card Flow Connections
    CARD_VIEW --> CARD_SCAN
    CARD_VIEW --> CARD_SHARE
    CARD_VIEW --> CARD_REMIND

    %% Offer Flow Connections
    OFFER_VIEW --> OFFER_REDEEM
    OFFER_REDEEM --> REWARD_TRACK
    REWARD_TRACK --> REWARD_REDEEM

    %% Commerce Flow Connections
    SHOP_BROWSE --> SHOP_CART
    SHOP_CART --> SHOP_CHECKOUT
    SHOP_CHECKOUT --> ORDER_TRACK

    %% Engagement Flow Connections
    ENGAGE_SURVEY --> ENGAGE_TIPS
    ENGAGE_TIPS --> ENGAGE_SOCIAL
    ENGAGE_SOCIAL --> ENGAGE_GAMIFY

    %% System Flow Connections
    SETTINGS --> SUPPORT
    SUPPORT --> UPDATES
    UPDATES --> SECURITY

    %% Cross-Flow Connections
    OFFER_REDEEM --> SHOP_CHECKOUT
    CARD_VIEW --> OFFER_VIEW
    REWARD_REDEEM --> SHOP_CART

    %% Styling with darker backgrounds and white text
    classDef entry fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef processing fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef card fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef offer fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef commerce fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff
    classDef engagement fill:#d69e2e,stroke:#ed8936,stroke-width:2px,color:#ffffff
    classDef system fill:#4a5568,stroke:#718096,stroke-width:2px,color:#ffffff

    class PUSH_ENTRY,BANNER_ENTRY,CENTER_ENTRY,LOCAL_ENTRY entry
    class PARSE_DATA,CHECK_CONTEXT,VALIDATE_PERMS processing
    class CARD_VIEW,CARD_SCAN,CARD_SHARE,CARD_REMIND card
    class OFFER_VIEW,OFFER_REDEEM,REWARD_TRACK,REWARD_REDEEM offer
    class SHOP_BROWSE,SHOP_CART,SHOP_CHECKOUT,ORDER_TRACK commerce
    class ENGAGE_SURVEY,ENGAGE_TIPS,ENGAGE_SOCIAL,ENGAGE_GAMIFY engagement
    class SETTINGS,SUPPORT,UPDATES,SECURITY system
```

## Implementation Patterns

### State Management Architecture

The notification system implements sophisticated state management to ensure reliable operation and prevent duplicate processing:

#### Notification Tracking Arrays
The system uses dedicated arrays to track notification states:

````javascript
const HANDLED = [],
    RECEIVED = [];

function handled(notificationId) {
    return HANDLED.includes(notificationId);
}

// In notification processing pipeline
if (!RECEIVED.includes(dataId)) {
    $.Event.emit(EVENT.received, { id, title, subtitle, body, image, sound, data, inApp, tapped });
    RECEIVED.push(dataId);
}
````

#### State Management Components
- **Notification Registry**: Tracks delivered notifications through `RECEIVED` array
- **Handled Tracking**: Prevents duplicate handling via `HANDLED` array
- **Active State**: Controls notification processing through `RemoteNotify.active` flag
- **Token Management**: Device tokens managed through the `Tokens` class from `common/installation`
- **Permission Tracking**: Notification permission state tracked through the permissions system

#### Token Management Integration
The system integrates with the installation token management system:

````javascript
export const Tokens = {
    get: () => ({ ...CACHED.tokens }),

    set: (name, token) => {
        if (CACHED.notifyToken === token) return;

        CACHED.tokens[name] = { token, modifiedAt: newDate() };
        refreshPermissions(NOTIFICATIONS);

        InstallationService({ tokens: CACHED.tokens })
            .then(() => {
                CACHED.notifyToken = token;
                persist();
            });
    },

    has: (name) => !!CACHED.tokens[name],
};
````

### Notification Data Models

The notification system uses sophisticated data models that integrate with the broader application architecture:

#### Action Model Integration
The notification system leverages the Action model for persistence and management:

````javascript
static findNotify() {
    const self = this,
        { notDeleted, notPurged } = QUERY,
        query = `object = "${ENGAGE}" && action = "${NOTIFY}" && ${notDeleted} && ${notPurged} && personId = "${_.Person.id}" ${SORT}`;

    return self.find(query, newDate());
}
````

#### Notification Persistence Schema
Notifications are persisted as Action records with specific object and action types:

````javascript
class Action extends Realm.Object {}
Action.schema = {
    name: 'Action',
    primaryKey: 'id',
    properties: {
        id: 'string',
        object: { type: 'string', indexed: true },
        action: { type: 'string', indexed: true },
        data: { type: 'string?', default: '{}' },
        triggers: { type: 'string?', default: '{}' },
        expiresAt: 'date?',
        createdAt: 'date',
        personId: 'string?',
        cardId: 'string?'
    }
};
````

### Provider Implementation Pattern

The provider pattern ensures consistent notification handling across platforms:

#### Base Provider Interface

````javascript
class NotifyProvider {
    registered = null

    constructor(handle, handled) {
        this.handle = handle;
        this.handled = handled;
    }

    register() {
        if (this.registered) return this.registered;

        this.registered = requestNotifications(['alert', 'sound'])
            .then(res => this.getToken()
                .then(deviceToken => {
                    if (deviceToken) this.setToken(deviceToken);
                }));

        return this.registered;
    }
}
````

#### Platform-Specific Implementation

````javascript
class FCM extends NotifyProvider {
    constructor(handle) {
        super(handle);
        this.provider = 'fcm';
    }

    init() {
        messaging().onNotificationOpenedApp(notice => this.handle(notice, true));
        messaging().onMessage(this.handle);
        messaging().setBackgroundMessageHandler(async () => Promise.resolve());
    }

    getToken() {
        return messaging().getToken();
    }
}
````

### Integration Implementation Patterns

#### Actions System Integration
The notification system integrates deeply with the Actions system for executing business logic:

````javascript
// Action execution when notification is tapped or app is active
if (action && (tapped || inApp)) {
    runAfterInteractions(() => {
        Actions.do(action)
            .then(res => {
                $.log(`[handleRemote] Action.do ${action.object}.${action.action} done`, 'Notify', 'action');
                return res;
            })
            .catch(err => { $.watch('[Notify]Action.do', err, { id, title, subtitle, body, action }); });
    });
}
````

#### Engagement System Integration
Notifications can trigger sophisticated engagement flows:

````javascript
const { route = [], engage = {} } = nav || {},
    { object, param } = engage,
    Model = object && _.className(object),
    eng = object && Model?.engage && Model.engage(param);

if (eng) Engage.first(eng);
````

#### Navigation System Integration
Deep linking and navigation routing from notifications:

````javascript
// Navigation routing when notification is tapped
if (nav && tapped && !$.DND.locked() && !eng) $.Nav.to(route);
````

## Configuration and Customization

### Notification Channels and Categories

The system supports sophisticated notification categorization for user control:

#### Android Notification Channels

````javascript
const CHANNELS = [
    { id: PERKD, name: PERKD, description: PERKD, importance: IMPORTANCE, sound: 'chord' },
    { id: ALERTS, name: ALERTS, description: ALERTS, importance: IMPORTANCE },
];

if (DEVICE.ANDROID_MIN_8) {
    CHANNELS.forEach(channel => {
        if (channel.id) notifee.createChannel(channel);
    });
}
````

#### User Preference Management
The system integrates with user preferences for notification control:

````javascript
class PreferenceReminders extends Realm.Object {}
PreferenceReminders.schema = {
    name: 'PreferenceReminders',
    properties: {
        id: 'string',
        card: 'PreferenceCardReminders',
        offer: 'PreferenceOfferReminders',
    },
};
````

### Localization and Internationalization

The notification system supports comprehensive localization:

#### Multi-Language Support
The system integrates with the application's i18n system for localized notification content:

````javascript
export const Language = {
    app: language,
    doubleByte: DOUBLE_BYTE.includes(language),
    i18n: I18n,

    translate(key, lang) {
        const t = getTranslation(lang);
        return key.split('.').reduce((res, k) => res?.[k], t || {});
    }
};
````

#### Dynamic Content Translation
Notification content can be dynamically translated based on user language preferences and device settings.

## Analytics and Business Intelligence Architecture

The notification infrastructure implements comprehensive analytics and business intelligence capabilities that provide critical insights for optimizing user engagement, measuring business impact, and driving strategic decision-making.

### Notification Effectiveness Measurement

The system implements sophisticated measurement mechanisms that quantify notification impact on business objectives:

#### Engagement Conversion Tracking

**Notification-to-Action Conversion**: The system tracks the complete user journey from notification delivery through action completion, providing precise measurement of notification effectiveness in driving desired business outcomes.

**Multi-Touch Attribution**: The notification system supports multi-touch attribution analysis, recognizing that users may interact with multiple notifications before completing high-value actions, enabling accurate assessment of notification contribution to business results.

**Engagement Quality Metrics**: Beyond simple conversion rates, the system measures engagement quality through metrics like time-to-action, completion rates, and subsequent user behavior patterns, providing insights into notification effectiveness in driving meaningful user engagement.

#### User Journey Analytics

**Cross-Channel Journey Mapping**: The system tracks user journeys that span multiple notification channels (push, local, banner, notification center), providing comprehensive understanding of how different notification types contribute to user engagement and business outcomes.

**Engagement Flow Analysis**: The system analyzes how notifications trigger engagement flows and measures the effectiveness of these flows in achieving business objectives, enabling optimization of engagement strategies.

**Retention Impact Measurement**: The notification system measures how notification strategies affect user retention, providing insights into the long-term business value of different notification approaches.

### Business Intelligence and Optimization

The notification system provides comprehensive business intelligence capabilities that support strategic decision-making:

#### Performance Analytics

**Delivery Success Rates**: The system tracks notification delivery success rates across different providers, platforms, and user segments, providing insights into technical performance and business impact.

**User Engagement Patterns**: Comprehensive analysis of user engagement patterns with notifications provides insights into optimal timing, frequency, and content strategies for different user segments.

**Cross-Platform Performance Comparison**: The system enables comparison of notification effectiveness across iOS, Android, and different market regions, supporting platform-specific optimization strategies.

#### Revenue and Business Impact Analytics

**Notification-Driven Revenue Attribution**: The system tracks revenue and business value generated through notification-driven user actions, providing direct measurement of notification ROI and business impact.

**Customer Lifetime Value Impact**: Analysis of how notification strategies affect customer lifetime value provides insights into the long-term business impact of engagement approaches.

**Cost-Effectiveness Analysis**: The system provides analytics on the cost-effectiveness of different notification strategies, enabling optimization of marketing spend and engagement investment.

### User Behavior Intelligence

The notification system implements advanced user behavior analysis that drives personalization and optimization:

#### Behavioral Segmentation Analytics

**Engagement Preference Analysis**: The system analyzes user preferences for different notification types, timing, and content, enabling sophisticated segmentation and personalization strategies.

**Notification Fatigue Detection**: Advanced analytics detect patterns indicating notification fatigue, enabling proactive adjustment of notification frequency and targeting to maintain engagement effectiveness.

**Response Pattern Analysis**: The system analyzes user response patterns to different notification strategies, providing insights for optimizing notification content, timing, and delivery methods.

#### Predictive Analytics

**Engagement Likelihood Prediction**: The system uses historical data to predict user likelihood of engaging with different types of notifications, enabling intelligent targeting and resource optimization.

**Optimal Timing Prediction**: Analysis of user behavior patterns enables prediction of optimal notification timing for individual users, maximizing engagement likelihood while respecting user preferences.

**Churn Risk Assessment**: The notification system contributes to churn risk assessment by analyzing user engagement patterns and notification response rates, enabling proactive retention strategies.

### Business Continuity and Resilience Architecture

The notification system implements comprehensive business continuity mechanisms that ensure reliable value delivery even under adverse conditions, while maintaining user experience quality and business operational integrity.

#### Provider Fallback and Market Adaptation

The system implements intelligent provider selection and fallback mechanisms that ensure business continuity across different markets and technical conditions:

**Market-Specific Provider Selection**: The system automatically selects appropriate notification providers based on market requirements (`!DEVICE.APP.IS_CN ? FCM : JPUSH`), ensuring business operations continue effectively in markets with different technical infrastructure requirements.

**Graceful Provider Degradation**: When primary notification providers fail, the system implements graceful degradation strategies that maintain core business functionality while working to restore full capability.

**Chinese Market Continuity**: The JPush provider implementation ensures business continuity in Chinese markets where Google services are unavailable, maintaining user engagement and business value delivery regardless of regional technical constraints.

#### Performance-Adaptive Business Rules

The notification system implements performance-aware business rules that adapt to device and network conditions while preserving business value:

**Device Performance Adaptation**: The system considers device performance characteristics (memory, processing capability) when making notification delivery decisions, ensuring business communications don't degrade user experience on lower-capability devices.

**Network-Aware Processing**: Notification processing adapts to network conditions, implementing intelligent retry strategies and content optimization that maintain business value delivery under varying connectivity conditions.

**Battery Conservation Balance**: The system balances notification delivery business requirements with device battery conservation, implementing intelligent scheduling that preserves user device usability while maintaining engagement effectiveness.

#### Error Recovery and Business Value Preservation

The notification system implements sophisticated error recovery mechanisms that prioritize business value preservation:

**Automatic Retry Strategies**: The system implements intelligent retry mechanisms with exponential backoff that balance persistence in delivering business value with respect for system resources and user experience.

**State Recovery Mechanisms**: When notification processing encounters errors, the system implements state recovery mechanisms that ensure business processes can continue from appropriate points without losing user context or business value.

**Fallback Communication Channels**: When primary notification channels fail, the system provides fallback mechanisms that ensure critical business communications still reach users through alternative channels.

#### System Health and Business Intelligence

The notification system implements comprehensive monitoring and intelligence gathering that supports business decision-making:

**Performance Monitoring**: The system continuously monitors notification delivery performance, providing business intelligence about engagement effectiveness and system reliability.

**Error Pattern Analysis**: Error tracking and analysis provide insights into system reliability patterns, enabling proactive optimization that prevents business value loss.

**User Experience Impact Assessment**: The system tracks how technical issues affect user experience and business outcomes, enabling data-driven optimization of resilience strategies.

### Strategic Business Integration

The notification infrastructure serves as a critical component of the overall business strategy, implementing sophisticated integration patterns that align technical capabilities with business objectives.

#### Loyalty Program Optimization

**Program Engagement Amplification**: The notification system amplifies loyalty program engagement by intelligently surfacing relevant opportunities, rewards, and benefits at optimal moments in the user journey, maximizing program participation and business value.

**Cross-Program Synergy**: The system enables synergy across multiple loyalty programs by intelligently coordinating notifications and engagement flows, preventing program conflicts while maximizing overall user engagement and business value.

**Lifecycle Management**: Notification strategies adapt to different stages of the customer lifecycle, from onboarding new users to re-engaging dormant customers, ensuring appropriate communication strategies for each business relationship stage.

#### Revenue Optimization Through Intelligent Engagement

**Purchase Intent Amplification**: The notification system identifies and amplifies purchase intent signals through targeted notifications that guide users toward high-value actions at optimal moments.

**Cross-Selling and Upselling**: Intelligent notification targeting enables sophisticated cross-selling and upselling strategies that increase customer value while maintaining user experience quality.

**Retention Revenue Protection**: The system implements proactive retention strategies through intelligent notification campaigns that identify and address churn risk before revenue impact occurs.

#### Business Process Integration

**Operational Efficiency Enhancement**: The notification system enhances operational efficiency by automating customer communication workflows, reducing manual intervention requirements while maintaining personalization and relevance.

**Customer Service Integration**: Notifications integrate with customer service processes, providing proactive communication that reduces support burden while improving customer satisfaction.

**Marketing Campaign Amplification**: The system amplifies marketing campaign effectiveness by providing targeted, personalized delivery mechanisms that increase campaign reach and engagement rates.

## Performance Optimization

The notification system implements comprehensive performance optimizations for reliable and efficient notification delivery:

### Image Caching and Resource Management
The notification system includes sophisticated image handling and caching:

````javascript
// iOS rich notification image handling
if (image) {
    try {
        const imagePath = await Download.image(image);

        if (imagePath) {
            const id = generateId(),
                extension = File.extension(imagePath),
                newPath = `${PATHS.NOTIFICATIONS}/${id}.${extension}`;

            // copy as addAttachment() cleanup image
            await File.copy(imagePath, newPath)
                .then(() => { notification.ios.attachments = [{ url: newPath }]; })
                .catch(err => { $.watch('[Notify]createNotification', err, { title, subtitle, body, image, imagePath, newPath }); });
        }
    } catch (err) {
        $.watch('[Notify]createNotification', err, { title, subtitle, body, image });
    }
}
````

### Performance Optimization Features
- **Image Caching**: Downloads and caches notification images through `Download.image()` for offline display
- **File Management**: Proper cleanup of notification resources when no longer needed
- **Channel Management**: Efficient notification channel configuration on Android 8+ devices
- **Background Handling**: Optimized background notification processing
- **Resource Cleanup**: Automatic cleanup of temporary files in `${PATHS.NOTIFICATIONS}/` directory

### Caching Strategies
- **Content Caching**: Notification content is cached to reduce processing overhead
- **Template Caching**: Notification templates are cached for rapid rendering
- **Image Optimization**: Downloaded images are optimized and stored in temporary notification directory

### Background Processing
- **Efficient Parsing**: Optimized notification parsing to minimize processing time
- **Batch Operations**: Bulk notification operations for improved efficiency
- **Resource Management**: Careful management of system resources during notification processing

### Memory Management
- **Cleanup Procedures**: Automatic cleanup of notification resources and temporary files
- **Memory Monitoring**: Active monitoring of memory usage during notification processing
- **Resource Pooling**: Efficient reuse of resources across notification operations

## Developer Guidelines and Best Practices

When extending or modifying the notification infrastructure, developers should follow these established patterns:

### Adding New Notification Actions

1. **Define notification data structure**:
```javascript
{
  title: 'Notification Title',
  subtitle: 'Optional Subtitle',
  body: 'Notification message body text',
  image: 'https://example.com/image.jpg', // Optional
  sound: 'soundfile.wav', // Optional
  data: {
    action: {
      object: 'yourObject',
      action: 'yourAction',
      data: { /* Action parameters */ }
    },
    nav: {
      route: ['RouteA', 'RouteB'],
      engage: { /* Optional engagement object */ }
    },
    options: { banner: true, sync: 'cache' }
  }
}
```

2. **Register remote notification listeners**:
```javascript
// Initialize notification system
import { init as initNotify } from 'common/notify';

// Call during app initialization
initNotify();
```

3. **Schedule local notifications**:
```javascript
import { LocalNotify } from 'common/notify';

// Schedule a notification for a future time
LocalNotify.schedule({
  title: 'Reminder',
  body: 'Don\'t forget to check your new offers',
  data: {
    action: { object: 'offer', action: 'list' }
  },
  date: new Date(Date.now() + 3600000) // 1 hour from now
});
```

### Best Practices

- **Permission Checks**: Always check notification permissions before attempting to schedule local notifications
- **Fallback Actions**: Include fallback actions for when users tap notifications without context
- **Frequency Management**: Limit the number and frequency of notifications to avoid user fatigue
- **Rich Content Optimization**: Use rich notifications (with images) sparingly and ensure images are optimized
- **Error Handling**: Implement proper error handling for failed notification deliveries
- **Testing Coverage**: Test notifications in both foreground and background app states
- **Localization**: Ensure notification text is localized through the app's localization system
- **Performance Monitoring**: Monitor the performance impact of notification operations
- **Resource Cleanup**: Implement proper cleanup of notification resources and temporary files

### Platform-Specific Considerations

#### iOS Rich Notifications
- **Attachment Handling**: Properly manage notification attachments and file cleanup
- **Foreground Presentation**: Configure appropriate foreground presentation options
- **Badge Management**: Use iOS-specific APIs for badge count management

#### Android Notification Channels
- **Channel Configuration**: Create appropriate notification channels for Android 8+ devices
- **Importance Levels**: Set appropriate importance levels for different notification types
- **Custom Styling**: Implement big picture style for image notifications

#### Chinese Android (JPush)
- **Alternative Registration**: Implement alternative registration flow for devices without Google services
- **Custom Parsing**: Handle JPush-specific notification format parsing
- **Fallback Mechanisms**: Provide fallback mechanisms when primary notification channels fail

## Summary

The Perkd notification infrastructure represents a sophisticated, multi-layered system that serves as the communication backbone for user engagement across the loyalty platform. This comprehensive business logic analysis reveals an architecture that emphasizes:

### Technical Excellence
- **Comprehensive Coverage**: Support for push notifications, local alerts, in-app banners, and notification center management
- **Cross-Platform Reliability**: Unified handling across iOS, Android, and Chinese Android markets through provider abstraction
- **Deep Integration**: Seamless integration with Actions, Engagement, Navigation, and Sync systems
- **Performance Optimization**: Efficient processing, caching, and resource management
- **Security and Privacy**: Comprehensive data protection and privacy compliance
- **Scalability**: Modular architecture supporting future enhancements and business requirements

### Business Intelligence and Logic
- **Sophisticated Badge Management**: Multi-dimensional badge calculation with attention flags and cross-platform synchronization
- **User Experience Protection**: Advanced DND system with context-aware blocking and business continuity mechanisms
- **Notification Intelligence**: Time-based windows, duplicate prevention, and engagement registry for optimal user experience
- **Advanced Personalization**: Card portfolio-based filtering, user preference compliance, and behavioral targeting
- **Business Continuity**: Provider fallback mechanisms, performance adaptation, and error recovery strategies
- **Analytics and Optimization**: Comprehensive measurement, user behavior analysis, and predictive capabilities

### Strategic Business Value
- **Loyalty Program Optimization**: Intelligent engagement amplification and cross-program synergy
- **Revenue Optimization**: Purchase intent amplification, cross-selling capabilities, and retention protection
- **Operational Efficiency**: Automated workflows, customer service integration, and marketing amplification
- **Data-Driven Decision Making**: Business intelligence gathering and performance analytics
- **User-Centric Design**: Experience quality preservation while maximizing business value delivery

This notification infrastructure design provides a robust foundation for user engagement while maintaining the flexibility needed for evolving business requirements and technological advances. The system successfully balances technical sophistication with business logic complexity and user experience priorities, creating a notification platform that drives loyalty program participation, revenue optimization, and long-term customer value while respecting user preferences and maintaining experience quality.

The comprehensive business logic architecture ensures that every notification serves a strategic purpose, contributes to measurable business outcomes, and enhances rather than detracts from the user experience. This approach positions the notification system as a critical business asset that drives engagement, retention, and revenue while maintaining the trust and satisfaction of the user base.
