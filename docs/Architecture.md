# Perkd Application Architecture

## Table of Contents
- [Overview](#overview)
- [Core Architecture](#core-architecture)
- [Key Components](#key-components)
- [Component Organization](#component-organization)
- [Data Flow](#data-flow)
- [Events](#events)
- [Navigation](#navigation)
- [Storage](#storage)
- [Error Handling](#error-handling)
- [Internationalization](#internationalization)
- [Security Architecture](#security-architecture)
- [Performance Optimization](#performance-optimization)
- [Styling Approach](#styling-approach)
- [Implementation Patterns](#implementation-patterns)
- [Integration Points](#integration-points)
- [Guidelines for Developers](#guidelines-for-developers)

## Overview

Perkd is a React Native mobile application designed for customer loyalty and rewards management. The application architecture follows a modular approach with clear separation between UI components, business logic controllers, and shared utilities. This document describes the high-level architecture of the system, outlining key structures, patterns, and relationships between components.

## Core Architecture

The Perkd application follows a modular architecture with these key components:

```mermaid
graph TD
    A[Application Initialization: perkd.js] --> B[Controllers]
    A --> C[Navigation]
    A --> D[Events]
    A --> E[Background Tasks]
    
    B --> F[UI Components]
    B --> G[Services]
    
    G --> H[Storage Services]
    H --> I[AsyncStorage]
    H --> J[Realm Database]
    H --> K[Secure Storage]
    
    G --> M[Remote API]
    G --> N[Files & Resources]
    
    O[Common Utilities] --> B
    O --> G
    
    B <--> P[Actions Layer]
    D <--> B
    D <--> P
    
    Q[Permissions] --> A
    Q --> B
    
    R[Security Services] --> H
    R --> M
```

### Application Layer
- **Entry Point**: `perkd.js` - Initializes the application, sets up navigation, event listeners, and core services
- **Application State**: Manages global state and lifecycle events
- **Background Tasks**: Handles operations that run in the background like sync, housekeeping

### Controller Layer
- Implements business logic for different features
- Acts as an intermediary between UI components and data services
- Controllers are organized by feature domains (Cards, Shop, Profile, etc.)
- Both consumes events and uses the Actions layer to perform operations

### Component Layer
- React Native UI components structured by feature
- Follows a component-based architecture for reusability
- Components receive data and callback functions from controllers

### Services Layer
- Core functionality shared across the application
- Contains data services, utilities, models, and API clients
- Provides abstractions for device capabilities and platform APIs

### Actions Layer
- Provides a unified API for all business operations
- Standardizes how controllers communicate with services
- Enables consistent error handling and event propagation
- Can be triggered by controllers or in response to events

### Event System
- Provides communication between different parts of the application
- Allows components to subscribe to and react to system and user events
- Connects with both Controllers and the Actions layer
- Enables loose coupling between application components

## Key Components

### Card Management
The core functionality revolves around card management, with several interconnected subsystems:
- **Card Listing**: Browsing and filtering available cards
- **Card Details**: Viewing and interacting with individual cards
- **Card Categories**: Organization and filtering of cards

### Shop System
Enables users to browse and purchase items:
- **Product Catalog**: Browsing available products
- **Shopping Cart**: Managing selected items
- **Checkout Process**: Completing purchases

### Profile & Account Management
Handles user information and preferences:
- **User Profile**: Managing personal information
- **Preferences**: User settings and configurations
- **Authentication**: User identity and access control

### Messaging System
Manages communications within the app:
- **Notifications**: User alerts and updates
- **In-app Messages**: Communications within the application

### Places & Location
Location-based features and place management:
- **Place Discovery**: Finding locations
- **Location Services**: Geolocation capabilities
- **Place Details**: Information about specific locations

## Component Organization

Components are organized by feature domains:

```
src/components/
  ├── Bag/         # Shopping bag components
  ├── Card/        # Card-related components
  ├── Discover/    # Discovery feature components
  ├── Message/     # Messaging components
  ├── Offer/       # Promotional offers components
  ├── Place/       # Location-based components
  ├── Profile/     # User profile components
  ├── Reward/      # Rewards system components
  └── Shop/        # Shopping components
```

Each feature directory contains related components that work together to implement the feature's UI.

## Data Flow

The application follows a unidirectional data flow pattern:

1. **Data Services** (in common/services) fetch and process data
2. **Controllers** access services, transform data, and manage state
3. **Components** receive data from controllers via props
4. **User Interactions** trigger callbacks defined in controllers
5. **State Updates** are managed by controllers and propagated to components

This pattern provides a predictable data flow that's easier to debug and maintain.

## Events

The Perkd application uses a centralized event system for communication between components and services:

### Event Architecture
- **Event Registry**: Centralized definition of all application events (`lib/Events.json`)
- **Event Emitter**: System for publishing and subscribing to events
- **Event Tracking**: Analytics and monitoring of user actions and system events

### Key Event Types
- **Application Events**: Lifecycle events like startup, background, foreground
- **User Action Events**: User-initiated actions like clicks, swipes, selections
- **Data Events**: Changes to application data like updates, deletions
- **System Events**: Device-level events like network status, permissions changes

### Implementation
The event system is initialized in the application startup sequence and provides:
- **Namespaced Events**: Events are grouped by feature (Card, Shop, Place, etc.)
- **Tracking Integration**: Events are automatically tracked for analytics
- **Synchronization**: Events can trigger data synchronization and UI updates

## Navigation

The application uses a custom navigation system built on `react-native-navigation`:

- **Tab-based Navigation**: Main app sections accessible via bottom tabs
- **Stack Navigation**: Screen stacks within each tab section
- **Modal Navigation**: Overlay screens for focused interactions
- **Deep Linking**: URL scheme support for external navigation

The navigation system is centralized in `lib/navigator.js` and provides a unified API for screen transitions, animations, and navigation state management.

## Storage

The Perkd application uses a specialized multi-tiered storage architecture to meet various requirements for data persistence, performance, and security.

### 1. Core Storage Mechanisms

| Storage Layer | Technology | Primary Use | Security Level |
|---------------|------------|-------------|----------------|
| **Key-Value Store** | AsyncStorage | App configuration, preferences | Standard |
| **Object Database** | Realm | Structured application data | Standard |
| **Secure Storage** | Keychain API | Credentials, tokens, payment info | Encrypted |
| **In-Memory Cache** | JavaScript objects | Temporary data, performance optimization | Transient |

### 2. Data Access Layers

#### Persistence Layer (`common/persist.js`)
Manages simple key-value storage needs using AsyncStorage:
- Handles application settings, user preferences, and session state
- Provides type-aware serialization/deserialization
- Implements memory caching for frequently accessed values
- Used for configuration that doesn't require relational queries

#### Database Layer (`common/db.js`, `common/storage.js`)
Manages structured data using Realm database:
- Defines schemas for all application models (card data, user data, etc.)
- Supports primary keys, relationships, and complex queries
- Enables transactions for data integrity
- Provides an abstraction layer for database operations through `storage.js`

#### Secure Storage Layer (`common/credentials.js`)
Handles sensitive information using device-specific secure storage:
- Stores credentials, tokens, and payment information
- Organized by domain (App, CardMaster, Card, Payment)
- Provides encrypted storage inaccessible to other applications
- Leverages the cryptography service for data encryption/decryption

### 3. Data Synchronization

The Synchronization Layer (`common/sync.js`) coordinates data between local storage and remote services:
- Manages background synchronization scheduling
- Handles conflict resolution
- Provides offline capabilities
- Implements optimistic updates for better UX

### 4. Storage Architecture Benefits

This multi-layered approach provides several advantages:

1. **Appropriate Storage Selection**: Uses the right storage mechanism for each data type
   - Transactional data → Realm
   - Configuration → AsyncStorage
   - Sensitive data → Secure Keychain

2. **Performance Optimization**: Each storage type is optimized for its specific use case
   - Fast access to simple settings via AsyncStorage
   - Efficient queries for complex data via Realm
   - Cached access for frequently used data

3. **Security**: Different security levels for different data sensitivity
   - Highest security for credentials and payment data
   - Standard security for regular application data

4. **Maintainability**: Clean separation of concerns with specialized APIs for each storage type

## Error Handling

The Perkd application implements a comprehensive error handling strategy:

### Error Classification
- **Network Errors**: Connection issues, timeouts, API failures
- **Authentication Errors**: Invalid credentials, expired tokens
- **Validation Errors**: Invalid user input, format violations
- **Application Errors**: Internal logic failures
- **External Service Errors**: Third-party API failures

### Error Handling Patterns

#### Centralized Error Definitions
- Error codes and messages centralized in `common/Errors.json`
- Consistent error format across the application
- Localized error messages for user display

#### Error Propagation
- Errors are wrapped and enriched as they propagate up the call stack
- Error transformation utilities in `common/utils/core.js`
- Error context is preserved for debugging

#### User-Facing Error Handling
- Network errors are handled consistently with retry options
- Authentication errors trigger re-authentication flows
- Validation errors provide clear feedback to users
- Critical errors are logged for analytics

## Internationalization

Perkd implements a robust internationalization (i18n) system to support multiple languages and locales:

### Architecture
- **Translation System**: Based on `i18n-js` and `react-native-localize`
- **Language Detection**: Automatic detection of device locale
- **Fallback System**: Graceful fallback to supported languages
- **Resource Management**: Language resources organized by locale

### Supported Languages
- English (en)
- Simplified Chinese (zh-Hans)
- Traditional Chinese (zh-Hant)
- Hong Kong Traditional Chinese (zh-Hant-HK)
- Japanese (ja)
- Korean (ko)
- Bahasa Melayu (ms)
- Bahasa Indonesia (id)

### Implementation Details
- Translation files stored in `lib/i18n/[locale].json`
- Localized string access via `$.L()` helper function
- Automatic formatting of dates, times, and numbers based on locale
- Special handling for double-byte languages in UI layouts

## Security Architecture

Perkd implements multiple layers of security to protect user data and ensure secure operations:

### Permission System
- Fine-grained permission management in `common/permissions.js`
- Support for platform-specific permission models
- Permission tracking and request handling
- Event-based permission state monitoring

### Data Security
- Sensitive data stored in device-specific secure storage
- Credential management through the Keychain/Keystore
- Secure credential transmission
- Integration with storage layers for encrypted data

### Authentication
- Token-based authentication
- Biometric authentication support where available
- Session management and token refreshing
- Secure logout procedures

### Network Security
- HTTPS for all API communication
- Certificate pinning for critical endpoints
- Request signing for API authentication
- Secure handling of payment information

### Cryptography Service
The Cryptography Service (`common/utils/crypto.js`) is a core security component that provides:
- **Encryption/Decryption**: AES encryption for sensitive data
- **Hash Functions**: SHA1, SHA256, MD5 for data integrity verification
- **Token Management**: JWT token handling and validation
- **Secure Random**: Random number generation for security operations

This service supports both the Secure Storage Layer and Network Security implementations, ensuring consistent cryptographic operations across the application.

## Performance Optimization

The Perkd application employs several strategies to ensure optimal performance:

### Launch Optimization
- Staged initialization process in `perkd.js`
- Performance-critical components loaded first
- Non-essential operations deferred to background tasks
- Device capability detection to adjust performance parameters

### Memory Management
- Memory footprint monitoring and optimization
- Image scaling based on device capabilities
- Resource cleanup during navigation transitions
- Background task scheduling to prevent UI blocking

### Data Access Optimization
- Multi-layered caching strategy
- Prefetching of likely-needed data
- Efficient database queries using indexes
- Throttled synchronization to reduce resource usage

### UI Performance
- Component memoization for expensive renders
- Virtualized lists for large data sets
- Image optimization and lazy loading
- Animation optimization using native drivers

### Background Processing
- Background fetch for data synchronization
- Offloading intensive operations to background threads
- Smart batching of network requests
- Device-aware concurrency limits

## Styling Approach

The styling system follows these principles:

- **Responsive Design**: Uses scaling utilities to adapt to different screen sizes
- **Centralized Styles**: Core styles defined in `styles/styles.js`
- **Feature-specific Styles**: Additional styles organized by feature
- **Internationalization Support**: Text sizing adjustments based on language

The styling system accommodates different device sizes and orientations while maintaining consistent visual design.

## Implementation Patterns

### Component Patterns
- Components are structured following the **presentation/container** pattern
- Each component focuses on a specific responsibility
- Props are used for data passing and callback functions

### Controller Patterns
- Controllers use a singleton pattern with exposed methods
- Business logic is centralized in controllers
- Controllers manage their own state and communicate with services

### Service Patterns
- Services implement specific functionality domains
- Common abstractions for platform capabilities
- Services are stateless where possible

## Integration Points

The application integrates with several external systems:

- **Analytics**: Tracking user behavior and app performance
- **Push Notifications**: Real-time user communications
- **Location Services**: Geolocation and place-based features
- **Payment Processing**: Secure transaction handling
- **Social Sharing**: Integration with social platforms
- **Deep Linking**: External URL scheme handling

## Guidelines for Developers

When extending or modifying the Perkd application:

### Code Organization
- Follow the established directory structure
- Place new components in the appropriate feature directory
- Place shared utilities in the common library
- Keep controllers focused on specific feature domains

### Navigation
- Use the centralized navigation system in `lib/navigator.js`
- Follow the established patterns for screen transitions
- Register new screens properly in the navigation system

### Styling
- Use the scaling utilities for responsive designs
- Follow the established style patterns
- Consider internationalization when styling text

### State Management
- Keep component state minimal
- Use controllers for complex state management
- Consider state persistence needs when designing features

### Testing
- Test controllers independently of UI components
- Test UI components with mock data and callbacks
- Test integration points with appropriate mocks 