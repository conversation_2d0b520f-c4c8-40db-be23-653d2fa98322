# Pay Actions

Pay actions handle payment processing including order payments, money transfers, and payment method management.

## Actions

### send(data, master, card)

Sends money to another card.

**Parameters**:
- `data`: object - Payment data (required)
  - `cardId`: string - Sender card ID (required)
  - `toCardId`: string - Recipient card ID (required)
  - `amount`: number - Transfer amount (required)
  - `idempotencyKey`: string - Idempotency key for the transfer (optional)
- `master`: object - Master context (required)
- `card`: object - Card context (required)

**Returns**: Promise<object> - Transfer result from CardService.pay()

**Example**:
```javascript
await PayActions.send({
  cardId: 'card_123',
  toCardId: 'card_456',
  amount: 50.00,
  idempotencyKey: 'unique_key_123'
}, master, card);
```

### request(data, master, card)

Requests a payment.

**Parameters**:
- `data`: object - Payment request data (required)
  - `items`: Array<object> - Order items (required)
  - `partial`: boolean - Allow partial payment (optional)
  - `options`: object - Payment options (optional)
    - `capture`: boolean - Capture payment immediately (optional)
    - `cardId`: string - Card ID for payment (optional)
  - `methods`: Array<string> - Allowed payment methods (optional)
  - `currency`: string - Currency code (required)
  - `totalPrice`: number - Total payment amount (required)
  - `idempotencyKey`: string - Idempotency key for payment (optional)
- `master`: object - Master context (required)
- `card`: object - Card context (required)

**Returns**: Promise<object>
- `order`: object - Order details (required)
- `payment`: object - Payment details (required)
  - `intent`: object - Payment intent details (required)
- `amount`: number - Payment amount (required)
- `methods`: Array<string> - Used payment methods (required)

**Example**:
```javascript
await PayActions.request({
  items: [{
    id: 'prod_123',
    amount: 99.99
  }],
  currency: 'USD',
  totalPrice: 99.99,
  options: {
    capture: true
  }
}, master, card);
```

### preview(data, master, card)

Shows a payment preview modal.

**Parameters**:
- `data`: object - Preview data (required)
  - `business`: object - Business details (required)
  - `store`: object - Store details (optional)
  - `items`: Array<object> - Order items (required)
  - `shippingPrice`: number - Shipping cost (optional)
  - `subtotalPrice`: number - Subtotal before tax (required)
  - `totalTax`: number - Tax amount (required)
  - `totalPrice`: number - Total amount (required)
  - `title`: string - Preview title (optional)
  - `options`: object - Payment options (optional)
  - `partial`: boolean - Allow partial payment (optional)
  - `through`: object - Additional routing info (optional)
  - `referenceId`: string - Reference ID (optional)
- `master`: object - Master context (required)
- `card`: object - Card context (optional)

**Returns**: Promise<object> - Payment result after user confirmation

**Example**:
```javascript
await PayActions.preview({
  business: { name: 'Store Name' },
  items: [{
    id: 'prod_123',
    price: 99.99
  }],
  totalPrice: 99.99
}, master);
```

### addmoney(data, master, card)

Shows add money modal for topping up card balance.

**Parameters**:
- `data`: object - Add money data (required)
  - `sku`: string - Product SKU (optional)
  - `limits`: object - Amount limits from payment config (required)
  - `amounts`: Array<number> - Predefined amount options (required)
  - `methods`: Array<string> - Allowed payment methods (required)
  - `business`: object - Business details (required)
  - `action`: string - Action type (required)
  - `ttl`: number - Time to live (optional)
- `master`: object - Master context (required)
- `card`: object - Card context (required)

**Returns**: Promise<object> - Top-up result after user confirmation

### authorize(data, master, card)

Shows payment authorization modal.

**Parameters**:
- `data`: object - Authorization data (required)
  - `business`: object - Business details (required)
  - `action`: string - Action type (required)
  - `ttl`: number - Time to live (optional)
  - `totalPrice`: number - Amount to authorize (required)
  - `cardMasterIds`: Array<string> - Card master IDs (required)
  - `merchantIds`: Array<string> - Merchant IDs (required)
  - `types`: Array<string> - Payment types (required)
- `master`: object - Master context (required)
- `card`: object - Card context (required)

**Returns**: Promise<object> - Authorization result after user confirmation

### Platform-specific Methods

The following methods handle platform-specific payments:

- `applepay(data)`: Promise<object> - Process Apple Pay payment
- `googlepay(data)`: Promise<object> - Process Google Pay payment
- `alipay(data)`: Promise<object> - Process Alipay payment
- `safepay(data)`: Promise<object> - Process Alipay (alias for URL scheme)
- `linepay(data)`: Promise<object> - Process LINE Pay payment
- `grabpay(data)`: Promise<object> - Process GrabPay payment
- `tng(data)`: Promise<object> - Process Touch 'n Go payment

## Error Handling

Pay actions follow the standard error pattern:

```javascript
try {
  await PayActions.request({...});
} catch (error) {
  switch (error.code) {
    case 'INSUFFICIENT_FUNDS':
      // Handle insufficient funds
      break;
    case 'PAYMENT_DECLINED':
      // Handle declined payment
      break;
    case 'INVALID_AMOUNT':
      // Handle invalid amount
      break;
    default:
      // Handle other errors
  }
}
```

Common error codes:
- `INSUFFICIENT_FUNDS` - Not enough funds
- `PAYMENT_DECLINED` - Payment declined
- `INVALID_AMOUNT` - Invalid payment amount
- `METHOD_UNAVAILABLE` - Payment method unavailable
- `TRANSFER_LIMIT` - Transfer limit exceeded
- `CURRENCY_MISMATCH` - Currency mismatch
- `RECIPIENT_NOT_FOUND` - Recipient not found
- `CANCELED` - Payment canceled by user
- `ALREADY_SUCCEEDED` - Payment already completed
- `MISSING_ID` - Missing payment ID
- `REQUIRE_CARD` - Card required for payment
- `INVALID_CARD` - Card is invalid or expired
- `AMOUNT_LIMIT` - Amount exceeds limit 