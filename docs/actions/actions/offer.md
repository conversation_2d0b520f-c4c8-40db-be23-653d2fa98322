# Offer Actions

Offer actions handle offer management including requesting, redeeming, and sharing offers.

## Actions

### request(data)

Requests issuance of an offer from CRM via card master.

**Parameters**:
- `data`: object (required) - Request configuration object
  - `masterId`: string (required) - Unique identifier for the card master
  - `cardId`: string (optional) - Specific card identifier to associate with offer
  - `id`: string (required) - Offer master identifier from CRM system
  - `quantity`: number (optional) - Number of offers to issue (default: 1)

**Returns**: Promise<object | string>
- Success response:
  - `offerIds`: Array<string> - Array of issued offer identifiers
- Cancel response: string - Returns 'canceled' if request is cancelled

**Example**:
```javascript
import { Actions as OfferActions } from 'common/actions/offer';

const result = await OfferActions.request({
  masterId: 'master_123',
  cardId: 'card_456', 
  id: 'offer_789',
  quantity: 2
});
```

### redeem(data, master, card)

Redeems offers with optional actions and navigation.

**Parameters**:
- `data`: object (required) - Redemption configuration object
  - `masterIds`: Array<string> (required) - List of offer master identifiers to redeem
  - `items`: Array<object> (required) - List of items to redeem
    - `id`: string (optional) - Unique item identifier
    - `variantId`: string (optional) - Item variant identifier
    - `sku`: string (required) - Item stock keeping unit
    - `quantity`: number (required) - Number of items to redeem
  - `orderId`: string (optional) - Associated order identifier
  - `machineId`: string (optional) - Point of sale or machine identifier
  - `through`: object (optional) - Attribution tracking information
  - `options`: object (optional) - Additional redemption options
    - `noNav`: boolean (optional) - Flag to skip navigation to offer view
    - `quantity`: number (optional) - Number of offers to redeem
  - `actions`: Array<object> (optional) - List of actions to perform during redemption
- `master`: object (required) - Card master context object
- `card`: object (required) - Card context object

**Returns**: Promise<Array<object> | null>
- Array of redeemed offer objects with details when successful
- null if navigating to offer view

**Example**:
```javascript
await OfferActions.redeem({
  masterIds: ['master_123'],
  items: [{
    sku: 'item_456',
    quantity: 1
  }],
  options: {
    noNav: true,
    quantity: 1
  }
}, cardmaster, card);
```

### share(data, master, card)

Shares an offer with other users.

**Parameters**:
- `data`: object (required) - Share configuration object
  - `id`: string (required) - Identifier of the offer to share
- `master`: object (required) - Card master context object
- `card`: object (required) - Card context object

**Returns**: Promise<void> - Resolves when sharing operation completes successfully

**Example**:
```javascript
await OfferActions.share({
  id: 'offer_123'
}, cardmaster, card);
```

## Offer States

Offers can have the following states:
- `pending` - Offer is pending activation
- `active` - Offer is active and available
- `redeemed` - Offer has been redeemed
- `fullyredeemed` - Offer has been fully redeemed (for multi-use offers)
- `transferred` - Offer has been transferred to another user
- `transferring` - Offer is in the process of being transferred
- `cancelled` - Offer has been cancelled
- `expired` - Offer has expired

## Offer Status

Active offers can have additional status indicators:
- `checkedin` - User has checked in with the offer
- `tocheckin` - User needs to check in
- `inprogress` - Offer is in progress
- `used` - Offer has been used
- `upcoming` - Offer is not yet active
- `expired` - Offer has expired

## Error Handling

Offer actions follow the standard error pattern:

```javascript
try {
  await OfferActions.redeem({...});
} catch (error) {
  switch (error.code) {
    case 'INVALID_OFFER':
      // Handle invalid offer
      break;
    case 'OFFER_EXPIRED':
      // Handle expired offer
      break;
    case 'ALREADY_REDEEMED':
      // Handle already redeemed offer
      break;
    default:
      // Handle other errors
  }
}
```

Common error codes:
- `INVALID_OFFER` - Offer is invalid
- `OFFER_EXPIRED` - Offer has expired
- `ALREADY_REDEEMED` - Offer already redeemed
- `MISSING_PARAM` - Missing required parameter
- `OFFER_MISMATCH` - Items don't match offer requirements
- `NETWORK_REQUIRED` - Network connection required
- `INVALID_ITEMS` - Invalid redemption items 