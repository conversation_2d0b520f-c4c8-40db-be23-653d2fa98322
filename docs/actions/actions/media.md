# Media Actions

Media actions handle camera operations, barcode scanning, calendar integration, and other media-related functionality.

## Actions

### scan(data, master, card)

Scans barcodes and QR codes using the device camera. On Android devices with Google Services, ML Kit is used for barcode detection.

**Parameters**:
- `data`: object (required) - Action configuration object containing scan settings
  - `barcodeTypes`: Array<string> (required) - Types of barcodes to scan (e.g., 'qr', 'ean13')
  - `description`: string (optional) - Human-readable description of scan purpose
  - `policy`: string (optional) - Scan policy configuration ('only')
  - `types`: Array<string> (optional) - Specific scan type categories ('card')
  - `resultType`: string (optional) - Format for scan result ('number', 'barcode', 'barcodeType')
  - `decode`: boolean (optional) - Whether to decode scan result
  - `options`: object (optional) - Scanner configuration options
    - `content`: boolean (optional) - Read content without executing action
    - `flash`: boolean (optional) - Enable camera flash/torch
    - `beep`: boolean (optional) - Play success beep sound
  - `product`: object (optional) - Product scanning specific configuration
  - `merchantId`: string (optional) - Merchant identifier for product search
  - `onOpen`: function (optional) - Callback when scanner opens
  - `onClose`: function (optional) - Callback when scanner closes
  - `spot`: object (optional) - Location context for the scan
- `master`: object (required) - Master context object for action execution
- `card`: object (optional) - Card context for scoped operations

**Returns**: Promise<object> - Resolves with scan result containing:
- `type`: string - Detected barcode type identifier
- `data`: string - Decoded scan data content
- `format`: string - Barcode format specification
- `raw`: string - Raw unprocessed scan data

**Events**:
None - The scan operation does not emit events directly. Results are handled through callbacks.

**Example**:
```javascript
import { Actions as MediaActions } from 'common/actions/media';

const result = await MediaActions.scan({
  barcodeTypes: ['qr', 'ean13', 'code128'],
  description: 'Scan product barcode',
  options: {
    flash: true,
    beep: true
  }
}, master, card);
```

### barcode(data, master, card)

Generates and displays a barcode.

**Parameters**:
- `data`: object (required) - Barcode generation configuration
  - `title`: string (required) - Display title for barcode view
  - `description`: string (optional) - Additional display description
  - `payload`: string|object (required) - Content to encode in barcode
  - `barcodeType`: string (optional) - Barcode format type (default: 'QRCODE')
  - `encode`: boolean (optional) - Whether to encode content (default: true)
  - `ttl`: number (optional) - Time to live in seconds (default: 30)
  - `actionId`: string (optional) - Associated action identifier
  - `host`: string (optional) - Host URL for dynamic codes
- `master`: object (required) - Master context for action execution
- `card`: object (optional) - Card context for scoped operations

**Returns**: Promise<object> - Resolves with:
- `barcode`: string - Generated barcode content
- `content`: object - Decoded barcode data structure
- `endTime`: Date - Expiration timestamp if TTL set

**Events**:
None - The barcode operation does not emit events directly. Results are handled through callbacks.

**Example**:
```javascript
await MediaActions.barcode({
  title: 'Member Card',
  description: 'Show this code at checkout',
  payload: {
    cardId: 'card_123',
    memberId: 'mem_456'
  },
  barcodeType: 'QRCODE'
}, master, card);
```

### addcalendar({ title, details, options })

Adds an event to the device calendar using react-native-calendar-events.

**Parameters**:
- `title`: string (required) - Event title
- `details`: object (required)
  - `startDate`: string (required) - ISO date string
  - `endDate`: string (required) - ISO date string
  - `allDay`: boolean (optional)
  - `location`: string (optional)
  - `description`: string (optional)
- `options`: object (optional)
  - `color`: string (optional) - Event color (default: accent color)

**Returns**: Promise<object>
- `eventId`: string - Created event ID

**Events**:
None - The calendar operation does not emit events directly. Results are handled through callbacks.

**Example**:
```javascript
await MediaActions.addcalendar({
  title: 'Team Meeting',
  details: {
    startDate: '2024-01-01T10:00:00Z',
    endDate: '2024-01-01T11:00:00Z',
    location: 'Conference Room A',
    description: 'Weekly team sync'
  },
  options: {
    color: '#FF0000'
  }
});
```

### takephoto(options)

Opens the camera to take a photo using react-native-image-picker.

**Parameters**:
- `options`: object (optional)
  - `quality`: number (optional) - Image quality (0-1, default: 0.5)
  - `maxWidth`: number (optional) - Output width (default: 1200)
  - `maxHeight`: number (optional) - Output height (default: 1200)
  - `cameraType`: string (optional) - Camera to use ('front'/'back')
  - `includeBase64`: boolean (optional) - Return base64 encoded image
  - `storageOptions`: object (optional)
    - `skipBackup`: boolean (optional) - Skip iOS backup
    - `path`: string (optional) - Storage path

**Returns**: Promise<string>
- If `includeBase64` is true: Base64 encoded image string
- Otherwise: Image file URI

**Events**:
None - The camera operation does not emit events directly. Results are handled through callbacks.

**Example**:
```javascript
const photo = await MediaActions.takephoto({
  quality: 0.8,
  maxWidth: 1200,
  maxHeight: 1200,
  cameraType: 'back',
  includeBase64: false
});
```

### choosephoto(options)

Opens photo picker to select photos using react-native-image-picker.

**Parameters**:
- `options`: object (optional)
  - `multiple`: boolean (optional) - Allow multiple selection
  - `maxWidth`: number (optional) - Maximum width (default: 1200)
  - `maxHeight`: number (optional) - Maximum height (default: 1200)
  - `quality`: number (optional) - Output quality (0-1, default: 0.5)
  - `includeBase64`: boolean (optional) - Return base64 encoded image
  - `mediaType`: string (optional) - Filter type ('photo', 'video', 'all')

**Returns**: Promise<string>
- If `includeBase64` is true: Base64 encoded image string
- Otherwise: Image file URI
- Note: On iOS, there's a 500ms delay after selection before resolving

**Events**:
None - The photo picker operation does not emit events directly. Results are handled through callbacks.

**Example**:
```javascript
const photo = await MediaActions.choosephoto({
  multiple: false,
  maxWidth: 1200,
  maxHeight: 1200,
  quality: 0.8,
  mediaType: 'photo'
});
```

## Error Handling

Media actions follow the standard error pattern:

```javascript
try {
  await MediaActions.scan({...});
} catch (error) {
  switch (error.code) {
    case 'noPermission':
      // Handle permission denied
      break;
    case 'cancelled':
      // Handle user cancel
      break;
    case 'failed':
      // Handle operation failure
      break;
    default:
      // Handle other errors
  }
}
```

Common error codes:
- `noPermission` - Required permission denied (camera, photo, calendar)
- `cancelled` - User canceled the operation
- `failed` - Operation failed 