# Interact Actions

Actions for handling user interactions, dialogs, loading states, and haptic feedback.

## Actions

### showloading()

Shows a loading indicator.

**Parameters**: None

**Returns**: Promise<void> - Resolves when loading indicator is shown and visible on screen

### hideloading()

Hides the loading indicator.

**Parameters**: None

**Returns**: Promise<void> - Resolves when loading indicator is fully hidden from screen

### dialog(data)

Shows a dialog with customizable buttons.

**Parameters**:
- `data`: object (required) - Configuration object for dialog display and behavior
  - `title`: string (required) - Title text displayed at top of dialog
  - `message`: string (required) - Main content message of the dialog
  - `buttons`: Array<object> (required) - List of button configurations for dialog
    - `text`: string (required) - Display text for the button
    - `style`: string (optional) - Visual style of button: 'default', 'cancel', or 'destructive'
  - `cancelable`: boolean (optional) - If true, allows dialog dismissal by tapping outside

**Returns**: Promise<number> - Resolves with index of selected button (0-based) or -1 if dismissed

**Example**:
```javascript
await Actions.dialog({
  title: 'Confirm',
  message: 'Are you sure?',
  buttons: [
    { text: 'Cancel', style: 'cancel' },
    { text: 'OK' }
  ],
  cancelable: true
});
```

### inputdialog(data)

Shows a dialog with text input.

**Parameters**:
- `data`: object (required) - Configuration object for input dialog
  - `title`: string (required) - Title text displayed at top of dialog
  - `message`: string (required) - Descriptive message above input field
  - `placeholder`: string (optional) - Placeholder text for empty input field
  - `buttons`: Array<object> (required) - List of button configurations
    - `text`: string (required) - Display text for the button
    - `style`: string (optional) - Visual style of button: 'default', 'cancel', or 'destructive'
  - `properties`: object (optional) - Input field configuration options
    - `keyboardType`: string (optional) - Type of keyboard to display
    - `secureTextEntry`: boolean (optional) - If true, masks input for password entry
    - `maxLength`: number (optional) - Maximum number of characters allowed

**Returns**: Promise<string | null> - Resolves with input text if confirmed, null if canceled

**Example**:
```javascript
const input = await Actions.inputdialog({
  title: 'Enter Name',
  message: 'Please enter your name',
  placeholder: 'Name',
  buttons: [
    { text: 'Cancel', style: 'cancel' },
    { text: 'Save' }
  ],
  properties: {
    keyboardType: 'default',
    maxLength: 50
  }
});
```