# Place Actions

Place actions handle place management including viewing maps, finding nearest locations, and managing place-related functionality.

## Actions

### viewmap(data)

Shows a map view of a place location on the device.

**Parameters**:
- `data`: object (required) - Container for view map parameters
  - `placeId`: string (required) - Unique identifier of the place to display on map
- `master`: object (optional) - Master context object containing environment settings
- `card`: object (optional) - Card context object containing card-specific data

**Returns**: Promise<void> - Resolves when map view is displayed successfully, with no return value

**Example**:
```javascript
import { Actions as PlaceActions } from 'common/actions/place';

await PlaceActions.viewmap({
  placeId: 'place_123'
});
```

### viewnearest(data)

Shows the nearest place location for a given card master.

**Parameters**:
- `data`: object (required) - Container for nearest location parameters
  - `masterId`: string (required) - Card master identifier to find nearest place for
  - `radius`: number (optional) - Search radius in meters, defaults to system setting
- `master`: object (required) - Master context object containing environment settings
- `card`: object (optional) - Card context object containing card-specific data

**Returns**: Promise<void> - Resolves when nearest place view is displayed, with no return value

**Example**:
```javascript
await PlaceActions.viewnearest({
  masterId: 'master_123',
  radius: 5000
});
```

## Place Details

Places have the following properties:
- `id`: string (required) - Unique identifier for the place
- `name`: string (required) - Place display name
- `brand`: object (required) - Brand information container
  - `short`: string (required) - Short brand name for display
  - `long`: string (required) - Full brand name
  - `logo`: object (required) - Brand logo information
    - `url`: string (required) - Full resolution logo URL
    - `thumbnail`: string (required) - Thumbnail logo URL
- `addressList`: Array<object> (required) - List of associated addresses
  - `formatted`: string (required) - Human-readable formatted address
  - `unit`: string (optional) - Unit number if applicable
  - `level`: string (optional) - Level number if applicable
  - `country`: string (required) - Two-letter country code
- `phoneList`: Array<object> (optional) - List of contact phone numbers
  - `fullNumber`: string (required) - Complete phone number with country code
- `geo`: object (required) - Geographic location data
  - `coordinates`: Array<number> (required) - [longitude, latitude] coordinates
- `opening`: object (required) - Current opening status
  - `status`: string (required) - Current status ('open', 'closing', 'closed')
- `openingHours`: object (optional) - Detailed opening hours information
  - `periods`: Array<object> (required) - Operating time periods
- `pickup`: object (optional) - Pickup service information
  - `available`: boolean (required) - Whether pickup is available at this location
- `deliver`: object (optional) - Delivery service information
  - `available`: boolean (required) - Whether delivery is available from this location
- `timeZone`: string (required) - Place timezone identifier
- `officialId`: string (optional) - Official place identifier from external system
- `tags`: Array<string> (optional) - Categorization tags for the place
- `custom`: boolean (optional) - Whether this is a custom-created place
- `external`: boolean (optional) - Whether this is an external system place
- `distance`: number (optional) - Distance from current location in meters

## Opening Hours Status

Places can have the following opening statuses:
- `open` - Place is currently open
- `closing` - Place is closing soon
- `closed` - Place is currently closed

## Error Handling

Place actions follow the standard error pattern:

```javascript
try {
  await PlaceActions.viewmap({...});
} catch (error) {
  switch (error.code) {
    case 'LOCATION_REQUIRED':
      // Handle missing location
      break;
    case 'INVALID_PLACE':
      // Handle invalid place
      break;
    case 'SEARCH_FAILED':
      // Handle search failure
      break;
    default:
      // Handle other errors
  }
}
```

Common error codes:
- `LOCATION_REQUIRED` - Location needed
- `INVALID_PLACE` - Invalid place ID
- `SEARCH_FAILED` - Search failed
- `NO_RESULTS` - No places found
- `OVER_QUERY_LIMIT` - Too many requests
- `REQUEST_DENIED` - API request denied
- `INVALID_REQUEST` - Invalid request parameters
- `NOT_FOUND` - Place not found