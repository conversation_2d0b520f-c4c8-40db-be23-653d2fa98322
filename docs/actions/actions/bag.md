# Bag Actions

Bag actions handle shopping bag operations like adding items, updating quantities, removing items, and managing fulfillment.

## Actions

### build(data, master, card)

Builds a new shopping bag with the specified items and fulfillment details.

**Parameters**:
- `data`: object (required) - Shopping bag configuration data
  - `items`: Array<object> (required) - List of items to add to bag
    - `variantId`: string (required) - Product variant identifier
    - `quantity`: number (required) - Item quantity
    - `options`: object (optional) - Additional item options
  - `fulfillment`: object (required) - Delivery/pickup configuration
    - `type`: string (required) - Fulfillment type ('delivery', 'pickup')
    - `address`: object (required for delivery) - Delivery address details
    - `time`: object (optional) - Scheduled delivery/pickup time
  - `policy`: object (optional) - Bag policy rules and constraints
  - `offers`: Array<object> (optional) - List of applicable offer codes
  - `through`: object (optional) - Attribution tracking information
- `master`: object (required) - Master context for authentication
- `card`: object (optional) - Card context for session

**Returns**: Promise<object> - Created bag with details including ID, items, totals, and fulfillment info

**Example**:
```javascript
const result = await BagActions.build({
  items: [{
    variantId: "prod_123",
    quantity: 2,
    options: { size: "large" }
  }],
  fulfillment: {
    type: "delivery",
    address: {
      street: "123 Main St"
    }
  }
}, master);
```

### additems(data, master, card)

Adds items to an existing shopping bag.

**Parameters**:
- `data`: object (required) - Item addition configuration
  - `bag`: string (optional) - Bag identifier, defaults to 'default'
  - `items`: Array<object> (required) - List of items to add
    - `variantId`: string (required) - Product variant identifier
    - `quantity`: number (required) - Item quantity
    - `options`: object (optional) - Additional item options
  - `through`: object (optional) - Attribution tracking information
- `master`: object (required) - Master context for authentication
- `card`: object (optional) - Card context for session

**Returns**: Promise<void> - Resolves when items are successfully added to bag

**Example**:
```javascript
await BagActions.additems({
  bag: "default",
  items: [{
    variantId: "prod_123",
    quantity: 1
  }]
}, master);
```

### configbundle(data, cardmaster, card, ctxId)

Configures a bundle item before adding to bag.

**Parameters**:
- `data`: object (required) - Bundle configuration data
  - `item`: object (required) - Bundle item details
    - `variantId`: string (required) - Bundle variant identifier
    - `quantity`: number (required) - Bundle quantity
    - `bundles`: Array<object> (required) - Bundle components
  - `callbacks`: object (optional) - Configuration callback functions
  - `through`: object (optional) - Attribution tracking information
- `cardmaster`: object (required) - Card master context
- `card`: object (required) - Card context
- `ctxId`: string (required) - Context identifier

**Returns**: Promise<void> - Resolves when bundle is configured

### configitems(data, cardmaster, card, ctxId)

Configures items before adding to bag.

**Parameters**:
- `data`: object
  - `items`: Array<object> - Items to configure
  - `through`: object - Attribution info
  - `policy`: object - Item policy
  - `callbacks`: object - Callback functions
- `cardmaster`: object
- `card`: object
- `ctxId`: string

**Returns**: Promise<void>

### updateitems(data, cardmaster, card, ctxId)

Updates items in the bag.

**Parameters**:
- `data`: object
  - `bag`: string - Bag name/ID
  - `items`: Array<object> - Items to update
    - `variantId`: string
    - `quantity`: number
    - `options`: object
  - `through`: object - Attribution info
- `cardmaster`: object
- `card`: object
- `ctxId`: string

**Returns**: Promise<void>

**Events**:
- `Bag.updateItems` - Items updated in bag

### removeitems(data, cardmaster, card, ctxId)

Removes items from the bag.

**Parameters**:
- `data`: object
  - `bag`: string - Bag name/ID
  - `items`: Array<object> - Items to remove
    - `variantId`: string
- `cardmaster`: object
- `card`: object
- `ctxId`: string

**Returns**: Promise<void>

**Events**:
- `Bag.removeItems` - Items removed from bag

### from(data, cardmaster, card)

Requests items from an external source to be added to the bag.

**Parameters**:
- `data`: object
  - `fulfillment`: object - Fulfillment details
    - `type`: string - Fulfillment type
    - `destination`: object - Destination details
      - `placeId`: string - Place ID
  - `through`: object - Attribution info
  - Additional request parameters passed to API

**Returns**: Promise<void>

**Events**:
- `Bag.addItems` - Items added to bag

### use(data, cardmaster, card)

Switches to using a specific bag.

**Parameters**:
- `data`: object
  - `name`: string - Name of bag to use

**Returns**: Promise<object> - Selected bag

### open(data, cardmaster, card)

Opens a specific bag.

**Parameters**:
- `data`: object
  - `cardId`: string - Card ID (optional, defaults to card.id)
  - `bag`: string - Bag name/ID

**Returns**: Promise<object> - Opened bag

### get(data, cardmaster, card)

Gets bag data based on widget or bag name.

**Parameters**:
- `data`: object
  - `widget`: string - Widget name
  - `bag`: string - Bag name
- `cardmaster`: object - Card master details
  - `merchantIds`: Array<string> - List of merchant IDs
- `card`: object - Card details

**Returns**: Promise<object> - Bag data

## Events

Bag actions emit the following events:
- `Bag.addItems` - When items are added to the bag
- `Bag.updateItems` - When items are updated in the bag
- `Bag.removeItems` - When items are removed from the bag
- `Bag.changed` - When the bag state changes

## Error Handling

Bag actions follow the standard error pattern:

```javascript
try {
  await BagActions.additems({...});
} catch (error) {
  switch (error.code) {
    case 'ITEM_NOT_AVAILABLE':
      // Handle item availability
      break;
    case 'INSUFFICIENT_STOCK':
      // Handle stock issues
      break;
    case 'INVALID_QUANTITY':
      // Handle quantity validation
      break;
    case 'BAG_LOCKED':
      // Handle bag locked for processing
      break;
    case 'INVALID_FULFILLMENT':
      // Handle fulfillment validation
      break;
    case 'POLICY_VIOLATION':
      // Handle policy violations
      break;
    default:
      // Handle other errors
  }
}
```

Common error codes:
- `ITEM_NOT_AVAILABLE` - Product not available
- `INSUFFICIENT_STOCK` - Not enough stock
- `INVALID_QUANTITY` - Invalid item quantity
- `BAG_NOT_FOUND` - Shopping bag not found
- `BAG_LOCKED` - Bag is locked for processing
- `INVALID_FULFILLMENT` - Fulfillment option not available
- `POLICY_VIOLATION` - Bag policy rules violated 