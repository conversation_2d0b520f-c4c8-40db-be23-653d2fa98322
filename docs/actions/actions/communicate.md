# Communicate Actions

Communicate actions handle various forms of communication including phone calls, email, SMS, and chat functionality.

## Actions

### call({ number })

Makes a phone call to the specified number.

**Parameters**:
- `data`: object - Action configuration object (required)
  - `number`: string - Phone number in E.164 format (e.g., ******-0123) (required)

**Returns**: Promise<void> - Resolves when call is initiated successfully, rejects if call fails

**Example**:
```javascript
await CommunicateActions.call({
  number: '******-0123'
});
```

### email({ address, cc, bcc, subject, body, data })

Sends an email using the device's email client.

**Parameters**:
- `data`: object - Email configuration object (required)
  - `address`: string - Primary recipient email address in valid format (required)
  - `cc`: array<string> - List of carbon copy recipient email addresses (optional)
  - `bcc`: array<string> - List of blind carbon copy recipient email addresses (optional)
  - `subject`: string - Email subject line with optional template variables (required)
  - `body`: string - Email body content with optional template variables (required)
  - `data`: object - Template variable replacement data (optional)
    - `person`: object - Person-related template data (optional)
      - `name`: string - Person's full name (optional)
      - `email`: string - Person's email address (optional)
    - `card`: object - Card-related template data (optional)
      - `number`: string - Card number (optional)
      - `id`: string - Card identifier (optional)

**Returns**: Promise<void> - Resolves when email is sent successfully, rejects if sending fails

**Example**:
```javascript
await CommunicateActions.email({
  address: '<EMAIL>',
  cc: ['<EMAIL>'],
  subject: 'Hello {{person.name}}',
  body: 'Your card {{card.number}} is ready',
  data: {
    person: { name: 'John' },
    card: { number: '1234' }
  }
});
```

### sms({ recipients, body, data })

Sends SMS messages to one or more recipients.

**Parameters**:
- `data`: object - SMS configuration object (required)
  - `recipients`: array<string> - List of recipient phone numbers in E.164 format (required)
  - `body`: string - Message content with optional template variables (required)
  - `data`: object - Template variable replacement data (optional)
    - `person`: object - Person-related template data (optional)
    - `card`: object - Card-related template data (optional)

**Returns**: Promise<void> - Resolves when SMS is sent successfully, rejects if sending fails

**Example**:
```javascript
await CommunicateActions.sms({
  recipients: ['******-0123'],
  body: 'Hi {{person.name}}, your card is ready',
  data: {
    person: { name: 'John' }
  }
});
```

### chat({ name, app, web })

Opens a chat application or web chat interface.

**Parameters**:
- `data`: object - Chat configuration object (required)
  - `name`: string - Chat platform identifier ('whatsapp', 'messenger', etc.) (required)
  - `app`: object - Native application configuration (optional)
    - `uri`: string - Deep link URI with optional template variables (required)
  - `web`: object - Web fallback configuration (optional)
    - `uri`: string - Web chat URL with optional template variables (required)

**Returns**: Promise<void> - Resolves when chat interface opens successfully, rejects if opening fails

**Example**:
```javascript
await CommunicateActions.chat({
  name: 'whatsapp',
  app: {
    uri: 'whatsapp://send?phone={{person.mobile}}'
  },
  web: {
    uri: 'https://wa.me/{{person.mobile}}'
  }
});
```

## Template Variables

All text fields (subject, body, uri) support template variables using Mustache syntax. Available variables:

- `{{person.*}}` - Person data fields
  - `{{person.name}}` - Person's name
  - `{{person.mobile}}` - Person's mobile number
  - `{{person.email}}` - Person's email
- `{{card.*}}` - Current card data fields
  - `{{card.id}}` - Card ID
  - `{{card.number}}` - Card number
  - `{{card.displayName}}` - Card display name

## Error Handling

Communicate actions follow the standard error pattern:

```javascript
try {
  await CommunicateActions.email({...});
} catch (error) {
  switch (error.code) {
    case 'INVALID_RECIPIENT':
      // Handle invalid email/phone
      break;
    case 'NO_APP_AVAILABLE':
      // Handle missing app
      break;
    case 'SEND_FAILED':
      // Handle send failure
      break;
    default:
      // Handle other errors
  }
}
```

Common error codes:
- `INVALID_RECIPIENT` - Invalid email or phone number
- `NO_APP_AVAILABLE` - Required app not installed
- `SEND_FAILED` - Message failed to send 