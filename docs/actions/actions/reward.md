# Reward Actions

Actions for managing rewards including requesting and redeeming rewards from CRM.

## Actions

### request(data)

Requests issuance of a reward from CRM via card master.

**Parameters**:
- `data`: object - Request configuration object (required)
  - `masterId`: string - Card master identifier (required)
  - `cardId`: string - Card identifier (required)
  - `id`: string - Reward master identifier from CRM (required)
  - `quantity`: number - Number of rewards to issue (optional)

**Returns**: Promise<void> - Resolves when reward is issued

**Example**:
```javascript
await Actions.reward.request({
  masterId: 'master_123',
  cardId: 'card_456',
  id: 'reward_789',
  quantity: 1
});
```

**Errors**:
- `INVALID_MASTER` - Invalid card master
- `INVALID_CARD` - Invalid card
- `INVALID_REWARD` - Invalid reward master
- `NOT_QUALIFIED` - User not qualified for reward
- `REWARD_EXPIRED` - Re<PERSON> has expired
- `LIMIT_EXCEEDED` - Reward limit exceeded 