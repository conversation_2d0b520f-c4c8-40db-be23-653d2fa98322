# Account Actions

Account actions handle user account management operations.

## Actions

### changemobile()

Changes the mobile number associated with a user account by opening a modal form for input and verification.

**Parameters**:
- `options`: object (optional)
  - `skipVerification`: boolean - Controls whether to bypass SMS/WhatsApp verification process (optional)
  - `countryCode`: string - The country code to pre-select in the phone input, e.g. '+1' (optional)
- `master`: object (required)
  - `user`: object (required)
    - `currentMobile`: string - The user's existing mobile number in E.164 format (required)
- `card`: object - Additional card-related configuration options (optional)

**Returns**: Promise<void> - Resolves when mobile number is successfully changed or modal is closed. No return value is provided.

**Example**:
```javascript
import { Actions as AccountActions } from 'common/actions/account';

try {
  await AccountActions.changemobile({
    skipVerification: false,
    countryCode: '+1'
  });
  // Mobile number changed successfully
} catch (error) {
  if (error.code === 'OFFLINE') {
    // Handle network connectivity issues
  } else if (error.code === 'mobileInvalid') {
    // Handle invalid mobile number format
  }
}
```

## Error Handling

Account actions may throw the following errors:
- `OFFLINE` - Network connectivity issues
- `TIMEOUT` - Operation timeout
- `mobileInvalid` - Invalid mobile number format

## Mobile Number Validation

Mobile numbers are validated according to the following rules:
- Must be at least 7 digits long (excluding country code)
- Must match the format for the selected country
- Cannot be the same as the current mobile number
- Must include a valid country code

## Verification Process

After entering a valid mobile number:
1. A confirmation dialog is shown with the formatted number
2. If confirmed, a verification code is sent via:
   - WhatsApp (if installed)
   - SMS (as fallback)
3. User must verify the new number before the change is applied