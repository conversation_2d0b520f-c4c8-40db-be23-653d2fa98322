# File Actions

File actions handle file system operations including sharing, downloading, and writing files.

## Actions

### share(data)

Shares a file using the system share sheet. If contents are provided, they are written to a temporary file before sharing. The temporary file is automatically cleaned up after sharing.

**Parameters**:
- `data`: object (required) - Configuration object for sharing
  - `contents`: string (optional) - File contents to be shared as text
  - `url`: string (optional) - URL to share when contents are not provided
  - `fileName`: string (required) - Name of the file to be created for sharing
  - `path`: string (optional) - Custom file system path for sharing the file
  - `encoding`: string (optional) - File encoding format (default: 'utf8')
- `master`: object (required) - Master context object containing app state

**Returns**: Promise<void> - Resolves without value when sharing completes successfully

**Example**:
```javascript
await FileActions.share({
  contents: 'Hello World',
  fileName: 'hello.txt',
  encoding: 'utf8'
});
```

### download(data)

Downloads a file from a URL to the app's temporary directory.

**Parameters**:
- `data`: object (required) - Configuration object for download
  - `url`: string (required) - Source URL to download the file from
  - `path`: string (optional) - Custom destination path (defaults to TMP_FOLDER/Downloads/[filename])
- `master`: object (required) - Master context object containing app state

**Returns**: Promise<string> - Resolves with the absolute file system path to the downloaded file

**Example**:
```javascript
const filePath = await FileActions.download({
  url: 'https://example.com/file.pdf'
});
```

### write(data)

Writes contents to a file.

**Parameters**:
- `data`: object (required) - Configuration object for file writing
  - `fileName`: string (required) - Name of the file to be created
  - `path`: string (optional) - Custom file path (defaults to TMP_FOLDER/Downloads/[filename])
  - `contents`: string (required) - Content to be written to the file
  - `encoding`: string (optional) - File encoding format (default: 'utf8')
- `master`: object (required) - Master context object containing app state

**Returns**: Promise<string> - Resolves with the absolute file system path to the written file

**Example**:
```javascript
const filePath = await FileActions.write({
  fileName: 'output.txt',
  contents: 'Hello World',
  encoding: 'utf8'
});
```

## Error Handling

File actions follow the standard error pattern:

```javascript
try {
  await FileActions.write({
    fileName: 'test.txt',
    contents: 'Hello World'
  });
} catch (error) {
  console.log('system.writefile.err', error);
}
```

Note: Errors are logged but not thrown with specific error codes. The implementation uses console.log for error tracking.