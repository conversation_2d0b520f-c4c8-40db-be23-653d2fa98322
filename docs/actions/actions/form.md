# Form Actions

Form actions handle form validation, data collection, and input management including specialized pickers and validators.

## Actions

### validate(data, master)

Validates form data against a JSON schema definition.

**Parameters**:
- `data`: object (required) - Configuration object for validation
  - `form`: object (required) - Form definition in JSON Schema format
  - `data`: object (required) - Form data entries to validate
  - `language`: string (optional) - Localization language code, defaults to app's language
- `master`: object (required) - Master context object

**Returns**: Promise<object> - Validation result containing status and errors
- `valid`: boolean - Indicates if validation passed successfully
- `errors`: Array<object> - List of validation error details
  - `field`: string - Name of the field that failed validation
  - `keyword`: string - Type of validation rule that failed
  - `message`: string - Human readable error description

**Example**:
```javascript
const result = await actions.validate({
  form: schema,
  data: formData,
  language: 'en'
});
```

### openinghours(data, master)

Opens the opening hours picker for business hours selection.

**Parameters**:
- `data`: object (required) - Opening hours picker configuration
  - `theme`: string (optional) - Color scheme for the picker UI
  - `value`: object (optional) - Current opening hours configuration
  - `options`: object (optional) - Additional picker configuration options
  - `fieldProps`: object (optional) - Additional field configuration properties
- `master`: object (required) - Master context object

**Returns**: Promise<object> - Selected opening hours configuration in standardized format

**Example**:
```javascript
const hours = await actions.openinghours({
  theme: 'light',
  value: currentHours
});
```

### showpicker(data, master)

Shows a picker modal for date, time, or duration selection.

**Parameters**:
- `data`: object (required) - Picker configuration object
  - `type`: string (required) - Picker type ('date', 'time', 'datetime', 'durationPicker')
  - `mode`: string (optional) - Display mode for the picker interface
  - `theme`: string (optional) - Color scheme ('light', 'dark', 'system')
  - `value`: any (optional) - Initial picker value
  - `options`: object (optional) - Additional picker configuration
    - `duration`: number (optional) - Duration value for durationPicker
    - `schedule`: object (optional) - Time schedule configuration
    - `time`: object (optional) - Time options configuration
    - `min`: number (optional) - Minimum allowed value
    - `max`: number (optional) - Maximum allowed value
- `master`: object (required) - Master context object

**Returns**: Promise<any> - Selected value from the picker based on picker type

**Example**:
```javascript
const selectedDate = await actions.showpicker({
  type: 'date',
  theme: 'light',
  value: new Date()
});
```

## Error Handling

Form actions follow the standard error pattern:

```javascript
try {
  const result = await actions.validate({
    form: schema,
    data: formData
  });
  if (!result.valid) {
    // Handle validation errors
    const { field, keyword, message } = result.errors[0];
    switch (keyword) {
      case 'required':
        // Handle required field error
        break;
      default:
        // Handle other validation errors
        break;
    }
  }
} catch (error) {
  // Handle unexpected errors
  console.log(error);
}
```

Common validation keywords:
- `required` - Field is required but missing
- `type` - Value is of wrong type
- `format` - Value does not match required format
- `pattern` - Value does not match required pattern
- `minimum` - Value is below minimum
- `maximum` - Value is above maximum