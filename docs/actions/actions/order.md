# Order Actions

Order actions handle order processing including card orders, product orders, and order management.

## Actions

### card(data, master, card)

Orders a card or membership.

**Parameters**:
- `data`: object - Order data (required)
  - `masterId`: string - Card master identifier (required)
  - `cardId`: string - Card identifier (optional)
  - `pricingName`: string - Pricing plan name (optional)
  - `quantity`: number - Order quantity (optional)
  - `currency`: string - Currency code (optional)
  - `methods`: Array<string> - Payment methods (optional)
  - `idempotencyKey`: string - Idempotency key (optional)
- `master`: object - Master context (required)
- `card`: object - Card context (optional)

**Returns**: Promise<object> - Order details containing:
- `orderId`: string - Order identifier (required)
- `status`: string - Order status (required)
- `cards`: Array<object> - Ordered cards (required)
- `payment`: object - Payment information (required)
- `totals`: object - Order totals (required)

**Example**:
```javascript
import { Actions as OrderActions } from 'common/actions/order';

const order = await OrderActions.card({
  masterId: 'master_123',
  pricingName: 'join',
  quantity: 1
}, master, card);
```

### products(data, master, card)

Orders products with various payment and fulfillment options.

**Parameters**:
- `data`: object - Product order data (required)
  - `masterId`: string - Card master identifier (required)
  - `cardId`: string - Card identifier (optional)
  - `items`: Array<object> - Order items (required)
    - `sku`: string - Product SKU (required)
    - `quantity`: number - Item quantity (required)
    - `options`: object - Item options (optional)
  - `discounts`: Array<object> - Order discounts (optional)
    - `code`: string - Discount code (required)
    - `type`: string - Discount type (required)
  - `vouchers`: Array<string> - Voucher codes (optional)
  - `fulfillment`: object - Fulfillment details (required)
    - `type`: string - Fulfillment type (required)
    - `recipient`: object - Recipient details (optional)
      - `name`: string - Recipient name (required)
      - `contact`: string - Contact number (required)
    - `destination`: object - Delivery destination (required for delivery)
      - `address`: string - Delivery address (required)
      - `coordinates`: object - Location coordinates (optional)
  - `options`: object - Order options (optional)
    - `cardId`: string - Payment card ID (optional)
    - `guest`: boolean - Guest checkout flag (optional)
  - `payments`: object - Payment configuration (optional)
    - `currency`: object - Currency settings (optional)
    - `limits`: object - Payment limits (optional)
    - `methods`: Array<string> - Allowed payment methods (optional)
  - `withoutPayment`: boolean - Skip payment step (optional)
  - `sync`: boolean - Sync order data (optional)
  - `idempotencyKey`: string - Idempotency key (optional)
- `master`: object - Master context (required)
- `card`: object - Card context (optional)

**Returns**: Promise<object> - Complete order details including:
- `orderId`: string - Order identifier (required)
- `status`: string - Order status (required)
- `items`: Array<object> - Ordered items (required)
- `payment`: object - Payment details (required)
- `totals`: object - Order totals (required)
- `fulfillment`: object - Fulfillment details (required)

**Example**:
```javascript
await OrderActions.products({
  masterId: 'master_123',
  items: [{
    sku: 'item_123',
    quantity: 1
  }],
  fulfillment: {
    type: 'delivery',
    destination: {
      address: '123 Main St',
      city: 'San Francisco'
    }
  },
  options: {
    cardId: 'card_123'
  },
  idempotencyKey: 'unique_key_123'
}, master, card);
```

### at(data, cardmaster, card)

Check in and navigate to store front.

**Parameters**:
- `data`: object - Check-in data (required)
  - `type`: string - Check-in type (optional)
  - `checkIn`: object - Check-in details (optional)
  - `nav`: Array<string> - Navigation path (optional)
  - `to`: string - Navigation target (optional)
  - `spot`: object - Location spot (required)
    - `placeId`: string - Place identifier (required)

**Returns**: Promise<object> - Check-in result
- `place`: object - Place details (required)
- `verified`: boolean - Verification status (required)
- `distance`: number - Distance from location (required)

**Example**:
```javascript
await OrderActions.at({
  spot: {
    placeId: 'place_123'
  }
}, cardmaster, card);
```

### store(data, cardmaster, card)

Selects store for order.

**Parameters**:
- `data`: object - Store selection data (required)
  - `category`: string - Store category (required)
  - `plural`: string - Category plural form (optional)
  - `types`: Array<string> - Store types (required)
  - `options`: object - Display options (optional)
    - `backgroundColor`: string - Background color (optional)

**Returns**: Promise<object> - Store selection result
- `store`: object - Store details (required)
- `available`: boolean - Availability status (required)
- `hours`: object - Operating hours (required)

**Example**:
```javascript
await OrderActions.store({
  category: 'restaurant',
  types: ['pickup', 'delivery'],
  options: {
    backgroundColor: '#FFFFFF'
  }
}, cardmaster, card);
```

## Error Handling

Order actions follow the standard error pattern:

```javascript
try {
  await OrderActions.products({...});
} catch (error) {
  switch (error.code) {
    case 'INVALID_ITEMS':
      // Handle invalid items
      break;
    case 'OUT_OF_STOCK':
      // Handle stock issues
      break;
    case 'DELIVERY_UNAVAILABLE':
      // Handle delivery issues
      break;
    default:
      // Handle other errors
  }
}
```

Common error codes:
- `INVALID_ITEMS` - Invalid order items
- `OUT_OF_STOCK` - Items out of stock
- `DELIVERY_UNAVAILABLE` - Delivery not available
- `PAYMENT_REQUIRED` - Payment needed
- `VOUCHER_INVALID` - Invalid voucher
- `LOCATION_REQUIRED` - Location needed
- `STORE_CLOSED` - Store is closed
- `PAYMENT_CANCELED` - Payment was canceled
- `PAYMENT_FAILED` - Payment failed
- `ORDER_EXPIRED` - Order has expired 