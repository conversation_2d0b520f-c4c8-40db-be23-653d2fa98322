# Message Actions

Message actions handle in-app messaging functionality including message requests, notifications, and message management.

## Actions

### request(data, master)

Requests issuance of a message from CRM via cardmaster.

**Parameters**:
- `data`: object (required) - Container for message request data
  - `masterId`: string (required) - Unique identifier for the card master instance
  - `cardId`: string (required) - Unique identifier for the specific card instance
  - `id`: string (required) - Message template identifier in the CRM system
- `master`: object (required) - Master reference object
  - `id`: string (required) - Master identifier that must match the masterId in data object

**Returns**: Promise<void> - Resolves with no value when message is successfully requested through the CRM system. Rejects with detailed error information if the request fails.

**Example**:
```javascript
import { Actions as MessageActions } from 'common/actions/message';

try {
  await MessageActions.request({
    masterId: 'master_123',
    cardId: 'card_456', 
    id: 'message_template_id'
  }, {
    id: 'master_123'
  });
} catch (error) {
  console.error('Message request failed:', error);
}
```

Note: Request will fail if required parameters are missing or invalid, if network connectivity issues occur, or if the message template ID is not found in the CRM system.