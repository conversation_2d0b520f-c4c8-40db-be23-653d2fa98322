# Remote Actions

Remote actions handle remote API calls, WebSocket connections, and other remote operations.

## Actions

### do(data)

Executes a remote action with loading indicator.

**Parameters**:
- `data`: object (required) - Action configuration object
  - `id`: string (required) - Unique identifier for the action
  - Additional parameters will be passed to the action

**Returns**: Promise<object> - Result object from ActionService.do() containing action response data

**Example**:
```javascript
import { Actions as RemoteActions } from 'common/actions/remote';

const result = await RemoteActions.do({
  id: 'action_123'
});
```

### connect(data)

Creates a WebSocket connection.

**Parameters**:
- `data`: object (required) - WebSocket connection configuration
  - `url`: string (required) - WebSocket server endpoint URL
  - `protocols`: Array<string> (optional) - List of WebSocket sub-protocols
  - `cardId`: string (optional) - Card identifier for authentication
  - `masterId`: string (optional) - Card master identifier for authentication
  - `credentials`: object (optional) - Authentication credentials object
  - `options`: object (optional) - Connection options
    - `source`: string (required) - Source identifier for connection tracking
    - `retries`: number (required) - Maximum number of reconnection attempts

**Returns**: Promise<string> - Unique socket identifier for the established connection

**Events** (emitted via `Applet.to`):
- `socket.message` - Message received, with data: `{ id, message }`
- `socket.error` - Socket error, with data: `{ id, error }`
- `socket.close` - Socket closed, with data: `{ id }`

**Example**:
```javascript
const socketId = await RemoteActions.connect({
  url: 'wss://api.example.com/ws',
  protocols: ['v1'],
  options: {
    source: 'user_123',
    retries: 3
  }
});
```

### send(data)

Sends data through WebSocket.

**Parameters**:
- `data`: object (required) - Message configuration
  - `id`: string (required) - Socket identifier for the target connection
  - `message`: any (required) - Message payload to send

**Returns**: Promise<void> - Resolves when message is successfully sent

**Example**:
```javascript
await RemoteActions.send({
  id: 'socket_123',
  message: {
    type: 'update',
    data: { status: 'active' }
  }
});
```

### close(data)

Closes a WebSocket connection.

**Parameters**:
- `data`: object (required) - Close configuration
  - `id`: string (required) - Socket identifier to close

**Returns**: Promise<void> - Resolves when connection is successfully closed

**Example**:
```javascript
await RemoteActions.close({
  id: 'socket_123'
});
```

### clean(source)

Cleans up WebSocket connections for a source.

**Parameters**:
- `source`: string (required) - Source identifier for connections to clean

**Returns**: Promise<void> - Resolves when all connections are cleaned up

**Example**:
```javascript
await RemoteActions.clean('user_123');
```

### api(data, master, card)

Makes a remote API call with automatic card context and spot information.

**Parameters**:
- `data`: object (required) - API request configuration
  - `method`: string (required) - HTTP request method
  - `url`: string (required) - API endpoint URL
  - `headers`: object (optional) - Custom HTTP request headers
  - `body`: object (optional) - Request payload data
  - `cardId`: string (optional) - Card identifier for authentication
  - `masterId`: string (optional) - Card master identifier for authentication
  - `credentials`: string (optional) - Credentials identifier
  - `showLoading`: boolean (optional) - Toggle loading indicator
  - `resolveError`: boolean (optional) - Handle errors as resolved responses
- `master`: object (optional) - Card master context information
- `card`: object (optional) - Card context information

**Returns**: Promise<object> - API response data

**Example**:
```javascript
const response = await RemoteActions.api({
  method: 'POST',
  url: '/api/v1/users',
  body: {
    name: 'John Doe'
  },
  headers: {
    'Content-Type': 'application/json'
  },
  showLoading: true
}, master, card);
```

## Error Handling

Remote actions follow the standard error pattern:

```javascript
try {
  await RemoteActions.api({...});
} catch (error) {
  switch (error.code) {
    case 'cardNotFound':
      // Handle card not found
      break;
    case 'cardMasterNotFound':
      // Handle card master not found
      break;
    case 'socketNotFound':
      // Handle socket not found
      break;
    default:
      // Handle other errors
  }
}
```

Common error codes:
- `cardNotFound` - Card not found when using card credentials
- `cardMasterNotFound` - Card master not found when using master credentials
- `socketNotFound` - WebSocket not found for given ID