# Location Actions

Location actions handle location-based services including navigation, geolocation, distance calculations, and place management.

## Actions

### navigate(data)

Navigates to a specified location using the device's navigation system.

**Parameters**:
- `data`: object (required) - Navigation configuration object
  - `to`: object (required) - Destination coordinates object
    - `lat`: number (required) - Latitude coordinate value
    - `lng`: number (required) - Longitude coordinate value
  - `options`: object (optional) - Navigation configuration options
    - `mode`: string (optional) - Navigation mode type ('driving', 'walking', 'transit')
    - `confirm`: boolean (optional) - Whether to show confirmation dialog
    - `place`: object (optional) - Additional place details
      - `name`: string (required) - Display name of the place

**Returns**: Promise<void> - Resolves when navigation is successfully initiated

**Example**:
```javascript
await LocationActions.navigate({
  to: {
    lat: 37.7749,
    lng: -122.4194
  },
  options: {
    mode: 'driving',
    confirm: true
  }
});
```

### current()

Gets the current location information.

**Parameters**: None

**Returns**: Promise<object> - Current location details object containing:
- `country`: object (required) - Country information
  - `code`: string (required) - Two-letter country code
  - `name`: string (required) - Full country name
- `location`: object (required) - Detailed location information
  - `address`: string (required) - Complete formatted address
  - `city`: string (required) - City/locality name
  - `state`: string (required) - State/province name
- `position`: object (required) - Geographic position
  - `lat`: number (required) - Latitude coordinate
  - `lng`: number (required) - Longitude coordinate

**Example**:
```javascript
const location = await LocationActions.current();
```

### checkin(data, master, card)

Checks in at a location.

**Parameters**:
- `data`: object (required) - Check-in configuration data
  - `spot`: object (required) - Location spot details
    - `placeId`: string (required) - Unique place identifier
  - `options`: object (optional) - Check-in configuration options
    - `noEvent`: boolean (optional) - Whether to suppress event triggers
  - `cardMasterIds`: Array<string> (optional) - List of card master identifiers
  - `fulfillments`: Array<string> (optional) - List of fulfillment type identifiers
  - `through`: object (optional) - Through relationship details
- `master`: object (required) - Master context object
- `card`: object (optional) - Card context object

**Returns**: Promise<object> - Returns spot details object if successful, undefined if failed

**Example**:
```javascript
const result = await LocationActions.checkin({
  spot: {
    placeId: 'place_123'
  },
  cardMasterIds: ['master_1']
}, masterContext, cardContext);
```

### setspot(data, cardmaster, card)

Alias for checkin action.

### removespot(data, cardmaster, card)

Removes the current spot check-in.

**Parameters**:
- `data`: object (required) - Removal configuration
  - `cardId`: string (required) - Unique card identifier
  - `cardMasterIds`: Array<string> (required) - List of card master identifiers
- `cardmaster`: object (required) - Card master context object
- `card`: object (optional) - Card context object

**Returns**: Promise<void> - Resolves when spot is successfully removed

### distance({ from, to })

Calculates distance between two points.

**Parameters**:
- `from`: object (optional) - Starting coordinates (defaults to current position)
  - `lat`: number (required) - Latitude coordinate
  - `lng`: number (required) - Longitude coordinate
- `to`: object (required) - Destination coordinates
  - `lat`: number (required) - Latitude coordinate
  - `lng`: number (required) - Longitude coordinate

**Returns**: Promise<number> - Distance in meters between the two points

### duration({ from, to, cardId })

Calculates travel duration between points.

**Parameters**:
- `from`: object (required) - Starting coordinates object
- `to`: object (required) - Destination coordinates object
- `cardId`: string (optional) - Card identifier for context

**Returns**: Promise<object> - Duration calculation result containing:
- `duration`: number (required) - Travel time in seconds
- `distance`: number (required) - Distance in meters
- `atPlace`: boolean (required) - Indicates if destination is a known place
- `place`: object (optional) - Place details if atPlace is true

### nearestplaces(data)

Finds nearest places.

**Parameters**:
- `data`: object (required) - Search configuration
  - `radius`: number (optional) - Search radius in meters
  - `cardId`: string (optional) - Card identifier for filtering
  - `cardMasterId`: string (optional) - Card master identifier for filtering

**Returns**: Array<object>|object - Returns array of place objects within radius if radius provided, otherwise returns nearest place object

## Error Handling

Location actions follow the standard error pattern:

```javascript
try {
  await LocationActions.checkin({
    spot: {
      placeId: 'place_123'
    }
  });
} catch (error) {
  // Handle error
  console.log(error);
}
```

Note: The implementation logs errors but doesn't use specific error codes. Errors are handled through standard Error objects. 