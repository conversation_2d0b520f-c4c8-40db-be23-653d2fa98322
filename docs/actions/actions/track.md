# Track Actions

Actions for tracking analytics, events, and logging.

## Actions

### log(data)

Logs a message with optional topic and priority.

**Parameters**:
- `data`: object - Log data (required)
  - `message`: string - Message to log (required)
  - `topic`: string - Topic category (optional)
  - `to`: string - Destination (optional)
  - `priority`: number - Priority level (optional)

**Returns**: Promise<void> - Resolves when message is logged

**Example**:
```javascript
await Actions.log({
  message: 'User completed onboarding',
  topic: 'onboarding',
  priority: 1
});
```

### event(data)

Tracks an application event with associated data.

**Parameters**:
- `data`: object - Event data (required)
  - `name`: string - Event name (required)
  - `data`: object - Event data (required)
    - `id`: string - Event ID (optional)
    - `masterId`: string - Master ID (optional)
  - `defn`: object - Event definition (optional)
    - `name`: string - Definition name (required)
    - `sync`: boolean - Whether to sync event (optional)
    - `card`: object - Card details (optional)
      - `id`: string - Card ID field name (required)
      - `fields`: Array<string> - Fields to include (required)

**Returns**: Promise<void> - Resolves when event is tracked

**Example**:
```javascript
await Actions.event({
  name: 'card.view.test',
  data: {
    id: '123',
    masterId: '456'
  },
  defn: {
    name: 'card.view.test',
    sync: true,
    card: {
      id: 'id',
      fields: ['id', 'number', 'displayName']
    }
  }
});
```

### watch(data)

Watches for errors or issues with optional topic and priority.

**Parameters**:
- `data`: object - Watch data (required)
  - `message`: string - Watch message (required)
  - `error`: Error - Error object (required)
  - `topic`: string - Topic category (optional)
  - `priority`: number - Priority level (optional)

**Returns**: Promise<void> - Resolves when watch is registered

**Example**:
```javascript
await Actions.watch({
  message: 'Failed to load profile',
  error: new Error('Network timeout'),
  topic: 'profile',
  priority: 2
}); 