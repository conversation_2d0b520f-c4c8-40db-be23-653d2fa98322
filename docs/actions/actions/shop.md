# Shop Actions

Actions for managing shopping and product functionality.

## Actions

### products(data, master)

Fetches and displays product details.

**Parameters**:
- `data`: object - Action input configuration object (required)
  - `gtins`: Array<string> - List of product Global Trade Item Numbers (GTINs) to fetch (required)
  - `products`: Array<object> - Pre-loaded product data to display instead of fetching (optional)
  - `merchantId`: string - Unique identifier for specific merchant to filter products (optional)
  - `url`: string - Custom product detail page URL override (optional)
  - `api`: object - API configuration settings for product fetch (optional)
  - `appOptions`: object - Application display and behavior settings (optional)
    - `membersOnly`: boolean - When true, filters for members-only products (optional)
    - `fetchDetails`: boolean - When true, retrieves extended product details (optional)
    - `singleSoldAt`: boolean - When true, displays single merchant view (optional)
  - `context`: object - Contextual data about the action (optional)
    - `source`: string - Identifier for where the action was triggered from (optional)
- `master`: object - Master application state and context object (required)

**Returns**: Promise<void> - Resolves when products are successfully fetched and displayed in the UI

**Example**:
```javascript
await Actions.products({
  gtins: ['123456789'],
  merchantId: 'store-1',
  appOptions: {
    membersOnly: true,
    fetchDetails: true
  },
  context: {
    source: 'search'
  }
}, master);
```

### likebrand(data, master)

Toggles favorite status for a merchant/brand.

**Parameters**:
- `data`: object - Action input configuration object (required)
  - `merchantId`: string - Unique identifier of merchant to toggle like status (required)
- `master`: object - Master application state and context object (required)

**Returns**: Promise<void> - Resolves when brand like status is successfully updated in the system

**Example**:
```javascript
await Actions.likebrand({
  merchantId: '123'
}, master);
```

### brand(data, master)

Opens brand search interface or displays brand details.

**Parameters**:
- `data`: object - Action input configuration object (required)
  - `merchantId`: string - Unique identifier of merchant to display details for (required)
  - `cardId`: string - Associated card identifier for tracking (optional)
  - `query`: string - Search terms for brand lookup functionality (optional)
  - `callbacks`: object - Custom callback functions for brand interactions (optional)
  - `context`: object - Contextual data about the action (optional)
    - `source`: string - Identifier for where the action was triggered from (optional)
- `master`: object - Master application state and context object (required)

**Returns**: Promise<void> - Resolves when brand view is successfully opened and displayed

**Example**:
```javascript
await Actions.brand({
  merchantId: '123',
  query: 'shoes',
  context: {
    source: 'search'
  }
}, master);