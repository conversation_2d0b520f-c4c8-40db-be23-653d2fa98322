# Network Actions

Actions for checking network connectivity and status.

## Actions

### info()

Gets current network information.

**Parameters**: None

**Returns**: Promise<object> - Returns detailed network status including connectivity, connection type, and network configuration details

- `isInternetReachable`: boolean (required) - Indicates if internet is currently accessible through any network interface
- `isConnected`: boolean (required) - Indicates if device has an active network connection to any network
- `type`: string (required) - Network connection type identifier (possible values: 'wifi', 'cellular', 'ethernet', 'none', 'unknown')
- `details`: object (required) - Detailed network connection configuration and status
  - `strength`: number (optional) - Signal strength percentage from 0-100 for wireless connections (wifi/cellular only)
  - `ssid`: string (optional) - Network name/SSID identifier (wifi only)
  - `ipAddress`: string (required) - <PERSON>ce's current network IP address
  - `subnet`: string (required) - Network subnet mask in CIDR notation

**Example**:
```javascript
try {
  const networkInfo = await Actions.network.info();
  
  if (networkInfo.isInternetReachable) {
    console.log('Connected to:', networkInfo.type);
    console.log('IP Address:', networkInfo.details.ipAddress);
  } else {
    console.log('No internet connection available');
  }
} catch (error) {
  console.error('Failed to get network info:', error);
}
```