# Engage Actions

Engage actions handle user engagement features including dialogs, prompts, applets, tickets, and member interactions.

## Actions

### dialog(data)

Shows a dialog with customizable content and actions. The dialog will lock the app's DND (Do Not Disturb) state while shown.

**Parameters**:
- `data`: object (required) - Dialog configuration object
  - `title`: string (required) - Display title for the dialog
  - `message`: string (required) - Main content message to show
  - `buttons`: Array<object> (required) - List of action buttons
    - `text`: string (required) - Button label text
    - `action`: object (optional) - Action configuration to execute when pressed
    - `callback`: function (optional) - Function to call when button is pressed
    - `style`: string (optional) - Visual style of button ('default', 'cancel', 'destructive')
  - `cancelable`: boolean (optional) - Whether dialog can be dismissed by tapping outside

**Returns**: Promise<void> - Resolves when dialog is dismissed or a button is pressed

**Example**:
```javascript
await engage.dialog({
  title: "Confirm Action",
  message: "Are you sure you want to proceed?",
  buttons: [
    { text: "Cancel", style: "cancel" },
    { text: "OK", style: "default" }
  ],
  cancelable: true
});
```

### optionsdialog(data)

Shows a dialog with selectable options.

**Parameters**:
- `data`: object (required) - Options dialog configuration
  - `title`: string (required) - Dialog header text
  - `options`: Array<object> (required) - List of selectable options

**Returns**: Promise<void> - Resolves with selected option when dialog is closed

### pbox(data)

Shows a prompt box for user interaction.

**Parameters**:
- `data`: object (required)
  - `object`: string (required) - Object type ('card', 'offer', 'reward', 'message')
  - `id`: string (required) - Object ID
  - `cardId`: string (optional) - Card ID
  - `masterId`: string (optional) - Card master ID
  - `title`: string (required) - Box title
  - `message`: string (required) - Box message
  - `options`: object (optional) - Additional options
  - `callback`: Function (optional) - Completion callback

**Returns**: Promise<void> - Resolves when prompt is closed

### dialogBox(data)

Shows a dialog box with custom content.

**Parameters**:
- `data`: object (required)
  - `id`: string (required) - Dialog ID
  - `avatarImage`: string (required) - Avatar image URL
  - `title`: string (required) - Dialog title
  - `message`: string (required) - Dialog message

**Returns**: Promise<void> - Resolves when dialog box is closed

### applet(data, cardMaster, card)

Launches an interactive applet.

**Parameters**:
- `data`: object (required) - Applet launch configuration
  - `applet`: object (required) - Applet definition object
    - `html`: string (optional) - Custom HTML content
    - `uri`: string (required) - Resource location for applet
    - `resources`: Array<string> (optional) - Additional resource URLs
    - `param`: object (optional) - Applet parameters
      - `title`: string (required) - Display title
      - `logoImage`: string (optional) - URL for logo display
      - `style`: object (optional) - Custom style definitions
      - `buttons`: Array<object> (optional) - Interactive buttons
  - `options`: object (optional) - Additional launch settings
    - `interact`: boolean (optional) - Enable user interaction
- `cardMaster`: object (required) - Card master context data
- `card`: object (optional) - Associated card data

**Returns**: Promise<void> - Resolves when applet execution completes

**Example**:
```javascript
await engage.applet({
  applet: {
    uri: "https://example.com/applet",
    param: {
      title: "My Applet"
    }
  }
}, cardMaster, card);
```

### ticket(data, cardMaster, card)

Shows a digital ticket or pass.

**Parameters**:
- `data`: object (required)
  - `cardId`: string (required) - Card ID
  - `masterIds`: string/Array (required) - Card master ID(s)
  - `ids`: Array<string> (required) - Ticket IDs
  - `spot`: object (required) - Location spot
  - `cardMasterIds`: Array<string> (required) - Card master IDs
- `cardMaster`: object (required) - Card master details
- `card`: object (required) - Card details

**Returns**: Promise<void> - Resolves when ticket is closed

### checkin(data, cardMaster, card)

Performs a check-in action.

**Parameters**:
- `data`: object (required)
  - `spot`: object (required) - Location spot
  - `ticket`: object (required) - Ticket details
  - `cardMasterIds`: Array<string> (required) - Card master IDs
- `cardMaster`: object (required) - Card master details
- `card`: object (required) - Card details

**Returns**: Promise<void> - Resolves when check-in is completed

### next(cardId, includeCards)

Gets and executes the next engagement action.

**Parameters**:
- `cardId`: string (required) - Card ID to filter engagements
- `includeCards`: boolean (required) - Whether to include card engagements

**Returns**: Promise<void> - Resolves when next engagement action is completed

### joinmember(data)

Initiates the member join flow.

**Parameters**:
- `data`: object (required)
  - `cardMasterIds`: Array<string> (required) - Card master IDs
  - `membersOnly`: boolean (required) - Whether this is a members-only action
  - `title`: string (required) - Dialog title
  - `message`: string (required) - Dialog message
  - `style`: object (required) - Dialog styles
  - `avatar`: string (required) - Avatar image URL
  - `options`: object (required) - Join options

**Returns**: Promise<void> - Resolves when member join flow is completed

### auth(data)

Authenticates using biometrics.

**Parameters**:
- `data`: object (required)
  - `title`: string (required) - Auth prompt title
  - `message`: string (required) - Auth prompt message
  - `confirm`: string (required) - Confirmation message

**Returns**: Promise<void> - Resolves when authentication is completed

### updateapp(data)

Shows an app update prompt.

**Parameters**:
- `data`: object (required) - Update options

**Returns**: Promise<void> - Resolves when app update prompt is closed

### rateapp(data)

Shows an app rating prompt.

**Parameters**:
- `data`: object (required) - Rating options

**Returns**: Promise<void> - Resolves when app rating prompt is closed

### action(data)

Executes a generic action.

**Parameters**:
- `data`: object (required) - Action details

**Returns**: Promise<void> - Resolves when action is completed

## Error Handling

Engage actions follow the standard error pattern:

```javascript
try {
  await EngageActions.applet({...});
} catch (error) {
  switch (error.code) {
    case 'APPLET_NOT_FOUND':
      // Handle missing applet
      break;
    case 'INVALID_PARAMS':
      // Handle invalid parameters
      break;
    case 'USER_CANCEL':
      // Handle user cancellation
      break;
    case 'BACKGROUND':
      // Handle app in background
      break;
    case 'DND':
      // Handle Do Not Disturb active
      break;
    default:
      // Handle other errors
  }
}
```

Common error codes:
- `DIALOG_CANCELED` - User canceled dialog
- `INVALID_INPUT` - Invalid user input
- `APPLET_NOT_FOUND` - Applet not found
- `TICKET_EXPIRED` - Ticket has expired
- `MEMBERSHIP_EXISTS` - User already a member
- `BACKGROUND` - App is in background
- `DND` - Do Not Disturb is active