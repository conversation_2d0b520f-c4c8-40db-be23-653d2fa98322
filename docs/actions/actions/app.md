# App Actions

App actions handle core application functionality like navigation, color scheme management, data synchronization, and updates.

## Actions

### navto(data)

Navigates to a specified route in the application.

**Parameters**:
- `data`: object (required) - Navigation configuration object
  - `route`: Array<object> (required) - Array of route objects defining the navigation path
    - `card`: object (optional) - Card navigation configuration
      - `id`: string (required) - Unique identifier for the target card
    - `widget`: object (optional) - Widget navigation configuration
      - `key`: string (required) - Unique identifier for the target widget
    - `offer`: object (optional) - Offer navigation configuration
      - `id`: string (required) - Unique identifier for the target offer
    - `discover`: object (optional) - Discover navigation configuration
      - `tags`: string (required) - Comma-separated list of discovery tags
    - `me`: object (optional) - Profile navigation configuration

**Returns**: Promise<void> - Resolves when navigation is complete without any data

**Example**:
```javascript
import { Actions as AppActions } from 'common/actions/app';

// Navigate to a card
await AppActions.navto({
  route: [{ card: { id: '12345' } }]
});

// Navigate to a card's offer
await AppActions.navto({
  route: [
    { card: { id: '12345' } },
    { offer: { id: '6789' } }
  ]
});
```

### colorscheme()

Gets the current color scheme (light/dark mode).

**Parameters**: None

**Returns**: Promise<string> - Current color scheme value ('light' or 'dark')

**Example**:
```javascript
const scheme = await AppActions.colorscheme();
console.log(`Current color scheme: ${scheme}`);
```

### sync()

Synchronizes all application data with the server.

**Parameters**: None

**Returns**: Promise<object> - Result object from the Sync.all() operation containing sync status

**Example**:
```javascript
try {
  await AppActions.sync();
} catch (error) {
  console.error('Sync failed:', error);
}
```

### update(data)

Updates the application by opening the relevant app store or downloading updates.

**Parameters**:
- `data`: object (optional) - Store configuration for different platforms
  - `android`: object (optional) - Android platform store configurations
    - `xiaomi`: string (optional) - Custom Xiaomi store application URL
    - `huawei`: string (optional) - Custom Huawei store application URL
    - `google`: string (optional) - Custom Google Play store application URL
  - `ios`: object (optional) - iOS platform store configurations
    - `apple`: string (optional) - Custom Apple App Store application URL

**Returns**: Promise<void> - Resolves when update process is successfully initiated

**Example**:
```javascript
// Using default store links
await AppActions.update();

// Using custom store links
await AppActions.update({
  android: {
    xiaomi: 'https://app.mi.com/details?id=com.example.app'
  }
});
```

## Error Handling

App actions follow the standard error pattern:

```javascript
try {
  await AppActions.sync();
} catch (error) {
  switch (error.code) {
    case 'NETWORK_ERROR':
      // Handle network connectivity issues
      break;
    case 'VERSION_MISMATCH':
      // Handle version incompatibility
      break;
    case 'STORAGE_FULL':
      // Handle storage issues
      break;
    default:
      // Handle other errors
  }
}
```

Common error codes:
- `NETWORK_ERROR` - Network connectivity issues
- `VERSION_MISMATCH` - App version incompatibility
- `STORAGE_FULL` - Insufficient storage space
- `INVALID_ROUTE` - Invalid navigation route
- `UPDATE_REQUIRED` - Mandatory update required