# Web Actions

Actions for handling web content and URLs.

## Actions

### open(data)

Opens a web URL with optional context.

**Parameters**:
- `data`: object - Web action data (required)
  - `param`: object - URL parameters (required)
    - `owner`: function - Owner context function (optional)
    - `web`: string - Web URL to open (required)
    - `uri`: string - URI to open (optional)
    - `mode`: string - Opening mode (required)
    - `context`: object - URL context (optional)
      - `affiliate`: boolean - Whether URL is affiliate link (optional)
      - `merchantId`: string - Merchant ID (optional)
      - `app`: object - App context (optional)

**Returns**: Promise<void> - Resolves when URL is opened

**Example**:
```javascript
await Actions.open({
  param: {
    web: 'https://example.com',
    mode: 'browser',
    context: {
      affiliate: true,
      merchantId: '123'
    }
  }
});
```

### search(data)

Triggers a web search event.

**Parameters**:
- `data`: object - Search parameters (required)
  - `query`: string - Search query (required)
  - `filters`: object - Search filters (optional)
    - `category`: string - Category filter (optional)
    - `sort`: string - Sort order (optional)

**Returns**: Promise<void> - Resolves when search is complete

**Example**:
```javascript
await Actions.search({
  query: 'search term',
  filters: {
    category: 'all',
    sort: 'relevance'
  }
}); 