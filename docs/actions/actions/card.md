# Card Actions

Card actions handle loyalty card operations including requesting new cards, adding cards to wallet, checking qualifications, and managing card states.

## Actions

### request(data)

Requests a new loyalty card.

**Parameters**:
- `data`: object - Action configuration data (required)
  - `masterId`: string - Unique identifier for the card master template (required)
- `master`: object - Master context containing environment settings (required)

**Returns**: Promise<void> - Resolves when card request is processed successfully, rejects with error if validation fails

**Example**:
```javascript
await CardActions.request({
  masterId: 'card_master_123'
});
```

### add(data)

Adds one or more cards to the user's wallet.

**Parameters**:
- `data`: object - Card addition configuration (required)
  - `masterId`: string - Single card master template identifier (optional)
  - `cardMasterIds`: Array<string> - Multiple card master template identifiers (optional)
  - `options`: object - Card registration options (optional)
    - `principalCardId`: string - Identifier of the principal card for linking (optional)
    - `through`: object - Attribution tracking information (optional)
- `master`: object - Master context containing environment settings (required)

**Returns**: Promise<object> - Created card object with following properties:
- `id`: string - Unique identifier of the created card
- `masterId`: string - Associated card master template identifier
- `formData`: object - Collected form data for the card
- `image`: object - Card image configuration
- `useCustomImage`: boolean - Flag indicating custom image usage
- `imageIndex`: number - Index of selected card image
- `cardImage`: object - Detailed card image properties

**Example**:
```javascript
const card = await CardActions.add({
  masterId: 'card_master_123',
  options: {
    principalCardId: 'principal_123'
  }
});
```

### qualified(data)

Checks if a user is qualified for a specific card.

**Parameters**:
- `data`: object - Qualification check configuration (required)
  - `masterId`: string - Card master template identifier to verify (required)
- `master`: object - Master context containing environment settings (required)

**Returns**: Promise<boolean> - Resolves to true if user meets qualification criteria, false otherwise

**Example**:
```javascript
const isQualified = await CardActions.qualified({
  masterId: 'card_master_123'
});
```

### clockin(data, master, card)

Records a clock-in event for time-based cards.

**Parameters**:
- `data`: object - Clock-in event details (required)
  - `placeId`: string - Unique identifier of the location (required)
  - `name`: string - Display name of the location (required)
  - `type`: string - Category of the location (required)
  - `position`: Array<object> - Detailed position coordinates and metadata (required)
  - `ttl`: number - Time-to-live duration in minutes (optional)
  - `range`: number - Acceptable range in meters (optional)
- `master`: object - Master context containing environment settings (required)
- `card`: object - Card context with current card state (required)

**Returns**: Promise<object> - Location spot details containing:
- `spot`: object - Created spot information
  - `placeId`: string - Location identifier
  - `name`: string - Location name
  - `type`: string - Location category
  - `position`: Array<object> - Position details
  - `ttl`: number - Time-to-live value
  - `range`: number - Range value

**Example**:
```javascript
const spot = await CardActions.clockin({
  placeId: 'place_123',
  name: 'Coffee Shop',
  type: 'retail',
  position: [{ table: '1' }],
  ttl: 60,
  range: 100
}, master, card);
```

### clockout(data, master, card)

Records a clock-out event for time-based cards.

**Parameters**:
- `data`: object - Clock-out configuration (required)
  - `cardId`: string - Identifier of the card to clock out (optional)
- `master`: object - Master context containing environment settings (required)
- `card`: object - Card context with current card state (required)

**Returns**: Promise<void> - Resolves when clock-out operation completes successfully

**Example**:
```javascript
await CardActions.clockout({
  cardId: 'card_123'
}, master, card);
```

## Events

Card actions emit the following events:
- `Card.new` - When a new card is added to the wallet
- `Card.clockIn` - When a card is clocked in
- `Card.clockOut` - When a card is clocked out

## Error Handling

Card actions follow the standard error pattern:

```javascript
try {
  await CardActions.add({...});
} catch (error) {
  switch (error.code) {
    case 'CARD_EXISTS':
      // Handle duplicate card
      break;
    case 'NOT_QUALIFIED':
      // Handle qualification requirements
      break;
    case 'INVALID_MASTER':
      // Handle invalid card master
      break;
    case 'CARD_NOT_FOUND':
      // Handle card not found
      break;
    case 'LOCATION_REQUIRED':
      // Handle missing location
      break;
    case 'OUTSIDE_HOURS':
      // Handle operation outside allowed hours
      break;
    default:
      // Handle other errors
  }
}
```

Common error codes:
- `CARD_EXISTS` - Card already exists in wallet
- `NOT_QUALIFIED` - User not qualified for card
- `INVALID_MASTER` - Invalid card master ID
- `CARD_NOT_FOUND` - Card not found
- `LOCATION_REQUIRED` - Location needed for operation
- `OUTSIDE_HOURS` - Operation outside allowed hours