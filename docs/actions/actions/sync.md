# Sync Actions

Sync actions handle data synchronization between local and remote storage.

## Actions

### all

Sync all data. This method is debounced with a 15-second wait time.

**Parameters**: None

**Returns**: Promise<object> - Results of all sync operations containing status and counts for each synced component
- `deferred`: object (required) - Deferred operations status container
  - `status`: string (required) - Current status of deferred operations (e.g., "pending", "complete")
  - `count`: number (required) - Total count of deferred items processed
- `cache`: object (required) - Cache synchronization results
  - `updated`: Array<string> (required) - List of cache keys that were updated
  - `deleted`: Array<string> (required) - List of cache keys that were removed
- `cardmasters`: object (required) - Card synchronization results
  - `synced`: number (required) - Total number of cards successfully synchronized
  - `conflicts`: Array<object> (required) - List of detected synchronization conflicts
- `settings`: object (required) - Settings synchronization results
- `actions`: object (required) - Actions synchronization results
- `appevents`: object (required) - Application events synchronization results
- `places`: object (required) - Places synchronization results
- `placeList`: object (required) - Place list synchronization results
- `messageResources`: object (required) - Message resources synchronization results
- `images`: object (required) - Image synchronization results

**Example**:
```javascript
const result = await SyncActions.all();
console.log(result.cache.updated); // ['key1', 'key2']
```

### cache

Sync cache data. This method is debounced with a 10-second wait time.

**Parameters**: None

**Returns**: Promise<void> - Resolves when cache synchronization is complete

**Example**:
```javascript
await SyncActions.cache();
```

## Error Handling

Sync actions use standard error handling:

```javascript
try {
  await SyncActions.all();
} catch (error) {
  switch (error.code) {
    case 'SYNC_IN_PROGRESS':
      // Handle sync in progress
      break;
    case 'NETWORK_ERROR':
      // Handle network error
      break;
    case 'CONFLICT_ERROR':
      // Handle conflict error
      break;
    default:
      // Handle other errors
  }
}
```

Common error codes:
- `SYNC_IN_PROGRESS` - Sync already running
- `NETWORK_ERROR` - Network connectivity issue
- `CONFLICT_ERROR` - Data conflict detected
- `INVALID_STATE` - Invalid sync state
- `TIMEOUT` - Operation timeout
- `STORAGE_ERROR` - Storage error
- `VERIFICATION_ERROR` - Verification failed
- `PERMISSION_DENIED` - Permission denied

## Implementation Details

- All sync methods use debouncing to prevent excessive API calls
- Default wait times:
  - ALL_WAIT: 15000ms (15 seconds)
  - CACHE_WAIT: 10000ms (10 seconds)
  - APPEVENT_WAIT: 500ms

- The sync process is initialized with:
  - Cache
  - CardMaster
  - Place
  - Action
  - Settings
  - AppEvent

- Sync operations are integrated with a sync service that handles:
  - Device identification
  - Background state detection
  - API communication
  - Resource fetching