# Permissions Actions

Actions for handling app permissions and user authorization.

## Actions

### request(data)

Checks and requests a permission if not granted.

**Parameters**:
- `data`: object (required) - Permission request configuration
  - `feature`: string (required) - Permission feature name to request (e.g., 'camera', 'location')
  - `type`: string (required) - Permission access type to request (e.g., 'always', 'whenInUse')
  - `showRationale`: boolean (optional) - If true, displays permission rationale dialog to user
- `master`: object (required) - Master configuration object

**Returns**: Promise<boolean> - Resolves with permission status
  - `true` - Permission was successfully granted
  - `false` - Permission was denied or request failed

**Example**:
```javascript
const granted = await Actions.permissions.request({
  feature: 'camera',
  type: 'always',
  showRationale: true
});
```

### ask(data)

Prompts user for permission without checking current status.

**Parameters**:
- `data`: object (required) - Permission request configuration
  - `feature`: string (required) - Permission feature name to request (e.g., 'camera', 'location')
  - `type`: string (required) - Permission access type to request (e.g., 'always', 'whenInUse')
  - `showRationale`: boolean (optional) - If true, displays permission rationale dialog to user
- `master`: object (required) - Master configuration object

**Returns**: Promise<boolean> - Resolves with permission status
  - `true` - Permission was successfully granted
  - `false` - Permission was denied or request failed

### check(data)

Checks current permission status.

**Parameters**:
- `data`: object (required) - Permission check configuration
  - `feature`: string (required) - Permission feature name to check (e.g., 'camera', 'location')
- `master`: object (required) - Master configuration object

**Returns**: Promise<boolean> - Resolves with current permission status
  - `true` - Permission is currently granted
  - `false` - Permission is currently denied

**Example**:
```javascript
const hasPermission = await Actions.permissions.check({
  feature: 'location'
});