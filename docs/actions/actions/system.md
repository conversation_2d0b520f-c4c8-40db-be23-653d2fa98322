# System Actions

Actions for interacting with system-level features including settings, clipboard, and app launching.

## Actions

### settings()

Opens device's system settings for the application.

**Parameters**: None

**Returns**: Promise<void> - Resolves when settings are opened

**Example**:
```javascript
await Actions.system.settings();
```

### time()

Gets current system time in milliseconds.

**Parameters**: None

**Returns**: Promise<number> - Current time in milliseconds

**Example**:
```javascript
const timestamp = await Actions.system.time();
```

### toclipboard(data)

Copies text to device's clipboard.

**Parameters**:
- `data`: object - Clipboard data (required)
  - `text`: string - Text to copy (required)

**Returns**: Promise<void> - Resolves when text is copied

**Example**:
```javascript
await Actions.system.toclipboard({
  text: 'Text to copy'
});
```

### fromclipboard()

Retrieves text from device's clipboard.

**Parameters**: None

**Returns**: Promise<string> - Text from clipboard

**Example**:
```javascript
const clipboardText = await Actions.system.fromclipboard();
```

### openapp(data)

Opens another application using URI schemes.

**Parameters**:
- `data`: object - App launch configuration (required)
  - `param`: object - URI parameters (required)
    - `uri`: string - URI to open (required)
  - `platform`: object - Platform-specific configurations (optional)
    - `ios`: object - iOS-specific parameters (optional)
    - `android`: object - Android-specific parameters (optional)

**Returns**: Promise<void> - Resolves when app is opened

**Example**:
```javascript
await Actions.system.openapp({
  param: {
    uri: 'otherapp://open'
  },
  platform: {
    ios: {
      param: {
        uri: 'otherapp://ios/open'
      }
    },
    android: {
      param: {
        uri: 'otherapp://android/open'
      }
    }
  }
});
```

**Errors**:
- `APP_NOT_FOUND` - Target app not installed
- `INVALID_URI` - Invalid URI format
- `SCHEME_NOT_SUPPORTED` - URI scheme not supported 