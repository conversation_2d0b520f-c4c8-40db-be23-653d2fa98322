# NFC Actions

NFC actions handle Near Field Communication (NFC) operations including reading and writing tags, managing passwords, and handling NFC-based interactions. The implementation uses `react-native-nfc-manager` and supports both iOS and Android platforms.

## Actions

### read(data, master, card)

Reads data from an NFC tag. Falls back to QR code scanner if NFC is not available.

**Parameters**:
- `data`: object (required) - Configuration object for read operation
  - `options`: object (required) - Read operation options
    - `content`: boolean (optional) - Read content without executing action (default: true)
    - `description`: string (optional) - Custom message shown during read operation (default: "Hold near NFC tag")
    - `decode`: boolean (optional) - Whether to decode content using PerkdCode
    - `fallback`: boolean (optional) - Enable scanner fallback if NFC unavailable (default: true)
    - `actions`: Array<object> (optional) - Actions to execute when fallback is true
    - `callbacks`: object (optional) - Callback functions for read operation
      - `validate`: function (optional) - Custom validation function for read content
- `master`: object (required) - Master configuration object
- `card`: object (optional) - Card configuration object

**Returns**: Promise<string | object> - Returns either raw tag content (string) or decoded payload (object)

**Example**:
```javascript
import { Actions as NFCActions } from 'common/actions/nfc';

const tag = await NFCActions.read({
  options: {
    content: true,
    description: 'Reading membership card',
    decode: true
  }
}, master, card);
```

### write(data, master, card)

Writes data to an NFC tag.

**Parameters**:
- `data`: object (required) - Configuration object for write operation
  - `value`: string | object (required) - Content to write to tag
  - `options`: object (optional) - Write operation options
    - `description`: string (optional) - Custom message shown during write operation (default: "Hold near NFC tag")
    - `encode`: boolean (optional) - Whether to encode content using PerkdCode (default: true)
    - `host`: string (optional) - Host domain for encoding (default: 'app.perkd.me')
    - `type`: string (optional) - Record type, either 'text' or 'uri'
    - `success`: string (optional) - Custom success message
- `master`: object (required) - Master configuration object
- `card`: object (optional) - Card configuration object

**Returns**: Promise<boolean> - Returns true if write operation successful, false otherwise

**Example**:
```javascript
await NFCActions.write({
  value: {
    cardId: 'card_123',
    type: 'membership',
    level: 'gold'
  },
  options: {
    description: 'Writing to card'
  }
}, master, card);
```

### setpassword(data)

Sets a password on an NFC tag. Supports NTAG 210, 212, 213, 215, 216, and Ultralight EV1.

**Parameters**:
- `data`: object (required) - Password configuration object
  - `password`: string (required) - Password to set on the tag
  - `options`: object (optional) - Password operation options
    - `description`: string (optional) - Custom message shown during operation (default: "Hold near NFC tag")

**Returns**: Promise<void> - Resolves when password is successfully set on tag

**Example**:
```javascript
await NFCActions.setpassword({
  password: 'secretpass123',
  options: {
    description: 'Setting password'
  }
});
```

### removepassword(data)

Removes password protection from an NFC tag.

**Parameters**:
- `data`: object (required) - Password removal configuration
  - `password`: string (required) - Current password on the tag
  - `options`: object (optional) - Password removal options
    - `description`: string (optional) - Custom message shown during operation (default: "Hold near NFC tag")

**Returns**: Promise<void> - Resolves when password is successfully removed from tag

**Example**:
```javascript
await NFCActions.removepassword({
  password: 'secretpass123',
  options: {
    description: 'Removing password'
  }
});
```

## Error Handling

NFC actions follow the standard error pattern:

```javascript
try {
  await NFCActions.read({...});
} catch (error) {
  switch (error.code) {
    case 'NFC_UNAVAILABLE':
      // Handle NFC not available
      break;
    case 'NFC_NOT_ENABLED':
      // Handle NFC not enabled
      break;
    case 'NFC_USER_CANCELLED':
      // Handle user cancellation
      break;
    case 'NFC_TIMEOUT':
      // Handle timeout
      break;
    case 'NFC_INVALID_TAG':
      // Handle invalid tag
      break;
    default:
      // Handle other errors
  }
}
```

Common error codes:
- `NFC_UNAVAILABLE` - NFC not supported on device
- `NFC_NOT_ENABLED` - NFC is disabled in system settings
- `NFC_USER_CANCELLED` - User cancelled the operation
- `NFC_TIMEOUT` - Operation timed out
- `NFC_INVALID_TAG` - Tag is invalid or unrecognized
- `NFC_WRONG_PASSWORD` - Incorrect password for protected tag
- `NFC_TAG_READONLY` - Tag is read-only
- `NFC_INSUFFICIENT_MEMORY` - Not enough space on tag
- `NFC_INCOMPATIBLE_TAG` - Tag not compatible with operation

## Platform-Specific Notes

### iOS
- Uses native NFC dialog for tag interaction
- Supports custom success messages
- Has a longer close delay (4000ms vs 1500ms on Android)

### Android
- Uses custom modal overlay for tag interaction
- Requires explicit NFC permission handling
- Supports background operations 