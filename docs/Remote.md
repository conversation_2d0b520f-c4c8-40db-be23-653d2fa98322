# Remote API System

## Overview

The Remote API System provides a standardized interface for making HTTP requests to both Perkd backend services and third-party APIs. It abstracts away the complexity of network communication while providing essential features like authentication, request/response handling, error management, and data synchronization.

## Table of Contents

- [Overview](#overview)
- [Purpose](#purpose)
- [Configuration](#configuration)
- [Features](#features)
- [Architecture](#architecture)
- [Headers](#headers)
- [Error Handling](#error-handling)
- [Usage Guidelines](#usage-guidelines)
- [Integration Points](#integration-points)
- [Common Issues](#common-issues)

## Purpose

The Remote system serves as the communication layer between the application and server-side resources, enabling:

- Authenticated API calls to Perkd services
- Communication with third-party services
- Automatic token management and refresh
- Standardized error handling
- Data synchronization between server and local storage
- Request deduplication through transaction management
- Network monitoring and diagnostics

## Configuration

### API Endpoints

```json
{
  "APIs": {
    "perkd": "https://api.perkd.io/v1",
    "crm": "https://crm.waveo.com/api"
  }
}
```

### Request Configuration

```json
{
  "remote": {
    "timeout": 30000,
    "retryCount": 3
  }
}
```

### Request Parameters

```json
{
  "method": "get|post|put|delete", 
  "uri": "/path/to/resource",
  "url": "https://full.url/to/resource",
  "crm": true|false,
  "routeParams": { "id": "value" },
  "qs": { "filter": "value" },
  "params": { "paramName": "value" }, 
  "body": { "data": "value" },
  "formData": true|false,
  "headers": { "custom-header": "value" },
  "timeout": 30000,
  "token": "jwt-token-string",
  "idempotencyKey": "unique-request-id",
  "responseType": "json|stream",
  "zip": true|false,
  "spot": { "id": "place-id" },
  "options": {
    "transaction": true|false,
    "rawResponse": true|false,
    "showTimeoutAlert": true|false
  }
}
```

### Parameter Priority and URL Construction

When making API calls, the system uses these rules to determine the final URL and request behavior:

1. **URL Construction Priority**:
   - If `url` is provided, it's used as the complete URL
   - If `uri` is provided with `crm: true`, the URL becomes `{APIs.crm}{uri}`
   - If only `uri` is provided, the URL becomes `{APIs.perkd}{uri}`

2. **Parameter Replacement**:
   - `routeParams` are used to replace parts of the URI/URL pattern like `/users/{userId}`
   - `qs` parameters are added as query string parameters

3. **Perkd API Detection Logic**:
   - A request is considered a "Perkd API" call if ANY of these are true:
     - The `uri` parameter is provided (defaults to Perkd endpoint)
     - The `url` includes the configured Perkd API base URL
     - The `url` includes the configured CRM API base URL
   - Only Perkd API calls receive automatic authentication and installation/location headers

4. **Parameter Overrides**:
   - If both token state and request.token exist, request.token takes precedence
   - If both location.spot and request.spot exist, request.spot takes precedence

### Headers Configuration

```json
{
  "headers": {
    "Content-Type": "application/json",
    "x-access-token": "jwt-token-string",
    "api-version": "2.0",
    "custom-auth": "Bearer token",
    "Idempotency-Key": "unique-request-id" 
  }
}
```

### Error Response Format

```json
{
  "code": "error_code",
  "message": "Human readable error message",
  "statusCode": 400,
  "details": {
    "field": "additional_info"
  }
}
```

## Features

- **Automatic Authentication** - Token management and refresh for Perkd APIs
- **Data Synchronization** - Automatic syncing of server changes to local models
- **Transaction Management** - Prevents duplicate concurrent requests
- **Standardized Error Handling** - Consistent error responses across API calls
- **Request Idempotency** - Support for idempotent requests via keys
- **Request Tracing** - AWS X-Ray compatible tracing identifiers
- **Timeout Management** - Configurable request timeouts with appropriate error responses
- **Network Status Detection** - Identification of offline states and appropriate error handling
- **Stream Response Handling** - Support for streaming API responses
- **Request Compression** - Automatic compression for large requests
- **Card Integration** - Support for card and card master credential injection

## Architecture

The Remote API System follows a layered architecture with clear separation of concerns:

1. **Request Interface** - Entry point for API calls via the `call()` function
2. **Authentication Layer** - Handles token injection and refresh for Perkd APIs
3. **Network Layer** - Manages the HTTP communication with appropriate headers
4. **Response Processing** - Handles API responses and standardizes errors
5. **Data Synchronization** - Processes sync data from responses to update local models

### Transaction Management

The system prevents duplicate concurrent requests through transaction management:

- Each request can be assigned a unique transaction ID (generated using `getId()`)
- If a request with the same ID is already in progress, a `{ code: 102 }` response is returned
- After request completion, the transaction is marked as done
- This prevents potential race conditions and duplicate operations

The transaction process works as follows:
1. Generate a unique ID: `const requestId = getId()`
2. Include it in the body: `body: { options: { reqId: requestId } }`
3. Enable transaction tracking: `options: { transaction: true }`

## Headers

### Automatic Headers for Perkd APIs

| Header Name | Description | Example |
|-------------|-------------|---------|
| `Content-Type` | Content type of request | `application/json` or `multipart/form-data` |
| `x-amzn-trace-id` | AWS X-Ray tracing ID | `Root=1-5f93520a-b06649127e371903a2de979` |
| `x-access-token` | JWT authentication token | `eyJhbGciOiJS...` |
| `tenant-code` | Used when no token is available | `perkd` |
| `perkd-install` | JWT-encoded installation info | JWT containing device, os, app details |
| `perkd-location` | Base64-encoded location data | Base64-encoded location object |
| `Date` | Current UTC timestamp | `Wed, 21 Oct 2023 07:28:00 GMT` |
| `Idempotency-Key` | For idempotent requests | Unique key to prevent duplicate operations |

The installation data encoded in the `perkd-install` header includes:

```json
{
  "id": "installation-id",
  "app": "app-name-and-version",
  "device": "device-model",
  "locale": "en_US",
  "os": "ios|android",
  "regions": ["US", "CA"]
}
```

The location data in the `perkd-location` header contains:

```json
{
  "lat": 37.7749,
  "lng": -122.4194,
  "accuracy": 10,
  "speed": 0,
  "heading": 90,
  "altitude": 10,
  "timestamp": 1664321789000,
  "spot": {
    "id": "place-id",
    "placeId": "place-id"
  }
}
```

### Third-Party API Headers

For non-Perkd APIs (any URL not matching the configured Perkd or CRM domains), only these headers are automatically added:
- `Content-Type`: `application/json` or `multipart/form-data`
- `x-access-token`: If a token is provided
- `Date`: Current UTC timestamp

## Error Handling

The system standardizes errors into a consistent format and handles several categories:

### Error Categories

1. **Network Errors**
   - `network_offline`: Device is offline or unable to reach the network
   - Detection: "Network Error" message from HTTP client

2. **Timeout Errors**
   - `request_timeout`: Request exceeded the timeout limit
   - Detection: ECONNABORTED code or "timeout" in error message
   - Configurable alert via `showTimeoutAlert` option

3. **Server Errors**
   - Standardized from the server's error response
   - May include sync data to apply even in error cases
   - Special handling for token expiration messages

4. **Transaction Errors**
   - `invalid_request`: Missing or invalid transaction ID
   - `request_in_progress`: Duplicate request with same ID already processing (code 102)

5. **Unknown Errors**
   - `unknown_error`: Fallback for unrecognized errors

### Error Codes Reference

Common error codes you may receive:

| Error Code | Description | HTTP Status |
|------------|-------------|-------------|
| `network_offline` | Device is offline | N/A |
| `request_timeout` | Request timed out | N/A |
| `authentication_failed` | Invalid credentials | 401 |
| `token_expired` | Access token has expired | 401 |
| `validation_error` | Invalid request data | 400 |
| `not_found` | Resource not found | 404 |
| `server_error` | Internal server error | 500 |
| `insufficient_permission` | Lack of required permission | 403 |
| `duplicate_request` | Transaction already in progress | 200 (code: 102) |
| `unknown_error` | Unclassified error | Various |

### Special Error Cases

The system includes special handling for certain error types:

1. **Token Expiration**: When a token expires, the system:
   - Detects 401 responses with specific error messages indicating token expiration
   - Automatically initiates a token refresh process
   - Queues the original request to retry after successful token refresh
   - Maintains a promise chain so the calling code doesn't need to handle refresh logic
   - If refresh fails, propagates the authentication error to the original caller
   - Limits refresh attempts to prevent infinite loops (typically one refresh attempt per expired request)

2. **Transaction Conflicts**: For duplicate request IDs, returns `{ code: 102 }` instead of rejecting
   - This allows the caller to handle concurrent operations gracefully
   - Recommended approach is to wait and check if the first request succeeded
   - No automatic retry is attempted for transaction conflicts

3. **Synchronization During Errors**: Even when an error occurs, if the response contains sync data, it will still be applied
   - This ensures data consistency even during partial failures
   - The original error is still propagated to the caller after sync is applied

### Sync Data Format

When a response includes sync data, it follows this format:

```json
{
  "data": {
    // Regular response data
  },
  "sync": {
    "users": [
      { "id": "user-1", "name": "Updated Name", "status": "active" }
    ],
    "orders": [
      { "id": "order-1", "status": "completed" },
      { "id": "order-2", "status": "cancelled" }
    ]
  }
}
```

### Data Synchronization Process

When the system receives a response with sync data, it automatically processes it through the Changes system:

- The sync data is extracted from the response
- The data is organized by entity type (users, orders, etc.)
- Each entity update is applied to the corresponding model
- Local data store is updated to reflect the changes
- No manual processing is required by application code

### Network Recovery Behavior

The system handles network interruptions and recovery:

- Requests that fail due to network errors are not automatically retried
- Upon network recovery, the application must initiate new requests
- For critical operations, implement application-level retry logic

#### Retry Pattern for Critical Operations

```javascript
// Example retry pattern
import { call } from 'lib/common/remote';

// Configure retry options
const retryOptions = {
  maxRetries: 3,
  retryableErrors: ['network_offline', 'request_timeout'],
  backoffStrategy: 'exponential' // 1s, 2s, 4s, etc.
};

// Implement retry wrapper
function withRetry(requestConfig) {
  return call(requestConfig)
    .catch(error => {
      // Implement retry logic when appropriate
      // See Common Issues section for guidance
    });
}

// Usage
withRetry({
  uri: '/critical-operation'
});
```

### Pagination Handling

```javascript
// Example pagination pattern
import { call } from 'lib/common/remote';

// Fetch a single page
call({
  uri: '/resources',
  qs: { 
    page: 1,
    limit: 20
  }
})
.then(response => {
  const { items, hasMore, totalPages } = response;
  // Process current page data
});

// For multi-page collection processing, see Common Issues section
```

### Request Cancellation

```javascript
// Example of cancellable request
import { call, createCancelToken } from 'lib/common/remote';

// Create a token and make a cancellable request
const cancelToken = createCancelToken();
const request = call({
  uri: '/long-operation',
  cancelToken
});

// Cancel the request when needed
cancelToken.cancel('User cancelled operation');

// Check for cancellation in error handler
request.catch(error => {
  if (error.cancelled) {
    // Handle cancellation case
  }
});
```

### Rate Limiting

```javascript
// Example of handling rate limits
import { call } from 'lib/common/remote';

call({
  uri: '/rate-limited-api'
})
.catch(error => {
  if (error.statusCode === 429) {
    // Handle rate limiting according to best practices
    // See Rate Limiting Best Practices section
  }
});
```

## Usage Guidelines

### Basic API Call

```javascript
import { call } from 'lib/common/remote';

call({
  method: 'post',
  uri: '/path/to/resource',
  body: { key: 'value' }
})
.then(response => {
  // Handle success
})
.catch(error => {
  // Handle error
});
```

### Advanced Request Options

```javascript
call({
  method: 'post',
  uri: '/path/to/resource/{id}',
  routeParams: { id: '12345' },
  qs: { filter: 'active' },
  body: { data: 'value' },
  timeout: 15000,
  options: {
    transaction: true,
    rawResponse: false,
    showTimeoutAlert: true
  }
})
```

### Error Handling Pattern

```javascript
call({
  uri: '/resource'
})
.then(response => {
  // Handle success
})
.catch(error => {
  const { code, message, statusCode, details } = error;
  
  switch(code) {
    case 'network_offline':
      // Handle offline state
      break;
    case 'request_timeout':
      // Handle timeout
      break;
    default:
      // Handle other errors
  }
});
```

### File Upload

```javascript
call({
  uri: '/upload',
  body: formData,
  formData: true
})
```

### Stream Response Handling

```javascript
call({
  method: 'get',
  uri: '/stream/events',
  responseType: 'stream'
})
.node('!.*', (item) => {
  // Process each streaming item
})
.done(() => {
  // Handle stream completion
});
```

The stream response uses Oboe.js under the hood, with these key methods:
- `.node(pattern, callback)`: Process each node matching the JSONPath pattern
- `.done(callback)`: Called when stream is complete
- `.fail(callback)`: Called on streaming error
- `.abort()`: Cancel the stream

### Request Compression

```javascript
call({
  uri: '/api/data',
  body: largeDataObject,
  zip: true
})
```

When compression is enabled with `zip: true`:
- Payloads larger than 1KB are automatically compressed using gzip
- The `Content-Encoding: gzip` header is added
- The server must support gzip-encoded requests

### Card Credential Integration

```javascript
import { Actions } from 'lib/common/actions/remote';

Actions.api({
  url: 'https://api.example.com/resource',
  cardId: 'card-123',
  credentials: 'api_key'
})
```

### URL Template Substitution

```javascript
call({
  uri: '/users/{userId}/orders/{orderId}',
  routeParams: {
    userId: '123',
    orderId: '456'
  }
})
// Calls /users/123/orders/456
```

URL substitution supports:
- Path parameters via `{paramName}` syntax
- Query parameters via the special `{qs}` placeholder that gets replaced with the serialized query string

## Integration Points

### Authentication System

The Remote system integrates with the authentication module to:
- Retrieve current access tokens
- Refresh expired tokens automatically
- Handle authentication failures

The token refresh process checks if the request is for a Perkd API, retrieves the current token, and if expired, attempts to refresh it.

### Model Registry

Integration with the model registry enables:
- Automatic data synchronization
- Type-specific handling of server responses
- Model instantiation from API data

Data synchronization works through the `Changes` object with methods:
- `Changes.get(objects)`: Prepares objects for syncing by type
- `Changes.apply(changes)`: Processes server sync data into model operations

### Location Services

Integration with the location system:
- Automatically injects location data for Perkd APIs
- Supports spot-based location tracking
- Encodes location data efficiently for transmission

To override the default location with a specific spot:

```javascript
call({
  uri: '/nearby',
  spot: { id: 'place-123' }
})
```

### Card System

Integration with the card system enables:
- Automatic injection of card credentials
- Card-specific API requests
- Support for card masters and their credentials

## Common Issues

### Authentication Failures

Error patterns:
- `Authentication: token has expired` - Token refresh may have failed
- `401 Unauthorized` - Invalid or missing credentials

### Timeout Errors

For operations that may take longer:

```javascript
call({
  uri: '/large-operation',
  timeout: 60000 // 60 seconds
})
```

### Transaction Conflicts

When receiving `{ code: 102 }`:
- This indicates a duplicate request with the same ID is already in progress
- Wait for the original request to complete

To implement transaction management correctly:

```javascript
import { call, getId } from 'lib/common/remote';

const requestId = getId(); // Generate unique ID

call({
  uri: '/resource',
  body: {
    data: 'value',
    options: { reqId: requestId }
  },
  options: { transaction: true }
})
```

### Content Type Issues

- For file uploads, set `formData: true`
- For JSON data, ensure the body is a valid object (not a string)

### Extracting Full Response Data

By default, the system returns the response data directly. To access the full response including headers and status:

```javascript
call({
  uri: '/resource',
  options: { rawResponse: true }
})
.then(response => {
  const { data, status, headers } = response;
  console.log('Status:', status);
  console.log('Headers:', headers);
  console.log('Data:', data);
})
```
