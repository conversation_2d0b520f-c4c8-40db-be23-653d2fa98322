# Storage Architecture

## Overview

The Perkd application implements a sophisticated multi-tiered storage architecture designed to handle diverse data persistence requirements while maintaining optimal performance, security, and user experience. This architecture strategically employs different storage technologies based on data characteristics, access patterns, and security requirements, creating a robust foundation for the application's data management needs.

## Storage Architecture Principles

### Strategic Storage Selection

The storage architecture follows a **data-driven selection principle** where storage technology choice is determined by sophisticated decision logic:

**Data Sensitivity Classification**:
- **Highly Sensitive**: Credentials, payment tokens, biometric data → Keychain API with hardware encryption
- **Moderately Sensitive**: User personal data, card information → Realm with standard encryption
- **Public Data**: Application configuration, cached content → AsyncStorage with memory caching
- **Transient Data**: UI state, temporary calculations → In-memory cache with automatic cleanup

**Access Pattern Analysis**:
- **High Frequency Access**: Configuration and preferences use AsyncStorage with aggressive memory caching
- **Complex Queries**: Relational data with foreign keys and indexes use Realm database
- **Simple Key-Value**: Settings and flags use AsyncStorage with type-aware serialization
- **Temporary Access**: Session data and UI state use memory cache with TTL expiration

**Performance Requirements**:
- **Sub-millisecond**: Memory cache for immediate access to frequently used data
- **Sub-10ms**: AsyncStorage with memory cache for configuration data
- **Sub-50ms**: Realm database with indexed queries for structured data
- **Sub-100ms**: Keychain operations for secure credential retrieval

**Synchronization Patterns**:
- **Real-time Sync**: User-generated content uses Realm with delta tracking and background sync
- **Periodic Sync**: Configuration changes use AsyncStorage with scheduled synchronization
- **No Sync**: Temporary data uses memory cache without persistence requirements
- **Secure Sync**: Sensitive data uses keychain with encrypted transmission protocols

### Multi-Layered Storage Hierarchy

```mermaid
graph TB
    subgraph "Application Layer"
        A[User Interface]
        B[Business Logic]
    end

    subgraph "Storage Abstraction Layer"
        C[Storage API]
        D[Sync Coordinator]
        R[Storage Router]
    end

    subgraph "Storage Technologies"
        E[AsyncStorage<br/>Key-Value Store]
        F[Realm Database<br/>Object Storage]
        G[Keychain API<br/>Secure Storage]
        H[Memory Cache<br/>Transient Storage]
        I[File System<br/>Asset Storage]
    end

    subgraph "Data Categories"
        J[Configuration Data]
        K[Application Data]
        L[Sensitive Data]
        M[Temporary Data]
        N[Media Assets]
    end

    A --> C
    B --> C
    C --> R
    C --> D
    R --> E
    R --> F
    R --> G
    R --> H
    R --> I

    E --> J
    F --> K
    G --> L
    H --> M
    I --> N

    style A fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style B fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style C fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style D fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style R fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style E fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style F fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style G fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style H fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style I fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
```

## Storage Layer Specifications

### Key-Value Store (AsyncStorage)

**Technology**: React Native AsyncStorage  
**Primary Use**: Application configuration, user preferences, session state  
**Security Level**: Standard encryption  

**Business Logic**:
- **Configuration Persistence**: Stores application settings that survive app restarts
- **User Preference Management**: Maintains user-customized settings and interface preferences
- **Session State Preservation**: Preserves user session information across app lifecycle events
- **Type-Aware Serialization**: Automatically handles complex data types through custom serialization

**Performance Characteristics**:
- **Memory Caching**: Implements intelligent memory caching for frequently accessed values
- **Batch Operations**: Supports efficient multi-key operations for bulk configuration updates
- **Lazy Loading**: Loads configuration data on-demand to minimize startup time

**Use Cases**:
- Application theme preferences
- Language and localization settings
- User interface customizations
- Feature flags and experimental settings
- Authentication tokens (non-sensitive)

### Object Database (Realm)

**Technology**: Realm Database (Schema Version 278+)  
**Primary Use**: Structured application data with relationships  
**Security Level**: Standard encryption with optional field-level protection  

**Business Logic**:
- **Relational Data Management**: Handles complex object relationships and foreign key constraints
- **Schema Evolution**: Supports automated migrations for database schema updates
- **Transaction Management**: Ensures data consistency through ACID-compliant transactions
- **Query Optimization**: Provides indexed queries for efficient data retrieval

**Data Models**:
- **Core Entities**: Person, Card, CardMaster, Offer, Reward, Message, Place
- **Relationship Mapping**: Complex many-to-many and one-to-many relationships
- **Audit Trail**: Tracks creation, modification, and deletion timestamps
- **Soft Deletion**: Implements logical deletion for data recovery capabilities

**Performance Optimization**:
- **Bulk Operations**: Optimized bulk write operations (847 masters/second)
- **Lazy Loading**: Loads related objects on-demand to minimize memory usage
- **Index Strategy**: Strategic indexing on frequently queried fields
- **Compaction**: Automatic database compaction for optimal storage efficiency

### Secure Storage (Keychain API)

**Technology**: Platform-specific secure storage (iOS Keychain, Android Keystore)  
**Primary Use**: Credentials, tokens, payment information  
**Security Level**: Hardware-backed encryption where available  

**Business Logic**:
- **Domain Segregation**: Organizes credentials by domain (App, CardMaster, Card, Payment)
- **Credential Lifecycle**: Manages credential creation, rotation, and secure deletion
- **Access Control**: Implements biometric and device-based access controls
- **Encryption Strategy**: Leverages platform-specific encryption capabilities

**Security Policies**:
- **Biometric Protection**: Requires biometric authentication for sensitive operations
- **Device Binding**: Credentials are bound to specific device hardware
- **Automatic Expiration**: Implements time-based credential expiration
- **Secure Deletion**: Ensures cryptographic deletion of sensitive data

### In-Memory Cache

**Technology**: JavaScript objects and Maps  
**Primary Use**: Temporary data, performance optimization  
**Security Level**: Transient (cleared on app termination)  

**Business Logic**:
- **Performance Acceleration**: Caches frequently accessed data for immediate retrieval
- **Memory Management**: Implements intelligent cache eviction policies
- **Cache Invalidation**: Automatic cache invalidation based on data freshness
- **Category-Based Caching**: Specialized caching for different data categories

**Cache Strategies**:
- **Time-Based Expiration**: Configurable TTL for different data types
- **Size-Based Eviction**: LRU eviction when memory limits are reached
- **Dependency Tracking**: Invalidates dependent cache entries on data changes
- **Preemptive Loading**: Predictive caching based on user behavior patterns

### File System Storage

**Technology**: React Native File System  
**Primary Use**: Media assets, documents, temporary files  
**Security Level**: Platform-standard file permissions  

**Business Logic**:
- **Asset Management**: Organizes media files by type and usage context
- **Cache Optimization**: Implements sophisticated image caching strategies
- **Temporary File Handling**: Manages lifecycle of temporary files and cleanup
- **Path Management**: Maintains consistent file path organization across platforms

**Storage Organization**:
```
DocumentDirectory/
├── x.realm                 # Main database file
├── images/                 # User images and cached assets
├── cards/                  # Custom card images
└── tmp/                    # Temporary files

CacheDirectory/
├── images/                 # Cached remote images
└── shopimages/            # E-commerce product images

TemporaryDirectory/
├── shared/                # Shared temporary resources
├── notifications/         # Notification assets
└── Downloads/             # Downloaded files
```

## Data Flow Architecture

### User Experience Flow

```mermaid
sequenceDiagram
    participant U as User
    participant UI as User Interface
    participant BL as Business Logic
    participant SA as Storage Abstraction
    participant SR as Storage Router
    participant AS as AsyncStorage
    participant R as Realm
    participant K as Keychain
    participant C as Cache

    U->>UI: User Action
    UI->>BL: Process Request
    BL->>SA: Data Operation
    SA->>SR: Route Storage Request

    alt Configuration Data
        SR->>AS: Store/Retrieve Config
        AS->>C: Update Memory Cache
        C-->>AS: Cache Hit/Miss
        AS-->>SR: Config Response
    else Application Data
        SR->>R: Database Operation
        Note over R: Transaction with<br/>Relationship Validation
        R-->>SR: Query Results
    else Sensitive Data
        SR->>K: Secure Operation
        Note over K: Biometric Auth<br/>+ Hardware Encryption
        K-->>SR: Encrypted Response
    else Temporary Data
        SR->>C: Cache Operation
        Note over C: TTL-based<br/>Expiration
        C-->>SR: Immediate Response
    end

    SR-->>SA: Operation Result
    SA-->>BL: Processed Data
    BL-->>UI: Updated State
    UI-->>U: Visual Feedback
```

### Data Synchronization Flow

```mermaid
graph LR
    subgraph "Local Storage"
        A[Realm Database]
        B[AsyncStorage]
        C[Memory Cache]
    end
    
    subgraph "Sync Layer"
        D[Sync Coordinator]
        E[Conflict Resolution]
        F[Background Sync]
    end
    
    subgraph "Remote Services"
        G[API Services]
        H[File Storage]
        I[Push Notifications]
    end
    
    A <--> D
    B <--> D
    C <--> D
    D <--> E
    D <--> F
    E <--> G
    F <--> G
    G <--> H
    G <--> I
    
    style A fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style B fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style C fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style D fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style E fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style F fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
```

## Storage Lifecycle Management

### Data Lifecycle Rules

**Creation Phase**:
- **Validation**: All data undergoes validation before storage using schema-defined rules
- **Sanitization**: Input data is sanitized according to field-specific rules and type constraints
- **Relationship Integrity**: Foreign key relationships are validated and maintained through schema definitions
- **Audit Logging**: Creation events are logged with timestamps and user context for compliance
- **Transaction Safety**: All creation operations are wrapped in transactions with automatic rollback on failure

**Modification Phase**:
- **Optimistic Updates**: UI updates immediately while background sync ensures server consistency
- **Conflict Detection**: Automatic detection of concurrent modifications through timestamp comparison
- **Version Control**: Maintains version history for critical data changes using `modifiedAt` timestamps
- **Change Tracking**: Delta tracking for efficient synchronization using `_delta` fields
- **Upsert Operations**: Uses Realm's upsert capability (create with update=true) for efficient data persistence

**Deletion Phase**:
- **Soft Deletion**: Logical deletion preserves data for recovery using `deletedAt` timestamps
- **Purge Time Enforcement**: Automatic removal of data past `purgeTime` through background cleanup processes
- **Cascade Rules**: Automatic handling of dependent data relationships through `removeRelations` function
- **Secure Deletion**: Cryptographic deletion for sensitive information in keychain storage
- **Cleanup Scheduling**: Automated cleanup of expired temporary data and invalid objects
- **Relationship Cleanup**: Recursive deletion of related objects to prevent orphaned data

### Performance Thresholds

**Response Time Targets**:
- AsyncStorage operations: < 10ms for cached data (measured: 1.26ms average for 100 operations)
- Realm queries: < 50ms for indexed queries (measured: 1.18ms per object for bulk operations)
- Keychain operations: < 100ms for credential retrieval (measured: 10ms for single operations)
- Cache operations: < 1ms for memory-resident data
- Background sync: 10-second timeout with intelligent scheduling

**Storage Limits**:
- AsyncStorage: 6MB practical limit with automatic serialization optimization
- Realm database: Unlimited with automatic compaction on database open
- Memory cache: Dynamic sizing with LRU eviction based on available device memory
- File cache: Platform-dependent with intelligent cleanup based on usage patterns
- Category storage: 10-minute TTL with automatic recategorization triggers
- Background fetch: 48-hour minimum interval with configurable timeout

### Business Logic Enforcement

**State Transition Rules**: The storage layer enforces sophisticated business logic through state management:

**Entity State Management**:
- **Card States**: Supports issued, active, expired, suspended states with automatic transitions
- **Offer States**: Manages pending, active, expired, cancelled states with time-based activation
- **Message States**: Handles draft, published, expired states with purge time enforcement
- **Place States**: Manages active, inactive states with geographic and temporal constraints

**Validation Enforcement**:
- **Schema Validation**: Enforces data types, required fields, and format constraints at storage level
- **Business Rule Validation**: Implements complex validation rules spanning multiple entities
- **Relationship Integrity**: Maintains foreign key relationships and prevents orphaned data
- **Temporal Validation**: Enforces time-based constraints like expiration dates and validity periods

**Access Control Enforcement**:
- **Permission-Based Access**: Storage operations respect user permissions and role-based access
- **Context-Aware Security**: Additional security measures based on operation context and data sensitivity
- **Audit Trail Maintenance**: Automatic logging of all storage operations for compliance and debugging
- **Data Classification**: Automatic classification and routing based on data sensitivity levels

## Security Architecture

### Data Classification

**Public Data**: Non-sensitive application data stored in Realm with standard encryption
**Internal Data**: User preferences and configuration stored in AsyncStorage with device encryption
**Confidential Data**: User credentials and tokens stored in secure keychain with hardware backing
**Restricted Data**: Payment information with additional encryption layers and access controls

### Access Control Patterns

**Authentication-Based Access**: Sensitive operations require user authentication
**Device-Based Security**: Hardware-backed security for credential storage
**Time-Based Access**: Automatic session expiration and credential rotation
**Context-Aware Security**: Additional security measures based on usage context

### Encryption Strategies

**At-Rest Encryption**: All persistent storage uses platform-provided encryption
**In-Transit Encryption**: Network communications use TLS 1.3 with certificate pinning
**Application-Level Encryption**: Additional encryption for highly sensitive data
**Key Management**: Secure key derivation and rotation policies

## Storage Abstraction Patterns

### Cross-Storage-Layer Interactions

**Storage Routing Logic**: The application implements sophisticated routing logic that automatically selects appropriate storage based on data characteristics:

**Type-Based Routing**:
- **Realm Objects**: Entities with primary keys and relationships automatically route to Realm database
- **Configuration Keys**: String-based keys with simple values route to AsyncStorage with memory caching
- **Credential Domains**: Security-sensitive data routes to keychain storage with domain segregation
- **Temporary Data**: Session-based data routes to memory cache with automatic cleanup

**Performance-Based Routing**:
- **Frequent Access**: Data accessed multiple times per session uses memory cache as primary storage
- **Bulk Operations**: Large datasets use Realm's bulk write operations for optimal performance
- **Background Operations**: Long-running operations use `runAfterInteractions` for UI responsiveness
- **Memory Pressure**: System automatically evicts cache data during memory pressure events

**Security-Based Routing**:
- **Biometric Requirements**: Payment and credential data requires biometric authentication
- **Device Binding**: Sensitive tokens are bound to specific device hardware identifiers
- **Encryption Levels**: Different encryption strategies based on data classification levels
- **Access Control**: Time-based and context-aware access control for sensitive operations

### Storage Layer Coordination

**Transaction Coordination**: Complex operations spanning multiple storage layers use coordinated transactions:
- **Atomic Operations**: Multi-layer updates use transaction boundaries to ensure consistency
- **Rollback Procedures**: Failed operations trigger rollback across all affected storage layers
- **Dependency Management**: Storage operations respect dependency chains and foreign key relationships
- **Conflict Resolution**: Cross-layer conflicts are resolved using timestamp-based precedence rules

**Cache Coherence**: Multi-level caching maintains consistency across storage layers:
- **Write-Through Caching**: Updates propagate immediately to persistent storage
- **Cache Invalidation**: Changes in persistent storage trigger automatic cache invalidation
- **Dependency Tracking**: Cache entries track dependencies and invalidate related data automatically
- **Batch Updates**: Multiple cache updates are batched for optimal performance

## Integration Patterns

### Cross-Reference Documentation

- **[Application Architecture](./app-architecture.md)**: Overall system architecture and component interactions
- **[Schemas Documentation](./schemas.md)**: Complete storage schemas and data model specifications
- **[Synchronization Patterns](./sync.md)**: Detailed synchronization strategies and conflict resolution
- **[Security Policies](./security.md)**: Comprehensive security implementation details
- **[Performance Optimization](./performance.md)**: Storage performance tuning and monitoring

### Component Integration

**Business Logic Integration**: Storage operations are abstracted through service layers with automatic routing
**UI Component Integration**: Reactive data binding with automatic UI updates and optimistic rendering
**Background Processing**: Efficient background synchronization without blocking UI using interaction queuing
**Error Handling**: Comprehensive error recovery with automatic retry logic and user notification strategies

## Synchronization Architecture

### Intelligent Sync Strategies

**Priority-Based Synchronization**: The system implements sophisticated priority-based synchronization that categorizes data by importance and user impact:

- **Critical Data**: User credentials, payment information, and active cards sync immediately
- **High Priority**: Recent user actions, active offers, and frequently accessed places
- **Standard Priority**: Historical data, inactive cards, and background content
- **Low Priority**: Analytics data, logs, and archived content

**Adaptive Sync Scheduling**: Dynamic synchronization scheduling based on:
- **Network Conditions**: Adjusts sync frequency based on connection quality
- **Battery Status**: Reduces sync frequency on low battery to preserve device resources
- **User Activity**: Increases sync frequency during active usage periods
- **Data Freshness**: Prioritizes stale data for synchronization

### Conflict Resolution Strategies

**Optimistic Updates**: The application implements optimistic updates where user changes are immediately reflected in the UI while background synchronization ensures server consistency. This approach provides responsive user experience even during network latency.

**Intelligent Conflict Resolution**: When synchronization conflicts occur, the system employs sophisticated resolution strategies:

- **Last-Writer-Wins**: For simple configuration changes where the most recent update takes precedence
- **Field-Level Merging**: For complex objects where individual fields can be merged independently
- **User Choice Resolution**: For critical conflicts that require user intervention to resolve
- **Automatic Reconciliation**: For conflicts that can be resolved through business logic rules

**Deferred Operations**: The system implements a sophisticated deferred operation mechanism that queues failed network operations for automatic retry when connectivity is restored, ensuring data consistency even during network interruptions.

### Background Synchronization

**Background Fetch Configuration**: Configurable background synchronization with intelligent scheduling:
- **Minimum Interval**: 48 hours (2880 minutes) default with user customization
- **Timeout Management**: 10-second timeout for background operations
- **Battery Optimization**: Reduced sync frequency on low battery
- **Network Awareness**: Adapts to WiFi vs cellular connectivity

**Sync Models**: Comprehensive model synchronization covering:
- **Fetch Models**: Person, Preference, Card, Offer, Reward, Place, WidgetData
- **Upload Models**: Person, Preference, Card, Place, WidgetData
- **Selective Sync**: Ability to disable specific model synchronization

## Edge Case Handling

### Network Resilience

**Offline Capabilities**: The storage architecture provides robust offline functionality:
- **Local-First Design**: All operations work offline with background synchronization
- **Conflict-Free Replicated Data Types**: Ensures eventual consistency across devices
- **Intelligent Retry Logic**: Exponential backoff with jitter for failed operations
- **Network State Awareness**: Adapts behavior based on network connectivity status

**Connection Quality Adaptation**:
- **Bandwidth Detection**: Adjusts sync payload size based on available bandwidth
- **Latency Compensation**: Implements timeout adjustments based on network latency
- **Cellular Data Awareness**: Reduces sync frequency on cellular connections
- **WiFi Preference**: Prioritizes large data transfers when WiFi is available

### Performance Edge Cases

**Large Dataset Management**: When dealing with large amounts of data, the system implements sophisticated management strategies:
- **Pagination**: Efficient pagination for large query results
- **Lazy Loading**: Loads data on-demand to minimize memory usage
- **Background Processing**: Moves heavy operations to background threads
- **Memory Management**: Intelligent memory cleanup and garbage collection

**Storage Limit Handling**:
- **Automatic Cleanup**: Removes expired temporary files and cached data
- **User Notification**: Alerts users when storage limits are approached
- **Selective Deletion**: Prioritizes deletion of least important data
- **Compression**: Implements data compression for large datasets

### Data Corruption Recovery

**Integrity Validation**: Comprehensive data integrity checks with sophisticated error handling:
- **Object Validity Checks**: Validates Realm object integrity using `isValid()` before operations
- **Relationship Validation**: Ensures foreign key relationships remain valid through schema constraints
- **Schema Validation**: Validates data against current schema definitions during migrations
- **Transaction Rollback**: Automatic transaction cancellation and rollback on validation failures
- **Invalid Object Handling**: Graceful handling of invalid objects with logging and recovery procedures

**Backup and Recovery**:
- **Synchronization-Based Backup**: Uses multi-device synchronization as primary backup mechanism
- **Database Bundling**: Supports bundled database installation for initial data seeding
- **Migration Safety**: Automated schema migrations with rollback capabilities for data preservation
- **Emergency Recovery**: Manual recovery procedures including database reset and re-sync capabilities
- **Cleanup Procedures**: Automated cleanup of corrupted or orphaned data through housekeeping processes

## Advanced Storage Patterns

### Caching Strategies

**Multi-Level Caching**: Implements sophisticated multi-level caching for optimal performance:

1. **L1 Cache (Memory)**: Immediate access to frequently used data
2. **L2 Cache (AsyncStorage)**: Persistent cache for configuration and preferences
3. **L3 Cache (File System)**: Large asset caching with intelligent eviction
4. **L4 Cache (Database)**: Structured data with complex query capabilities

**Cache Invalidation Policies**:
- **Time-Based Expiration**: Configurable TTL for different data types
- **Event-Based Invalidation**: Cache invalidation triggered by data changes
- **Dependency Tracking**: Invalidates dependent cache entries automatically
- **Manual Invalidation**: User-triggered cache clearing for troubleshooting

**Image Caching Optimization**:
- **Progressive Loading**: Loads low-resolution images first, then high-resolution
- **Format Optimization**: Automatic format selection based on device capabilities
- **Size Optimization**: Dynamic image resizing based on display requirements
- **Preemptive Caching**: Predictive caching based on user behavior patterns

### Storage Optimization

**Database Optimization**:
- **Index Strategy**: Strategic indexing on frequently queried fields
- **Query Optimization**: Optimized query patterns for common operations
- **Bulk Operations**: Efficient bulk insert/update operations
- **Compaction**: Regular database compaction for optimal performance

**File System Optimization**:
- **Directory Organization**: Logical directory structure for efficient access
- **File Naming**: Consistent naming conventions for easy management
- **Cleanup Automation**: Automated cleanup of temporary and expired files
- **Storage Monitoring**: Continuous monitoring of storage usage and performance

## Monitoring and Maintenance

### Performance Monitoring

**Storage Metrics**: Comprehensive tracking of storage performance:
- **Query Performance**: Average query execution times and optimization opportunities
- **Cache Hit Rates**: Cache effectiveness across different storage layers
- **Storage Usage**: Disk space utilization and growth trends
- **Sync Performance**: Synchronization success rates and failure analysis

**User Experience Metrics**: Track storage-related impact on user experience:
- **Load Times**: Application startup and screen transition times
- **Responsiveness**: UI responsiveness during storage operations
- **Error Rates**: Storage-related error frequency and user impact
- **Battery Impact**: Storage operation impact on device battery life

### Maintenance Procedures

**Automated Maintenance**:
- **Database Compaction**: Automatic database compaction on initialization and periodic optimization
- **Purge Time Enforcement**: Automatic removal of entities past their `purgeTime` through background processes
- **Cache Cleanup**: Intelligent cleanup of expired cached data with TTL-based expiration
- **Clock Cache Management**: Automatic cleanup of expired location-based cache entries
- **Sync Delta Cleanup**: Automatic cleanup of synchronization delta records after successful sync
- **Reserved Order Cleanup**: Housekeeping of reserved offer orders based on payment status
- **Log Flushing**: Automatic log flushing and submission during background sync operations

**Manual Maintenance**:
- **Schema Migration**: Controlled database schema updates with rollback capabilities
- **Security Audits**: Regular security assessments and credential rotation
- **Data Validation**: Periodic data integrity checks and repair procedures
- **Backup Verification**: Regular backup integrity verification and restore testing

### Edge Case Handling Excellence

**Invalid Object Management**: The storage system implements sophisticated handling of edge cases:

**Realm Object Validity**:
- **Invalid Object Detection**: Automatic detection of invalid Realm objects using `isValid()` checks
- **Graceful Degradation**: Operations continue with valid objects when encountering invalid ones
- **Error Logging**: Comprehensive logging of invalid object scenarios for debugging
- **Recovery Procedures**: Automatic cleanup and recovery from invalid object states

**Transaction Edge Cases**:
- **Transaction Cancellation**: Automatic rollback when transactions are cancelled due to errors
- **Concurrent Modification**: Handling of concurrent modifications through optimistic locking
- **Memory Pressure**: Graceful handling of operations during low memory conditions
- **Background Interruption**: Proper handling of operations interrupted by app backgrounding

**Synchronization Edge Cases**:
- **Network Interruption**: Intelligent retry logic with exponential backoff for network failures
- **Partial Sync Failures**: Handling of scenarios where only some data synchronizes successfully
- **Clock Skew**: Proper handling of timestamp-based conflicts due to device clock differences
- **Large Dataset Sync**: Efficient handling of large dataset synchronization with pagination

**Storage Limit Edge Cases**:
- **AsyncStorage Quota**: Automatic cleanup when approaching AsyncStorage limits
- **Memory Cache Overflow**: LRU eviction and memory pressure handling
- **File System Limits**: Intelligent file cleanup when approaching storage limits
- **Database Corruption**: Detection and recovery procedures for database corruption scenarios

### Troubleshooting Framework

**Diagnostic Tools**:
- **Storage Inspector**: Real-time storage state inspection with object validity checking
- **Performance Profiler**: Detailed performance analysis with operation timing metrics
- **Sync Debugger**: Synchronization state tracking with conflict analysis and resolution logging
- **Error Reporter**: Comprehensive error reporting with context preservation and automatic categorization

**Recovery Procedures**:
- **Data Recovery**: Step-by-step procedures including database reset, re-sync, and backup restoration
- **Performance Recovery**: Procedures for cache cleanup, index rebuilding, and memory optimization
- **Sync Recovery**: Recovery from synchronization failures with conflict resolution and data reconciliation
- **Emergency Procedures**: Critical failure recovery including database compaction and storage reset
