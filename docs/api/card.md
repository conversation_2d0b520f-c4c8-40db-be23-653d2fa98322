# Card API Documentation

## Overview
The Card API provides functionality to manage cards, including registration, sharing, payments, and orders.

## Base URL
```
https://x3gw.perkd.me/api/
```

## Authentication
All API requests require authentication using an access token:
```
Authorization: Bearer <access_token>
```

## Common Parameters
All card endpoints accept these common parameters in their options object:

| Parameter | Type | Description |
|-----------|------|-------------|
| locale | String | Locale code (e.g., "en", "zh-Hant") |
| at | Number | Timestamp for the operation |

## Common Response Format
All responses include these fields when an error occurs:
```typescript
{
  code: string;      // Error code if operation failed
  message?: string;  // Optional error message
}
```

## Error Codes
| Code | Description |
|------|-------------|
| 102 | Processing - Operation already in progress |
| 400 | Bad Request - Invalid parameters |
| 401 | Unauthorized - Invalid or expired token |
| 404 | Not Found - Card or resource not found |
| 409 | Conflict - Card already registered/activated |

## Card Management

### Registration Operations

#### Register New Card
```
POST /CardMasters/{masterId}/app/register
```

Body Parameters:
```typescript
{
  personId: string;          // Required: Person ID
  formData: {               // Required: Registration form data based on master's form definition
    [key: string]: any;     // Fields as defined in the card master's form
  };
  options?: {               // Optional: Additional options
    locale?: string;        // Optional: e.g., "en", "zh-Hant"
    principalCardId?: string;// Optional: For supplementary cards
    sharer?: string;        // Optional: Sharer's ID
  };
  image?: {                 // Optional: Card image data
    front?: string;         // Optional: Front image URL
    thumbnail?: string;     // Optional: Thumbnail URL
    raw?: string;          // Optional: Original image URL
    transform?: {
      scale: number;
      rotate: number;
      cropX: number;
      cropY: number;
      cropW: number;
      cropH: number;
    };
  };
  imageIndex?: number;      // Optional: Index in master's image array
}
```

Response:
```typescript
{
  cardId: string;
}
```

#### Request Card
```
POST /CardMasters/{masterId}/app/request
```

Body Parameters:
```typescript
{
  personId: string;         // Required: Person ID
  options?: {              // Optional: Additional options
    locale?: string;       // Optional: Locale code
  };
}
```

Response:
```typescript
{
  cardId: string;
}
```

#### Register Issued Card
```
POST /Cards/{id}/app/register
```

Body Parameters:
```typescript
{
  profile: {               // Required: Profile information
    name: string;         // Required: User's name
    mobile: string;       // Required: Mobile number
    email?: string;       // Optional: Email address
  };
  options?: {             // Optional: Additional options
    locale?: string;      // Optional: Locale code
  };
}
```

Response:
```typescript
{
  card: Card;            // Registered card object
}
```

### Sharing Operations

#### Share Card
```
POST /Cards/{id}/app/share
```

Body Parameters:
```typescript
{
  recipient: {           // Required: Recipient information
    mobile: string;     // Required: Mobile number
    name?: string;      // Optional: Name
    email?: string;     // Optional: Email
    personId?: string;  // Optional: Person ID
  };
  mode: ShareMode;      // Required: Share mode
  options?: {           // Optional: Additional options
    message?: string;   // Optional: Custom message
    expiry?: number;    // Optional: Unix timestamp
  };
}
```

Where `ShareMode` is one of:
- `"invite"` - Default mode. Invite recipient to get a card
- `"transfer"` - Transfer card ownership to recipient (can only share to one person)
- `"send"` - Send a copy of the card to recipient (can only share to one person)

Response:
```typescript
{
  recipients: Array<{
    mobile: string;
    name?: string;
    shared: boolean;
    reason?: {
      code: string;     // Error code if share failed
    };
  }>;
}
```

#### Share to Multiple Users
```
POST /Cards/{id}/app/shareToMany
```

Body Parameters:
```typescript
{
  recipients: Array<{    // Required: Array of recipients
    mobile: string;     // Required: Mobile number
    name?: string;      // Optional: Name
    email?: string;     // Optional: Email
    personId?: string;  // Optional: Person ID
  }>;
  mode: ShareMode;      // Required: Share mode
  options?: {           // Optional: Additional options
    message?: string;   // Optional: Custom message
    expiry?: number;    // Optional: Unix timestamp
  };
}
```

Response: Same as Share Card response

#### Cancel Share
```
POST /Cards/{id}/app/share/cancel
```

Body Parameters:
```typescript
{
  sharingId: string;    // Required: ID of the sharing to cancel
}
```

### Card Status Operations

#### Accept Card
```
POST /Cards/{id}/app/accept
```

Body Parameters:
```typescript
{
  options: {
    at: number;  // Unix timestamp of acceptance
  };
}
```

Response:
```typescript
{
  card: Card;           // Updated card object
}
```

#### Decline Card
```
POST /Cards/{id}/app/decline
```

Body Parameters:
```typescript
{
  options: {
    at: number;          // Required: Unix timestamp of decline
  };
}
```

Response:
```typescript
{
  card: Card;           // Updated card object
}
```

#### Mark Cards as Notified
```
POST /Cards/app/notified
```

Body Parameters:
```typescript
{
  idList: string[];      // Required: Array of card IDs
  at: number;           // Required: Unix timestamp of notification
}
```

Response:
```typescript
{
  cards: Card[];        // Array of updated card objects
}
```

### Card Activation

#### Request Activation
```
GET /Cards/{id}/app/activate
```

Query Parameters:
```typescript
{
  mobile: string;         // Required: Mobile number
  options: {
    locale: string;      // Required: Locale code (e.g., "en", "zh-Hant")
  };
}
```

Response:
```typescript
{
  card: Card;           // Card object
}
```

#### Complete Activation
```
POST /Cards/{id}/app/activate
```

Body Parameters:
```typescript
{
  code: string;         // Required: Activation code
  mobile: string;       // Required: Mobile number
  name: string;         // Required: User's name
}
```

Response:
```typescript
{
  card: Card;           // Activated card object
}
```

## Payment Management

### Payment Operations

#### Create Payment Method
```
POST /CardMasters/{masterId}/app/v5/pay/method
```

Body Parameters:
```typescript
{
  personId: string;      // Required: Person ID
  method: {             // Required: Payment method details
    type: PaymentMethod;// Required: Payment method type
    provider: string;   // Required: Payment provider (e.g., "stripe", "mypay")
    country: string;    // Required: ISO country code
    brand?: CardNetwork;// Required for card type
    funding?: string;   // Optional: For card type (e.g., "credit", "debit")
    expiry?: string;    // Optional: For card type (Format: "MM/YY")
    last4?: string;     // Optional: For card type (Last 4 digits)
    tokenData?: string; // Optional: For digital wallet types
  };
}
```

Response:
```typescript
{
  status: "success";
  method: {
    id: string;        // Payment method ID
    type: PaymentMethod;
    provider: string;
    country: string;
    brand?: CardNetwork;
    last4?: string;
  };
}
```

#### Delete Payment Method
```
DELETE /CardMasters/{masterId}/app/v5/pay/method
```

Body Parameters:
```typescript
{
  personId: string;      // Required: Person ID
  method: {             // Required: Payment method to delete
    id: string;         // Required: Payment method ID
    type: PaymentMethod;// Required: Payment method type
    provider: string;   // Required: Payment provider
  };
}
```

Response:
```typescript
{
  status: "success";
}
```

### Order Operations

#### Create Order
```
POST /CardMasters/{masterId}/app/v5/order
```

Body Parameters:
```typescript
{
  personId: string;      // Required: Person ID
  type: PaymentMethod;   // Required: Payment method type
  order: {              // Required: Order details
    items: Array<{      // Required: Order items
      id: string;       // Required: Item ID
      name?: string;    // Optional: Item name
      sku?: string;     // Optional: Item SKU
      quantity: number; // Required: Item quantity
      price: number;    // Required: Item price
      tax?: number;     // Optional: Item tax
      cardId?: string;  // Optional: Associated card ID
      options?: Record<string, any>; // Optional: Additional item options
    }>;
    subtotalPrice: number; // Required: Subtotal before tax
    totalTax: number;      // Required: Total tax amount
    totalPrice: number;    // Required: Total price including tax
    taxIncluded?: boolean; // Optional: Whether tax is included in prices
    storeId?: string;      // Optional: Store ID
    fulfillment?: {        // Optional: Fulfillment details
      type: "store" | "deliver" | "pickup" | "dinein" | "vending";
      destination?: {
        type: "store" | "cstore" | "vending" | "private" | "commercial";
        placeId?: string;
        name?: string;
        street: string;
        city: string;
        state: string;
        postCode: string;
        country: string;
      };
      recipient?: {
        fullName: string;
        phone: string;
        email?: string;
      };
      scheduled?: {
        minTime: string;   // ISO datetime
        maxTime?: string;  // ISO datetime
      };
    };
  };
  options?: {            // Optional: Additional options
    locale?: string;     // Optional: Locale code
    booking?: {          // Optional: Booking details
      id?: string;
      queueNumber?: string;
      referenceId?: string;
    };
    guest?: boolean;     // Optional: Whether order is for guest
    capture?: boolean;   // Optional: Whether to capture payment immediately
  };
  idempotencyKey: string;// Required: Unique key to prevent duplicate orders
}
```

Response:
```typescript
{
  status: "success";
  order: {
    id: string;
    status: string;
    items?: Array<OrderItem>;
    payment?: {
      id: string;
      status: PaymentState;
      amount: number;
      currency: string;
      provider?: string;
      method?: string;
      intent?: {
        type: string;
        // Other intent fields based on payment method
      };
    };
  };
}
```

#### Pay Order
```
POST /CardMasters/{masterId}/app/v5/order/pay
```

Body Parameters:
```typescript
{
  personId: string;      // Required: Person ID
  type: PaymentMethod;   // Required: Payment method type
  payments: {            // Required: Payment details
    method: PaymentMethod;// Required: Payment method
    provider: string;    // Required: Payment provider
    amount: number;      // Required: Payment amount
    currency: string;    // Required: Currency code
    source: {           // Required: Payment source
      id?: string;      // Optional: Saved payment method ID
      token?: string;   // Optional: New payment method token
    };
  };
  order: {              // Required: Order details
    id: string;         // Required: Order ID
    items: OrderItem[]; // Required: Order items
    fulfillment?: {     // Optional: Fulfillment details
      type: string;
      destination?: any;
      recipient?: any;
    };
  };
  options?: {           // Optional: Additional options
    capture?: boolean;  // Optional: Whether to capture payment immediately
    setupFutureUsage?: boolean; // Optional: Whether to save payment method
    through?: Record<string, any>; // Optional: Additional payment options
    guest?: boolean;    // Optional: Whether order is for guest
  };
  idempotencyKey: string;// Required: Unique key to prevent duplicate payments
}
```

Response:
```typescript
{
  status: "success";
  payment: {
    id: string;
    status: PaymentState;
    amount: number;
    currency: string;
    provider: string;
    method: string;
    intent?: {
      type: string;
      // Payment method specific fields
    };
  };
  order?: {
    id: string;
    status: string;
  };
}
```

#### Commit Order
```
POST /CardMasters/{masterId}/app/v5/order/commit
```

Body Parameters:
```typescript
{
  personId: string;      // Required: Person ID
  type: PaymentMethod;   // Required: Payment method type
  payment: {            // Required: Payment details
    id: string;         // Required: Payment ID
    status: PaymentState;// Required: Payment status
  };
  order: {              // Required: Order details
    id: string;         // Required: Order ID
    status: string;     // Required: Order status
  };
  idempotencyKey: string;// Required: Unique key to prevent duplicate commits
}
```

Response:
```typescript
{
  status: "success";
  order: {
    id: string;
    status: string;
  };
  payment: {
    id: string;
    status: PaymentState;
  };
}
```

#### Cancel Order
```
POST /CardMasters/{masterId}/app/v5/order/cancel
```

Body Parameters:
```typescript
{
  personId: string;      // Required: Person ID
  type: PaymentMethod;   // Required: Payment method type
  payment: {            // Required: Payment details
    id: string;         // Required: Payment ID
    status: PaymentState;// Required: Payment status
  };
  idempotencyKey: string;// Required: Unique key to prevent duplicate cancellations
}
```

Response:
```typescript
{
  status: "success";
  order: {
    id: string;
    status: string;
  };
  payment: {
    id: string;
    status: PaymentState;
  };
}
```

#### Get Order Status
```
GET /CardMasters/{masterId}/app/v4/order/{id}
```

Response:
```typescript
{
  id: string;
  status: string;
  payment?: {
    status: PaymentState;
    amount: number;
    currency: string;
  };
  fulfillment?: {
    status: string;
    tracking?: string;
  };
}
```

#### Get Order Details
```
GET /CardMasters/{masterId}/app/v5/order/{id}
```

Response: Full `Order` object with current status

## Data Types and Models

### Card Components

#### Registration Form
```typescript
interface RegistrationForm {
  // The structure is dynamic based on the card master's form definition
  // These are common examples but not limited to:
  displayName?: string;
  cardNumber?: string | {
    number?: string;
    barcode?: string;
    barcodeType?: string;
  };
  startTime?: number;    // Unix timestamp
  endTime?: number;      // Unix timestamp
  name?: string;
  mobile?: string;
  email?: string;
  // ... other fields as defined in the card master's form definition
  [key: string]: any;    // Additional fields based on form definition
}
```

#### Card Image
```typescript
interface CardImageTransform {
  scale: number;
  rotate: number;
  cropX: number;
  cropY: number;
  cropW: number;
  cropH: number;
}

interface CardImageData {
  front?: string;      // Image URL/Base64
  thumbnail?: string;  // Image URL/Base64
  raw?: string;        // Original image URL/Base64
  transform?: CardImageTransform;
}
```

#### Profile Data
```typescript
interface ProfileData {
  name: string;
  mobile: string;
  email?: string;
}
```

### Payment Method Types

#### Basic Payment Method
```typescript
interface BasePaymentMethod {
  type: PaymentMethod;      // From PaymentMethod enum
  provider: string;         // e.g., "stripe", "mypay"
  country: string;         // ISO country code
}
```

#### Card Payment Method
```typescript
interface CardPaymentMethod extends BasePaymentMethod {
  type: "card";
  brand: CardNetwork;      // From CardNetwork enum
  funding?: string;        // e.g., "credit", "debit"
  expiry?: string;        // Format: "MM/YY"
  last4?: string;         // Last 4 digits
}
```

#### Digital Wallet Method
```typescript
interface WalletPaymentMethod extends BasePaymentMethod {
  type: "applepay" | "googlepay" | "alipay" | "grabpay" | "linepay";
  tokenData?: string;     // Encrypted payment token
}
```

### Order Components

#### Basic Order Info
```typescript
interface OrderBase {
  id?: string;
  channel?: string;
  subtotalPrice: number;
  totalTax: number;
  totalPrice: number;
  taxIncluded?: boolean;
  storeId?: string;
}
```

#### Order Item
```typescript
interface OrderItem {
  id: string;
  name?: string;
  sku?: string;
  quantity: number;
  price: number;
  tax?: number;
  cardId?: string;        // For card-related items
  options?: Record<string, any>;
}
```

#### Fulfillment Types
```typescript
type FulfillmentType = "store" | "deliver" | "pickup" | "dinein" | "vending";
type DestinationType = "store" | "cstore" | "vending" | "private" | "commercial";
```

#### Address
```typescript
interface Address {
  type: DestinationType;
  placeId?: string;
  name?: string;
  house?: string;
  level?: string;
  unit?: string;
  street: string;
  city: string;
  state: string;
  postCode: string;
  country: string;
  formatted: string;
  short?: string;
  geo?: {
    type: "Point";
    coordinates: [number, number]; // [longitude, latitude]
  };
}
```

#### Recipient
```typescript
interface Recipient {
  fullName: string;
  phone: string;
  email?: string;
}
```

#### Fulfillment
```typescript
interface Fulfillment {
  type: FulfillmentType;
  destination?: Address;
  origin?: {
    placeId: string;
    name: string;
  };
  recipient?: Recipient;
  scheduled?: {
    minTime: string;      // ISO datetime
    maxTime?: string;     // ISO datetime
  };
  note?: string;
}
```

### Common Enums

#### Payment Methods
```typescript
enum PaymentMethod {
  APPLEPAY = "applepay",
  GOOGLEPAY = "googlepay",
  ALIPAY = "alipay", 
  GRABPAY = "grabpay",
  LINEPAY = "linepay",
  CARD = "card",
  STOREDVALUE = "storedvalue",
  CRYPTO = "crypto",
  WALLET = "wallet",
  MANUAL = "manual",
  VOUCHER = "voucher"
}
```

#### Card Networks
```typescript
enum CardNetwork {
  VISA = "visa",
  MASTERCARD = "mastercard",
  AMEX = "amex",
  UNIONPAY = "unionpay",
  DISCOVER = "discover",
  INTERAC = "interac",
  JCB = "jcb"
}
```

#### Payment States
```typescript
enum PaymentState {
  PENDING = "pending",
  REQUIRES_ACTION = "requires_action",
  REQUIRES_CONFIRMATION = "requires_confirmation",
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled"
}
```

#### Card States
```typescript
enum CardState {
  ACTIVE = "active",
  INACTIVE = "inactive",
  PENDING = "pending",
  EXPIRED = "expired"
}
```

### Response Types

#### SuccessResponse
```typescript
interface CardResponse {
  card?: Card;          // Single card object (for single card operations)
  cards?: Card[];       // Array of card objects (for batch operations)
  // Additional fields depending on the operation
}
```

#### ErrorResponse
```typescript
interface ErrorResponse {
  code: string;         // Error code
  message: string;      // Error message
  details?: Record<string, any>;  // Additional error details
}
```

## Error Handling

### Common Error Codes
```typescript
{
  code: string;       // Error code
  message: string;    // Error message
  details?: any;      // Additional error details
}
```

- `payment_invalid_card` - Card not found or invalid
- `payment_card_expired` - Card has expired
- `payment_card_is_restored` - Card is restored
- `payment_card_maximum_limit` - Card limit exceeded
- `payment_setup_declined` - Payment setup declined
- `payment_setup_failed` - Payment setup failed
- `payment_order_invalid` - Invalid order
- `payment_order_not_qualified` - Order not qualified

### Payment Method Errors
- `payment_incorrect_cvc` - Invalid card CVC
- `payment_incorrect_expiry_month` - Invalid expiry month
- `payment_incorrect_expiry_year` - Invalid expiry year
- `payment_incorrect_number` - Invalid card number
- `payment_incorrect_address` - Invalid billing address
- `payment_incorrect_zip` - Invalid ZIP/postal code

### Payment State Errors
- `payment_pending` - Payment is pending
- `payment_canceled` - Payment was canceled
- `payment_expired` - Payment has expired
- `payment_declined` - Payment was declined
- `payment_failed` - Payment failed
- `payment_succeeded` - Payment already succeeded
- `payment_insufficient_funds` - Insufficient funds
- `payment_in_progress` - Payment is in progress

### Payment Configuration Errors
- `payment_type_not_found` - Payment type not found
- `payment_type_not_supported` - Payment type not supported
- `payment_provider_invalid` - Invalid payment provider
- `payment_method_invalid` - Invalid payment method
- `payment_amount_too_small` - Payment amount too small
- `payment_amount_too_large` - Payment amount too large
- `payment_amount_exceeded_limit` - Amount exceeds limit

### Card Operation Errors
- `card_not_found` - Card not found
- `card_not_qualified` - Card not qualified
- `card_is_restored` - Card is restored
- `card_maximum_limit` - Maximum card limit reached
- `card_has_excluded` - Card has been excluded
- `card_was_deleted` - Card was deleted
- `card_is_required` - Card is required
- `card_duplicated` - Card is duplicated
- `card_unsaved` - Card is unsaved

### General Errors
- `400` - Bad Request - Invalid parameters
- `401` - Unauthorized - Invalid or missing authentication
- `403` - Forbidden - Insufficient permissions
- `404` - Not Found - Resource not found
- `409` - Conflict - Resource conflict
- `500` - Internal Server Error

## Implementation Notes

### General
- All payment operations require an idempotency key to prevent duplicate transactions
- Card activation may require additional verification steps
- Order and payment operations maintain consistent states through commit/cancel operations

### Regional Considerations
- Payment methods vary by region and card master configuration
- Supported payment methods and card networks may vary by region

### Payment Methods
- Direct payment methods include: APPLEPAY, GOOGLEPAY, CARD, STOREDVALUE, CRYPTO, WALLET
- Partial payment methods include: STOREDVALUE, CRYPTO, WALLET, VOUCHER
- Share operations support different modes for different sharing scenarios 

## Parameter Types

### Card Object
```typescript
interface Card {
  id: string;
  masterId: string;
  cardName?: string;
  displayName?: string;
  number?: string;
  barcode?: string;
  barcodeType?: string;
  startTime?: Date;
  endTime?: Date;
  state: CardState;
  custom?: Record<string, any>;
  preIssued: boolean;
  useCustomImage?: boolean;
  options?: CardOptions;
  storedValue?: StoredValue;
  shareModes?: string[];
  visible?: boolean;
  personId: string;
  image?: CustomImage;
  cardImage?: CardMasterImage;
}
```

### CardOptions Object
```typescript
interface CardOptions {
  overrideMaster?: Record<string, any>;
  notification?: {
    enabled?: boolean;
    channels?: string[];
  };
  noDelete?: boolean;
  hideZeroBalance?: boolean;
  hideBalance?: boolean;
}
```

### CustomImage Object
```typescript
interface CustomImage {
  originalImage?: string;
  frontImage?: string;
  thumbImage?: string;
  cropImage?: string;
  cropCoord?: {
    scale: number;
    rotate: number;
    imageWidth: number;
    cropX: number;
    cropH: number;
    offsetY: number;
    widthPixels: number;
    platform: number;
    cropY: number;
    imageHeight: number;
    density: number;
    offsetX: number;
    heightPixels: number;
    cropW: number;
  };
}
```

### CardMasterImage Object
```typescript
interface CardMasterImage {
  id: string;
  front?: string;
  thumbnail?: string;
  irregularShape?: boolean;
  width?: number;
  height?: number;
  pHash?: string;
  transparency?: boolean;
}
```

### StoredValue Object
```typescript
interface StoredValue {
  balance: string;
  style?: {
    fontSize?: number;
    color?: string;
    container?: Record<string, any>;
    text?: Record<string, any>;
  };
}
```

### Payment Object
```typescript
interface Payment {
  method: PaymentMethod;
  network?: CardNetwork;
  currency: string;
  amount: number;
  partial?: boolean;
  through?: {
    provider?: string;
    reference?: string;
  };
  referenceId?: string;
}
```

### Order Object
```typescript
interface Order {
  items: OrderItem[];
  subtotalPrice: number;
  totalTax: number;
  totalPrice: number;
  business: {
    name: string;
    id?: string;
  };
  options?: {
    cardId?: string;
    guest?: boolean;
  };
}
```

### OrderItem Object
```typescript
interface OrderItem {
  id: string;
  sku?: string;
  name?: string;
  quantity: number;
  price: number;
  tax?: number;
  options?: Record<string, any>;
}
```

## Error Codes

| Code | Description |
|------|-------------|
| INVALID_CARD | Card not found or invalid |
| INVALID_MASTER | Card master not found or invalid |
| INVALID_PERSON | Person not found or invalid |
| PAYMENT_FAILED | Payment processing failed |
| DUPLICATE_REQUEST | Duplicate request detected |
| VALIDATION_ERROR | Invalid input parameters |
| PERMISSION_DENIED | Insufficient permissions |
| CARD_EXPIRED | Card has expired |
| CARD_INACTIVE | Card is not active |
| CARD_LOCKED | Card is locked | 