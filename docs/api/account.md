# Account API Documentation

## Overview
The Account API handles user authentication, registration, and account management.

## Base URL
```
https://x3gw.perkd.me/api/
```

## Authentication
All API requests require authentication using an access token:
```
Authorization: Bearer <access_token>
```

## Common Parameters
All account endpoints accept these common parameters:

| Parameter | Type | Description |
|-----------|------|-------------|
| locale | String | Locale code (e.g., "en", "zh-Hant") |
| channel | String | Communication channel for notifications |
| at | Number | Timestamp for the operation |

## Common Response Format
All responses include these fields when an error occurs:
```typescript
{
  code: string;      // Error code if operation failed
  message?: string;  // Optional error message
}
```

## Error Codes
| Code | Description |
|------|-------------|
| OFFLINE | Network connectivity issues |
| TIMEOUT | Request timeout |
| INVALID | Invalid request parameters |
| PROCESSING | Operation in progress |
| UNKNOWN | Unknown error |
| notFound | Account not found |
| notVerified | Mobile number not verified |
| mobileRegistered | Mobile number already registered |
| mobileNotRegistered | Mobile number not registered |
| mobileRequired | Mobile number required |
| mobileInUse | Mobile number in use |
| mobileInvalid | Invalid mobile number format |
| mobileChangeLimit | Mobile number change limit reached |
| passwordInvalid | Invalid password |
| passwordRequestLimit | Password reset request limit reached |
| vcodeRequestLimit | Verification code request limit reached |
| vcodeInvalid | Invalid verification code |
| deactivated | Account deactivated |

## Account Management

### Registration
```
POST /Accounts/app/register
```

Request:
```typescript
{
  profile: {
    mobile: string;      // Required: User's mobile number
  },
  options: {
    skipValidate?: boolean;  // Optional: Skip validation
    carrier?: string;        // Optional: Carrier information
    channel?: string;        // Optional: Communication channel
    locale?: string;         // Optional: Locale setting
  }
}
```

Response:
```typescript
{
  token: string;          // JWT token containing user information
}

// The token payload contains:
{
  user: {
    personId: string;    // Person ID
    accountId: string;   // Account ID
    // Note: Additional user fields may be present but are not documented here
  }
}
```

### Login
```
POST /Accounts/app/login
```

Request:
```typescript
// Password Login
{
  mobile: string;        // Required: User's mobile number
  password: string;      // Required: User's password
}

// OR Biometric Login
{
  touchID: true;        // Required: Use biometric authentication
}
```

Response:
```typescript
{
  token: string;          // JWT token containing user information
  toChangePassword?: boolean;  // Whether user needs to change password
  openId?: {             // OpenID information if available
    IdentityId: string;  // Identity ID
    Logins: {           // Login providers
      [provider: string]: string;  // Provider-specific tokens
    }
  }
}

// The token is a JWT containing:
{
  exp: number;          // Token expiration timestamp
  user: {
    personId: string;   // Person ID
    // Note: Additional user fields may be present but are not documented here
  }
}
```

### Logout
```
POST /Accounts/app/logout
```

Request:
```typescript
{
  checkPassword: true;    // Always true
  forced?: boolean;      // Optional: Force logout
}
```

### Launch
```
POST /Accounts/app/launch
```

Request:
```typescript
{
  installation: {
    id: string;          // Installation ID
    device: {
      uuid: string;      // Device UUID
      name: string;      // Device name
      brand: string;     // Device brand
      manufacturer: string; // Device manufacturer
      model: string;     // Device model
      simulator: boolean; // Whether running on simulator
      ip?: string;       // Optional: Device IP address
    };
    os: {
      name: string;      // OS name (ios/android)
      version: string;   // OS version
    };
    app: {
      id: string;        // App ID
      version: string;   // App version
      env: string;       // App environment
    };
    carrier: {
      name: string;      // Carrier name
      country: string;   // Carrier country code
      mobileCountryCode: string; // Mobile country code
    };
    locale: {
      languages: string[]; // Supported languages
      country: string;    // Country code
      currency: string;   // Currency code
    };
    capabilities: {
      name: string;      // Capability name (payment/biometrics/connectivity)
      support: string[]; // Supported features
    }[];
    tokens?: {          // Optional: Device tokens
      [key: string]: {
        token: string;   // Token value
        modifiedAt: string; // Token modification time
      };
    };
    permissions: string[]; // Granted permissions
    geo?: {             // Optional: Geolocation
      type: string;     // Point
      coordinates: [number, number]; // [longitude, latitude]
    };
    appList: string[];  // Installed apps
    payments: {         // Payment capabilities
      [method: string]: any;
    };
  };
  at: string;           // ISO timestamp
}
```

Response:
```typescript
{
  token?: string;         // Optional: JWT token
  latestVersion?: string; // Optional: Latest app version
}
```

### Delete Account
```
POST /Accounts/app/delete
```

Request:
```typescript
{} // Empty body
```

### Request Code
```
GET /Accounts/app/verify
```

Query Parameters:
```typescript
{
  mobile: string;         // Required: Mobile number
  channel: string;        // Required: Delivery channel
  purpose?: string;       // Optional: Verification purpose (default: 'register')
  options: {             // Optional parameters
    locale?: string;     // Optional: Locale setting
  }
}
```

### Verify Code
```
POST /Accounts/app/verify
```

Request:
```typescript
{
  code: string;          // Required: Verification code
}
```

### Change Number
```
POST /Accounts/app/mobile
```

Request:
```typescript
{
  mobile: string;         // Required: New mobile number
  options: {
    channel: string;      // Required: Verification channel
    skipValidate?: boolean; // Optional: Skip validation
    locale?: string;      // Optional: Locale setting
  }
}
```

### Verify Number Change
```
POST /Accounts/app/mobile/verify
```

Request:
```typescript
{
  mobile: string;        // Required: New mobile number
  code: string;         // Required: Verification code
}
```

### Request Password
```
GET /Accounts/app/password
```

Query Parameters:
```typescript
{
  mobile: string;        // Required: User's mobile
  channel: string;       // Required: Delivery channel
  options: {
    locale?: string;     // Optional: Locale setting
  }
}
```

### Change Password
```
POST /Accounts/app/password
```

Request:
```typescript
{
  newPassword: string;   // Required: New password
  oldPassword?: string;  // Required if not ignoring old
  ignoreOld?: boolean;   // Optional: Skip old verification
}
```

### Refresh Token
```
POST /Accounts/app/token/refresh
```

Response:
```typescript
{
  token: string;         // New authentication token
}
```

## Authentication Headers
All requests must include:

| Header | Description |
|--------|-------------|
| x-token | Bearer token for authentication |
| Date | UTC timestamp in RFC2822 format |
| Signature | Custom signature based on installation ID and timestamp |

Example:
```http
x-token: Bearer <jwt_token>
Date: Wed, 21 Oct 2023 07:28:00 GMT
```