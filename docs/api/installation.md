# Installation API Documentation

## Overview
The Installation API handles device registration and updates, managing device capabilities, tokens, and settings.

## Base URL
```
https://x3gw.perkd.me/api/
```

## Authentication Headers
All requests must include:

| Header | Description |
|--------|-------------|
| x-access-token | Bearer token for authentication |
| Date | UTC timestamp in RFC2822 format |
| perkd-install | JWT encoded installation info containing: id, app, device, locale, os, regions |
| perkd-location | Base64 encoded location info |

Example:
```http
x-access-token: Bearer <jwt_token>
Date: Wed, 21 Oct 2023 07:28:00 GMT
```

## Common Response Format
All responses include these fields when an error occurs:
```typescript
{
  code: string;      // Error code if operation failed
  message?: string;  // Optional error message
}
```

## Error Codes
| Code | Description |
|------|-------------|
| NETWORK_ERROR | Network connectivity issues |
| TIMEOUT | Request timeout |
| REQUEST_FAILED | Request failed |

## Installation Management

### Register/Update Installation
```
POST /Installations/app/register
```

Request:
```typescript
{
  id: string;          // Installation ID
  device: {
    uuid: string;      // Device UUID
    name: string;      // Device name
    brand: string;     // Device brand
    manufacturer: string; // Device manufacturer
    model: string;     // Device model
    simulator: boolean; // Whether device is simulator
    ip?: string;       // Optional: Device IP address
  };
  os: {
    name: string;      // OS name (ios/android)
    version: string;   // OS version
  };
  app: {
    id: string;        // App ID
    version: string;   // App version
    env: string;       // App environment
  };
  carrier: {
    name: string;      // Carrier name (or 'no-sim')
    country: string;   // Carrier country code (uppercase)
    mobileCountryCode: string; // Mobile country code
    changed?: boolean; // Whether carrier has changed
  };
  locale: {
    languages: string[]; // Supported languages
    country: string;    // Country code
    currency: string;   // Currency code
  };
  capabilities: {
    name: string;      // Capability type: 'payment'|'biometrics'|'connectivity'
    support: string[]; // For biometrics: ['faceid'|'touchid']
                      // For connectivity: ['nfc']
                      // For payment: supported payment methods
  }[];
  tokens?: {                    // Optional: Device tokens (e.g. push notification)
    [name: string]: {           // Token name as key
      token: string;            // Token value
      modifiedAt: string;       // Token modification timestamp
    };
  };
  permissions?: {               // Optional: List of permission statuses
    feature: string;            // Permission feature name:
                               // CAMERA, PHOTO, CONTACTS, CALENDARS, LOCATION,
                               // NOTIFICATIONS, BACKGROUND_FETCH, BIOMETRICS,
                               // BLUETOOTH, NFC, CELLULAR_DATA
    status: string;            // Current status:
                               // UNAVAILABLE - feature not available on device
                               // UNKNOWN - before request & requestable
                               // BLOCKED - denied by user (on Android: Never ask again)
                               // DENIED - denied by user, can ask again (Android only)
                               // LIMITED - limited access granted
                               // ALLOWED - full access granted
    options: string[];         // Feature-specific options:
                               // - For location: ['whenInUse'|'always', 'fullAccuracy']
                               // - For notifications: ['alert', 'badge', 'sound'] (iOS)
                               // - For biometrics: ['faceid'|'touchid']
                               // - For bluetooth: ['peripheral'] (iOS)
    grantedAt?: string;        // Timestamp when permission was granted (if allowed/limited)
    revokedAt?: string;        // Timestamp when permission was revoked (if blocked/denied)
  }[];
  geo?: {             // Optional: Geolocation
    type: "Point";    // Always "Point"
    coordinates: [number, number]; // [longitude, latitude]
  };
  appList: string[];  // Installed apps (from supported app list)
  payments?: {                  // Optional: Payment capabilities by type
    [type: string]: {          // Payment type as key
      methods?: {              // Optional: List of payment methods for this type
        [key: string]: any;    // Method-specific configuration
      }[];
    } | {};                    // Empty object if no methods for this type
  };
}
```

Note: Other installation data (id, app, device, locale, os, regions) is sent in the `perkd-install` header.

Response: Empty response on success
```typescript
{}
```

Note: If the authentication token is expired, the API will attempt to refresh it before making the request. 