# Sync API Documentation

## Overview
The Sync API provides functionality to synchronize data between the client and server. It supports various data types including Cache, CardMaster, Place, Settings, Action, and AppEvent.

## Base URL
```
https://x3gw.perkd.me/api/
```

## Authentication
All API requests require authentication using an access token:
```
Authorization: Bearer <access_token>
```

## Common Parameters
All sync endpoints accept these common parameters:

| Parameter | Type | Description |
|-----------|------|-------------|
| deviceId | String | Unique device identifier |
| syncUntil | Number | Timestamp of last sync |
| background | Boolean | Whether sync is running in background |

## Common Response Format
```typescript
{
  count: number;        // Number of objects processed
  syncUntil: number;    // Server timestamp for next sync
  partial: boolean;     // Whether more data is available
  code?: number;        // HTTP status code (optional)
  sync?: any;          // Optional sync data for immediate application
}
```

## Error Codes
| Code | Description |
|------|-------------|
| 102 | Processing - Sync already in progress |
| 503 | Service Unavailable |
| OFFLINE | Network connectivity issues |
| TIMEOUT | Request timeout |
| UNKNOWN | Unknown error |

## Rate Limits
- Cache sync: 10 second delay between requests (CACHE_WAIT)
- AppEvent sync: 500ms delay between requests (APPEVENT_WAIT)
- General sync: 15 second delay between requests (ALL_WAIT)

## Batch Processing
- Objects are processed in batches of 50 (BATCH_SIZE)
- Maximum of 20 objects per refresh operation (MAX_REFRESH)

## Sync Modules

### 1. Cache Sync
#### Overview
The Cache sync module handles synchronization of core data models including Person, Preference, Card, Offer, Reward, Place, and WidgetData.

#### Endpoints

##### Sync Cache
```
POST /Cache/app/sync
```

Request:
```typescript
{
  deviceId: string;      // Required: Device identifier
  objectsUp: Array<{     // Required: Array of changed objects to sync
    id: string;          // Object ID
    _model: string;      // Model type
    modifiedAt: number;  // Modification timestamp
    deletedAt?: number;  // Optional: Deletion timestamp
  }>;
  options: {
    syncUntil: number;   // Required: Last sync timestamp
    background: boolean; // Required: Whether sync is running in background
    fetchIds?: string[]; // Optional: Array of object IDs to fetch
  }
}
```

Response:
```typescript
{
  count: number;         // Number of objects processed
  syncUntil: number;     // Server timestamp for next sync
  partial: boolean;      // Whether more data is available
  objects: Array<{       // Synced objects
    id: string;          // Object ID
    _model: string;      // Model type
    modifiedAt?: number; // Optional: Modification timestamp
    deletedAt?: number;  // Optional: Deletion timestamp
  }>
}
```

#### Supported Models
- Person
- Preference  
- Card
- Offer
- Reward
- Place
- WidgetData

### 2. CardMaster Sync
#### Overview
The CardMaster sync module handles synchronization of card master data, which contains card templates and configurations.

#### Endpoints

##### Sync CardMaster
```
POST /CardMasterPub/app/sync
```

Request:
```typescript
{
  deviceId: string;      // Required: Device identifier
  options: {
    syncUntil: number;   // Required: Last sync timestamp
    background: boolean; // Required: Whether sync is running in background
  }
}
```

Response:
```typescript
{
  count: number;         // Number of objects processed
  syncUntil: number;     // Server timestamp for next sync
  partial: boolean;      // Whether more data is available
  objects: Array<{       // Card master objects
    id: string;          // Master ID
    name: string;        // Template name
    modifiedAt: number;  // Modification timestamp
    deletedAt?: number;  // Optional: Deletion timestamp
  }>
}
```

### 3. Place Sync
#### Overview
The Place sync module handles synchronization of place data including locations, addresses, and related metadata.

#### Endpoints

##### Sync Places
```
POST /PlaceLists/app/sync
```

Request:
```typescript
{
  deviceId: string;      // Required: Device identifier
  objectsUp?: Array<{    // Optional: Array of places to sync
    id: string;          // Place ID
  }>;
  options: {
    syncUntil: number;   // Required: Last sync timestamp
    background: boolean; // Required: Whether sync is running in background
    regions: string[];   // Required: Array of region/country codes
    ids: string[];      // Required: Array of place list IDs
  }
}
```

Response:
```typescript
{
  count: number;         // Number of objects processed
  syncUntil: number;     // Server timestamp for next sync
  partial: boolean;      // Whether more data is available
  objects: Array<{       // Place objects
    id: string;          // Place ID
    name: string;        // Place name
    address: {
      formatted: string; // Full address string
      street: string;    // Street name
      unit: string;      // Unit number
      postCode: string;  // Postal code
      city: string;      // City name
      state: string;     // State name
      country: string;   // Country code
    };
    coordinates: {
      latitude: number;  // Latitude
      longitude: number; // Longitude
    };
    metadata: {
      openingHours: any;
      contact: any;
      attributes: any;
    }
  }>
}
```

### 4. Settings Sync
#### Overview
The Settings sync module handles synchronization of application settings and configurations.

#### Endpoints

##### Sync Settings
```
POST /Settings/app/sync
```

Request:
```typescript
{
  deviceId: string;      // Required: Device identifier
  options: {
    syncUntil: number;   // Required: Last sync timestamp
    background: boolean; // Required: Whether sync is running in background
  }
}
```

Response:
```typescript
{
  count: number;         // Number of objects processed
  syncUntil: number;     // Server timestamp for next sync
  partial: boolean;      // Whether more data is available
  objects: Array<{       // Settings objects
    app: {
      sessionDuration: number;
      askLocationDays: number;
      maxEngage: number;
    };
    features: {
      [key: string]: {
        enabled: boolean;
        countries: string[] | null;
      }
    };
    location: {
      trackOptions: {
        type: string;
        deviation: number;
        limit: number;
      }
    }
  }>
}
```

### 5. Action Sync
#### Overview
The Action sync module handles synchronization of user actions and deferred operations.

#### Endpoints

##### Sync Actions
```
POST /Actions/app/sync
```

Request:
```typescript
{
  deviceId: string;      // Required: Device identifier
  objectsUp?: Array<{    // Optional: Array of actions to sync
    id: string;          // Action ID
    type: string;        // Action type
    status: string;      // Action status
    data: any;          // Action data
    createdAt: number;   // Creation timestamp
  }>;
  options: {
    syncUntil: number;   // Required: Last sync timestamp
    background: boolean; // Required: Whether sync is running in background
    fetchIds?: string[]; // Optional: Array of action IDs to fetch
  }
}
```

Response:
```typescript
{
  count: number;         // Number of objects processed
  syncUntil: number;     // Server timestamp for next sync
  partial: boolean;      // Whether more data is available
  objects: Array<{       // Action objects
    id: string;          // Action ID
    type: string;        // Action type
    status: string;      // Action status
    data: any;          // Action data
    createdAt: number;   // Creation timestamp
  }>
}
```

### 6. AppEvent Sync
#### Overview
The AppEvent sync module handles synchronization of application events and analytics data.

#### Endpoints

##### Sync AppEvents
```
POST /AppEvents/app/sync
```

Request:
```typescript
{
  deviceId: string;      // Required: Device identifier
  objectsUp: Array<{     // Required: Array of events to sync
    id: string;          // Event ID
    type: string;        // Event type
    timestamp: number;   // Event timestamp
    data: {
      objectId: string;  // Related object ID
      metadata: any;     // Event metadata
      context: any;      // Event context
    }
  }>;
  options: {
    syncUntil: number;   // Required: Last sync timestamp
    background: boolean; // Required: Whether sync is running in background
  }
}
```

Response:
```typescript
{
  count: number;         // Number of objects processed
  syncUntil: number;     // Server timestamp for next sync
  partial: boolean;      // Whether more data is available
}
```

## Error Handling
All endpoints may return these error responses:

```typescript
{
  code: string;         // Error code (OFFLINE, TIMEOUT, UNKNOWN)
  message: string;      // Error message
  statusCode?: number;  // Optional HTTP status code
  details?: {          // Optional error details
    sync?: any;        // Optional sync data to apply even in error case
  }
}
```

## Background Sync Behavior
- Sync operations check AppState to determine if running in background
- Background sync has configurable timeout and minimum intervals
- Failed background syncs are retried automatically
- Sync operations are batched and ordered to optimize performance 