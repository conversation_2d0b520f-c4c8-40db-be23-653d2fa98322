# Perkd Commerce Architecture

## Overview

The Perkd commerce system provides comprehensive e-commerce capabilities, from product discovery to checkout. This sophisticated system enables seamless shopping experiences across digital and physical channels while integrating deeply with the application's loyalty card ecosystem, offers management, and payment processing infrastructure.

The commerce architecture follows a policy-driven approach that emphasizes business rule flexibility, sophisticated discount engines, and multi-channel fulfillment capabilities. It serves as a critical integration point that connects cards, offers, rewards, places, and payment systems into a cohesive shopping experience.

The system is composed of two main parts: the **Shop** system for product discovery and browsing, and the **Bag/Checkout** system for managing the shopping cart and completing purchases.

## Core Commerce Components

### Shop System

The Shop system is responsible for product discovery, browsing, and viewing. It provides the user interface and business logic for interacting with the product catalog.

#### Shop Architecture

```mermaid
graph TB
    subgraph "📱 Shop User Interface"
        SHOP_UI[Shop Containers<br/>src/containers/Shop/]
        SHOP_COMP[Shop Components<br/>src/components/Shop/]
        SHOP_WIDGET[Shop Widgets<br/>Card Integration]
    end

    subgraph "🎛️ Controller Layer"
        SHOP_CTRL[Shop Controller<br/>src/controllers/shop.js]
        BRAND_CTRL[Brand Controller<br/>src/lib/shop/brand.js]
        PRODUCT_CTRL[Product Controller<br/>src/lib/shop/product.js]
    end

    subgraph "💾 Data Management"
        SHOP_SERVICE[Shop Service<br/>src/common/services/shop.js]
        PRODUCT_MODEL[Product Model<br/>src/lib/models/Product.js]
        CACHE[Image Cache<br/>@georstat/react-native-image-cache]
    end

    SHOP_UI --> SHOP_CTRL
    SHOP_COMP --> SHOP_CTRL
    SHOP_WIDGET --> SHOP_CTRL
    SHOP_CTRL --> BRAND_CTRL
    SHOP_CTRL --> PRODUCT_CTRL
    SHOP_CTRL --> SHOP_SERVICE
    SHOP_SERVICE --> PRODUCT_MODEL
    SHOP_SERVICE --> CACHE
```

### Bag/Checkout System

The bag system represents the heart of Perkd's commerce capabilities, implementing a sophisticated shopping cart and checkout engine with advanced business logic.

#### Bag Architecture

```mermaid
graph TB
    subgraph "🛒 Bag Core System"
        BAG[Bag Class<br/>src/lib/checkout/bag.js]
        BAGCTRL[Bag Controller<br/>src/controllers/Bag.js]
        POLICY[Policy Engine<br/>src/lib/checkout/policy.js]
        ITEMS[Item Management<br/>src/lib/checkout/item.js]
    end

    subgraph "💰 Pricing & Discounts"
        DISCOUNT[Discount Engine<br/>src/lib/checkout/discount.js]
        CURRENCY[Currency System<br/>src/lib/common/commerce.js]
        TAX[Tax Calculation<br/>Integrated Tax Engine]
        CALC[Price Calculator<br/>Dynamic Pricing]
    end

    subgraph "🚚 Fulfillment System"
        FULFILL[Fulfillment Engine<br/>src/lib/checkout/fulfillment.js]
        DELIVERY[Delivery Options<br/>src/lib/fulfillments/]
        PICKUP[Pickup Services<br/>Store & Location Based]
        SCHEDULE[Scheduling System<br/>Time-based Fulfillment]
    end

    subgraph "💳 Payment Integration"
        PAYMENT[Payment Methods<br/>src/lib/common/payments.js]
        METHODS[Payment Providers<br/>Multiple Provider Support]
        VALIDATION[Payment Validation<br/>Business Rules]
        PROCESSING[Transaction Processing<br/>Secure Payment Flow]
    end

    %% Core System Connections
    BAGCTRL --> BAG
    BAG --> POLICY
    BAG --> ITEMS
    POLICY --> DISCOUNT
    BAG --> CURRENCY

    %% Pricing Connections
    DISCOUNT --> CALC
    CURRENCY --> TAX
    CALC --> TAX

    %% Fulfillment Connections
    BAG --> FULFILL
    FULFILL --> DELIVERY
    FULFILL --> PICKUP
    FULFILL --> SCHEDULE

    %% Payment Connections
    BAG --> PAYMENT
    PAYMENT --> METHODS
    PAYMENT --> VALIDATION
    VALIDATION --> PROCESSING

    %% Styling with darker backgrounds and white text
    classDef core fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef pricing fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef fulfillment fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef payment fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff

    class BAG,BAGCTRL,POLICY,ITEMS core
    class DISCOUNT,CURRENCY,TAX,CALC pricing
    class FULFILL,DELIVERY,PICKUP,SCHEDULE fulfillment
    class PAYMENT,METHODS,VALIDATION,PROCESSING payment
```

#### Business Logic Patterns

**State Management**: The bag system implements sophisticated state transitions with comprehensive lifecycle management:

*Concurrent Access Control*:
- **Active State**: Normal shopping operations with real-time calculations and policy refresh
- **Locked State**: Checkout-initiated locking via `lockBag()` prevents concurrent modifications
- **Lock Scope**: Policy refresh operations respect lock state to prevent race conditions
- **Automatic Unlocking**: Guaranteed unlock in checkout finally blocks regardless of success/failure

*Lifecycle Management Patterns*:
- **TTL-Based Expiration**: Configurable time-to-live policies for inactive bag cleanup
- **Persistence Strategy**: Selective persistence excluding transient state (status, callbacks, qualified offers)
- **State Recovery**: Robust bag restoration from persisted state with policy reapplication
- **Modification Tracking**: `modifiedAt` timestamp updates for change detection and sync optimization

*Validation and Consistency*:
- **Continuous Validation**: Real-time validation of items, discounts, and fulfillment options
- **Cross-Module Sync**: Event-driven synchronization with offers, cards, and location systems
- **Error Recovery**: Graceful handling of validation failures with user feedback mechanisms

**Policy-Driven Architecture**: Business rules are centralized through sophisticated policy inheritance and override mechanisms:

*Configuration Inheritance Hierarchy*:
- **Base Policy**: Default commerce policies defined at application level
- **Card Master Policies**: Merchant-specific overrides in `CardMaster.bagPolicy` JSON configuration
- **Runtime Merging**: Dynamic policy resolution using `BagController.policy()` with deep merge logic
- **Fulfillment Policy Integration**: Automatic integration of fulfillment options when not explicitly overridden

*Dynamic Rule Application Patterns*:
- **Context-Aware Resolution**: Policies applied based on card master configurations and current context
- **Real-time Policy Refresh**: `refreshPolicy()` method ensures current rules without bag locking
- **Offer Integration**: Policy offers merged with live offer data from `BagController.policyOffers()`
- **Exclusion Management**: Dynamic offer exclusion through `excludeOffers` policy configuration

*Merchant-Specific Customization*:
- **Channel Configuration**: Commerce channel specification for order submission namespacing
- **Payment Method Control**: Configurable payment options per merchant context
- **Fulfillment Constraints**: Location and time-based availability rules per merchant
- **Pricing Policy Overrides**: Merchant-specific pricing and tax calculation rules

**Sophisticated Discount Engine**: The discount system supports complex promotional logic with nuanced business rules:

*Stacking Rules and Conflict Resolution*:
- **ALWAYS**: Discounts that apply automatically without restrictions
- **COMBINE**: Can be used with other COMBINE discounts, optimized for maximum savings
- **SINGLE**: Item-level restriction preventing combination with any other discount on the same item
- **ALONE**: Mutually exclusive discounts that cannot be combined with any other offers
- **ONE**: Only the best single discount from this category is applied
- **Priority-Based Selection**: Higher priority discounts (higher numeric value) are applied first, with automatic re-sorting after each application

*Allocation Methods and Distribution Logic*:
- **ACROSS**: Discount value spread proportionally across all entitled items based on price ratios
- **EACH**: Discount value applied to each qualifying item individually, respecting quantity limits
- **Waste Minimization**: System selects discount combinations that minimize unused discount value
- **Dynamic Re-evaluation**: Savings and waste recalculated after each discount application

*Buy X Get Y Sophisticated Patterns*:
- **Prerequisite Tracking**: Complex logic for qualifying items (X) with support for overlapping entitlements
- **Entitlement Calculation**: Automatic computation of entitled quantities (Y) based on prerequisite fulfillment
- **Common Item Handling**: Sophisticated logic when items qualify as both prerequisite and entitlement
- **Gift Injection**: Automatic addition of entitled items when not present in bag
- **Ratio-Based Allocation**: Intelligent distribution when X and Y items overlap

*Exclusion and Conflict Resolution*:
- **Offer Master Exclusions**: Specific offers that cannot be applied together
- **Applied Discount Tracking**: Prevention of excluded offers on items with existing discounts
- **Mutual Exclusivity**: Automatic conflict detection and resolution based on discount rules

### Order Management

The order system provides comprehensive transaction lifecycle management with sophisticated business logic.

#### Order Processing Flow

```mermaid
graph TB
    subgraph "📝 Order Creation"
        INIT[Order Initialization<br/>ID Generation & Setup]
        VALIDATE[Order Validation<br/>Business Rules Check]
        ITEMS[Item Processing<br/>Inventory & Pricing]
        POLICY[Policy Application<br/>Rules & Constraints]
    end

    subgraph "💸 Payment Processing"
        METHODS[Payment Method Selection<br/>Available Options]
        VALIDATE_PAY[Payment Validation<br/>Limits & Restrictions]
        PROCESS[Payment Processing<br/>Provider Integration]
        CONFIRM[Payment Confirmation<br/>Success Handling]
    end

    subgraph "📦 Fulfillment Processing"
        FULFILL_SELECT[Fulfillment Selection<br/>Delivery/Pickup Options]
        SCHEDULE[Scheduling<br/>Time Slot Management]
        LOCATION[Location Validation<br/>Service Area Check]
        PREPARE[Order Preparation<br/>Fulfillment Queue]
    end

    subgraph "✅ Order Completion"
        COMMIT[Order Commit<br/>Final State Update]
        NOTIFY[Notifications<br/>Customer & Merchant]
        TRACK[Order Tracking<br/>Status Updates]
        COMPLETE[Order Complete<br/>Final State]
    end

    %% Flow Connections
    INIT --> VALIDATE
    VALIDATE --> ITEMS
    ITEMS --> POLICY
    POLICY --> METHODS

    METHODS --> VALIDATE_PAY
    VALIDATE_PAY --> PROCESS
    PROCESS --> CONFIRM

    POLICY --> FULFILL_SELECT
    FULFILL_SELECT --> SCHEDULE
    SCHEDULE --> LOCATION
    LOCATION --> PREPARE

    CONFIRM --> COMMIT
    PREPARE --> COMMIT
    COMMIT --> NOTIFY
    NOTIFY --> TRACK
    TRACK --> COMPLETE

    %% Styling with darker backgrounds and white text
    classDef creation fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef payment fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef fulfillment fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef completion fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff

    class INIT,VALIDATE,ITEMS,POLICY creation
    class METHODS,VALIDATE_PAY,PROCESS,CONFIRM payment
    class FULFILL_SELECT,SCHEDULE,LOCATION,PREPARE fulfillment
    class COMMIT,NOTIFY,TRACK,COMPLETE completion
```

## Commerce Integration Architecture

### Cross-Module Integration

The commerce system serves as a central integration hub that connects various application modules through sophisticated patterns.

#### Integration with Cards System

**Card-Commerce Binding**: Each commerce session is bound to a specific loyalty card, enabling:
- **Member-Only Shopping**: Exclusive access to products and pricing
- **Personalized Experiences**: Card-specific offers and recommendations
- **Loyalty Integration**: Points earning and redemption during checkout
- **Card Lifecycle Integration**: Commerce availability based on card status

**Widget Integration**: Commerce capabilities are exposed through card widgets:
- **Bag Widget**: Shopping cart functionality embedded in card interface
- **Shop Widget**: Product catalog access from card context
- **Offer Widget**: Promotional offers integrated with shopping experience

#### Integration with Offers & Rewards

**Dynamic Offer Application**: The commerce system integrates deeply with the offers engine through sophisticated qualification and reservation patterns:

*Real-time Qualification Engine*:
- **Context-Aware Evaluation**: Offers qualified using combined bag and time context via `App.ContextBuilder`
- **Sift-Based Filtering**: MongoDB-style query evaluation for complex offer qualification rules
- **Continuous Re-evaluation**: Offer eligibility recalculated after each discount application
- **Prerequisite Validation**: Complex Buy X Get Y prerequisite tracking with quantity aggregation

*Automatic Application Logic*:
- **Best Discount Selection**: Sophisticated algorithm prioritizing savings while minimizing waste
- **Stacking Optimization**: Intelligent combination of compatible offers for maximum customer benefit
- **Allocation Tracking**: Precise tracking of discount allocations per item with index-based references
- **Gift Item Injection**: Automatic addition of entitled items for Buy X Get Y promotions

*Reservation and Transaction Management*:
- **Checkout Reservation**: `_.Offer.reserve(ids, masterId, orderId)` during checkout initiation
- **Payment Status Monitoring**: Offer fate determined by payment intent status (paid/cancelled)
- **Rollback Recovery**: `_.Offer.recover(ids)` restores offer availability on payment failure
- **Async Payment Handling**: Different reservation cleanup for direct vs. indirect payment methods

**Reward System Integration**: Commerce operations contribute to and consume loyalty rewards through sophisticated calculation patterns:

*Points and Rewards Calculation*:
- **Dynamic Points Earning**: Automatic points calculation based on purchase amounts and merchant-specific rates
- **Stamp Collection Logic**: Transaction-based stamp progression with sophisticated milestone tracking
- **Reward Redemption Integration**: Using accumulated rewards as payment methods with validation
- **Tier Progression Rules**: Purchase-based loyalty tier advancement with threshold management

*Reward Application Business Logic*:
- **Eligibility Validation**: Real-time validation of reward redemption eligibility based on card status
- **Limit Enforcement**: Per-user and per-order limits enforced through `CardMasterPolicies.perUser/perOrder`
- **Expiration Handling**: Automatic expiration checking for time-sensitive rewards
- **Balance Management**: Sophisticated balance tracking and deduction for stored value rewards

#### Integration with Places & Location

**Location-Aware Commerce**: The system leverages location services for enhanced functionality:
- **Proximity-Based Offers**: Location-triggered promotional content
- **Store-Specific Inventory**: Real-time availability based on location
- **Fulfillment Options**: Delivery and pickup based on current location
- **Geofenced Experiences**: Location-based shopping restrictions and enhancements

**Place-Based Fulfillment**: Integration with the places system enables:
- **Store Pickup**: Integration with physical store locations
- **Delivery Zones**: Service area validation and pricing
- **In-Store Experiences**: QR code and proximity-based shopping
- **Multi-Location Support**: Merchant chains with location-specific policies

### Payment System Integration

**Multi-Provider Architecture**: The commerce system supports diverse payment methods:
- **Digital Wallets**: Apple Pay, Google Pay, and regional alternatives
- **Traditional Cards**: Credit and debit card processing
- **Alternative Methods**: Alipay, GrabPay, LinePay, and local providers
- **Stored Value**: Integration with loyalty card stored value systems

**Payment Flow Integration**: Sophisticated payment processing with commerce logic:
- **Method Availability**: Dynamic payment options based on context
- **Partial Payments**: Support for split payments across multiple methods
- **Validation Logic**: Business rules for payment method restrictions
- **Error Handling**: Comprehensive error recovery and retry mechanisms

## Configuration Management and Business Rules

### Policy Configuration Architecture

The commerce system implements a sophisticated configuration management system that enables flexible business rule application while maintaining consistency across merchant implementations.

#### Configuration Inheritance and Override Mechanisms

**Hierarchical Configuration Resolution**:
```typescript
// Policy resolution follows this hierarchy (highest to lowest precedence):
// 1. Runtime policy overrides (passed to BagController.policy())
// 2. CardMaster.bagPolicy JSON configuration
// 3. CardMaster.fulfillments default configuration
// 4. Application-level defaults
```

*Configuration Merging Logic*:
- **Deep Merge Strategy**: `merge({}, bagPolicy || {}, policy || {})` combines configurations
- **Fulfillment Integration**: Automatic fulfillment policy integration when not explicitly overridden
- **Conditional Override**: Fulfillment policies only override when no specific fulfillments are configured
- **JSON Schema Validation**: CardMaster bagPolicy stored as JSON with runtime parsing and validation

*Dynamic Rule Application*:
- **Context-Sensitive Resolution**: Policies resolved based on card master, current location, and user context
- **Runtime Flexibility**: Policy changes applied without code deployment through configuration updates
- **A/B Testing Support**: Policy-based experimentation through configuration variations
- **Merchant Customization**: Per-merchant business rule variations through CardMaster-specific policies

#### Commerce Policy Configuration Patterns

**Offer Policy Integration**:
- **Policy Offers**: Merchant-defined offers in `bagPolicy.offers` array with discount specifications
- **Offer Master Binding**: Policy offers linked to offer masters through `discount.offerMasterId`
- **Image Fallback**: Policy offers inherit card master images when not explicitly specified
- **Discount Configuration**: Complete discount specifications including kind, value, allocation, and qualifiers

**Payment Method Configuration**:
- **Method Availability**: Dynamic payment method filtering based on merchant policies
- **Context-Aware Restrictions**: Payment options vary by transaction amount, type, and merchant category
- **Credential Management**: Secure payment credential storage and retrieval per merchant
- **Fee Configuration**: Merchant-specific payment processing fees and limits

**Fulfillment Policy Management**:
- **Type-Specific Policies**: Different policies for store, pickup, delivery, dinein, and vending fulfillment
- **Lead Time Configuration**: Minimum lead time requirements per fulfillment type and location
- **Availability Scheduling**: Time-based availability rules with prescheduled option support
- **Geographic Constraints**: Service area validation and pricing zone configuration

### Fulfillment Availability Constraints and Validation

The fulfillment system implements sophisticated availability validation with complex business rules for different fulfillment types.

#### Fulfillment Type-Specific Validation Rules

**Delivery Fulfillment Constraints**:
- **Minimum Time Validation**: Delivery must be scheduled at least one day in advance (`moment(minTime).diff(NOW, 'day', true) > 0`)
- **Geographic Validation**: Destination country validation required for all delivery requests
- **Service Area Checking**: Zone-based validation using sift queries for address qualification
- **Dynamic Pricing**: Location-based pricing through external fulfillment pricing APIs

**Pickup Fulfillment Validation**:
- **Location Type Validation**: Strict validation of pickup location types (store, cstore, vending)
- **Lead Time Enforcement**: Minimum lead time calculated as `moment(NOW).add(policy.leadTime, 'm')`
- **Scheduled Time Validation**: Pickup time must be after calculated minimum lead time
- **Place Availability**: Real-time availability checking through `place.isAvailable(type)` validation

**Store and Dine-in Fulfillment**:
- **Always Available**: Store and dine-in fulfillment types have no time-based restrictions
- **Location Context**: Validation based on current location and place availability
- **Capacity Constraints**: Support for capacity-based validation (future enhancement)

#### Dynamic Fulfillment Pricing Logic

**Pricing Resolution Hierarchy**:
1. **Policy-Based Pricing**: Cheapest pricing from merchant-defined pricing policies
2. **External API Lookup**: Dynamic pricing through fulfillment service APIs
3. **Zone-Based Calculation**: Geographic zone pricing with distance-based rates
4. **Fallback Pricing**: Default pricing when other methods unavailable

**Complex Pricing Patterns**:
- **Multi-Zone Support**: Different pricing zones with overlapping geographic areas
- **Weight-Based Pricing**: Item weight consideration for delivery cost calculation
- **Distance Calculation**: Dynamic distance-based pricing for delivery services
- **Promotional Pricing**: Integration with discount system for fulfillment cost reductions

### Inventory Validation and Allocation Rules

The commerce system implements sophisticated inventory management with real-time validation and intelligent allocation strategies.

#### Inventory Status Management

**Real-time Availability Tracking**:
- **Soldout Status**: Dynamic soldout status tracking with automatic filtering from calculations
- **Remain Quantity**: Available inventory tracking with real-time updates
- **Allocation Tracking**: Sophisticated tracking of allocated quantities per variant through `Allotment` class
- **Reserved Quantities**: Separate tracking of reserved items for Buy X Get Y prerequisites

**Status-Based Filtering**:
- **Calculation Exclusion**: Soldout items automatically excluded from price and discount calculations
- **Display Preservation**: Soldout items remain visible in bag but marked as unavailable
- **Quantity Validation**: Automatic quantity adjustment when requested exceeds available inventory
- **Cross-Variant Aggregation**: Quantity aggregation across multiple items with same variantId

#### Allocation Strategy Patterns

**Discount Allocation Tracking**:
- **Per-Item Allocation**: Precise tracking of discount allocations per item with index references
- **Quantity-Based Limits**: Allocation limits based on available quantity minus already allocated amounts
- **Cross-Discount Coordination**: Prevention of over-allocation when multiple discounts apply
- **Rollback Capability**: Allocation rollback support for failed transactions

**Buy X Get Y Allocation Logic**:
- **Prerequisite Reservation**: Temporary reservation of prerequisite items during entitlement calculation
- **Common Item Handling**: Sophisticated logic when items qualify as both prerequisite and entitlement
- **Quantity Optimization**: Intelligent allocation to minimize prerequisite usage while maximizing entitlements
- **Excess Handling**: Management of excess prerequisite quantities beyond entitlement requirements

**Inventory Validation Edge Cases**:
- **Concurrent Access**: Thread-safe inventory checking during high-traffic periods
- **Partial Fulfillment**: Handling scenarios where full requested quantity unavailable
- **Substitution Logic**: Support for item substitution when primary items unavailable
- **Backorder Management**: Integration with backorder systems for out-of-stock items

## Business Logic & Architectural Decisions

### Sophisticated Patterns

**Policy-Driven Design**: The architecture emphasizes configuration over code through:
- **Centralized Rules**: Business logic externalized to policy configurations
- **Runtime Flexibility**: Dynamic rule application without code changes
- **Merchant Customization**: Per-merchant business rule variations
- **A/B Testing Support**: Policy-based experimentation capabilities

**Event-Driven Architecture**: Commerce operations leverage sophisticated event propagation patterns:

*Cross-Module Event Integration*:
- **Checkout Initiation**: `EVENT.Bag.initiateCheckout` broadcasts bag state for analytics and monitoring
- **Check-in Integration**: `EVENT.Widget.checkin` coordinates location-based commerce activation
- **Offer Synchronization**: `EVENT.Sync.cache` triggers offer refresh when discount validation fails
- **Widget Communication**: Event-based coordination between bag, shop, and offer widgets

*State Synchronization Patterns*:
- **Real-time UI Updates**: Event propagation ensures immediate UI synchronization across components
- **Cross-Device Sync**: Event-driven synchronization maintains consistency across multiple devices
- **Background Processing**: Deferred operations triggered through event system for heavy computations
- **Error Recovery**: Event-based error propagation enables sophisticated recovery mechanisms

*Analytics and Audit Integration*:
- **Transaction Tracking**: Automatic event emission for business intelligence and analytics
- **User Behavior Analysis**: Commerce events provide detailed user journey tracking
- **Performance Monitoring**: Event-based performance metrics collection and analysis
- **Compliance Auditing**: Complete transaction history through comprehensive event logging

**Deferred Operations**: Complex operations are handled through deferred execution:
- **Background Processing**: Heavy calculations moved off the UI thread
- **Retry Logic**: Automatic retry for failed operations
- **Queue Management**: Ordered processing of commerce operations
- **Error Recovery**: Sophisticated error handling and recovery mechanisms

### Lifecycle Rules and Edge Case Handling

**Bag Lifecycle Management**: Sophisticated state management with comprehensive edge case handling:

*Concurrent Modification Protection*:
- **Checkout Locking**: `lockBag()` prevents policy refresh and modifications during checkout
- **Lock Validation**: All modification operations check lock state before proceeding
- **Atomic Operations**: Bag persistence and calculation operations designed for atomicity
- **Race Condition Prevention**: Event-driven updates respect bag lock state

*State Persistence and Recovery*:
- **Selective Persistence**: Excludes transient properties (callbacks, status, qualified offers)
- **Modification Tracking**: `modifiedAt` timestamps enable efficient synchronization
- **Policy Reapplication**: Restored bags automatically reapply current policies and offers
- **Cross-Device Continuity**: Bag state synchronized across multiple device sessions

**Order State Transitions**: Well-defined order lifecycle with sophisticated validation and rollback:

*Transaction Integrity Patterns*:
- **Three-Phase Commit**: Offer reservation → Payment processing → Order commit
- **Rollback Scenarios**: Comprehensive error recovery for payment failures, fulfillment unavailability
- **Idempotency Support**: `idempotencyKey` prevents duplicate order processing
- **State Validation**: Strict business rules enforce valid state transitions

*Error Handling and Recovery*:
- **Payment Failure Recovery**: Automatic offer recovery and user notification
- **Fulfillment Unavailability**: Dynamic handling with alternative option suggestions
- **Network Resilience**: Offline capability with deferred operation queuing
- **Graceful Degradation**: Fallback mechanisms for service unavailability

### Performance Optimizations

**Calculation Efficiency**: The system optimizes expensive operations:
- **Lazy Calculation**: Deferred computation until required
- **Caching Strategies**: Multi-level caching for frequently accessed data
- **Batch Operations**: Grouped operations for improved performance
- **Memory Management**: Efficient object lifecycle management

**Network Optimization**: Intelligent API usage patterns:
- **Request Batching**: Combined API calls for related operations
- **Offline Capability**: Local storage for offline shopping experiences
- **Progressive Loading**: Incremental data loading for large catalogs
- **Connection Management**: Efficient network resource utilization

## User Experience Flow

### Shopping Journey

The commerce system provides a seamless user experience through carefully orchestrated flows:

```mermaid
graph TB
    subgraph "🎯 Discovery Phase"
        BROWSE[Product Browse<br/>Catalog Navigation]
        SEARCH[Product Search<br/>Query & Filter]
        RECOMMEND[Recommendations<br/>Personalized Suggestions]
        DETAIL[Product Detail<br/>Information & Options]
    end

    subgraph "🛒 Shopping Phase"
        ADD[Add to Bag<br/>Item Configuration]
        CONFIGURE[Item Options<br/>Variants & Customization]
        QUANTITY[Quantity Selection<br/>Inventory Validation]
        REVIEW[Bag Review<br/>Items & Pricing]
    end

    subgraph "💰 Checkout Phase"
        OFFERS[Offer Application<br/>Discount Calculation]
        FULFILL[Fulfillment Selection<br/>Delivery Options]
        PAYMENT[Payment Method<br/>Selection & Validation]
        CONFIRM[Order Confirmation<br/>Final Review]
    end

    subgraph "📦 Post-Purchase"
        PROCESS[Order Processing<br/>Backend Fulfillment]
        TRACK[Order Tracking<br/>Status Updates]
        COMPLETE[Order Complete<br/>Receipt & Rewards]
        FEEDBACK[Feedback Collection<br/>Experience Rating]
    end

    %% Flow Connections
    BROWSE --> SEARCH
    SEARCH --> RECOMMEND
    RECOMMEND --> DETAIL
    DETAIL --> ADD

    ADD --> CONFIGURE
    CONFIGURE --> QUANTITY
    QUANTITY --> REVIEW

    REVIEW --> OFFERS
    OFFERS --> FULFILL
    FULFILL --> PAYMENT
    PAYMENT --> CONFIRM

    CONFIRM --> PROCESS
    PROCESS --> TRACK
    TRACK --> COMPLETE
    COMPLETE --> FEEDBACK

    %% Styling with darker backgrounds and white text
    classDef discovery fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef shopping fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef checkout fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef postpurchase fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff

    class BROWSE,SEARCH,RECOMMEND,DETAIL discovery
    class ADD,CONFIGURE,QUANTITY,REVIEW shopping
    class OFFERS,FULFILL,PAYMENT,CONFIRM checkout
    class PROCESS,TRACK,COMPLETE,FEEDBACK postpurchase
```

### Data Flow Patterns

**Real-time Synchronization**: The system maintains consistency through:
- **Optimistic Updates**: Immediate UI feedback with background validation
- **Conflict Resolution**: Sophisticated handling of concurrent modifications
- **State Reconciliation**: Automatic synchronization of local and remote state
- **Event Propagation**: Real-time updates across all connected components

**Context Preservation**: User context is maintained throughout the shopping journey:
- **Session Persistence**: Reliable storage of shopping state
- **Cross-Device Continuity**: Synchronized shopping experience across devices
- **Location Context**: Preservation of location-based preferences
- **Personalization State**: Maintained user preferences and history

## Security & Validation

### Data Protection

**Payment Security**: The commerce system implements comprehensive security measures:
- **PCI Compliance**: Adherence to payment card industry standards
- **Tokenization**: Secure handling of payment credentials
- **Encryption**: End-to-end encryption for sensitive data
- **Audit Logging**: Complete transaction audit trails

**Business Rule Validation**: Multi-layered validation with sophisticated edge case handling:

*Input Validation and Sanitization*:
- **Item Validation**: Comprehensive validation of required properties (unitPrice, quantity, variantId)
- **Price Consistency**: Validation of price vs. unitPrice×quantity relationships with automatic defaults
- **Tag Normalization**: Automatic lowercase conversion for consistent tag matching
- **Property Validation**: Strict validation against allowed property sets per item type

*Inventory and Availability Validation*:
- **Real-time Availability**: Dynamic inventory checking with soldout status tracking
- **Fulfillment Constraints**: Location and time-based availability validation
- **Lead Time Validation**: Minimum lead time enforcement for pickup and delivery options
- **Service Area Validation**: Geographic constraint checking for delivery zones

*Business Logic Enforcement*:
- **Policy-Based Validation**: Dynamic rule enforcement based on merchant configurations
- **Card Lifecycle Validation**: Commerce availability based on card status and validity periods
- **Amount Limits**: Transaction amount validation against merchant-defined limits
- **Payment Method Eligibility**: Context-aware payment method availability validation

*Tax Calculation Methodologies*:
- **Inclusive vs. Exclusive**: Support for both tax-inclusive and tax-exclusive pricing models
- **Multi-Rate Support**: Complex tax calculations with multiple tax rates per transaction
- **Item-Level Taxes**: Individual tax rates per item with aggregation at order level
- **Fulfillment Taxes**: Separate tax calculation for shipping and fulfillment services
- **Rounding Precision**: Consistent rounding strategies to prevent calculation discrepancies

### Error Handling

**Graceful Degradation**: The system handles failures elegantly:
- **Fallback Mechanisms**: Alternative flows for failed operations
- **User Communication**: Clear error messages and recovery guidance
- **Automatic Recovery**: Self-healing capabilities for transient failures
- **Monitoring Integration**: Comprehensive error tracking and alerting

## Cross-References

For detailed information on related systems, see:
- [Application Architecture](./app-architecture.md) - Overall application design patterns
- [Payments](./payments.md) - Payment processing and method management
- [Offers](./offers.md) - Promotional offers and discount management
- [Cards](./cards.md) - Loyalty card system integration
- [Places](./places.md) - Location services and place management

## Summary

The Perkd commerce architecture represents a sophisticated, policy-driven e-commerce platform with comprehensive business logic coverage that seamlessly integrates with the broader loyalty ecosystem. The bag/checkout system serves as the central commerce engine, providing:

**Core Business Logic Capabilities**:
- **Hierarchical Policy Management**: Sophisticated configuration inheritance with CardMaster-level customization
- **Advanced Discount Engine**: Complex stacking rules, Buy X Get Y logic, and intelligent conflict resolution
- **Comprehensive State Management**: Concurrent access control, lifecycle management, and robust error recovery
- **Dynamic Offer Integration**: Real-time qualification, reservation systems, and transaction rollback capabilities

**Sophisticated Validation and Edge Case Handling**:
- **Multi-Layer Validation**: Input sanitization, business rule enforcement, and inventory availability checking
- **Tax Calculation Complexity**: Support for inclusive/exclusive models with multi-rate calculations
- **Fulfillment Constraints**: Type-specific validation with lead time enforcement and geographic restrictions
- **Inventory Allocation**: Real-time tracking with sophisticated allocation strategies and concurrent access protection

**Integration and Event Architecture**:
- **Cross-Module Coordination**: Event-driven integration with cards, offers, rewards, and payment systems
- **Real-time Synchronization**: Sophisticated state management across devices and components
- **Transaction Integrity**: Three-phase commit patterns with comprehensive rollback mechanisms
- **Performance Optimization**: Lazy calculation, caching strategies, and intelligent network usage

This architecture provides a robust foundation for complex commerce operations while maintaining the flexibility needed for diverse merchant requirements, sophisticated business rules, and evolving operational needs. The emphasis on policy-driven configuration enables rapid business rule changes without code deployment, while the comprehensive validation and error handling ensure reliable operation under all conditions.
