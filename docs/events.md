# Perkd Event-Driven Architecture

## Overview

Perkd implements a sophisticated event-driven architecture that serves as the communication backbone for the entire application ecosystem. Built on EventEmitter3, this system enables decoupled, reactive communication between components, controllers, models, and services, facilitating real-time data synchronization, cross-component coordination, and comprehensive analytics tracking.

## Purpose and Business Value

The event system addresses critical architectural challenges by providing sophisticated business logic coordination:

### Strategic Business Drivers

**Scalable Architecture Foundation**: The event-driven design enables the application to scale from simple card management to complex multi-domain loyalty ecosystems without architectural refactoring. This future-proofs the platform for business expansion into new verticals and partnership integrations.

**Operational Intelligence**: Every user interaction generates structured events that feed business intelligence systems, enabling data-driven decision making about user behavior, feature adoption, and business performance. This transforms the application from a simple utility into a strategic business asset.

**Resilient User Experience**: The decoupled architecture ensures that failures in one domain (e.g., payment processing) don't cascade to other features (e.g., card viewing), maintaining user engagement even during partial system outages.

### Core Business Capabilities

- **Decoupled Communication**: Components interact without direct dependencies, enabling independent feature development and reducing time-to-market for new capabilities
- **Real-time Synchronization**: Immediate propagation of state changes ensures consistent user experience across all touchpoints, critical for loyalty program integrity
- **Analytics Integration**: Comprehensive event logging supports business intelligence, user behavior analysis, and regulatory compliance requirements
- **Background Coordination**: Seamless coordination of background tasks enables sophisticated workflows like deferred operations during network outages
- **Cross-Feature Integration**: Events enable complex business scenarios like coordinated card-offer-reward workflows that drive user engagement and revenue

## Core Architecture

### Global Event Infrastructure

The event system is built around a global event emitter accessible throughout the application via `$.Event`:

```javascript
// Global event emitter initialization in globals.js
global.$ = {};
Object.defineProperties($, {
    Event: {
        value: new Emitter(), writable: false,
    }
});
```

This global accessibility pattern ensures that any component, controller, or service can participate in the event ecosystem without complex dependency injection or prop drilling.

### EventEmitter3 Integration

The system leverages EventEmitter3 for high-performance event handling with sophisticated listener management:

- **Context Binding**: Automatic context binding for method handlers
- **Memory Management**: Intelligent listener cleanup to prevent memory leaks
- **Performance Optimization**: Efficient event emission with minimal overhead
- **Error Isolation**: Event handler errors don't propagate to other listeners

### Event Definition Architecture

Events are centrally defined across multiple configuration files, creating a structured event taxonomy:

- **`src/lib/Events.json`**: Core application events organized by domain
- **`src/lib/common/appEventDefn.js`**: App-specific event configurations with analytics metadata
- **Domain-specific definitions**: Events embedded within feature modules

This centralized approach ensures consistency, prevents naming conflicts, and enables comprehensive event documentation.

## Event Configuration and Definitions

### Event Definition Structure
Events are defined with comprehensive metadata for tracking, analytics, and processing:

```javascript
const EVENTS = {
    [EVENT.Card.view]: {
        name: 'View Card',
        sync: true,
        card: {
            id: 'id',
            fields: ['id', 'number', 'displayName', 'masterId']
        },
        cardMaster: {
            id: 'masterId',
            fields: ['id', 'name', 'brand', 'tenant']
        }
    }
};
```

### Event Logging Configuration
Events support sophisticated configuration options for automatic logging and analytics:

```javascript
{
    name: 'Event Name',           // Display name for analytics
    sync: true,                   // Sync immediately to server
    skip: { condition: value },   // Skip conditions for conditional processing
    occurredAt: 'timestamp',      // Custom timestamp field
    context: { key: 'field' }     // Additional context data mapping
}
```

This configuration enables:
- **Automatic Analytics**: Events are automatically tracked and logged
- **Conditional Processing**: Skip patterns prevent unnecessary processing
- **Data Enrichment**: Automatic inclusion of related model data
- **Performance Optimization**: Sync flags control immediate vs. deferred processing

## Event Lifecycle and Processing

### Event Emission Patterns

The system supports multiple event emission patterns optimized for different use cases:

#### Model-Driven Events
Models automatically emit lifecycle events through the Event mixin:

```javascript
// Automatic event emission from models
export default {
    emitCreated(data) {
        const { className } = this;
        const EVT = EVENT[className] || {};
        
        if (EVT.created) {
            this.emit(EVT.created, { ids: [data.id] });
        }
    }
}
```

#### Controller-Coordinated Events
Controllers orchestrate complex business operations through event sequences:

```javascript
// Controller event coordination
$.Event.emit(EVENT.Card.view, { id, masterId });
$.Event.emit(EVENT.Analytics.track, { action: 'card_viewed', cardId: id });
```

#### Cross-Component Communication
Components communicate through domain events without direct coupling:

```javascript
// Component A triggers action
$.Event.emit(EVENT.Bag.addItems, { items, cardId });

// Component B responds to action
$.Event.on(EVENT.Bag.itemsAdded, this.updateUI, this);
```

### Sophisticated Event Processing

#### Business Rule Enforcement Through Conditional Processing
The system implements sophisticated business logic through conditional event processing that reflects real-world operational requirements:

**Session Boundary Logic**: App resume events are intelligently filtered to prevent analytics noise from rapid app switching scenarios. The `skip: { newSession: true }` pattern ensures that only meaningful user sessions are tracked, providing accurate engagement metrics for business analysis.

**Context-Aware Processing**: Events automatically capture business-relevant context like user location, session duration, and interaction patterns. This contextual enrichment enables sophisticated business intelligence without requiring explicit instrumentation in every component.

#### Performance-Optimized Deferred Processing
The deferred processing architecture addresses critical business requirements for responsive user experience while maintaining comprehensive analytics:

**UI Responsiveness Priority**: Non-critical operations are deferred using `runAfterInteractions()` to ensure that user interactions remain fluid. This architectural decision prioritizes user experience over immediate data consistency, reflecting the business priority of user engagement.

**Intelligent Sync Coordination**: Critical business events (marked with `sync: true`) trigger immediate synchronization to ensure data consistency for revenue-impacting operations like card acceptance and offer redemption. This selective synchronization balances performance with business-critical data integrity.

#### Event-Driven State Transitions
The system orchestrates complex business workflows through event-driven state management:

**Lifecycle Orchestration**: Card lifecycle events (created, updated, accepted, declined) trigger cascading business logic across multiple domains, ensuring consistent state management without tight coupling between components.

**Cross-Domain Coordination**: Events enable sophisticated business scenarios where actions in one domain (e.g., card acceptance) automatically trigger related operations in other domains (e.g., offer activation, analytics tracking, notification delivery).

## Event Middleware and Analytics Integration

### AppEvents System Integration
The event-driven architecture includes a sophisticated analytics layer through the AppEvents system, which operates as a specialized consumer of the global event infrastructure. This system transforms real-time application events into structured business intelligence data:

**Architectural Relationship**: The AppEvents system functions as an analytics middleware layer that observes the global `$.Event` flow without interfering with primary business logic. This separation ensures that analytics collection supports business intelligence while maintaining application performance and reliability.

**Business Intelligence Pipeline**: AppEvents automatically captures relevant events from the global event system, enriches them with business context, and persists them for analytics and external platform integration. This creates a comprehensive audit trail and business intelligence foundation.

**Performance-Optimized Processing**: The AppEvents system uses deferred processing patterns to ensure that analytics collection never impacts user experience, prioritizing immediate business functionality over analytics processing speed.

### Cross-Platform Analytics Coordination
The system implements sophisticated analytics integration that serves multiple business intelligence platforms:

**Internal Business Intelligence**: Complete event sequences provide detailed user journey analysis and feature usage patterns that drive product optimization decisions and business strategy development.

**Multi-Platform Consistency**: Events are transformed and synchronized across all analytics platforms while maintaining temporal consistency and data integrity for reliable business reporting.

For detailed information about the AppEvents system architecture, processing patterns, and business logic, see **[AppEvents System Documentation](./app-events.md)**.

## Integration Patterns and Business Logic Orchestration

### Model-Driven Business Logic
Models serve as authoritative sources of business events, ensuring data consistency and business rule enforcement:

**Intelligent Lifecycle Management**: The Event mixin automatically distinguishes between business-significant changes (updates) and administrative operations (soft deletes via `deletedAt` or `hiddenAt`). This separation ensures that analytics and downstream systems receive only meaningful business events, not technical housekeeping operations.

**Batch Operation Optimization**: Model events are intelligently batched to handle bulk operations efficiently while maintaining individual event granularity for business intelligence. This pattern supports both high-performance bulk updates and detailed audit trails required for compliance.

**Domain-Specific Event Enrichment**: Models automatically enrich events with business-relevant context through configurable field extraction, ensuring that downstream systems receive complete business context without manual instrumentation.

### Component-Driven User Experience Orchestration
UI components participate in sophisticated event-driven workflows that prioritize user experience while maintaining business logic integrity:

**Reactive State Synchronization**: Components automatically reflect business state changes through model events, ensuring UI consistency without manual state management. This pattern eliminates the common source of user experience bugs where UI state diverges from business state.

**User Intent Propagation**: User interactions generate domain events that propagate business intent throughout the system, enabling sophisticated workflows like cross-component coordination and analytics tracking without tight coupling.

**Lifecycle-Aware Resource Management**: Components implement sophisticated listener management patterns that prevent memory leaks while ensuring business events are properly handled throughout component lifecycles.

### Controller-Orchestrated Business Workflows
Controllers implement complex business logic coordination through event-driven patterns that separate concerns while maintaining workflow integrity:

**Multi-Domain Orchestration**: Controllers coordinate events across multiple business domains (cards, offers, rewards, messaging) to implement complex business scenarios like loyalty program workflows and promotional campaigns.

**Error Recovery and Resilience**: Controllers implement sophisticated error handling patterns that use events to coordinate recovery strategies, ensuring business continuity even during partial system failures.

**Business Rule Enforcement**: Controllers serve as business rule enforcement points, listening to domain events and triggering appropriate business logic responses while maintaining loose coupling between domains.

### Storage-Layer Business Intelligence
The storage layer implements event-driven patterns that support both operational efficiency and business intelligence requirements:

**Change Detection and Audit**: Storage operations automatically generate events that support both real-time system coordination and comprehensive audit trails required for business compliance and analytics. The AppEvents system captures these storage events to create detailed audit trails for business intelligence and regulatory compliance.

**Conflict Resolution Strategies**: Storage events coordinate sophisticated conflict resolution strategies that prioritize business logic consistency over technical convenience, ensuring data integrity in distributed scenarios.

**Performance-Optimized Synchronization**: Storage events coordinate with sync systems to implement business-appropriate synchronization strategies that balance data consistency requirements with user experience priorities. Critical business events trigger immediate synchronization through the AppEvents system to ensure revenue-impacting data is immediately available for business analysis.

## Performance and Optimization

### Event Handler Optimization
The system implements several optimization patterns:

- **Deferred Processing**: Non-critical operations use `runAfterInteractions()` to maintain UI responsiveness
- **Batch Operations**: Related events are batched to reduce processing overhead
- **Memory Management**: Automatic cleanup of event listeners prevents memory leaks
- **Error Isolation**: Event handler errors don't affect other listeners or system stability

### Event Filtering and Prioritization
The system supports sophisticated event filtering:

- **Skip Patterns**: Conditional event processing reduces unnecessary overhead
- **Priority Handling**: Critical events receive immediate processing
- **Debouncing**: High-frequency events are debounced to prevent system overload

## Event System Architecture Diagram

```mermaid
graph TB
    subgraph "🎯 Event Sources"
        MODELS[Model Layer<br/>Lifecycle Events]
        UI[UI Components<br/>User Interactions]
        CONTROLLERS[Controllers<br/>Business Logic]
        NETWORK[Network Layer<br/>API Responses]
        SYSTEM[System Events<br/>App Lifecycle]
    end

    subgraph "⚡ Core Event Infrastructure"
        GLOBAL[Global Event Emitter<br/>$.Event]
        EMITTER[EventEmitter3<br/>High-Performance Engine]
        REGISTRY[Event Registry<br/>Centralized Definitions]
    end

    subgraph "🔄 Event Processing"
        MIDDLEWARE[Event Middleware<br/>AppEvent Logging]
        FILTER[Event Filtering<br/>Skip Patterns]
        DEFER[Deferred Processing<br/>runAfterInteractions]
        SYNC[Sync Coordination<br/>Real-time Updates]
    end

    subgraph "📊 Event Consumers"
        ANALYTICS[AppEvents System<br/>Business Intelligence]
        STORAGE[Storage Layer<br/>Data Persistence]
        UIUPDATE[UI Updates<br/>Reactive Components]
        BACKGROUND[Background Tasks<br/>Sync, Notifications]
    end

    %% Event Flow Connections
    MODELS --> GLOBAL
    UI --> GLOBAL
    CONTROLLERS --> GLOBAL
    NETWORK --> GLOBAL
    SYSTEM --> GLOBAL

    GLOBAL --> EMITTER
    EMITTER --> REGISTRY
    REGISTRY --> MIDDLEWARE

    MIDDLEWARE --> FILTER
    FILTER --> DEFER
    DEFER --> SYNC

    SYNC --> ANALYTICS
    SYNC --> STORAGE
    SYNC --> UIUPDATE
    SYNC --> BACKGROUND

    %% AppEvents Integration
    ANALYTICS --> STORAGE

    %% Styling with darker backgrounds and white text
    classDef sources fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef infrastructure fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef processing fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef consumers fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff

    class MODELS,UI,CONTROLLERS,NETWORK,SYSTEM sources
    class GLOBAL,EMITTER,REGISTRY infrastructure
    class MIDDLEWARE,FILTER,DEFER,SYNC processing
    class ANALYTICS,STORAGE,UIUPDATE,BACKGROUND consumers
```

## Event Categories and Domains

### Application Lifecycle Events
Application lifecycle events coordinate critical state transitions and system management:

```javascript
EVENT.App = {
    launched: "app.launch.offline",
    resume: "app.resume",
    pause: "app.pause",
    background: "app.background",
    upgraded: "app.upgraded",
    active: "app.active",
    inactive: "app.inactive"
}
```

These events enable:
- **State Coordination**: Seamless transitions between app states
- **Background Task Management**: Coordination of background processing
- **Upgrade Handling**: Version transition management and data migration
- **Performance Optimization**: Resource management during state changes

### Card Management Events
Card events form the core of the loyalty card ecosystem:

```javascript
EVENT.Card = {
    created: "created",
    updated: "updated",
    deleted: "deleted",
    view: "card.view",
    accept: "card.accept",
    decline: "card.decline",
    new: "card.new",
    register: "card.register",
    share: "card.share"
}
```

These events facilitate:
- **Lifecycle Management**: Complete card lifecycle from creation to deletion
- **User Interactions**: Card viewing, acceptance, and sharing workflows
- **State Synchronization**: Real-time updates across all card displays
- **Analytics Integration**: Comprehensive tracking of card usage patterns

### Navigation and System Events
Navigation events coordinate screen transitions and system interactions:

```javascript
EVENT.Navigator = {
    screenAppeared: "navigator.screenAppeared",
    modalDismissed: "navigator.modalDismissed",
    bottomTabSelected: "navigator.bottomTabSelected",
    dismissOverlay: "navigator.dismissOverlay",
    popToRoot: "navigator.popToRoot"
}
```

### Sync and Data Events
Synchronization events manage data consistency and background operations:

```javascript
EVENT.Sync = {
    all: "sync.all",
    cache: "sync.cache",
    appEvent: "sync.appevent"
}
```

### Offer and Reward Events
Offer and reward events handle promotional campaigns and loyalty programs:

```javascript
EVENT.Offer = {
    updated: "updated",
    deleted: "deleted",
    new: "offer.new",
    view: "offer.view",
    redeem: "offer.redeem"
}
```

### Location and Context Events
Location events enable contextual experiences and geofencing:

```javascript
EVENT.Location = {
    countryChanged: "location.country.changed",
    positionChanged: "location.position.changed",
    spotChanged: "location.spot.changed"
}
```

### Widget and Interaction Events
Widget events coordinate interactive components and user engagement:

```javascript
EVENT.Widget = {
    view: "widget.view",
    exit: "widget.exit",
    checkin: "widget.attend.checkin",
    usage: "widget.applet.usage",
    changed: "widget.changed"
}
```

## Best Practices and Patterns

### Event Design Principles
- **Minimal Payloads**: Events carry only essential data to reduce memory usage
- **Consistent Naming**: Events follow domain.action.target naming conventions using dot notation
- **Immutable Data**: Event payloads are immutable to prevent side effects
- **Error Resilience**: Event handlers are designed to fail gracefully

### Event Naming Conventions
- Use dot notation for hierarchical events: `card.view`, `app.resume`
- Follow consistent naming patterns across modules
- Use descriptive names that indicate the action and target
- Organize events by domain for better maintainability

### Listener Management Best Practices
```javascript
class MyComponent extends React.Component {
    componentDidMount() {
        // Always bind listeners with context
        $.Event.on(EVENT.Card.updated, this.onCardUpdate, this);
    }

    componentWillUnmount() {
        // Remove listeners to prevent memory leaks
        $.Event.removeListener(EVENT.Card.updated, this.onCardUpdate, this);
    }

    onCardUpdate = (data) => {
        // Handle card update
    }
}
```

### Performance Considerations
- **Avoid Heavy Processing**: Event handlers perform minimal work and delegate heavy operations
- **Use Deferred Processing**: Non-critical operations are deferred using `runAfterInteractions()`
- **Implement Debouncing**: High-frequency events are debounced to prevent system overload
- **Monitor Memory Usage**: Event listeners are properly cleaned up to prevent memory leaks

### Common Issues and Solutions

#### Memory Leaks
- **Problem**: Listeners not removed on component unmount
- **Solution**: Always call `removeListener()` or `removeAllListeners()` in cleanup methods

#### Event Handler Context
- **Problem**: `this` context lost in event handlers
- **Solution**: Use arrow functions or bind context explicitly

#### Duplicate Listeners
- **Problem**: Same listener added multiple times
- **Solution**: Check existing listeners before adding new ones

#### Performance Issues
- **Problem**: Heavy processing in event handlers blocking UI
- **Solution**: Use `runAfterInteractions()` for non-critical work

### Event System Lifecycle

#### Initialization Sequence
The event system follows a specific startup sequence:

```javascript
// In perkd.js - Application startup
AppEvent.start();    // Start app event logging
Sync.start();        // Start sync event handling
```

1. **Global Setup**: `$.Event` emitter created in `globals.js`
2. **Event Definitions**: Load event configurations from JSON files
3. **System Services**: Start AppEvent and Sync systems
4. **Component Registration**: Controllers and components register listeners
5. **Ready State**: Event system fully operational

#### Shutdown Process
```javascript
// Cleanup during app shutdown
AppEvent.stop();
Sync.stop();
```

## Event Flow Patterns

### User Experience Event Flow

The following diagram illustrates how events facilitate the complete user experience flow:

```mermaid
graph TB
    subgraph "👤 User Interactions"
        LAUNCH[App Launch]
        SCAN[Card Scan]
        VIEW[Card View]
        REDEEM[Offer Redeem]
        CHECKOUT[Checkout]
    end

    subgraph "⚡ Event Processing Flow"
        EMIT[Event Emission]
        VALIDATE[Event Validation]
        MIDDLEWARE[Middleware Processing]
        PERSIST[Event Persistence]
        PROPAGATE[Event Propagation]
    end

    subgraph "🔄 System Responses"
        UIUPDATE[UI Updates]
        ANALYTICS[Analytics Tracking]
        SYNC[Data Synchronization]
        NOTIFICATION[Notifications]
        BACKGROUND[Background Tasks]
    end

    subgraph "💾 Data Flow"
        MODELUPDATE[Model Updates]
        STORAGE[Storage Operations]
        NETWORK[Network Requests]
        CACHE[Cache Updates]
    end

    %% User Interaction Flow
    LAUNCH --> EMIT
    SCAN --> EMIT
    VIEW --> EMIT
    REDEEM --> EMIT
    CHECKOUT --> EMIT

    %% Event Processing Flow
    EMIT --> VALIDATE
    VALIDATE --> MIDDLEWARE
    MIDDLEWARE --> PERSIST
    PERSIST --> PROPAGATE

    %% System Response Flow
    PROPAGATE --> UIUPDATE
    PROPAGATE --> ANALYTICS
    PROPAGATE --> SYNC
    PROPAGATE --> NOTIFICATION
    PROPAGATE --> BACKGROUND

    %% Data Flow
    SYNC --> MODELUPDATE
    MODELUPDATE --> STORAGE
    STORAGE --> NETWORK
    NETWORK --> CACHE

    %% Feedback Loops
    CACHE --> UIUPDATE
    ANALYTICS --> BACKGROUND
    NOTIFICATION --> LAUNCH

    %% Styling with darker backgrounds and white text
    classDef interactions fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef processing fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef responses fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef dataflow fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff

    class LAUNCH,SCAN,VIEW,REDEEM,CHECKOUT interactions
    class EMIT,VALIDATE,MIDDLEWARE,PERSIST,PROPAGATE processing
    class UIUPDATE,ANALYTICS,SYNC,NOTIFICATION,BACKGROUND responses
    class MODELUPDATE,STORAGE,NETWORK,CACHE dataflow
```

### Cross-Component Communication Flow

This diagram shows how events enable sophisticated cross-component communication:

```mermaid
graph LR
    subgraph "🎯 Component Layer"
        CARDLIST[Card List<br/>Component]
        CARDDETAIL[Card Detail<br/>Component]
        BAGWIDGET[Bag Widget<br/>Component]
        OFFERLIST[Offer List<br/>Component]
    end

    subgraph "🎮 Controller Layer"
        CARDCTRL[Card Controller]
        BAGCTRL[Bag Controller]
        SHOPCTRL[Shop Controller]
        MSGCTRL[Message Controller]
    end

    subgraph "💾 Model Layer"
        CARDMODEL[Card Model]
        OFFERMODEL[Offer Model]
        BAGMODEL[Bag Model]
        MSGMODEL[Message Model]
    end

    subgraph "⚡ Event Bus"
        GLOBAL[$.Event<br/>Global Event Emitter]
    end

    %% Component to Event Bus
    CARDLIST -.->|EVENT.Card.view| GLOBAL
    CARDDETAIL -.->|EVENT.Card.updated| GLOBAL
    BAGWIDGET -.->|EVENT.Bag.addItems| GLOBAL
    OFFERLIST -.->|EVENT.Offer.redeem| GLOBAL

    %% Event Bus to Controllers
    GLOBAL -.->|EVENT.Card.view| CARDCTRL
    GLOBAL -.->|EVENT.Bag.addItems| BAGCTRL
    GLOBAL -.->|EVENT.Offer.redeem| SHOPCTRL
    GLOBAL -.->|EVENT.Message.new| MSGCTRL

    %% Controllers to Models
    CARDCTRL --> CARDMODEL
    BAGCTRL --> BAGMODEL
    SHOPCTRL --> OFFERMODEL
    MSGCTRL --> MSGMODEL

    %% Model Events Back to Bus
    CARDMODEL -.->|EVENT.Card.updated| GLOBAL
    OFFERMODEL -.->|EVENT.Offer.updated| GLOBAL
    BAGMODEL -.->|EVENT.Bag.changed| GLOBAL
    MSGMODEL -.->|EVENT.Message.received| GLOBAL

    %% Event Bus to Components (Updates)
    GLOBAL -.->|EVENT.Card.updated| CARDLIST
    GLOBAL -.->|EVENT.Bag.changed| BAGWIDGET
    GLOBAL -.->|EVENT.Offer.updated| OFFERLIST
    GLOBAL -.->|EVENT.Message.received| CARDDETAIL

    %% Styling
    classDef components fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef controllers fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef models fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef eventbus fill:#d69e2e,stroke:#ed8936,stroke-width:3px,color:#ffffff

    class CARDLIST,CARDDETAIL,BAGWIDGET,OFFERLIST components
    class CARDCTRL,BAGCTRL,SHOPCTRL,MSGCTRL controllers
    class CARDMODEL,OFFERMODEL,BAGMODEL,MSGMODEL models
    class GLOBAL eventbus
```

## Advanced Event Patterns

### Event Middleware and Business Logic Pipeline
The system implements a sophisticated middleware architecture that enforces business rules and operational requirements:

**Business Rule Validation**: Events undergo validation that enforces business constraints before propagation, ensuring data integrity and business logic compliance throughout the system.

**Contextual Enrichment**: Events are automatically enriched with business-relevant context including user location (when permitted), session information, and related business entities, providing complete context for downstream business logic.

**Intelligent Filtering**: Skip patterns implement sophisticated business logic that prevents unnecessary processing of events that don't meet business criteria, optimizing system performance while maintaining business rule compliance.

**Analytics Integration**: The AppEvents system provides comprehensive event middleware for analytics and business intelligence, capturing and enriching events for external platforms while maintaining performance optimization. See **[AppEvents System Documentation](./app-events.md)** for detailed analytics processing patterns.

### Deferred Operation Architecture for Business Continuity
The deferred operation system implements sophisticated business continuity patterns that ensure critical operations complete even during adverse conditions:

**Priority-Based Processing**: Business-critical operations (revenue-impacting events like card acceptance, offer redemption) receive priority processing, while analytics and non-critical operations are deferred to maintain user experience.

**Graceful Degradation**: During network outages or system stress, the system intelligently defers non-critical operations while maintaining core business functionality, ensuring user engagement continues even during partial system failures.

**Business Context Preservation**: Deferred operations maintain complete business context, enabling sophisticated retry and recovery strategies that preserve business logic integrity across system restarts and network reconnections.

## Business Resilience and Error Recovery Architecture

### Business Continuity Through Error Isolation
The system implements sophisticated error isolation strategies that prioritize business continuity over technical perfection:

**Fault Containment**: Event handler failures are isolated to prevent cascading business disruptions. When one business domain experiences issues (e.g., analytics service failure), other critical business functions (card management, offer redemption) continue operating normally.

**Business Impact Assessment**: Error handling strategies are differentiated based on business impact. Revenue-critical operations receive immediate attention and recovery resources, while non-critical operations (analytics, logging) fail gracefully without affecting user experience.

**Operational Monitoring**: Failed events generate monitoring events that enable business operations teams to understand system health from a business perspective, not just technical metrics.

### Intelligent Recovery Strategies for Business Operations
The recovery system implements business-aware strategies that prioritize operational continuity and data consistency:

**Business-Priority Recovery**: Critical business operations (card acceptance, offer redemption, payment processing) receive priority in recovery queues, ensuring revenue-impacting functions are restored first.

**Context-Aware Retry Logic**: Recovery mechanisms preserve complete business context, enabling intelligent retry strategies that understand business constraints like time-sensitive offers or location-dependent operations.

**Graceful Degradation Patterns**: When permanent failures occur, the system implements business-appropriate fallback strategies that maintain core functionality while alerting business operations teams to take corrective action.

### Event-Driven Circuit Breaker Patterns
The system implements sophisticated circuit breaker patterns that protect business operations from cascading failures:

**Domain-Specific Protection**: Circuit breakers are implemented at business domain boundaries, ensuring that failures in one business area (e.g., promotional campaigns) don't affect other critical business functions (core card management).

**Business Metric Monitoring**: Circuit breakers monitor business-relevant metrics (transaction success rates, user engagement levels) rather than just technical metrics, providing business-meaningful protection thresholds.

**Adaptive Recovery Strategies**: Circuit breakers implement business-aware recovery strategies that consider factors like peak business hours, promotional campaign schedules, and user behavior patterns when determining recovery timing.

## Testing and Debugging Business Logic

### Business Logic Testing Strategies
The event-driven architecture enables sophisticated testing patterns that validate business logic integrity:

**Business Workflow Testing**: Event-driven tests validate complete business workflows by verifying that business actions trigger appropriate event sequences. This ensures that complex business scenarios (card acceptance workflows, promotional campaign execution) function correctly across domain boundaries.

**Business Rule Validation**: Tests verify that business rules are properly enforced through event processing, including conditional logic, skip patterns, and business constraint validation.

**Cross-Domain Integration Testing**: Event-based testing validates that business operations correctly coordinate across multiple domains (cards, offers, rewards, messaging) without requiring complex test setup or mocking strategies.

### Business Intelligence Through Event Analysis
The system provides sophisticated debugging and monitoring capabilities that focus on business insights:

**Business Flow Visualization**: Event logging enables visualization of complete business workflows, helping identify bottlenecks, inefficiencies, and opportunities for business process optimization.

**User Behavior Analysis**: Event streams provide detailed insights into user behavior patterns, enabling data-driven decisions about feature development, user experience improvements, and business strategy.

**Performance Impact Assessment**: Event timing and frequency analysis helps identify performance issues that impact business metrics like user engagement, conversion rates, and operational efficiency.

**Business Rule Validation**: Event history analysis enables validation that business rules are being correctly applied across all system components, ensuring business logic integrity and compliance requirements.

## API Reference

### Global Event Methods
The global event system provides these core methods:

```javascript
$.Event.on(event, handler, context)      // Add listener
$.Event.once(event, handler, context)    // Add one-time listener
$.Event.emit(event, data)                // Emit event
$.Event.removeListener(event, handler, context)  // Remove listener
```

### Emitter Class Methods
The Emitter class provides local event handling capabilities:

```javascript
new Emitter(host, emitter)               // Create emitter
Emitter.extend(host)                     // Extend object with events
emitter.on(event, handler, context)     // Add listener
emitter.emit(event, data)                // Emit event
emitter.removeAllListeners()             // Remove all listeners
```

### Event Definition Properties
Event definitions support these configuration properties:

```javascript
{
    name: string,           // Display name for analytics
    sync: boolean,          // Sync to server immediately
    skip: object,           // Skip conditions
    occurredAt: string,     // Custom timestamp field
    context: object,        // Context data mapping
    [model]: {              // Model data extraction
        id: string,         // ID field path
        fields: array       // Fields to include
    }
}
```

## Migration and Integration Guide

### From Direct Communication
Replace direct method calls with events for better decoupling:

```javascript
// Before: Direct coupling
cardController.updateUI(data);

// After: Event-driven
$.Event.emit(EVENT.Card.updated, data);
```

### Adding New Events
Follow this process when adding new events:

1. **Define Event**: Add event to `Events.json`
2. **Configure Analytics**: Add configuration to appropriate definition file
3. **Implement Emission**: Add event emission in source component
4. **Add Listeners**: Register listeners in target components
5. **Test Integration**: Verify event flow works correctly

### Event Deprecation Process
When removing events, follow this deprecation process:

1. **Mark as Deprecated**: Add deprecation notice in documentation
2. **Add Warnings**: Include console warnings for usage in development
3. **Provide Migration Path**: Document alternative approaches
4. **Grace Period**: Allow time for migration before removal
5. **Clean Removal**: Remove event definition and related code

### Best Practices Summary

#### Do's
- Use consistent event naming conventions
- Include relevant context data in events
- Remove listeners on component cleanup
- Use event definitions for analytics integration
- Implement proper error handling in event handlers
- Follow the established event taxonomy and organization

#### Don'ts
- Don't perform heavy operations in event handlers
- Don't create circular event dependencies
- Don't emit events synchronously in constructors
- Don't ignore memory leak prevention
- Don't bypass the event system for cross-component communication
- Don't use inconsistent event naming patterns

## Event System Architecture Layers

### Primary Event System vs. AppEvents Subsystem

The Perkd event architecture operates on two complementary layers that serve distinct but related purposes:

#### Primary Event System (This Document)
**Purpose**: Real-time business logic coordination and application state management
- **Function**: Enables decoupled communication between components, controllers, and services
- **Scope**: Immediate application behavior, cross-component coordination, and system lifecycle management
- **Lifecycle**: Events are emitted, processed by listeners, and consumed immediately for business logic execution
- **Examples**: Card state changes, user navigation, offer redemption workflows, sync coordination

#### AppEvents Analytics Subsystem
**Purpose**: Business intelligence, analytics, and historical data collection
- **Function**: Observes the primary event flow and transforms events into structured analytics data
- **Scope**: Historical data collection, user behavior tracking, cross-platform analytics integration
- **Lifecycle**: Events are captured, enriched with business context, persisted, and synchronized to analytics platforms
- **Examples**: User interaction analytics, revenue tracking, engagement metrics, business intelligence data

#### Architectural Integration
The two systems work together through a sophisticated observer pattern:

1. **Shared Infrastructure**: Both systems use the same global `$.Event` emitter and centralized event definitions
2. **Independent Operation**: AppEvents processing never blocks or interferes with primary business logic
3. **Business Value Multiplication**: Primary events enable application functionality while AppEvents enable data-driven optimization
4. **Performance Isolation**: Analytics processing is deferred to maintain user experience quality

This layered architecture ensures that comprehensive business intelligence collection supports rather than hinders core application performance and user experience.

## Architectural Decision Summary

### Why Event-Driven Architecture?

The choice of event-driven architecture reflects strategic business requirements that prioritize scalability, resilience, and operational intelligence:

**Business Scalability**: The decoupled nature enables independent scaling of business domains. As the loyalty program ecosystem grows to include new partners, payment methods, and promotional strategies, each domain can evolve independently without requiring system-wide architectural changes.

**Operational Resilience**: Event-driven patterns ensure that business operations continue even during partial system failures. Critical revenue-generating functions (card management, offer redemption) remain operational even when non-critical systems (analytics, notifications) experience issues.

**Business Intelligence Foundation**: Every user interaction generates structured events that feed comprehensive business intelligence systems. This transforms the application from a simple utility into a strategic business asset that provides insights into user behavior, feature adoption, and business performance.

**Regulatory Compliance**: The comprehensive event logging and audit trail capabilities support regulatory compliance requirements for financial services and loyalty programs, providing complete traceability of all business operations.

### Strategic Business Impact

The event-driven architecture enables sophisticated business capabilities that would be difficult or impossible with traditional tightly-coupled architectures:

- **Real-time Personalization**: Events enable real-time analysis of user behavior to deliver personalized experiences and targeted promotional campaigns
- **Business Process Optimization**: Event streams provide detailed insights into business process efficiency, enabling continuous optimization of user workflows and operational procedures
- **Predictive Analytics**: Historical event data supports predictive analytics for user behavior, churn prevention, and business forecasting
- **Partner Integration**: The decoupled architecture simplifies integration with business partners, payment providers, and third-party services

## Cross-References

### Core Event System Documentation
- **[Application Architecture](./app-architecture.md)**: Overall application architecture and patterns
- **[Actions System](./actions.md)**: Action-event integration patterns
- **[Storage Architecture](./storage.md)**: Storage layer event integration
- **[Sync System](./sync.md)**: Event-driven synchronization patterns

### Specialized Event Subsystems
- **[AppEvents System](./app-events.md)**: Comprehensive documentation of the analytics and business intelligence event processing subsystem that consumes and enriches events from the global event system

### Integration Guidelines
When working with the event system:
- **Event Definitions**: New events should be added to the centralized event definitions and configured for both real-time processing and analytics collection
- **Performance Considerations**: Primary event handlers should focus on immediate business logic, while analytics processing should use the AppEvents system
- **Business Logic Separation**: Keep real-time business coordination in the primary event system and historical/analytics concerns in the AppEvents subsystem
- **Cross-System Coordination**: Use the shared event infrastructure to maintain consistency between real-time and analytics processing

---

*This documentation reflects the actual implementation as of the current codebase analysis and focuses on the business logic and architectural decisions behind the event-driven system.*
