# Permissions Management System

## Overview

The Perkd application implements a sophisticated multi-layered permissions management system that governs access control across device capabilities, application features, and business operations. This system combines device-level permissions with application-level authorization to create a comprehensive security framework that ensures appropriate access while maintaining user experience quality.

## Architecture Overview

The permissions management system operates through three distinct but interconnected layers:

### 1. Device Permissions Layer
- **Platform Integration**: Native iOS/Android permission management
- **Feature Access Control**: Camera, location, notifications, biometrics, NFC, Bluetooth
- **Cross-Platform Abstraction**: Unified API across different mobile platforms
- **State Management**: Persistent tracking of permission states and changes

### 2. Application Authorization Layer  
- **Role-Based Access Control (RBAC)**: Staff, customer, and administrative role management
- **Widget Access Control Lists (ACL)**: Fine-grained widget-level permissions
- **Feature-Based Permissions**: Granular control over application functionality
- **Context-Aware Authorization**: Dynamic permissions based on user context and location

### 3. Business Logic Permissions Layer
- **Entity-Specific Access**: Card, offer, ticket, and place-specific permissions
- **Operational Authorization**: Payment, redemption, and sharing permissions
- **Merchant-Specific Controls**: Brand and merchant-level access restrictions
- **Geographic Compliance**: Location-based permission enforcement

## Permission Architecture Flow

```mermaid
graph TB
    subgraph "User Request"
        A[User Action] --> B[Permission Check]
    end
    
    subgraph "Device Layer"
        B --> C{Device Permission?}
        C -->|Missing| D[Request Device Permission]
        C -->|Granted| E[Continue to App Layer]
        D --> F{Permission Granted?}
        F -->|No| G[Show Rationale/Settings]
        F -->|Yes| E
    end
    
    subgraph "Application Layer"
        E --> H{User Role Check}
        H --> I{Widget ACL Check}
        I --> J{Feature Permission}
        J -->|Authorized| K[Continue to Business Layer]
        J -->|Denied| L[Access Denied]
    end
    
    subgraph "Business Layer"
        K --> M{Entity Access Check}
        M --> N{Operational Permission}
        N --> O{Geographic Compliance}
        O -->|Authorized| P[Execute Action]
        O -->|Denied| Q[Business Rule Violation]
    end
    
    style A fill:#e1f5fe
    style P fill:#c8e6c9
    style G fill:#ffcdd2
    style L fill:#ffcdd2
    style Q fill:#ffcdd2
```

## Device Permissions Management

### Permission Types and States

The system manages comprehensive device permissions with sophisticated state tracking:

**Core Device Permissions:**
- **Camera**: Photo capture and barcode scanning capabilities
- **Photo Library**: Access to device photo storage for image selection
- **Location**: GPS positioning for place detection and check-in functionality
- **Notifications**: Push notification delivery and badge management
- **Contacts**: Contact list access for sharing functionality
- **Biometrics**: Fingerprint and Face ID authentication
- **NFC**: Near Field Communication for contactless interactions
- **Bluetooth**: Peripheral device connectivity
- **Background Fetch**: Background data synchronization

**Permission States:**
- `UNAVAILABLE`: Feature not supported on device
- `UNKNOWN`: Permission not yet requested
- `DENIED`: Permission denied but can be re-requested
- `BLOCKED`: Permission permanently denied by user
- `LIMITED`: Partial permission granted (iOS photo library)
- `ALLOWED`: Full permission granted

### Permission Request Flow

The system implements a sophisticated permission request flow that prioritizes user experience:

1. **Contextual Checking**: Permissions are checked at the moment of feature use
2. **Rationale Display**: Clear explanations provided before permission requests
3. **Graceful Degradation**: Alternative functionality when permissions are denied
4. **Settings Guidance**: Direct users to system settings for blocked permissions
5. **State Persistence**: Permission states cached and synchronized across app launches

### Integration with Installation Management

Device permissions are tightly integrated with the installation management system:

- **Automatic Synchronization**: Permission states synchronized with backend services
- **Deferred Updates**: Permission changes queued when offline and synchronized later
- **Event Broadcasting**: Permission state changes trigger system-wide events
- **Capability Detection**: Device capabilities influence available permission options

## Application Authorization System

### Role-Based Access Control (RBAC)

The application implements a sophisticated RBAC system that governs access to features and functionality:

#### User Role Hierarchy

**Customer Roles:**
- **Standard Customer**: Basic card and offer access
- **Premium Customer**: Enhanced features and priority access
- **VIP Customer**: Exclusive offers and premium support

**Staff Roles:**
- **Staff Member**: Basic operational access with widget restrictions
- **Manager**: Enhanced operational permissions and reporting access
- **Administrator**: Full system access and configuration capabilities

**System Roles:**
- **System Administrator**: Complete system control and user management
- **Merchant Administrator**: Merchant-specific administrative access
- **Brand Manager**: Brand-level configuration and oversight

### Widget Access Control Lists (ACL)

The system implements fine-grained widget-level access control through ACL mechanisms:

#### ACL Resolution Process

```mermaid
graph LR
    subgraph "Widget Access Check"
        A[Widget Request] --> B{Requires ACL?}
        B -->|No| C[Allow Access]
        B -->|Yes| D[Get User ACL]
        D --> E{Widget in ACL?}
        E -->|Yes| F[Allow Access]
        E -->|No| G[Deny Access]
    end
    
    subgraph "ACL Sources"
        H[JWT Token] --> I[User Credentials]
        I --> J[Staff Permissions]
        J --> K[Widget List]
        K --> D
    end
    
    style C fill:#c8e6c9
    style F fill:#c8e6c9
    style G fill:#ffcdd2
```

#### Widget Qualification System

The widget system implements comprehensive qualification checking that combines multiple authorization layers:

**Qualification Criteria:**
- **Version Compatibility**: Minimum app version requirements
- **Network Requirements**: Online connectivity validation
- **Device Permissions**: Required device permission verification
- **Membership Status**: Active membership validation for restricted widgets
- **Role Authorization**: User role and ACL verification
- **Geographic Restrictions**: Location-based access control
- **Step-Based Visibility**: Card flow step requirements
- **Spot-Based Access**: Location check-in requirements

### Authentication and Credential Management

The application implements a multi-layered authentication system that supports the authorization framework:

#### JWT Token-Based Authentication

**Token Structure and Validation:**
- **Access Tokens**: Short-lived tokens for API authentication
- **Refresh Tokens**: Long-lived tokens for session management
- **Token Hierarchy**: App-level, CardMaster-level, and Card-level credentials
- **Automatic Refresh**: Seamless token renewal with lead-time management

**Credential Inheritance Pattern:**
The system implements a sophisticated credential inheritance model where permissions cascade through multiple levels:

1. **Application Credentials**: Global app-level permissions and capabilities
2. **CardMaster Credentials**: Brand or merchant-specific permissions
3. **Card Credentials**: Individual card-specific access rights

#### Staff Authentication and ACL Integration

Staff members receive enhanced authentication with ACL-based permissions:

**Staff Credential Structure:**
- **Staff ID**: Unique identifier for staff member
- **Role Assignments**: Multiple role assignments per staff member
- **Widget Permissions**: Explicit widget access lists
- **Operational Permissions**: Specific business operation authorizations
- **Geographic Restrictions**: Location-based access limitations

## Business Logic Permissions

### Entity-Specific Access Control

The system implements granular access control for different business entities:

#### Card Access Permissions

**Card State-Based Access:**
- **Active Cards**: Full functionality access for registered cards
- **Pending Cards**: Limited access during registration process
- **Expired Cards**: Read-only access with renewal prompts
- **Transferred Cards**: Restricted access during transfer process

**Card Type Permissions:**
- **Standard Cards**: Basic feature access
- **Premium Cards**: Enhanced feature availability
- **Staff Cards**: Administrative and operational capabilities
- **Custom Cards**: User-defined permission sets

#### Offer and Ticket Authorization

**Redemption Authorization Logic:**
The system implements sophisticated authorization logic for offer redemption:

```mermaid
graph TD
    subgraph "Redemption Authorization"
        A[Redemption Request] --> B{Valid Offer?}
        B -->|No| C[Offer Mismatch Error]
        B -->|Yes| D{Authorization Check}
        D --> E{Master ID Match?}
        E -->|Yes| F[Authorize Redemption]
        E -->|No| G{Merchant ID Match?}
        G -->|Yes| F
        G -->|No| H{Card Master ID Match?}
        H -->|Yes| F
        H -->|No| I[Authorization Failed]
    end

    style F fill:#c8e6c9
    style C fill:#ffcdd2
    style I fill:#ffcdd2
```

**Authorization Hierarchy:**
1. **Master ID Authorization**: Direct master-to-offer relationship
2. **Merchant ID Authorization**: Merchant-specific offer access
3. **Card Master ID Authorization**: Card brand-specific permissions
4. **Default Authorization**: Open access when no restrictions apply

### Payment Authorization System

The payment system implements comprehensive authorization checks:

#### Payment Method Authorization

**Authorization Flow:**
- **Method Availability**: Validate payment method support
- **Merchant Authorization**: Verify merchant acceptance
- **Card Master Authorization**: Confirm brand-level payment permissions
- **Amount Validation**: Verify transaction limits and restrictions

**Manual Payment Authorization Business Logic:**

For manual payment processing, the system implements sophisticated authorization logic that reflects real-world payment processing requirements:

**Authorization Decision Matrix:**

The manual payment authorization follows a sophisticated decision matrix that considers multiple business factors:

1. **Unrestricted Payment Authorization**: When no `merchantIds` or `cardMasterIds` are specified, the payment is considered universally authorized, representing open payment acceptance policies.

2. **Merchant-Specific Authorization**: When `merchantIds` are specified, the system validates that the card master's merchant list includes at least one of the required merchant IDs. This enables merchant-specific payment restrictions and partnership agreements.

3. **Brand-Level Authorization**: When `cardMasterIds` are specified, the system validates that the card master ID matches one of the authorized brands. This enables brand-specific payment policies and exclusions.

4. **Combined Authorization Logic**: The system uses logical OR operations between merchant and brand authorization, meaning payment is authorized if either merchant OR brand criteria are met. This provides flexibility in payment acceptance policies.

**Payment Method Availability Logic:**

The system implements comprehensive payment method availability logic:

- **Method Support Validation**: Each payment method is validated for support on the current device and platform configuration
- **Merchant Acceptance Verification**: Payment methods are filtered based on merchant acceptance policies and capabilities
- **Brand-Level Restrictions**: Card master-specific payment method restrictions are enforced to comply with brand policies
- **Amount-Based Validation**: Transaction limits and restrictions are validated based on payment method, merchant policies, and regulatory requirements

**Authorization Flow Security:**

The payment authorization implements multiple security layers:

- **Action Validation**: Each payment action (authorize, capture, refund) is validated against the specific business context and user permissions
- **Temporal Validation**: Payment authorizations include time-to-live (TTL) validation to prevent replay attacks and ensure timely processing
- **Cryptographic Validation**: Payment requests are cryptographically signed and validated to ensure integrity and authenticity

### Geographic and Compliance Permissions

#### Location-Based Access Control

**Regional Restrictions:**
- **Country-Level Permissions**: Feature availability by country
- **State/Province Restrictions**: Regional compliance requirements
- **City-Level Controls**: Municipal regulation compliance
- **Venue-Specific Permissions**: Location-based feature access

**Compliance Integration:**
- **Data Protection Regulations**: GDPR, CCPA compliance enforcement
- **Financial Regulations**: Payment processing compliance
- **Age Verification**: Age-restricted feature access
- **Geographic Licensing**: Location-based service licensing

## Permission Inheritance and Cascading

### Hierarchical Permission Model

The system implements a sophisticated permission inheritance model:

#### Permission Cascade Flow

```mermaid
graph TB
    subgraph "Permission Inheritance"
        A[System Permissions] --> B[Brand Permissions]
        B --> C[Merchant Permissions]
        C --> D[Card Master Permissions]
        D --> E[Card Permissions]
        E --> F[User Permissions]
        F --> G[Session Permissions]
    end

    subgraph "Override Rules"
        H[Explicit Deny] --> I[Blocks Inheritance]
        J[Explicit Allow] --> K[Grants Access]
        L[No Rule] --> M[Inherits Parent]
    end

    style I fill:#ffcdd2
    style K fill:#c8e6c9
    style M fill:#fff3e0
```

#### Permission Resolution Algorithm

The system resolves permissions using a sophisticated algorithm that considers multiple factors:

1. **Explicit Permissions**: Direct permission grants or denials
2. **Inherited Permissions**: Permissions inherited from parent entities
3. **Role-Based Permissions**: Permissions derived from user roles
4. **Context-Based Permissions**: Dynamic permissions based on current context
5. **Time-Based Permissions**: Temporal restrictions and expiration
6. **Location-Based Permissions**: Geographic access controls

### Deferred Permission Operations

The system implements deferred permission operations for offline scenarios:

**Deferred Operation Types:**
- **Permission Grants**: Delayed permission assignments
- **Role Updates**: Deferred role modifications
- **ACL Changes**: Queued access control list updates
- **Credential Updates**: Delayed credential synchronization

**Synchronization Strategy:**
- **Queue Management**: FIFO processing of deferred operations
- **Conflict Resolution**: Handling conflicting permission changes
- **Rollback Capability**: Reverting failed permission operations
- **Audit Trail**: Complete logging of permission changes

## Security Policies and Validation

### Input Validation and Sanitization

The permissions system implements comprehensive input validation:

**Validation Layers and Business Logic:**

The permission system implements comprehensive validation that goes beyond simple type checking to enforce complex business rules:

**Parameter Validation Business Logic:**
- **Type Safety Enforcement**: Ensures that permission requests conform to expected data types and formats, preventing system errors and security vulnerabilities
- **Range Validation**: Validates that permission parameters fall within acceptable business ranges (e.g., location accuracy requirements, notification types)
- **Format Compliance**: Ensures that permission identifiers and options conform to platform-specific requirements and business standards

**Business Rule Validation:**
- **Contextual Validation**: Permission requests are validated against current business context, including user state, card status, and environmental factors
- **Temporal Validation**: Time-based validation ensures that permission requests are made within acceptable timeframes and business hours
- **Dependency Validation**: Complex permission dependencies are validated to ensure that prerequisite permissions are granted before dependent permissions are requested

**Security Validation and Edge Cases:**
- **Injection Prevention**: All permission-related inputs are sanitized to prevent injection attacks and malicious data manipulation
- **Rate Limiting**: Permission requests are rate-limited to prevent abuse and ensure system stability
- **Anomaly Detection**: Unusual permission request patterns are detected and flagged for security review

**Authorization Validation Edge Cases:**

The system handles numerous edge cases that reflect real-world complexity:

1. **Expired Credential Handling**: When JWT tokens expire during permission validation, the system implements graceful degradation with automatic token refresh and retry logic
2. **Partial Permission Scenarios**: When users grant partial permissions (e.g., limited photo access on iOS), the system adapts functionality while maintaining user experience
3. **Permission Revocation During Use**: The system handles scenarios where permissions are revoked while features are actively being used, implementing graceful shutdown and user notification
4. **Cross-Platform Permission Mapping**: Different permission models between iOS and Android are handled through sophisticated mapping logic that preserves business intent across platforms
5. **Offline Permission Validation**: When network connectivity is unavailable, the system uses cached permission states with appropriate fallback logic and user communication

### Security Controls

#### Multi-Factor Authentication Integration

**Biometric Authentication:**
- **Fingerprint Authentication**: TouchID/Fingerprint sensor integration
- **Face Recognition**: FaceID/Face unlock capability
- **Fallback Mechanisms**: PIN and password alternatives
- **Security Threshold Management**: Failed attempt handling

**Authentication Flow Integration:**
The permissions system integrates with biometric authentication for sensitive operations:

```mermaid
graph LR
    subgraph "Secure Operation Flow"
        A[Sensitive Action] --> B{Biometric Required?}
        B -->|Yes| C[Request Biometric Auth]
        B -->|No| D[Check Permissions]
        C --> E{Auth Success?}
        E -->|Yes| D
        E -->|No| F[Authentication Failed]
        D --> G{Authorized?}
        G -->|Yes| H[Execute Action]
        G -->|No| I[Access Denied]
    end

    style H fill:#c8e6c9
    style F fill:#ffcdd2
    style I fill:#ffcdd2
```

#### Data Protection and Encryption

**Credential Security:**
- **Token Encryption**: JWT token encryption and signing
- **Credential Storage**: Secure storage of authentication credentials
- **Transport Security**: HTTPS and certificate pinning
- **Data Integrity**: Checksum and signature verification

### Audit and Compliance

#### Permission Change Tracking

**Audit Trail Components:**
- **User Actions**: Complete logging of permission-related user actions
- **System Changes**: Automatic permission modifications tracking
- **Administrative Actions**: Admin-level permission changes
- **Compliance Events**: Regulatory compliance-related events

**Event Structure:**
- **Timestamp**: Precise timing of permission events
- **Actor**: User or system component initiating change
- **Target**: Entity affected by permission change
- **Action**: Specific permission operation performed
- **Context**: Environmental context of the change
- **Result**: Success or failure of the operation

## User Experience and Permission Flows

### Progressive Permission Requesting

The system implements progressive permission requesting to optimize user experience:

#### Permission Request Strategy

**Contextual Timing:**
- **Just-in-Time Requests**: Permissions requested when features are accessed
- **Feature Introduction**: Permissions explained during feature onboarding
- **Graceful Degradation**: Alternative functionality when permissions denied
- **Re-engagement Flows**: Gentle re-prompting for denied permissions

**User Education Flow:**

```mermaid
graph TD
    subgraph "Permission Education"
        A[Feature Access] --> B{Permission Required?}
        B -->|Yes| C[Show Feature Benefits]
        C --> D[Explain Permission Need]
        D --> E[Request Permission]
        E --> F{Permission Granted?}
        F -->|Yes| G[Enable Feature]
        F -->|No| H[Show Alternative]
        H --> I[Offer Settings Link]
    end

    style G fill:#c8e6c9
    style H fill:#fff3e0
```

### Permission State Management

#### State Synchronization

**Cross-Component Synchronization:**
- **Event-Driven Updates**: Permission changes broadcast via event system
- **Cache Invalidation**: Automatic cache refresh on permission changes
- **UI State Updates**: Real-time UI updates reflecting permission changes
- **Background Synchronization**: Periodic permission state refresh

#### Error Handling and Recovery

**Error Scenarios:**
- **Network Failures**: Offline permission handling and queuing
- **Authentication Failures**: Token refresh and re-authentication
- **Authorization Failures**: Clear error messaging and guidance
- **System Failures**: Graceful degradation and recovery mechanisms

## Integration Patterns

### Installation Management Integration

The permissions system is deeply integrated with installation management:

**Installation-Level Permissions:**
- **Device Capabilities**: Hardware capability detection and permission mapping
- **App Configuration**: Installation-specific permission configurations
- **Merchant Settings**: Merchant-specific permission overrides
- **Regional Compliance**: Location-based permission adjustments

### Notification System Integration

**Permission-Aware Notifications:**
- **Notification Permissions**: Dynamic notification permission checking
- **Content Filtering**: Permission-based notification content filtering
- **Delivery Optimization**: Permission-aware notification delivery
- **Fallback Mechanisms**: Alternative notification methods when permissions denied

### Widget System Integration

**Widget Permission Enforcement:**
The widget system implements comprehensive permission enforcement:

**Widget Lifecycle Integration:**
- **Preload Validation**: Permission checking during widget preload
- **Qualification Process**: Multi-layer permission validation
- **Runtime Enforcement**: Continuous permission monitoring during widget execution
- **Cleanup Operations**: Permission-aware resource cleanup

## Performance Considerations

### Permission Caching Strategy

**Multi-Level Caching:**
- **Memory Cache**: In-memory permission state caching
- **Persistent Cache**: Local storage of permission states
- **Remote Synchronization**: Backend permission state synchronization
- **Cache Invalidation**: Smart cache invalidation on permission changes

### Optimization Patterns

**Batch Operations:**
- **Bulk Permission Checks**: Efficient multiple permission validation
- **Grouped Requests**: Batched permission requests for related features
- **Deferred Processing**: Background permission processing for non-critical operations
- **Lazy Loading**: On-demand permission loading for unused features

## Best Practices and Guidelines

### Development Guidelines

**Permission Implementation:**
- **Principle of Least Privilege**: Grant minimum necessary permissions
- **Defense in Depth**: Multiple layers of permission validation
- **Fail Secure**: Default to deny when permission state is uncertain
- **User Transparency**: Clear communication about permission requirements

**Code Organization:**
- **Separation of Concerns**: Clear separation between permission layers
- **Consistent APIs**: Uniform permission checking interfaces
- **Error Handling**: Comprehensive error handling and user feedback
- **Testing Strategy**: Thorough testing of permission scenarios

### Security Best Practices

**Permission Security:**
- **Regular Audits**: Periodic review of permission configurations
- **Principle of Least Privilege**: Minimal permission grants
- **Time-Limited Permissions**: Temporal restrictions on sensitive permissions
- **Multi-Factor Validation**: Additional verification for critical operations

## Conclusion

The Perkd permissions management system represents a sophisticated, multi-layered approach to access control that balances security requirements with user experience optimization. Through its integration of device permissions, application authorization, and business logic controls, the system provides comprehensive protection while enabling rich functionality.

The system's strength lies in its layered architecture, which allows for fine-grained control at multiple levels while maintaining performance and usability. The progressive permission requesting strategy, combined with comprehensive error handling and graceful degradation, ensures that users can access functionality even when some permissions are denied.

Future enhancements to the system should focus on continued optimization of the user experience, expansion of role-based access controls, and integration with emerging authentication technologies while maintaining the robust security foundation that protects user data and business operations.

## Device Permissions Implementation Guidelines

### Permission Request Flow Best Practices

The recommended permission request flow follows these steps:

1. **Check Current Status**: Always check the permission status first
2. **Provide Context**: Explain to the user why the permission is needed
3. **Request with Rationale**: Use `showRationale: true` to provide context
4. **Handle Denial**: Provide alternatives when permissions are denied
5. **Guide to Settings**: Help users navigate to settings when permissions are blocked

### Handling Different Permission States

The Status object provides convenience getters to check permission states:

<augment_code_snippet path="src/lib/common/permissions.js" mode="EXCERPT">
````javascript
async function handleCameraAccess() {
  const status = await check('camera');

  if (status.allowed) {
    // Continue with camera access
    return true;
  }

  if (status.blocked) {
    // Show UI explaining how to enable in settings
    showSettingsInstructions('camera');
    return false;
  }

  if (status.unknown || status.denied) {
    // Request permission with explanation
    return ask('camera', { showRationale: true });
  }

  if (status.unavailable) {
    // Show alternative UI for devices without camera
    showNoCameraUI();
    return false;
  }
}
````
</augment_code_snippet>

### Device Permission Best Practices

- **Request Context**: Only request permissions when needed for a specific feature
- **Explain Purpose**: Always provide clear explanations for why permissions are needed
- **Handle Denial**: Provide a fallback experience when permissions are denied
- **Timing**: Request permissions at the moment of use, not at app startup
- **Incremental Requests**: Request permissions individually, not all at once
- **Permission Flow**: Follow the check → explain → request → handle pattern
- **Refresh Strategy**: Refresh permissions when the app comes to foreground
- **Gradual Access**: Implement progressive permission requesting based on user engagement
- **Permission Grouping**: Request related permissions together (e.g., camera and photo library)
- **Transparent Purpose**: Make it clear how user data from permissions will be used
- **Respect Choices**: If a user denies a permission, don't repeatedly ask for it
- **Status Monitoring**: Listen for permission changes using the event system
- **Permission Persistence**: Don't assume permissions persist across app launches

## Cross-References

### Related Documentation

- **Device Permissions API**: See `src/lib/common/permissions.js` for complete implementation
- **Installation Management**: See `docs/installations.md` for permission integration patterns
- **Widget System**: See `docs/widgets.md` for widget-level permission enforcement
- **Notifications**: See `docs/notifications.md` for notification permission handling
- **Sharing System**: See `docs/sharing.md` for contact permission requirements

### Integration Points

- **Actions System**: Permission actions available through `src/lib/common/actions/permissions.js`
- **Constants**: Permission constants defined in `src/lib/common/constants.js`
- **Installation Module**: Permission state persistence through `src/lib/common/installation.js`
- **Widget Qualification**: Permission checking in widget lifecycle through `src/lib/widgets/Widget.js`

