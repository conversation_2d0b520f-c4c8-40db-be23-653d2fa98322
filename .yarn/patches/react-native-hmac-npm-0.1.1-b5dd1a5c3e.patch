diff --git a/android/src/main/java/com/reactnativehmac/HmacModule.kt b/android/src/main/java/com/reactnativehmac/HmacModule.kt
index 0f761e0264a0b79a62c477c54300f1ad129049c5..a52da385827069f3335d912ce16421a80f6219b2 100644
--- a/android/src/main/java/com/reactnativehmac/HmacModule.kt
+++ b/android/src/main/java/com/reactnativehmac/HmacModule.kt
@@ -7,7 +7,7 @@ import com.facebook.react.bridge.Promise
 
 import javax.crypto.Mac
 import javax.crypto.spec.SecretKeySpec
-import kotlin.experimental.and
+import android.util.Base64
 import java.lang.Exception
 
 
@@ -38,8 +38,9 @@ fun hmac (message: String, key: String, algorithm: String, promise: Promise) {
       val keySpec = SecretKeySpec(key.toByteArray(), algorithm)  
       val mac = Mac.getInstance(algorithm)
       mac.init(keySpec)
-      val sign = mac.doFinal(message.toByteArray()).joinToString("") { String.format("%02x", it and 255.toByte()) }
-      promise.resolve(sign)
+      val sign = mac.doFinal(message.toByteArray())
+      val base64String = Base64.encodeToString(sign, Base64.DEFAULT)
+      promise.resolve(base64String)
     } catch (e: Exception) {
       promise.reject("HMAC Generation Error", e.toString(), e)
     }
diff --git a/ios/Hmac.swift b/ios/Hmac.swift
index 5178cd13c7dc74cc6bdeec2285cced3de39ae445..256480a8ef5716ed926d0da40a8fd2401538391c 100644
--- a/ios/Hmac.swift
+++ b/ios/Hmac.swift
@@ -27,11 +27,9 @@ class Hmac: NSObject {
 func handleAuthenticator(message: String, key: String, variant: HMAC.Variant, resolve: RCTPromiseResolveBlock, reject: RCTPromiseRejectBlock) -> Void {
         do {
             let hmac = try HMAC(key: key, variant: variant).authenticate(message.bytes)
-            var hexString: String = ""
-            for byte in hmac {
-                hexString.append(String(format:"%02X", byte))
-            }
-            resolve(hexString)
+            // Convert byte array to Base64 string
+            let base64String = Data(hmac).base64EncodedString()
+            resolve(base64String)
         } catch {
             let err = NSError(domain: "", code: 200, userInfo: nil)
             reject("E_HMAC", "HMAC Generation Error", err)
