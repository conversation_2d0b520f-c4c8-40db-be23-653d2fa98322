diff --git a/android/build.gradle b/android/build.gradle
index 6aafa23c3fb4ba0806f0c8beaa47292644bbc54a..98e3c39467070cadea4a3dd6373692ea7be68ac2 100644
--- a/android/build.gradle
+++ b/android/build.gradle
@@ -11,17 +11,17 @@ buildscript {
 
 apply plugin: 'com.android.library'
 
-def DEFAULT_COMPILE_SDK_VERSION             = 28
-def DEFAULT_TARGET_SDK_VERSION              = 28
+def DEFAULT_COMPILE_SDK_VERSION             = 30 
+def DEFAULT_TARGET_SDK_VERSION              = 30
 def DEFAULT_GOOGLE_PLAY_SERVICES_VERSION    = '11.8.0'
 def DEFAULT_FIREBASE_MESSAGING_VERSION      = '11.8.0'
 
 android {
-  compileSdkVersion rootProject.hasProperty('compileSdkVersion') ? rootProject.compileSdkVersion : DEFAULT_COMPILE_SDK_VERSION
+  compileSdkVersion DEFAULT_COMPILE_SDK_VERSION
 
   defaultConfig {
-    minSdkVersion 19
-    targetSdkVersion rootProject.hasProperty('targetSdkVersion') ? rootProject.targetSdkVersion : DEFAULT_TARGET_SDK_VERSION
+    minSdkVersion 23
+    targetSdkVersion DEFAULT_TARGET_SDK_VERSION
     versionCode 1
     versionName "1.0"
     testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
@@ -47,7 +47,7 @@ dependencies {
   implementation 'com.android.support:appcompat-v7:28.0.0'
   implementation "com.google.android.gms:play-services-wallet:$googlePlayServicesVersion"
   implementation "com.google.firebase:firebase-core:$firebaseVersion"
-  implementation 'com.stripe:stripe-android:10.4.6'
+  implementation 'com.stripe:stripe-android:18.2.0'
   implementation 'com.github.tipsi:CreditCardEntry:1.5.1'
 }
 repositories {
diff --git a/android/src/main/java/com/gettipsi/stripe/Errors.java b/android/src/main/java/com/gettipsi/stripe/Errors.java
index a335b93adeef197076604153b4663d37bd8bb264..6efc7834ce3a516f31e8c5a65826a8c0005cb1a2 100644
--- a/android/src/main/java/com/gettipsi/stripe/Errors.java
+++ b/android/src/main/java/com/gettipsi/stripe/Errors.java
@@ -1,6 +1,6 @@
 package com.gettipsi.stripe;
 
-import android.support.annotation.NonNull;
+import androidx.annotation.NonNull;
 
 import com.facebook.react.bridge.ReadableMap;
 import com.gettipsi.stripe.util.ArgCheck;
diff --git a/android/src/main/java/com/gettipsi/stripe/GoogleApiPayFlowImpl.java b/android/src/main/java/com/gettipsi/stripe/GoogleApiPayFlowImpl.java
index 426f2484ebaf69654b74ae940f6f16cabbc5b018..55b6d7096f2c2991f2d85038b066fd5a0b3450c6 100644
--- a/android/src/main/java/com/gettipsi/stripe/GoogleApiPayFlowImpl.java
+++ b/android/src/main/java/com/gettipsi/stripe/GoogleApiPayFlowImpl.java
@@ -2,9 +2,11 @@ package com.gettipsi.stripe;
 
 import android.app.Activity;
 import android.content.Intent;
-import android.support.annotation.NonNull;
+import android.util.Log;
+import androidx.annotation.NonNull;
 
 import com.facebook.react.bridge.Promise;
+import com.facebook.react.bridge.ReadableArray;
 import com.facebook.react.bridge.ReadableMap;
 import com.gettipsi.stripe.util.ArgCheck;
 import com.gettipsi.stripe.util.Converters;
@@ -14,28 +16,33 @@ import com.google.android.gms.common.api.Status;
 import com.google.android.gms.tasks.OnCompleteListener;
 import com.google.android.gms.tasks.Task;
 import com.google.android.gms.wallet.AutoResolveHelper;
-import com.google.android.gms.wallet.CardRequirements;
 import com.google.android.gms.wallet.IsReadyToPayRequest;
 import com.google.android.gms.wallet.PaymentData;
 import com.google.android.gms.wallet.PaymentDataRequest;
 import com.google.android.gms.wallet.PaymentMethodTokenizationParameters;
 import com.google.android.gms.wallet.PaymentsClient;
-import com.google.android.gms.wallet.ShippingAddressRequirements;
-import com.google.android.gms.wallet.TransactionInfo;
 import com.google.android.gms.wallet.Wallet;
 import com.google.android.gms.wallet.WalletConstants;
-import com.stripe.android.BuildConfig;
-import com.stripe.android.model.Token;
+import com.stripe.android.ApiResultCallback;
+import com.stripe.android.GooglePayConfig;
+import com.stripe.android.PaymentConfiguration;
+import com.stripe.android.Stripe;
+import com.stripe.android.model.PaymentMethod;
+import com.stripe.android.model.PaymentMethodCreateParams;
+
+import org.json.JSONArray;
+import org.json.JSONException;
+import org.json.JSONObject;
 
-import java.util.Arrays;
 import java.util.Collection;
 
 import static com.gettipsi.stripe.Errors.toErrorCode;
-import static com.gettipsi.stripe.util.Converters.convertTokenToWritableMap;
+import static com.gettipsi.stripe.util.Converters.convertPaymentMethodToWritableMap;
 import static com.gettipsi.stripe.util.Converters.getAllowedShippingCountryCodes;
-import static com.gettipsi.stripe.util.Converters.getBillingAddress;
-import static com.gettipsi.stripe.util.Converters.putExtraToTokenMap;
 import static com.gettipsi.stripe.util.PayParams.CURRENCY_CODE;
+import static com.gettipsi.stripe.util.PayParams.COUNTRY_CODE;
+import static com.gettipsi.stripe.util.PayParams.ALLOWED_AUTH_METHODS;
+import static com.gettipsi.stripe.util.PayParams.ALLOWED_CARD_NETWORKS;
 import static com.gettipsi.stripe.util.PayParams.BILLING_ADDRESS_REQUIRED;
 import static com.gettipsi.stripe.util.PayParams.SHIPPING_ADDRESS_REQUIRED;
 import static com.gettipsi.stripe.util.PayParams.PHONE_NUMBER_REQUIRED;
@@ -52,6 +59,7 @@ public final class GoogleApiPayFlowImpl extends PayFlow {
   private static final int LOAD_PAYMENT_DATA_REQUEST_CODE = 65534;
 
   private PaymentsClient mPaymentsClient;
+  private Stripe mStripe;
   private Promise payPromise;
 
   public GoogleApiPayFlowImpl(@NonNull Fun0<Activity> activityProvider) {
@@ -94,13 +102,15 @@ public final class GoogleApiPayFlowImpl extends PayFlow {
       .setPaymentMethodTokenizationType(WalletConstants.PAYMENT_METHOD_TOKENIZATION_TYPE_PAYMENT_GATEWAY)
       .addParameter("gateway", "stripe")
       .addParameter("stripe:publishableKey", getPublishableKey())
-      .addParameter("stripe:version", BuildConfig.VERSION_NAME)
+      .addParameter("stripe:version", "5.0.1")
       .build();
   }
 
   private PaymentDataRequest createPaymentDataRequest(ReadableMap payParams) {
     final String estimatedTotalPrice = payParams.getString(TOTAL_PRICE);
     final String currencyCode = payParams.getString(CURRENCY_CODE);
+    final ReadableArray allowedAuthMethods = payParams.getArray(ALLOWED_AUTH_METHODS);
+    final ReadableArray allowedCardNetworks = payParams.getArray(ALLOWED_CARD_NETWORKS);
     final boolean billingAddressRequired = Converters.getValue(payParams, BILLING_ADDRESS_REQUIRED, false);
     final boolean shippingAddressRequired = Converters.getValue(payParams, SHIPPING_ADDRESS_REQUIRED, false);
     final boolean phoneNumberRequired = Converters.getValue(payParams, PHONE_NUMBER_REQUIRED, false);
@@ -110,6 +120,8 @@ public final class GoogleApiPayFlowImpl extends PayFlow {
     return createPaymentDataRequest(
       estimatedTotalPrice,
       currencyCode,
+      allowedAuthMethods,
+      allowedCardNetworks,
       billingAddressRequired,
       shippingAddressRequired,
       phoneNumberRequired,
@@ -120,6 +132,8 @@ public final class GoogleApiPayFlowImpl extends PayFlow {
 
   private PaymentDataRequest createPaymentDataRequest(@NonNull final String totalPrice,
                                                       @NonNull final String currencyCode,
+                                                      @NonNull final ReadableArray allowedAuthMethods,
+                                                      @NonNull final ReadableArray allowedCardNetworks,
                                                       final boolean billingAddressRequired,
                                                       final boolean shippingAddressRequired,
                                                       final boolean phoneNumberRequired,
@@ -130,40 +144,39 @@ public final class GoogleApiPayFlowImpl extends PayFlow {
     ArgCheck.isDouble(totalPrice);
     ArgCheck.notEmptyString(currencyCode);
 
-    PaymentDataRequest.Builder builder = PaymentDataRequest.newBuilder();
-    builder.setTransactionInfo(
-      TransactionInfo.newBuilder()
-        .setTotalPriceStatus(WalletConstants.TOTAL_PRICE_STATUS_ESTIMATED)
-        .setTotalPrice(totalPrice)
-        .setCurrencyCode(currencyCode)
-        .build());
-
-    builder
-      .setCardRequirements(
-        CardRequirements.newBuilder()
-          .addAllowedCardNetworks(
-            Arrays.asList(
-              WalletConstants.CARD_NETWORK_AMEX,
-              WalletConstants.CARD_NETWORK_DISCOVER,
-              WalletConstants.CARD_NETWORK_VISA,
-              WalletConstants.CARD_NETWORK_MASTERCARD))
-          .setBillingAddressRequired(billingAddressRequired)
-          .build())
-      .addAllowedPaymentMethod(WalletConstants.PAYMENT_METHOD_CARD)
-      .addAllowedPaymentMethod(WalletConstants.PAYMENT_METHOD_TOKENIZED_CARD)
-      .setEmailRequired(emailRequired)
-      .setShippingAddressRequired(shippingAddressRequired)
-      .setPhoneNumberRequired(phoneNumberRequired);
-
-    if (countryCodes.size() > 0) {
-      builder.setShippingAddressRequirements(
-        ShippingAddressRequirements.newBuilder()
-          .addAllowedCountryCodes(countryCodes)
-          .build());
+    Activity activity = activityProvider.call();
+
+    try {
+      PaymentConfiguration.init(activity, getPublishableKey(), getStripeAccount());
+
+      final JSONObject tokenizationSpec =
+              new GooglePayConfig(activity).getTokenizationSpecification();
+
+      final JSONObject cardPaymentMethod = new JSONObject()
+              .put("type", "CARD")
+              .put("parameters", new JSONObject()
+                      .put("allowedAuthMethods", getAllowedAuthMethods(allowedAuthMethods))
+                      .put("allowedCardNetworks", getAllowedCardNetworks(allowedCardNetworks))
+              )
+              .put("tokenizationSpecification", tokenizationSpec);
+
+      // create PaymentDataRequest
+      final JSONObject paymentDataRequest = new JSONObject()
+              .put("apiVersion", 2)
+              .put("apiVersionMinor", 0)
+              .put("allowedPaymentMethods",  new JSONArray().put(cardPaymentMethod))
+              .put("transactionInfo", new JSONObject()
+                      .put("totalPrice", totalPrice)
+                      .put("totalPriceStatus", "FINAL")
+                      .put("currencyCode", currencyCode)
+              );
+
+      return PaymentDataRequest.fromJson(paymentDataRequest.toString());
+    } catch (JSONException e) {
+      e.printStackTrace();
     }
 
-    builder.setPaymentMethodTokenizationParameters(createPaymentMethodTokenizationParameters());
-    return builder.build();
+    return null;
   }
 
   private void startPaymentRequest(@NonNull Activity activity, @NonNull PaymentDataRequest request) {
@@ -179,7 +192,8 @@ public final class GoogleApiPayFlowImpl extends PayFlow {
   }
 
   @Override
-  public void paymentRequestWithAndroidPay(@NonNull ReadableMap payParams, @NonNull Promise promise) {
+  public void paymentRequestWithAndroidPay(final ReadableArray items, @NonNull ReadableMap payParams, Stripe stripe, @NonNull Promise promise) {
+    ArgCheck.nonNull(items);
     ArgCheck.nonNull(payParams);
     ArgCheck.nonNull(promise);
 
@@ -192,6 +206,7 @@ public final class GoogleApiPayFlowImpl extends PayFlow {
       return;
     }
 
+    this.mStripe = stripe;
     this.payPromise = promise;
     startPaymentRequest(activity, createPaymentDataRequest(payParams));
   }
@@ -229,25 +244,38 @@ public final class GoogleApiPayFlowImpl extends PayFlow {
           case Activity.RESULT_OK:
             PaymentData paymentData = PaymentData.getFromIntent(data);
             ArgCheck.nonNull(paymentData);
-            String tokenJson = paymentData.getPaymentMethodToken().getToken();
-            Token token = Token.fromString(tokenJson);
-            if (token == null) {
-              payPromise.reject(
-                getErrorCode("parseResponse"),
-                getErrorDescription("parseResponse")
+            
+            try {
+              final PaymentMethodCreateParams paymentMethodCreateParams =
+                      PaymentMethodCreateParams.createFromGooglePay(
+                              new JSONObject(paymentData.toJson()));
+
+              mStripe.createPaymentMethod(
+                      paymentMethodCreateParams,
+                      new ApiResultCallback<PaymentMethod>() {
+                        @Override
+                        public void onSuccess(@NonNull PaymentMethod result) {
+                          payPromise.resolve(convertPaymentMethodToWritableMap(result));
+                        }
+
+                        @Override
+                        public void onError(@NonNull Exception e) {
+                          Log.e("tipsi", "onError " + e.getMessage());
+                          payPromise.reject(
+                                  getErrorCode("cancelled"),
+                                  getErrorDescription("cancelled")
+                          );
+                        }
+                      }
               );
-            } else {
-              payPromise.resolve(putExtraToTokenMap(
-                convertTokenToWritableMap(token),
-                getBillingAddress(paymentData),
-                paymentData.getShippingAddress(),
-                paymentData.getEmail()));
+            } catch (JSONException e) {
+              e.printStackTrace();
             }
             break;
           case Activity.RESULT_CANCELED:
             payPromise.reject(
-              getErrorCode("purchaseCancelled"),
-              getErrorDescription("purchaseCancelled")
+              getErrorCode("cancelled"),
+              getErrorDescription("cancelled")
             );
             break;
           case AutoResolveHelper.RESULT_ERROR:
@@ -263,6 +291,7 @@ public final class GoogleApiPayFlowImpl extends PayFlow {
 
           default:
             // Do nothing.
+            payPromise = null;
         }
         payPromise = null;
         return true;
@@ -271,4 +300,56 @@ public final class GoogleApiPayFlowImpl extends PayFlow {
     return false;
   }
 
+  private static JSONArray getAllowedAuthMethods(ReadableArray allowedAuthMethods) {
+    JSONArray res = new JSONArray();
+
+    int size = allowedAuthMethods.size();
+    for (int i = 0; i < size; ++i) {
+      String allowedAuthMethod = allowedAuthMethods.getString(i);
+      switch (allowedAuthMethod) {
+        case "panOnly":
+          res.put("PAN_ONLY");
+          break;
+        case "cryptogram3ds":
+          res.put("CRYPTOGRAM_3DS");
+          break;
+        default:
+      }
+    }
+
+    return res;
+  }
+
+  private static JSONArray getAllowedCardNetworks(ReadableArray allowedCardNetworks) {
+    JSONArray res = new JSONArray();
+
+    int size = allowedCardNetworks.size();
+    for (int i = 0; i < size; ++i) {
+      String allowedCardNetwork = allowedCardNetworks.getString(i);
+      switch (allowedCardNetwork) {
+        case "visa":
+          res.put("VISA");
+          break;
+        case "mastercard":
+          res.put("MASTERCARD");
+          break;
+        case "amex":
+          res.put("AMEX");
+          break;
+        case "discover":
+          res.put("DISCOVER");
+          break;
+        case "interac":
+          res.put("INTERAC");
+          break;
+        case "jcb":
+          res.put("JCB");
+          break;
+        default:
+          res.put(allowedCardNetwork.toUpperCase());
+      }
+    }
+
+    return res;
+  }
 }
diff --git a/android/src/main/java/com/gettipsi/stripe/OpenBrowserActivity.java b/android/src/main/java/com/gettipsi/stripe/OpenBrowserActivity.java
index 1a20549ac2c9201a353a279fcbc94ce839858044..5ae1051c3ec1f11c0de348c3324d0d260f7fbcb0 100644
--- a/android/src/main/java/com/gettipsi/stripe/OpenBrowserActivity.java
+++ b/android/src/main/java/com/gettipsi/stripe/OpenBrowserActivity.java
@@ -4,7 +4,7 @@ import android.app.Activity;
 import android.content.Intent;
 import android.net.Uri;
 import android.os.Bundle;
-import android.support.annotation.Nullable;
+import androidx.annotation.Nullable;
 
 /**
  * Created by remer on 16/11/17.
diff --git a/android/src/main/java/com/gettipsi/stripe/PayFlow.java b/android/src/main/java/com/gettipsi/stripe/PayFlow.java
index 3a92dda462a3f776d532fdfec193168404fdefb1..596a6acf81254359fe380acdf5b2e4ae33bdbac0 100644
--- a/android/src/main/java/com/gettipsi/stripe/PayFlow.java
+++ b/android/src/main/java/com/gettipsi/stripe/PayFlow.java
@@ -2,21 +2,23 @@ package com.gettipsi.stripe;
 
 import android.app.Activity;
 import android.content.Intent;
-import android.support.annotation.NonNull;
-import android.util.Log;
+import androidx.annotation.NonNull;
 
 import com.facebook.react.bridge.Promise;
+import com.facebook.react.bridge.ReadableArray;
 import com.facebook.react.bridge.ReadableMap;
 import com.gettipsi.stripe.util.ArgCheck;
 import com.gettipsi.stripe.util.Fun0;
 import com.google.android.gms.common.ConnectionResult;
 import com.google.android.gms.common.GoogleApiAvailability;
 import com.google.android.gms.wallet.WalletConstants;
+import com.stripe.android.Stripe;
 
 public abstract class PayFlow {
 
   protected final @NonNull Fun0<Activity> activityProvider;
   private String publishableKey; // invalid value by default
+  private String stripeAccount;
   private int environment; // invalid value by default
   private ReadableMap errorCodes; // invalid value by default, set in runtime
 
@@ -47,7 +49,6 @@ public abstract class PayFlow {
 
   public void setEnvironment(int environment) {
     ArgCheck.isTrue(isValidEnvironment(environment));
-    ArgCheck.isTrue(!isEnvironmentChangeAttempt(this.environment, environment));
 
     this.environment = environment;
   }
@@ -60,6 +61,14 @@ public abstract class PayFlow {
     this.publishableKey = ArgCheck.notEmptyString(publishableKey);
   }
 
+  protected String getStripeAccount() {
+    return ArgCheck.notEmptyString(stripeAccount);
+  }
+
+  public void setStripeAccount(@NonNull String stripeAccount) {
+    this.stripeAccount = ArgCheck.notEmptyString(stripeAccount);
+  }
+
   public void setErrorCodes(ReadableMap errorCodes) {
     if (this.errorCodes == null) {
       this.errorCodes = errorCodes;
@@ -78,7 +87,7 @@ public abstract class PayFlow {
     return Errors.getDescription(getErrorCodes(), key);
   }
 
-  abstract void paymentRequestWithAndroidPay(final ReadableMap payParams, final Promise promise);
+  abstract void paymentRequestWithAndroidPay(final ReadableArray items, final ReadableMap payParams, Stripe mStripe, final Promise promise);
 
   abstract void deviceSupportsAndroidPay(boolean isExistingPaymentMethodRequired, final Promise promise);
 
@@ -92,10 +101,4 @@ public abstract class PayFlow {
 
     return result == ConnectionResult.SUCCESS;
   }
-
-  protected static void log(String TAG, String msg) {
-    if (BuildConfig.DEBUG) {
-      Log.d(TAG, msg);
-    }
-  }
 }
diff --git a/android/src/main/java/com/gettipsi/stripe/RedirectUriReceiver.java b/android/src/main/java/com/gettipsi/stripe/RedirectUriReceiver.java
index 5de3922c68ebb9c87faeddad57cdbbe172e5e6ee..37f589a92c2d4f79641d8e1484403fc7ded429db 100644
--- a/android/src/main/java/com/gettipsi/stripe/RedirectUriReceiver.java
+++ b/android/src/main/java/com/gettipsi/stripe/RedirectUriReceiver.java
@@ -2,7 +2,7 @@ package com.gettipsi.stripe;
 
 import android.app.Activity;
 import android.os.Bundle;
-import android.support.annotation.Nullable;
+import androidx.annotation.Nullable;
 import android.util.Log;
 
 /**
diff --git a/android/src/main/java/com/gettipsi/stripe/StripeModule.java b/android/src/main/java/com/gettipsi/stripe/StripeModule.java
index 26f3b215272f96091c3ce0f06bedd58010bcb013..7f665c1052710a80ea2875430f0292bc26881757 100644
--- a/android/src/main/java/com/gettipsi/stripe/StripeModule.java
+++ b/android/src/main/java/com/gettipsi/stripe/StripeModule.java
@@ -4,8 +4,9 @@ import android.app.Activity;
 import android.content.Intent;
 import android.net.Uri;
 import android.os.AsyncTask;
-import android.support.annotation.NonNull;
-import android.support.annotation.Nullable;
+import androidx.activity.ComponentActivity;
+import androidx.annotation.NonNull;
+import androidx.annotation.Nullable;
 import android.text.TextUtils;
 
 import com.facebook.react.bridge.ActivityEventListener;
@@ -15,6 +16,7 @@ import com.facebook.react.bridge.Promise;
 import com.facebook.react.bridge.ReactApplicationContext;
 import com.facebook.react.bridge.ReactContextBaseJavaModule;
 import com.facebook.react.bridge.ReactMethod;
+import com.facebook.react.bridge.ReadableArray;
 import com.facebook.react.bridge.ReadableMap;
 import com.facebook.react.bridge.ReadableMapKeySetIterator;
 import com.gettipsi.stripe.dialog.AddCardDialogFragment;
@@ -22,20 +24,21 @@ import com.gettipsi.stripe.util.ArgCheck;
 import com.gettipsi.stripe.util.Converters;
 import com.gettipsi.stripe.util.Fun0;
 import com.google.android.gms.wallet.WalletConstants;
+import com.stripe.android.AlipayAuthenticator;
 import com.stripe.android.ApiResultCallback;
 import com.stripe.android.AppInfo;
+import com.stripe.android.PaymentConfiguration;
 import com.stripe.android.PaymentIntentResult;
 import com.stripe.android.SetupIntentResult;
-import com.stripe.android.SourceCallback;
 import com.stripe.android.Stripe;
-import com.stripe.android.TokenCallback;
 import com.stripe.android.model.Address;
+import com.stripe.android.model.BankAccountTokenParams;
 import com.stripe.android.model.ConfirmPaymentIntentParams;
 import com.stripe.android.model.ConfirmSetupIntentParams;
+import com.stripe.android.model.PaymentIntent;
 import com.stripe.android.model.PaymentMethod;
 import com.stripe.android.model.PaymentMethodCreateParams;
 import com.stripe.android.model.Source;
-import com.stripe.android.model.Source.SourceStatus;
 import com.stripe.android.model.SourceParams;
 import com.stripe.android.model.StripeIntent;
 import com.stripe.android.model.Token;
@@ -56,7 +59,6 @@ import static com.gettipsi.stripe.util.Converters.convertSetupIntentResultToWrit
 import static com.gettipsi.stripe.util.Converters.convertSourceToWritableMap;
 import static com.gettipsi.stripe.util.Converters.convertTokenToWritableMap;
 import static com.gettipsi.stripe.util.Converters.createBankAccount;
-import static com.gettipsi.stripe.util.Converters.createCard;
 import static com.gettipsi.stripe.util.Converters.getBooleanOrNull;
 import static com.gettipsi.stripe.util.Converters.getMapOrNull;
 import static com.gettipsi.stripe.util.Converters.getStringOrNull;
@@ -101,6 +103,7 @@ public class StripeModule extends ReactContextBaseJavaModule {
   private Source mCreatedSource;
 
   private String mPublicKey;
+  private String mStripeAccount;
   private Stripe mStripe;
   private PayFlow mPayFlow;
   private ReadableMap mErrorCodes;
@@ -138,13 +141,15 @@ public class StripeModule extends ReactContextBaseJavaModule {
     String newPubKey = Converters.getStringOrNull(options, PUBLISHABLE_KEY);
     String newAndroidPayMode = Converters.getStringOrNull(options, ANDROID_PAY_MODE_KEY);
 
-    if (newPubKey != null && !TextUtils.equals(newPubKey, mPublicKey)) {
+    if (newPubKey != null) {
       ArgCheck.notEmptyString(newPubKey);
 
       mPublicKey = newPubKey;
       Stripe.setAppInfo(AppInfo.create(APP_INFO_NAME, APP_INFO_VERSION, APP_INFO_URL));
       mStripe = new Stripe(getReactApplicationContext(), mPublicKey);
       getPayFlow().setPublishableKey(mPublicKey);
+
+      PaymentConfiguration.init(getReactApplicationContext(), mPublicKey);
     }
 
     if (newAndroidPayMode != null) {
@@ -189,6 +194,12 @@ public class StripeModule extends ReactContextBaseJavaModule {
   @ReactMethod
   public void setStripeAccount(final String stripeAccount) {
     ArgCheck.notEmptyString(mPublicKey);
+
+    mStripeAccount = stripeAccount;
+    getPayFlow().setStripeAccount(mStripeAccount);
+
+    PaymentConfiguration.init(getReactApplicationContext(), mPublicKey);
+
     if (stripeAccount == null) {
       mStripe = new Stripe(getReactApplicationContext(), mPublicKey);
     } else {
@@ -196,29 +207,6 @@ public class StripeModule extends ReactContextBaseJavaModule {
     }
   }
 
-  @ReactMethod
-  public void createTokenWithCard(final ReadableMap cardData, final Promise promise) {
-    try {
-      ArgCheck.nonNull(mStripe);
-      ArgCheck.notEmptyString(mPublicKey);
-
-      mStripe.createToken(
-        createCard(cardData),
-        mPublicKey,
-        new TokenCallback() {
-          public void onSuccess(Token token) {
-            promise.resolve(convertTokenToWritableMap(token));
-          }
-          public void onError(Exception error) {
-            error.printStackTrace();
-            promise.reject(toErrorCode(error), error.getMessage());
-          }
-        });
-    } catch (Exception e) {
-      promise.reject(toErrorCode(e), e.getMessage());
-    }
-  }
-
   @ReactMethod
   public void createTokenWithBankAccount(final ReadableMap accountData, final Promise promise) {
     try {
@@ -226,13 +214,14 @@ public class StripeModule extends ReactContextBaseJavaModule {
       ArgCheck.notEmptyString(mPublicKey);
 
       mStripe.createBankAccountToken(
-        createBankAccount(accountData),
-        mPublicKey,
         null,
-        new TokenCallback() {
+        new ApiResultCallback<Token>() {
+          @Override
           public void onSuccess(Token token) {
             promise.resolve(convertTokenToWritableMap(token));
           }
+
+          @Override
           public void onError(Exception error) {
             error.printStackTrace();
             promise.reject(toErrorCode(error), error.getMessage());
@@ -262,8 +251,8 @@ public class StripeModule extends ReactContextBaseJavaModule {
   }
 
   @ReactMethod
-  public void paymentRequestWithAndroidPay(final ReadableMap payParams, final Promise promise) {
-    getPayFlow().paymentRequestWithAndroidPay(payParams, promise);
+  public void paymentRequestWithAndroidPay(final ReadableArray items, final ReadableMap payParams, final Promise promise) {
+    getPayFlow().paymentRequestWithAndroidPay(items, payParams, mStripe, promise);
   }
 
   private void attachPaymentResultActivityListener(final Promise promise) {
@@ -369,18 +358,7 @@ public class StripeModule extends ReactContextBaseJavaModule {
 
     Activity activity = getCurrentActivity();
     if (activity != null) {
-      mStripe.confirmPayment(activity, extractConfirmPaymentIntentParams(options));
-    }
-  }
-
-  @ReactMethod
-  public void authenticatePaymentIntent(final ReadableMap options, final Promise promise) {
-    attachPaymentResultActivityListener(promise);
-
-    String clientSecret = options.getString(CLIENT_SECRET);
-    Activity activity = getCurrentActivity();
-    if (activity != null) {
-      mStripe.authenticatePayment(activity, clientSecret);
+      mStripe.confirmPayment((ComponentActivity) activity, extractConfirmPaymentIntentParams(options));
     }
   }
 
@@ -390,22 +368,10 @@ public class StripeModule extends ReactContextBaseJavaModule {
 
     Activity activity = getCurrentActivity();
     if (activity != null) {
-      mStripe.confirmSetupIntent(activity, extractConfirmSetupIntentParams(options));
-    }
-  }
-
-  @ReactMethod
-  public void authenticateSetupIntent(final ReadableMap options, final Promise promise) {
-    attachSetupResultActivityListener(promise);
-
-    String clientSecret = options.getString(CLIENT_SECRET);
-    Activity activity = getCurrentActivity();
-    if (activity != null) {
-      mStripe.authenticateSetup(activity, clientSecret);
+      mStripe.confirmSetupIntent((ComponentActivity) activity, extractConfirmSetupIntentParams(options));
     }
   }
 
-
   @ReactMethod
   public void createPaymentMethod(final ReadableMap options, final Promise promise) {
 
@@ -433,7 +399,7 @@ public class StripeModule extends ReactContextBaseJavaModule {
 
     ArgCheck.nonNull(sourceParams);
 
-    mStripe.createSource(sourceParams, new SourceCallback() {
+    mStripe.createSource(sourceParams, new ApiResultCallback<Source>() {
       @Override
       public void onError(Exception error) {
         promise.reject(toErrorCode(error));
@@ -441,7 +407,7 @@ public class StripeModule extends ReactContextBaseJavaModule {
 
       @Override
       public void onSuccess(Source source) {
-        if (Source.SourceFlow.REDIRECT.equals(source.getFlow())) {
+        if (Source.Flow.Redirect.equals(source.getFlow())) {
           Activity currentActivity = getCurrentActivity();
           if (currentActivity == null) {
             promise.reject(
@@ -476,9 +442,9 @@ public class StripeModule extends ReactContextBaseJavaModule {
 
     if (paymentMethod != null) {
       csip = ConfirmSetupIntentParams.create(extractPaymentMethodCreateParams(paymentMethod),
-        clientSecret, returnURL);
+        clientSecret);
     } else if (paymentMethodId != null) {
-      csip = ConfirmSetupIntentParams.create(paymentMethodId, clientSecret, returnURL);
+      csip = ConfirmSetupIntentParams.create(paymentMethodId, clientSecret);
     }
 
     ArgCheck.nonNull(csip);
@@ -489,6 +455,21 @@ public class StripeModule extends ReactContextBaseJavaModule {
   private ConfirmPaymentIntentParams extractConfirmPaymentIntentParams(final ReadableMap options) {
     ConfirmPaymentIntentParams cpip = null;
     String clientSecret = options.getString("clientSecret");
+    String methodType = options.getString("type");
+
+    if (methodType != null) {
+      if (methodType.equals("alipay")) return ConfirmPaymentIntentParams.createAlipay(clientSecret);
+
+      if (methodType.equals("grabpay")) {
+        PaymentMethod.BillingDetails billingDetails =
+                new PaymentMethod.BillingDetails.Builder().build();
+
+        PaymentMethodCreateParams paymentMethodCreateParams =
+                PaymentMethodCreateParams.createGrabPay(billingDetails);
+
+        return ConfirmPaymentIntentParams.createWithPaymentMethodCreateParams(paymentMethodCreateParams, clientSecret);
+      }
+    }
 
     ReadableMap paymentMethod = getMapOrNull(options, "paymentMethod");
     String paymentMethodId = getStringOrNull(options,"paymentMethodId");
@@ -509,12 +490,12 @@ public class StripeModule extends ReactContextBaseJavaModule {
     if (paymentMethod != null) {
 
       PaymentMethodCreateParams pmcp = extractPaymentMethodCreateParams(paymentMethod);
-      cpip = ConfirmPaymentIntentParams.createWithPaymentMethodCreateParams(pmcp, clientSecret, returnURL, savePaymentMethod, extraParams);
+      cpip = ConfirmPaymentIntentParams.createWithPaymentMethodCreateParams(pmcp, clientSecret);
 
     // Create with Payment Method ID
     } else if (paymentMethodId != null) {
 
-      cpip = ConfirmPaymentIntentParams.createWithPaymentMethodId(paymentMethodId, clientSecret, returnURL, savePaymentMethod, extraParams);
+      cpip = ConfirmPaymentIntentParams.createWithPaymentMethodId(paymentMethodId, clientSecret);
 
     // Create with Source
     /**
@@ -545,7 +526,7 @@ public class StripeModule extends ReactContextBaseJavaModule {
     has payment method information and just needs to be confirmed.
     */
     } else {
-      cpip = ConfirmPaymentIntentParams.create(clientSecret, returnURL);
+      cpip = ConfirmPaymentIntentParams.create(clientSecret);
     }
 
     cpip.withShouldUseStripeSdk(true);
@@ -675,7 +656,6 @@ public class StripeModule extends ReactContextBaseJavaModule {
           options.getString("card"));
         break;
       case "card":
-        sourceParams = SourceParams.createCardParams(Converters.createCard(options));
         break;
     }
     return sourceParams;
@@ -739,18 +719,18 @@ public class StripeModule extends ReactContextBaseJavaModule {
         }
 
         switch (source.getStatus()) {
-          case SourceStatus.CHARGEABLE:
-          case SourceStatus.CONSUMED:
+          case Chargeable:
+          case Consumed:
             promise.resolve(convertSourceToWritableMap(source));
             break;
-          case SourceStatus.CANCELED:
+          case Canceled:
             promise.reject(
               getErrorCode(mErrorCodes, "redirectCancelled"),
               getDescription(mErrorCodes, "redirectCancelled")
             );
             break;
-          case SourceStatus.PENDING:
-          case SourceStatus.FAILED:
+          case Pending:
+          case Failed:
           default:
             promise.reject(
               getErrorCode(mErrorCodes, "redirectFailed"),
diff --git a/android/src/main/java/com/gettipsi/stripe/dialog/AddCardDialogFragment.java b/android/src/main/java/com/gettipsi/stripe/dialog/AddCardDialogFragment.java
index d1a2826e2a33c35b53b3361a790bd187527bb530..6f58ecf2865e8e3d1d9f5279e73e51009119dabc 100644
--- a/android/src/main/java/com/gettipsi/stripe/dialog/AddCardDialogFragment.java
+++ b/android/src/main/java/com/gettipsi/stripe/dialog/AddCardDialogFragment.java
@@ -6,7 +6,7 @@ import android.app.DialogFragment;
 import android.content.Context;
 import android.content.DialogInterface;
 import android.os.Bundle;
-import android.support.v4.content.ContextCompat;
+import androidx.core.content.ContextCompat;
 import android.text.Editable;
 import android.text.TextUtils;
 import android.text.TextWatcher;
@@ -172,51 +172,37 @@ public class AddCardDialogFragment extends DialogFragment {
     doneButton.setEnabled(false);
     progressBar.setVisibility(View.VISIBLE);
     final CreditCard fromCard = from.getCreditCard();
-    final Card card = new Card.Builder(
-      fromCard.getCardNumber(),
-      fromCard.getExpMonth(),
-      fromCard.getExpYear(),
-      fromCard.getSecurityCode()).build();
-
-    String errorMessage = Utils.validateCard(card);
-    if (errorMessage == null) {
-
-        PaymentMethodCreateParams pmcp = PaymentMethodCreateParams.create(
-          new PaymentMethodCreateParams.Card.Builder().
-            setCvc(fromCard.getSecurityCode()).
-            setExpiryMonth(fromCard.getExpMonth()).
-            setExpiryYear(fromCard.getExpYear()).
-            setNumber(fromCard.getCardNumber()).
-            build(),
-          null);
-
-        StripeModule.getInstance().getStripe().createPaymentMethod(
-          pmcp,
-          new ApiResultCallback<PaymentMethod>() {
-
-            @Override
-            public void onError(Exception error) {
-              doneButton.setEnabled(true);
-              progressBar.setVisibility(View.GONE);
-              showToast(error.getLocalizedMessage());
-            }
 
-            @Override
-            public void onSuccess(PaymentMethod paymentMethod) {
-              if (promise != null) {
-                promise.resolve(Converters.convertPaymentMethodToWritableMap(paymentMethod));
-                promise = null;
-                successful = true;
-                dismiss();
-              }
-            }
-        });
+    PaymentMethodCreateParams pmcp = PaymentMethodCreateParams.create(
+      new PaymentMethodCreateParams.Card.Builder().
+        setCvc(fromCard.getSecurityCode()).
+        setExpiryMonth(fromCard.getExpMonth()).
+        setExpiryYear(fromCard.getExpYear()).
+        setNumber(fromCard.getCardNumber()).
+        build(),
+      null);
 
-    } else {
-      doneButton.setEnabled(true);
-      progressBar.setVisibility(View.GONE);
-      showToast(errorMessage);
-    }
+    StripeModule.getInstance().getStripe().createPaymentMethod(
+      pmcp,
+      new ApiResultCallback<PaymentMethod>() {
+
+        @Override
+        public void onError(Exception error) {
+          doneButton.setEnabled(true);
+          progressBar.setVisibility(View.GONE);
+          showToast(error.getLocalizedMessage());
+        }
+
+        @Override
+        public void onSuccess(PaymentMethod paymentMethod) {
+          if (promise != null) {
+            promise.resolve(Converters.convertPaymentMethodToWritableMap(paymentMethod));
+            promise = null;
+            successful = true;
+            dismiss();
+          }
+        }
+    });
   }
 
   public void showToast(String message) {
diff --git a/android/src/main/java/com/gettipsi/stripe/util/Converters.java b/android/src/main/java/com/gettipsi/stripe/util/Converters.java
index cb303bef0e351068e1e42a3d2a59ea97bd8cdf4a..c72c8346a1bd81db31e50f091b07534fdca9e265 100644
--- a/android/src/main/java/com/gettipsi/stripe/util/Converters.java
+++ b/android/src/main/java/com/gettipsi/stripe/util/Converters.java
@@ -1,7 +1,7 @@
 package com.gettipsi.stripe.util;
 
-import android.support.annotation.NonNull;
-import android.support.annotation.Nullable;
+import androidx.annotation.NonNull;
+import androidx.annotation.Nullable;
 import android.text.TextUtils;
 
 import com.facebook.react.bridge.Arguments;
@@ -18,14 +18,13 @@ import com.stripe.android.SetupIntentResult;
 import com.stripe.android.model.Address;
 import com.stripe.android.model.BankAccount;
 import com.stripe.android.model.Card;
+import com.stripe.android.model.CardBrand;
+import com.stripe.android.model.CardFunding;
+import com.stripe.android.model.CardParams;
 import com.stripe.android.model.PaymentIntent;
 import com.stripe.android.model.PaymentMethod;
 import com.stripe.android.model.SetupIntent;
 import com.stripe.android.model.Source;
-import com.stripe.android.model.SourceCodeVerification;
-import com.stripe.android.model.SourceOwner;
-import com.stripe.android.model.SourceReceiver;
-import com.stripe.android.model.SourceRedirect;
 import com.stripe.android.model.Token;
 
 import java.util.ArrayList;
@@ -43,7 +42,7 @@ public class Converters {
 
     if (token == null) return newToken;
 
-    newToken.putString("tokenId", token.getId());
+    newToken.putString("id", token.getId());
     newToken.putBoolean("livemode", token.getLivemode());
     newToken.putBoolean("used", token.getUsed());
     newToken.putDouble("created", token.getCreated().getTime());
@@ -85,8 +84,6 @@ public class Converters {
     if (card == null) return result;
 
     result.putString("cardId", card.getId());
-    result.putString("number", card.getNumber());
-    result.putString("cvc", card.getCVC() );
     result.putInt("expMonth", card.getExpMonth() );
     result.putInt("expYear", card.getExpYear() );
     result.putString("name", card.getName() );
@@ -97,8 +94,8 @@ public class Converters {
     result.putString("addressZip", card.getAddressZip() );
     result.putString("addressCountry", card.getAddressCountry() );
     result.putString("last4", card.getLast4() );
-    result.putString("brand", card.getBrand() );
-    result.putString("funding", card.getFunding() );
+    result.putString("brand", card.getBrand().toString());
+    result.putString("funding", card.getFunding().toString());
     result.putString("fingerprint", card.getFingerprint() );
     result.putString("country", card.getCountry() );
     result.putString("currency", card.getCurrency() );
@@ -112,11 +109,10 @@ public class Converters {
     if (account == null) return result;
 
     result.putString("routingNumber", account.getRoutingNumber());
-    result.putString("accountNumber", account.getAccountNumber());
     result.putString("countryCode", account.getCountryCode());
     result.putString("currency", account.getCurrency());
     result.putString("accountHolderName", account.getAccountHolderName());
-    result.putString("accountHolderType", account.getAccountHolderType());
+    result.putString("accountHolderType", account.getAccountHolderType().toString());
     result.putString("fingerprint", account.getFingerprint());
     result.putString("bankName", account.getBankName());
     result.putString("last4", account.getLast4());
@@ -183,30 +179,6 @@ public class Converters {
     return allowedCountriesForShipping;
   }
 
-  public static Card createCard(final ReadableMap cardData) {
-    return new Card.Builder(
-        cardData.getString("number"),
-        cardData.getInt("expMonth"),
-        cardData.getInt("expYear"),
-        getValue(cardData, "cvc"))
-      .name(getValue(cardData, "name"))
-      .addressLine1(getValue(cardData, "addressLine1"))
-      .addressLine2(getValue(cardData, "addressLine2"))
-      .addressCity(getValue(cardData, "addressCity"))
-      .addressState(getValue(cardData, "addressState"))
-      .addressZip(getValue(cardData, "addressZip"))
-      .addressCountry(getValue(cardData, "addressCountry"))
-      .brand(getValue(cardData, "brand"))
-      .last4(getValue(cardData, "last4"))
-      .fingerprint(getValue(cardData, "fingerprint"))
-      .funding(getValue(cardData, "funding"))
-      .country(getValue(cardData, "country"))
-      .currency(getValue(cardData, "currency"))
-      .id(getValue(cardData, "id"))
-      .build();
-  }
-
-
 
   @NonNull
   public static WritableMap convertSourceToWritableMap(@Nullable Source source) {
@@ -221,17 +193,16 @@ public class Converters {
     newSource.putInt("created", source.getCreated().intValue());
     newSource.putMap("codeVerification", convertCodeVerificationToWritableMap(source.getCodeVerification()));
     newSource.putString("currency", source.getCurrency());
-    newSource.putString("flow", source.getFlow());
+    newSource.putString("flow", source.getFlow().toString());
     newSource.putBoolean("livemode", source.isLiveMode());
-    newSource.putMap("metadata", stringMapToWritableMap(source.getMetaData()));
     newSource.putMap("owner", convertOwnerToWritableMap(source.getOwner()));
     newSource.putMap("receiver", convertReceiverToWritableMap(source.getReceiver()));
     newSource.putMap("redirect", convertRedirectToWritableMap(source.getRedirect()));
     newSource.putMap("sourceTypeData", mapToWritableMap(source.getSourceTypeData()));
-    newSource.putString("status", source.getStatus());
+    newSource.putString("status", source.getStatus().toString());
     newSource.putString("type", source.getType());
     newSource.putString("typeRaw", source.getTypeRaw());
-    newSource.putString("usage", source.getUsage());
+    newSource.putString("usage", source.getUsage().toString());
 
     return newSource;
   }
@@ -288,7 +259,7 @@ public class Converters {
     wm.putString("id", paymentMethod.id);
     wm.putInt("created", paymentMethod.created.intValue());
     wm.putBoolean("livemode", paymentMethod.liveMode);
-    wm.putString("type", paymentMethod.type);
+    wm.putString("type", paymentMethod.type.toString());
     wm.putMap("billingDetails", convertBillingDetailsToWritableMap(paymentMethod.billingDetails));
     wm.putMap("card", convertPaymentMethodCardToWritableMap(paymentMethod.card));
     wm.putString("customerId", paymentMethod.customerId);
@@ -307,7 +278,7 @@ public class Converters {
 
     // Omitted (can be introduced later): card.checks, card.threeDSecureUsage, card.wallet
 
-    wm.putString("brand", card.brand);
+    wm.putString("brand", card.brand.toString().toLowerCase());
     wm.putString("country", card.country);
     wm.putInt("expMonth", card.expiryMonth);
     wm.putInt("expYear", card.expiryYear);
@@ -348,7 +319,7 @@ public class Converters {
   }
 
   @NonNull
-  public static WritableMap convertOwnerToWritableMap(@Nullable final SourceOwner owner) {
+  public static WritableMap convertOwnerToWritableMap(@Nullable final Source.Owner owner) {
     WritableMap map = Arguments.createMap();
 
     if (owner == null) {
@@ -386,7 +357,7 @@ public class Converters {
   }
 
   @NonNull
-  public static WritableMap convertReceiverToWritableMap(@Nullable final SourceReceiver receiver) {
+  public static WritableMap convertReceiverToWritableMap(@Nullable final Source.Receiver receiver) {
     WritableMap map = Arguments.createMap();
 
     if (receiver == null) {
@@ -402,7 +373,7 @@ public class Converters {
   }
 
   @NonNull
-  public static WritableMap convertRedirectToWritableMap(@Nullable SourceRedirect redirect) {
+  public static WritableMap convertRedirectToWritableMap(@Nullable Source.Redirect redirect) {
     WritableMap map = Arguments.createMap();
 
     if (redirect == null) {
@@ -410,14 +381,14 @@ public class Converters {
     }
 
     map.putString("returnUrl", redirect.getReturnUrl());
-    map.putString("status", redirect.getStatus());
+    map.putString("status", redirect.getStatus().toString());
     map.putString("url", redirect.getUrl());
 
     return map;
   }
 
   @NonNull
-  public static WritableMap convertCodeVerificationToWritableMap(@Nullable SourceCodeVerification codeVerification) {
+  public static WritableMap convertCodeVerificationToWritableMap(@Nullable Source.CodeVerification codeVerification) {
     WritableMap map = Arguments.createMap();
 
     if (codeVerification == null) {
@@ -425,7 +396,7 @@ public class Converters {
     }
 
     map.putInt("attemptsRemaining", codeVerification.getAttemptsRemaining());
-    map.putString("status", codeVerification.getStatus());
+    map.putString("status", codeVerification.getStatus().toString());
 
     return map;
   }
@@ -489,19 +460,7 @@ public class Converters {
   }
 
   public static BankAccount createBankAccount(ReadableMap accountData) {
-    BankAccount account = new BankAccount(
-      // required fields only
-      accountData.getString("accountNumber"),
-      getValue(accountData, "accountHolderName"),
-      getValue(accountData, "accountHolderType"),
-      null,
-      accountData.getString("countryCode"),
-      accountData.getString("currency"),
-      null,
-      null,
-      getValue(accountData, "routingNumber", "")
-    );
-
+    BankAccount account = new BankAccount();
     return account;
   }
 
diff --git a/android/src/main/java/com/gettipsi/stripe/util/PayParams.java b/android/src/main/java/com/gettipsi/stripe/util/PayParams.java
index fd3c6219cb35f3617b3390cadfe777f2c8610909..0bca29bfb8f787744a918276b9ca46bb4d75921e 100644
--- a/android/src/main/java/com/gettipsi/stripe/util/PayParams.java
+++ b/android/src/main/java/com/gettipsi/stripe/util/PayParams.java
@@ -6,14 +6,17 @@ package com.gettipsi.stripe.util;
 
 public abstract class PayParams {
 
-  public static final String CURRENCY_CODE = "currency_code";
-  public static final String BILLING_ADDRESS_REQUIRED = "billing_address_required";
-  public static final String SHIPPING_ADDRESS_REQUIRED = "shipping_address_required";
-  public static final String PHONE_NUMBER_REQUIRED = "phone_number_required";
-  public static final String EMAIL_REQUIRED = "email_required";
-  public static final String TOTAL_PRICE = "total_price";
-  public static final String UNIT_PRICE = "unit_price";
-  public static final String LINE_ITEMS = "line_items";
+  public static final String CURRENCY_CODE = "currencyCode";
+  public static final String COUNTRY_CODE = "countryCode";
+  public static final String ALLOWED_AUTH_METHODS = "allowedAuthMethods";
+  public static final String ALLOWED_CARD_NETWORKS = "supportedNetworks";
+  public static final String BILLING_ADDRESS_REQUIRED = "requiredBillingAddress";
+  public static final String SHIPPING_ADDRESS_REQUIRED = "requiredShippingAddress";
+  public static final String PHONE_NUMBER_REQUIRED = "requiredPhoneNumber";
+  public static final String EMAIL_REQUIRED = "requiredEmail";
+  public static final String TOTAL_PRICE = "totalPrice";
+  public static final String UNIT_PRICE = "unitPrice";
+  public static final String LINE_ITEMS = "lineItems";
   public static final String QUANTITY = "quantity";
   public static final String DESCRIPTION = "description";
 
diff --git a/android/src/main/java/com/gettipsi/stripe/util/Utils.java b/android/src/main/java/com/gettipsi/stripe/util/Utils.java
index a3793e878357ac118dafba8b0cd0daf69dd2067a..e024fce008b4cad4180033b572c539808ef4498d 100644
--- a/android/src/main/java/com/gettipsi/stripe/util/Utils.java
+++ b/android/src/main/java/com/gettipsi/stripe/util/Utils.java
@@ -9,13 +9,6 @@ import com.stripe.android.model.Card;
 public class Utils {
 
     public static String validateCard(final Card card) {
-        if (!card.validateNumber()) {
-            return "The card number that you entered is invalid";
-        } else if (!card.validateExpiryDate()) {
-            return "The expiration date that you entered is invalid";
-        } else if (!card.validateCVC()) {
-            return "The CVC code that you entered is invalid";
-        }
         return null;
     }
 
diff --git a/ios/TPSStripe/TPSStripeManager.m b/ios/TPSStripe/TPSStripeManager.m
index 4f5ea739817b89f42e38b2450abdcd98e9882a70..723a3d678ac96d50312538b7ce35b9079011373f 100644
--- a/ios/TPSStripe/TPSStripeManager.m
+++ b/ios/TPSStripe/TPSStripeManager.m
@@ -266,7 +266,7 @@ @interface StripeModule () <STPAuthenticationContext>
 
     BOOL requestIsCompleted;
 
-    void (^applePayCompletion)(PKPaymentAuthorizationStatus);
+    void (^applePayCompletion)(NSString * _Nullable, NSError * _Nullable);
     NSError *applePayStripeError;
 }
 @end
@@ -301,8 +301,10 @@ - (NSDictionary *)constantsToExport
 RCT_EXPORT_METHOD(init:(NSDictionary *)options errorCodes:(NSDictionary *)errors) {
     publishableKey = options[@"publishableKey"];
     merchantId = options[@"merchantId"];
+    stripeAccount = options[@"stripeAccount"];
     errorCodes = errors;
     [StripeAPI setDefaultPublishableKey:publishableKey];
+    [[STPAPIClient sharedClient] setStripeAccount:stripeAccount];
 }
 
 RCT_EXPORT_METHOD(setStripeAccount:(NSString *)_stripeAccount) {
@@ -382,10 +384,11 @@ - (NSDictionary *)constantsToExport
     STPAPIClient *api = self.newAPIClient;
     [api confirmPaymentIntentWithParams:parsed
                              completion:^(STPPaymentIntent * __nullable intent, NSError * __nullable error){
+                                 NSString * code = error.userInfo[STPError.stripeDeclineCodeKey] ? error.userInfo[STPError.stripeDeclineCodeKey] : error.userInfo[STPError.stripeErrorCodeKey];
+                                 
                                  if (!intent && error) {
                                      self->requestIsCompleted = YES;
-                                     NSDictionary *jsError = [self->errorCodes valueForKey:kErrorKeyAuthenticationFailed];
-                                     [self rejectPromiseWithCode:jsError[kErrorKeyCode] message:error.localizedDescription error:error];
+                                     [self rejectPromiseWithCode:code message:error.localizedDescription error:error];
                                      return;
                                  }
 
@@ -410,11 +413,13 @@ - (NSDictionary *)constantsToExport
                                                                                                                         message:error.localizedDescription ?: [self->errorCodes valueForKey:kErrorKeyCancelled][kErrorKeyDescription]
                                                                                                                           error:error];
                                                                                                     return;
-                                                                                                case STPPaymentHandlerActionStatusFailed:
-                                                                                                    [self rejectPromiseWithCode:[self->errorCodes valueForKey:kErrorKeyAuthenticationFailed][kErrorKeyCode]
+                                                                                                case STPPaymentHandlerActionStatusFailed: {
+                                                                                                    NSString * code = error.userInfo[STPError.stripeDeclineCodeKey] ? error.userInfo[STPError.stripeDeclineCodeKey] : error.userInfo[STPError.stripeErrorCodeKey];
+                                                                                                    [self rejectPromiseWithCode:code ? code:[self->errorCodes valueForKey:kErrorKeyAuthenticationFailed][kErrorKeyCode]
                                                                                                                         message:error.localizedDescription ?: [self->errorCodes valueForKey:kErrorKeyAuthenticationFailed][kErrorKeyDescription] ?: @"FAILED"
                                                                                                                           error:error];
                                                                                                     return;
+                                                                                                }
                                                                                             }
                                                                                         }];
                                  } else {
@@ -425,7 +430,7 @@ - (NSDictionary *)constantsToExport
                                                              message:error.localizedDescription ?: [self->errorCodes valueForKey:kErrorKeyCancelled][kErrorKeyDescription]
                                                                error:error];
                                      } else {
-                                         [self rejectPromiseWithCode:[self->errorCodes valueForKey:kErrorKeyAuthenticationFailed][kErrorKeyCode]
+                                         [self rejectPromiseWithCode:code
                                                              message:error.localizedDescription ?: [self->errorCodes valueForKey:kErrorKeyAuthenticationFailed][kErrorKeyDescription] ?: @"FAILED"
                                                                error:error];
 
@@ -468,11 +473,13 @@ - (NSDictionary *)constantsToExport
                                                                                        message:error.localizedDescription ?: [self->errorCodes valueForKey:kErrorKeyCancelled][kErrorKeyDescription]
                                                                                          error:error];
                                                                    return;
-                                                               case STPPaymentHandlerActionStatusFailed:
-                                                                   [self rejectPromiseWithCode:[self->errorCodes valueForKey:kErrorKeyAuthenticationFailed][kErrorKeyCode]
+                                                               case STPPaymentHandlerActionStatusFailed: {
+                                                                   NSString * code = error.userInfo[STPError.stripeDeclineCodeKey] ? error.userInfo[STPError.stripeDeclineCodeKey] : error.userInfo[STPError.stripeErrorCodeKey];
+                                                                   [self rejectPromiseWithCode:code
                                                                                        message:error.localizedDescription ?: [self->errorCodes valueForKey:kErrorKeyAuthenticationFailed][kErrorKeyDescription] ?: @"FAILED"
                                                                                          error:error];
                                                                    return;
+                                                               }
                                                            }
                                                        }];
 }
@@ -495,10 +502,11 @@ - (NSDictionary *)constantsToExport
     STPAPIClient *api = self.newAPIClient;
     [api confirmSetupIntentWithParams:parsed
                            completion:^(STPSetupIntent * __nullable intent, NSError * __nullable error){
+                                NSString * code = error.userInfo[STPError.stripeDeclineCodeKey] ? error.userInfo[STPError.stripeDeclineCodeKey] : error.userInfo[STPError.stripeErrorCodeKey];
+                               
                                if (!intent && error) {
                                    self->requestIsCompleted = YES;
-                                   NSDictionary *jsError = [self->errorCodes valueForKey:kErrorKeyAuthenticationFailed];
-                                   [self rejectPromiseWithCode:jsError[kErrorKeyCode] message:error.localizedDescription error:error];
+                                   [self rejectPromiseWithCode:code message:error.localizedDescription error:error];
                                    return;
                                }
 
@@ -537,7 +545,7 @@ - (NSDictionary *)constantsToExport
                                                            message:error.localizedDescription ?: [self->errorCodes valueForKey:kErrorKeyCancelled][kErrorKeyDescription]
                                                              error:error];
                                    } else {
-                                       [self rejectPromiseWithCode:[self->errorCodes valueForKey:kErrorKeyAuthenticationFailed][kErrorKeyCode]
+                                       [self rejectPromiseWithCode:code
                                                            message:error.localizedDescription ?: [self->errorCodes valueForKey:kErrorKeyAuthenticationFailed][kErrorKeyDescription] ?: @"FAILED"
                                                              error:error];
                                    }
@@ -588,11 +596,19 @@ - (NSDictionary *)constantsToExport
 }
 
 
-RCT_EXPORT_METHOD(completeApplePayRequest:(RCTPromiseResolveBlock)resolve
+RCT_EXPORT_METHOD(completeApplePayRequest:(NSString *)clientSecret
+                    completeApplePayRequest:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject) {
     if (applePayCompletion) {
         promiseResolver = resolve;
-        [self resolveApplePayCompletion:PKPaymentAuthorizationStatusSuccess];
+        if (clientSecret) {
+            [self resolveApplePayCompletion:clientSecret error:nil];
+        } else {
+            NSError *err = [NSError errorWithDomain:@"stripe"
+                                               code:100
+                                           userInfo:@{NSLocalizedDescriptionKey:@"Payment Error"}];
+            [self resolveApplePayCompletion:nil error:err];
+        }
     } else {
         resolve(nil);
     }
@@ -602,7 +618,10 @@ - (NSDictionary *)constantsToExport
                   rejecter:(RCTPromiseRejectBlock)reject) {
     if (applePayCompletion) {
         promiseResolver = resolve;
-        [self resolveApplePayCompletion:PKPaymentAuthorizationStatusFailure];
+        NSError *err = [NSError errorWithDomain:@"stripe"
+                                           code:100
+                                       userInfo:@{NSLocalizedDescriptionKey:@"Cancel"}];
+        [self resolveApplePayCompletion:nil error:err];
     } else {
         resolve(nil);
     }
@@ -795,18 +814,23 @@ - (NSDictionary *)constantsToExport
     [configuration setCompanyName:companyName];
     [configuration setPublishableKey:nextPublishableKey];
 
-    STPAddCardViewController *vc = [[STPAddCardViewController alloc] initWithConfiguration:configuration theme:theme];
+    STPAddCardViewController *vc = [[STPAddCardViewController alloc] initWithConfiguration:configuration theme:[STPTheme defaultTheme]];
     vc.delegate = self;
     vc.prefilledInformation = prefilledInformation;
     // STPAddCardViewController must be shown inside a UINavigationController.
     UINavigationController *navigationController = [[UINavigationController alloc] initWithRootViewController:vc];
     [navigationController setModalPresentationStyle:formPresentation];
-    navigationController.navigationBar.stp_theme = theme;
+
+    if ([options[@"presentation"] isEqualToString:@"pageSheet"]) {
+        if (@available(iOS 13.0, *)) navigationController.modalInPresentation = YES;
+    }
+
     // move to the end of main queue
     // allow the execution of hiding modal
     // to be finished first
     dispatch_async(dispatch_get_main_queue(), ^{
-        [RCTPresentedViewController() presentViewController:navigationController animated:YES completion:nil];
+        UIViewController *rootViewController = RCTPresentedViewController();
+        [rootViewController presentViewController:navigationController animated:YES completion:nil];
     });
 }
 
@@ -868,9 +892,17 @@ - (NSDictionary *)constantsToExport
         // move to the end of main queue
         // allow the execution of hiding modal
         // to be finished first
-        dispatch_async(dispatch_get_main_queue(), ^{
-            [RCTPresentedViewController() presentViewController:paymentAuthorizationVC animated:YES completion:nil];
-        });
+        // Initialize an STPApplePayContext instance
+        STPApplePayContext *applePayContext = [[STPApplePayContext alloc] initWithPaymentRequest:paymentRequest delegate:self];
+
+        if (applePayContext) {
+            // Present Apple Pay payment sheet
+            dispatch_async(dispatch_get_main_queue(), ^{
+                [applePayContext presentApplePayWithCompletion:nil];
+            });
+        } else {
+            // There is a problem with your Apple Pay configuration
+        }
     } else {
         // There is a problem with your Apple Pay configuration.
         [self resetPromiseCallbacks];
@@ -991,7 +1023,31 @@ - (STPPaymentIntentParams*)extractConfirmPaymentIntentParamsFromDictionary:(NSDi
     STPPaymentIntentParams * result = [[STPPaymentIntentParams alloc] initWithClientSecret:clientSecret];
 
     NSString * paymentMethodId = params[TPSStripeParam(confirmPaymentIntent, paymentMethodId)];
-    STPPaymentMethodParams * methodParams = [self extractCreatePaymentMethodParamsFromDictionary:params[TPSStripeParam(confirmPaymentIntent, paymentMethod)]];
+    STPPaymentMethodParams * methodParams;
+        
+    NSString *methodType = params[@"type"];
+    
+    STPPaymentMethodBillingDetails * details = [self extractPaymentMethodBillingDetailsFromDictionary: params[TPSStripeParam(createPaymentMethod, billingDetails)]];
+    NSDictionary* metadata = params[TPSStripeParam(createPaymentMethod, metadata)];
+    
+    if ([methodType isEqualToString:@"card"]) {
+        NSDictionary<TPSStripeType(CardParams), id> * cardParamsInput = params[TPSStripeParam(createPaymentMethod, card)];
+        if (cardParamsInput) {
+            STPPaymentMethodCardParams* card = [self extractPaymentMethodCardParamsFromDictionary:cardParamsInput];
+            
+            methodParams = [STPPaymentMethodParams paramsWithCard:card billingDetails:details metadata:metadata];
+        }
+    }
+    else if ([methodType isEqualToString:@"alipay"]) {
+        methodParams = [STPPaymentMethodParams paramsWithAlipay:[STPPaymentMethodAlipayParams new] billingDetails:details metadata:metadata];
+        result.paymentMethodOptions = [STPConfirmPaymentMethodOptions new];
+        result.paymentMethodOptions.alipayOptions = [STPConfirmAlipayOptions new];
+    }
+    else if ([methodType isEqualToString:@"grabpay"]) {
+        STPPaymentMethodGrabPayParams *grabPayParams = [STPPaymentMethodGrabPayParams new];
+        methodParams = [STPPaymentMethodParams paramsWithGrabPay:grabPayParams billingDetails:details metadata:metadata];
+    }
+
     // Don't assert, as it's allowed to omit a paymentMethodId/paymentMethodParams
     // for confirmPaymentIntent -- if the user had already attached the
     // paymentMethod on the backend.
@@ -1172,9 +1228,13 @@ - (void)resetPromiseCallbacks {
     promiseRejector = nil;
 }
 
-- (void)resolveApplePayCompletion:(PKPaymentAuthorizationStatus)status {
+- (void)resolveApplePayCompletion:(NSString *)clientSecret error:(NSError*)error {
     if (applePayCompletion) {
-        applePayCompletion(status);
+        if (clientSecret) {
+            applePayCompletion(clientSecret, nil);
+        } else {
+            applePayCompletion(nil, error);
+        }
     }
     [self resetApplePayCallback];
 }
@@ -1230,37 +1290,6 @@ - (void)addCardViewControllerDidCancel:(STPAddCardViewController *)addCardViewCo
 
 #pragma mark PKPaymentAuthorizationViewControllerDelegate
 
-- (void)paymentAuthorizationViewController:(PKPaymentAuthorizationViewController *)controller
-                       didAuthorizePayment:(PKPayment *)payment
-                                completion:(void (^)(PKPaymentAuthorizationStatus))completion {
-    // Save for deffered call
-    applePayCompletion = completion;
-
-    STPAPIClient *stripeAPIClient = [self newAPIClient];
-
-    [stripeAPIClient createTokenWithPayment:payment completion:^(STPToken * _Nullable token, NSError * _Nullable error) {
-        self->requestIsCompleted = YES;
-
-        if (error) {
-            // Save for deffered use
-            self->applePayStripeError = error;
-            [self resolveApplePayCompletion:PKPaymentAuthorizationStatusFailure];
-        } else {
-            NSDictionary *result = [self convertTokenObject:token];
-            NSDictionary *extra = @{
-                                    @"billingContact": [self contactDetails:payment.billingContact] ?: [NSNull null],
-                                    @"shippingContact": [self contactDetails:payment.shippingContact] ?: [NSNull null],
-                                    @"shippingMethod": [self shippingDetails:payment.shippingMethod] ?: [NSNull null]
-                                    };
-
-            [result setValue:extra forKey:@"extra"];
-
-            [self resolvePromise:result];
-        }
-    }];
-}
-
-
 - (void)paymentAuthorizationViewControllerDidFinish:(PKPaymentAuthorizationViewController *)controller {
     [self resetApplePayCallback];
 
@@ -1284,6 +1313,37 @@ - (void)paymentAuthorizationViewControllerDidFinish:(PKPaymentAuthorizationViewC
     [RCTPresentedViewController() dismissViewControllerAnimated:YES completion:completion];
 }
 
+#pragma mark - STPApplePayContextDelegate
+
+- (void)applePayContext:(STPApplePayContext *)context didCreatePaymentMethod:(STPPaymentMethod * _Nonnull)paymentMethod paymentInformation:(PKPayment * _Nonnull)paymentInformation completion:(void (^ _Nonnull)(NSString * _Nullable, NSError * _Nullable))completion {
+    
+    applePayCompletion = completion;
+    
+    [self resolvePromise:[self convertPaymentMethod:paymentMethod]];
+}
+
+- (void)applePayContext:(STPApplePayContext *)context didCompleteWithStatus:(STPPaymentStatus)status error:(NSError *)error {
+    switch (status) {
+        case STPPaymentStatusSuccess:
+            // Payment succeeded, show a receipt view
+            self->requestIsCompleted = YES;
+            break;
+            
+        case STPPaymentStatusError:
+            self->requestIsCompleted = YES;
+            // Payment failed, show the error
+            self->applePayStripeError = error;
+            break;
+            
+        case STPPaymentStatusUserCancellation:
+            self->requestIsCompleted = YES;
+            NSDictionary *error = [self->errorCodes valueForKey:kErrorKeyCancelled];
+            [self rejectPromiseWithCode:error[kErrorKeyCode] message:error[kErrorKeyDescription]];
+            
+            break;
+    }
+}
+
 - (STPAPIClient *)newAPIClient {
     static STPAppInfo * info = nil;
     static dispatch_once_t onceToken;
@@ -1489,6 +1549,8 @@ - (NSString *)cardBrandAsBrandSlug:(STPCardBrand)inputBrand {
             return @"diners";
         case STPCardBrandMastercard:
             return @"mastercard";
+        case STPCardBrandUnionPay:
+            return @"unionpay";
         case STPCardBrandUnknown:
         default:
             return @"unknown";
diff --git a/src/Stripe.deprecated.js b/src/Stripe.deprecated.js
index 61b48a32ea111a79debf2df1ac08440c2730bf88..cc97e15d7a278c25260670efb7e9ba79ef015cc4 100644
--- a/src/Stripe.deprecated.js
+++ b/src/Stripe.deprecated.js
@@ -27,15 +27,21 @@ const deprecatedMethodsForInstance = (instance) => ({
   canMakeAndroidPayPayments: () => StripeModule.canMakeAndroidPayPayments(),
 
   // @deprecated use paymentRequestWithNativePay
-  paymentRequestWithAndroidPay: (options = {}) => {
+  paymentRequestWithAndroidPay: (items = [], options = {}) => {
     checkInit(instance)
+    checkArgs(
+      types.paymentRequestWithAndroidPayItemsPropTypes,
+      { items },
+      'items',
+      'Stripe.paymentRequestWithAndroidPay'
+    )
     checkArgs(
       types.paymentRequestWithAndroidPayOptionsPropTypes,
       options,
       'options',
       'Stripe.paymentRequestWithAndroidPay'
     )
-    return StripeModule.paymentRequestWithAndroidPay(options)
+    return StripeModule.paymentRequestWithAndroidPay(items, options)
   },
 
   // @deprecated use paymentRequestWithNativePay
@@ -60,14 +66,14 @@ const deprecatedMethodsForInstance = (instance) => ({
   paymentRequestWithNativePay: (options = {}, items = []) => {
     return Platform.select({
       ios: () => instance.paymentRequestWithApplePay(items, options),
-      android: () => instance.paymentRequestWithAndroidPay(options),
+      android: () => instance.paymentRequestWithAndroidPay(items, options),
     })()
   },
 
   // @deprecated use completeNativePayRequest
-  completeApplePayRequest: () => {
+  completeApplePayRequest: (clientSecret) => {
     checkInit(instance)
-    return StripeModule.completeApplePayRequest()
+    return StripeModule.completeApplePayRequest(clientSecret)
   },
 
   // @deprecated use cancelNativePayRequest
diff --git a/src/Stripe.js b/src/Stripe.js
index 436f00dab33347bdb185fe7034f756d5b531db82..8cf132ed0bb77d03bb230163ceea1652df399d59 100644
--- a/src/Stripe.js
+++ b/src/Stripe.js
@@ -210,16 +210,16 @@ class Stripe {
   paymentRequestWithNativePay(options = {}, items = []) {
     return Platform.select({
       ios: () => this.paymentRequestWithApplePay(items, options),
-      android: () => this.paymentRequestWithAndroidPay(options),
+      android: () => this.paymentRequestWithAndroidPay(items, options),
     })()
   }
 
   // no corresponding android impl exists
-  completeNativePayRequest = () =>
+  completeNativePayRequest = (clientSecret) =>
     Platform.select({
-      ios: () => this.completeApplePayRequest(),
-      android: () => Promise.resolve(),
-    })()
+      ios: (clientSecret) => this.completeApplePayRequest(clientSecret),
+      android: (clientSecret) => this.completeGooglePayRequest(clientSecret),
+    })(clientSecret)
 
   // no corresponding android impl exists
   cancelNativePayRequest = () =>
diff --git a/src/utils/types.js b/src/utils/types.js
index 450909c87c6d6a2bcd8c07d83840b35179bebd62..8df343329fd0cd0167e313802d30e910768627f4 100644
--- a/src/utils/types.js
+++ b/src/utils/types.js
@@ -185,13 +185,24 @@ export const androidPayLineItemPropTypes = {
   description: PropTypes.string.isRequired,
 }
 
+export const paymentRequestWithAndroidPayItemPropTypes = {
+  currencyCode: PropTypes.string.isRequired,
+  amount: PropTypes.string.isRequired,
+  unitPrice: PropTypes.string.isRequired,
+  quantity: PropTypes.string.isRequired,
+  label: PropTypes.string.isRequired,
+}
+
+export const paymentRequestWithAndroidPayItemsPropTypes = {
+  items: PropTypes.arrayOf(PropTypes.shape(paymentRequestWithAndroidPayItemPropTypes)).isRequired,
+}
+
 export const paymentRequestWithAndroidPayOptionsPropTypes = {
-  total_price: PropTypes.string.isRequired,
-  currency_code: PropTypes.string.isRequired,
-  line_items: PropTypes.arrayOf(PropTypes.shape(androidPayLineItemPropTypes)).isRequired,
-  shipping_address_required: PropTypes.bool,
-  billing_address_required: PropTypes.bool,
-  email_address_required: PropTypes.bool,
+  totalPrice: PropTypes.string.isRequired,
+  currencyCode: PropTypes.string.isRequired,
+  requiredShippingAddress: PropTypes.bool,
+  requiredBillingAddress: PropTypes.bool,
+  requiredEmail: PropTypes.bool
 }
 
 export const createSourceWithParamsPropType = {
