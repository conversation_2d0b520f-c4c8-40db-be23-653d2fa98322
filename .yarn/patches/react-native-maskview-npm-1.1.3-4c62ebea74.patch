diff --git a/android/build.gradle b/android/build.gradle
index 50e2a73d97d02bf97281556ae1e604707e49fac3..3d017c4ee6f46ff4c47aef4f53f2b0d8ca592e91 100644
--- a/android/build.gradle
+++ b/android/build.gradle
@@ -1,17 +1,5 @@
 // android/build.gradle
 
-// based on:
-//
-// * https://github.com/facebook/react-native/blob/0.60-stable/template/android/build.gradle
-//   previous location:
-//   - https://github.com/facebook/react-native/blob/0.58-stable/local-cli/templates/HelloWorld/android/build.gradle
-//
-// * https://github.com/facebook/react-native/blob/0.60-stable/template/android/app/build.gradle
-//   previous location:
-//   - https://github.com/facebook/react-native/blob/0.58-stable/local-cli/templates/HelloWorld/android/app/build.gradle
-
-// These defaults should reflect the SDK versions used by
-// the minimum React Native version supported.
 def DEFAULT_COMPILE_SDK_VERSION = 30
 def DEFAULT_BUILD_TOOLS_VERSION = '29.0.3'
 def DEFAULT_MIN_SDK_VERSION = 21
@@ -22,23 +10,15 @@ def safeExtGet(prop, fallback) {
 }
 
 apply plugin: 'com.android.library'
-apply plugin: 'maven'
+apply plugin: 'maven-publish'
 
 buildscript {
-    // The Android Gradle plugin is only required when opening the android folder stand-alone.
-    // This avoids unnecessary downloads and potential conflicts when the library is included as a
-    // module dependency in an application project.
-    // ref: https://docs.gradle.org/current/userguide/tutorial_using_tasks.html#sec:build_script_external_dependencies
-    if (project == rootProject) {
-        repositories {
-            google()
-
-        }
-        dependencies {
-            // This should reflect the Gradle plugin version used by
-            // the minimum React Native version supported.
-            classpath 'com.android.tools.build:gradle:3.4.1'
-        }
+    repositories {
+        google()
+        mavenCentral() // Added to ensure dependencies are available
+    }
+    dependencies {
+        classpath 'com.android.tools.build:gradle:7.0.4'
     }
 }
 
@@ -57,23 +37,19 @@ android {
 }
 
 repositories {
-    // ref: https://www.baeldung.com/maven-local-repository
     mavenLocal()
     maven {
-        // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
         url "$rootDir/../node_modules/react-native/android"
     }
     maven {
-        // Android JSC is installed from npm
         url "$rootDir/../node_modules/jsc-android/dist"
     }
     google()
-    
+    mavenCentral()
 }
 
 dependencies {
-    //noinspection GradleDynamicVersion
-    implementation 'com.facebook.react:react-native:+'  // From node_modules
+    implementation 'com.facebook.react:react-native:+'
 }
 
 def configureReactNativePom(def pom) {
@@ -104,9 +80,31 @@ def configureReactNativePom(def pom) {
     }
 }
 
+publishing {
+    publications {
+        mavenJava(MavenPublication) {
+            groupId = "com.jjlf.rnmaskview"
+            artifactId = project.name
+            version = "1.0"
+
+            afterEvaluate {
+                from components.release
+            }
+
+            pom.withXml {
+                def root = asNode()
+                configureReactNativePom(root)
+            }
+        }
+    }
+    repositories {
+        maven {
+            url = uri("file://${projectDir}/../android/maven")
+        }
+    }
+}
+
 afterEvaluate { project ->
-    // some Gradle build hooks ref:
-    // https://www.oreilly.com/library/view/gradle-beyond-the/9781449373801/ch03.html
     task androidJavadoc(type: Javadoc) {
         source = android.sourceSets.main.java.srcDirs
         classpath += files(android.bootClasspath)
@@ -138,13 +136,4 @@ afterEvaluate { project ->
         archives androidSourcesJar
         archives androidJavadocJar
     }
-
-    task installArchives(type: Upload) {
-        configuration = configurations.archives
-        repositories.mavenDeployer {
-            // Deploy to react-native-event-bridge/maven, ready to publish to npm
-            repository url: "file://${projectDir}/../android/maven"
-            configureReactNativePom pom
-        }
-    }
 }
