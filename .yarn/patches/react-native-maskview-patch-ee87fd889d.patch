diff --git a/android/build.gradle b/android/build.gradle
index 2c5d1b22dba605896b94fea03ba4638fd6d79201..583bc7c3325ebf433692801c67dce8f81084ab90 100644
--- a/android/build.gradle
+++ b/android/build.gradle
@@ -88,7 +88,7 @@ publishing {
             version = "1.0"
 
             afterEvaluate {
-                from components.release
+                from components.findByName('release')
             }
 
             pom.withXml {
@@ -113,12 +113,12 @@ afterEvaluate { project ->
     }
 
     task androidJavadocJar(type: Jar, dependsOn: androidJavadoc) {
-        classifier = 'javadoc'
+        archiveClassifier.set('javadoc')
         from androidJavadoc.destinationDir
     }
 
     task androidSourcesJar(type: Jar) {
-        classifier = 'sources'
+        archiveClassifier.set('sources')
         from android.sourceSets.main.java.srcDirs
         include '**/*.java'
     }
