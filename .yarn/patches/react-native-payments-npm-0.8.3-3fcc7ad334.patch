diff --git a/android/build.gradle b/android/build.gradle
index 0d066bd15543931e4029146f627a5902f244d4ee..9405d643cb8b8943b49f8f0899df40e4e7ed5c8f 100644
--- a/android/build.gradle
+++ b/android/build.gradle
@@ -23,5 +23,5 @@ dependencies {
     implementation 'com.google.android.gms:play-services-base:17.0.0'
     implementation 'com.google.android.gms:play-services-identity:17.0.0'
     implementation 'com.google.android.gms:play-services-wallet:17.0.0'
-    implementation 'com.android.support:support-v4:23.0.1'
+    implementation 'androidx.appcompat:appcompat:1.0.2'
 }
diff --git a/android/src/main/AndroidManifest.xml b/android/src/main/AndroidManifest.xml
index 3bc7e98e7ddd8b121424af84ff4ed0b708585db3..0a14dbfdac30946ce83b4588b36bb73a04e722d9 100644
--- a/android/src/main/AndroidManifest.xml
+++ b/android/src/main/AndroidManifest.xml
@@ -2,4 +2,9 @@
 <manifest xmlns:android="http://schemas.android.com/apk/res/android"
           package="com.reactnativepayments">
 
+    <application android:label="@string/app_name">
+        <meta-data
+            android:name="com.google.android.gms.wallet.api.enabled"
+            android:value="true" />
+    </application>
 </manifest>
diff --git a/android/src/main/java/com/reactnativepayments/ReactNativePaymentsModule.java b/android/src/main/java/com/reactnativepayments/ReactNativePaymentsModule.java
index a47b50d44cf14bb53a442fcd4dd992f303c61c3d..7b76e8704601f7a8266a9f570241862f1eb142d7 100644
--- a/android/src/main/java/com/reactnativepayments/ReactNativePaymentsModule.java
+++ b/android/src/main/java/com/reactnativepayments/ReactNativePaymentsModule.java
@@ -5,23 +5,28 @@ import android.view.WindowManager;
 import android.app.Activity;
 import android.content.Intent;
 import android.os.Bundle;
-import android.support.annotation.Nullable;
-import android.support.annotation.NonNull;
+import androidx.annotation.Nullable;
+import androidx.annotation.NonNull;
 import android.app.Fragment;
 import android.app.FragmentManager;
-import android.support.annotation.RequiresPermission;
+import androidx.annotation.RequiresPermission;
 import android.util.Log;
 
 import com.facebook.react.bridge.Callback;
 import com.facebook.react.bridge.ReactBridge;
 import com.facebook.react.bridge.ReadableArray;
 import com.facebook.react.bridge.ReadableMapKeySetIterator;
+import com.google.android.gms.common.GoogleApiAvailability;
 import com.google.android.gms.common.api.GoogleApiClient;
 import com.google.android.gms.common.api.BooleanResult;
 import com.google.android.gms.common.api.ResultCallback;
 import com.google.android.gms.common.ConnectionResult;
 import com.google.android.gms.identity.intents.model.UserAddress;
 import com.google.android.gms.wallet.*;
+import com.google.android.gms.common.api.ApiException;
+import com.google.android.gms.common.api.Status;
+import com.google.android.gms.tasks.OnCompleteListener;
+import com.google.android.gms.tasks.Task;
 
 import com.facebook.react.bridge.ActivityEventListener;
 import com.facebook.react.bridge.BaseActivityEventListener;
@@ -38,19 +43,20 @@ import java.util.HashMap;
 import java.util.List;
 import java.util.Map;
 
-public class ReactNativePaymentsModule extends ReactContextBaseJavaModule implements GoogleApiClient.ConnectionCallbacks, GoogleApiClient.OnConnectionFailedListener {
+import org.json.JSONArray;
+import org.json.JSONException;
+import org.json.JSONObject;
+
+public class ReactNativePaymentsModule extends ReactContextBaseJavaModule {
     private static final int LOAD_MASKED_WALLET_REQUEST_CODE = 88;
-    private static final int LOAD_FULL_WALLET_REQUEST_CODE = 89;
 
 
-    // Google API Client
-    private GoogleApiClient mGoogleApiClient = null;
+    // Payments Client
+    private PaymentsClient mPaymentsClient;
 
     // Callbacks
     private static Callback mShowSuccessCallback = null;
     private static Callback mShowErrorCallback = null;
-    private static Callback mGetFullWalletSuccessCallback= null;
-    private static Callback mGetFullWalletErrorCallback = null;
 
     public static final String REACT_CLASS = "ReactNativePayments";
 
@@ -64,31 +70,81 @@ public class ReactNativePaymentsModule extends ReactContextBaseJavaModule implem
             if (data != null) {
                 errorCode = data.getIntExtra(WalletConstants.EXTRA_ERROR_CODE, -1);
             }
+
             switch (requestCode) {
                 case LOAD_MASKED_WALLET_REQUEST_CODE:
                     switch (resultCode) {
                         case Activity.RESULT_OK:
                             if (data != null) {
-                                MaskedWallet maskedWallet =
-                                        data.getParcelableExtra(WalletConstants.EXTRA_MASKED_WALLET);
 
-                                Log.i(REACT_CLASS, "ANDROID PAY SUCCESS" + maskedWallet.getEmail());
-                                Log.i(REACT_CLASS, "ANDROID PAY SUCCESS" + buildAddressFromUserAddress(maskedWallet.getBuyerBillingAddress()));
+                                PaymentData paymentData = PaymentData.getFromIntent(data);
+                                String paymentInformation = paymentData.toJson();
+
+                                Log.i(REACT_CLASS, "GOOGLE PAY SUCCESS " + paymentInformation);
+
+                                JSONObject paymentMethodData;
+
+                                try {
+                                    paymentMethodData = new JSONObject(paymentInformation).getJSONObject("paymentMethodData");
+
+
+                                    WritableNativeMap paymentDetails = new WritableNativeMap();
+                                    paymentDetails.putString("description", paymentMethodData.getString("description"));
+
+                                    WritableNativeMap tokenizationData = new WritableNativeMap();
+                                    JSONObject tokenizationDataJson = paymentMethodData.getJSONObject("tokenizationData");
+                                    tokenizationData.putString("type", tokenizationDataJson.getString("type"));
+                                    tokenizationData.putString("token", tokenizationDataJson.getString("token"));
+                                    paymentDetails.putMap("tokenizationData", tokenizationData);
+
+
+                                    WritableNativeMap paymentToken = new WritableNativeMap();
+
+                                    JSONObject paymentTokenJson = new JSONObject(tokenizationDataJson.getString("token"));
+
+                                    if (tokenizationDataJson.getString("type").equals("PAYMENT_GATEWAY")) {
+                                        try {
+                                            // stripe
+                                            String id = paymentTokenJson.getString("id");
+                                            paymentToken.putString("id", id);
+                                        } catch (JSONException e) {
+                                            paymentToken.putString("tokenizationType", tokenizationDataJson.getString("type"));
+                                            paymentToken.putString("token", tokenizationDataJson.getString("token"));
+
+                                            paymentToken.putString("raw", paymentInformation);
+                                        }
 
-                                UserAddress userAddress = maskedWallet.getBuyerShippingAddress();
-                                WritableNativeMap shippingAddress = userAddress != null
-                                    ? buildAddressFromUserAddress(userAddress)
-                                    : null;
+                                    } else {
+                                        String protocolVersion = paymentTokenJson.getString("protocolVersion");
+                                        String signature = paymentTokenJson.getString("signature");
+                                        String signedMessage = paymentTokenJson.getString("signedMessage");
 
+                                        paymentToken.putString("protocolVersion", protocolVersion);
+                                        paymentToken.putString("signature", signature);
+                                        paymentToken.putString("signedMessage", signedMessage);
+                                    }
 
-                                // TODO: Move into function
-                                WritableNativeMap paymentDetails = new WritableNativeMap();
-                                paymentDetails.putString("paymentDescription", maskedWallet.getPaymentDescriptions()[0]);
-                                paymentDetails.putString("payerEmail", maskedWallet.getEmail());
-                                paymentDetails.putMap("shippingAddress", shippingAddress);
-                                paymentDetails.putString("googleTransactionId", maskedWallet.getGoogleTransactionId());
 
-                                sendEvent(reactContext, "NativePayments:onuseraccept", paymentDetails);
+                                    paymentDetails.putMap("paymentToken", paymentToken);
+
+
+                                    paymentDetails.putString("type", paymentMethodData.getString("type"));
+
+                                    WritableNativeMap info = new WritableNativeMap();
+                                    JSONObject infoJson = paymentMethodData.getJSONObject("info");
+                                    info.putString("cardNetwork", infoJson.getString("cardNetwork"));
+                                    info.putString("cardDetails", infoJson.getString("cardDetails"));
+                                    paymentDetails.putMap("cardInfo", info);
+
+                                    sendEvent(reactContext, "NativePayments:onuseraccept", paymentDetails);
+
+                                    // Logging token string.
+                                    // Log.d("GooglePaymentToken", paymentMethodData.getJSONObject("tokenizationData").getString("token"));
+                                } catch (JSONException e) {
+                                    Log.e("handlePaymentSuccess", "Error: " + e.toString());
+                                    return;
+                                }
+
                             }
                             break;
                         case Activity.RESULT_CANCELED:
@@ -96,23 +152,13 @@ public class ReactNativePaymentsModule extends ReactContextBaseJavaModule implem
 
                             break;
                         default:
-                            Log.i(REACT_CLASS, "ANDROID PAY ERROR? " + errorCode);
+                            Log.i(REACT_CLASS, "GOOGLE PAY ERROR " + errorCode);
                             mShowErrorCallback.invoke(errorCode);
+                            sendEvent(reactContext, "NativePayments:onuserdismiss", null);
 
                             break;
                     }
                     break;
-                case LOAD_FULL_WALLET_REQUEST_CODE:
-                    if (resultCode == Activity.RESULT_OK && data != null) {
-                        FullWallet fullWallet = data.getParcelableExtra(WalletConstants.EXTRA_FULL_WALLET);
-                        String tokenJSON = fullWallet.getPaymentMethodToken().getToken();
-                        Log.i(REACT_CLASS, "FULL WALLET SUCCESS" + tokenJSON);
-
-                        mGetFullWalletSuccessCallback.invoke(tokenJSON);
-                    } else {
-                        Log.i(REACT_CLASS, "FULL WALLET FAILURE");
-                        mGetFullWalletErrorCallback.invoke();
-                    }
                 case WalletConstants.RESULT_ERROR:activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_SECURE);
 //                    handleError(errorCode);
                     break;
@@ -151,30 +197,53 @@ public class ReactNativePaymentsModule extends ReactContextBaseJavaModule implem
     }
 
     @ReactMethod
-    public void canMakePayments(ReadableMap paymentMethodData, Callback errorCallback, Callback successCallback) {
-        final Callback callback = successCallback;
-        IsReadyToPayRequest req = IsReadyToPayRequest.newBuilder()
-                .addAllowedCardNetwork(WalletConstants.CardNetwork.MASTERCARD)
-                .addAllowedCardNetwork(WalletConstants.CardNetwork.VISA)
-                .build();
-
-        int environment = getEnvironmentFromPaymentMethodData(paymentMethodData);
-        if (mGoogleApiClient == null) {
-            buildGoogleApiClient(getCurrentActivity(), environment);
-        }
+    public void canMakePayments(ReadableMap paymentMethodData, final Callback errorCallback, final Callback successCallback) {
 
-        Wallet.Payments.isReadyToPay(mGoogleApiClient, req)
-                .setResultCallback(new ResultCallback<BooleanResult>() {
-                    @Override
-                    public void onResult(@NonNull BooleanResult booleanResult) {
-                        callback.invoke(booleanResult.getValue());
-                    }
-                });
+        try {
+
+            GoogleApiAvailability googleApiAvailability = GoogleApiAvailability.getInstance();
+            int resultCode = googleApiAvailability.isGooglePlayServicesAvailable(getReactApplicationContext());
+            if(resultCode != ConnectionResult.SUCCESS) {
+                successCallback.invoke(false);
+                return;
+            }
+
+            JSONObject isReadyToPayRequest = getBaseRequest();
+            isReadyToPayRequest.put("allowedPaymentMethods", new JSONArray().put(getBaseCardPaymentMethod(paymentMethodData)));
+            isReadyToPayRequest.put("existingPaymentMethodRequired", true);
+
+            IsReadyToPayRequest request = IsReadyToPayRequest.fromJson(isReadyToPayRequest.toString());
+
+            int environment = getEnvironmentFromPaymentMethodData(paymentMethodData);
+            createPaymentsClient(getCurrentActivity(), environment);
+
+            Task<Boolean> task = mPaymentsClient.isReadyToPay(request);
+            task.addOnCompleteListener(
+                    new OnCompleteListener<Boolean>() {
+                        @Override
+                        public void onComplete(@NonNull Task<Boolean> task) {
+                            boolean result = task.isSuccessful();
+                            if (result) {
+                                successCallback.invoke(task.getResult());
+                            } else {
+                                Log.w("isReadyToPay failed", task.getException());
+                                Exception exception = task.getException();
+                                if (exception != null) {
+                                    errorCallback.invoke(exception.getMessage());
+                                } else {
+                                    errorCallback.invoke("payment failed");
+                                }
+                            }
+                        }
+                    });
+        } catch (Exception e) {
+            errorCallback.invoke(e.getMessage());
+        }
     }
 
     @ReactMethod
     public void abort(Callback errorCallback, Callback successCallback) {
-        Log.i(REACT_CLASS, "ANDROID PAY ABORT" + getCurrentActivity().toString());
+        Log.i(REACT_CLASS, "GOOGLE PAY ABORT" + getCurrentActivity().toString());
         successCallback.invoke();
     }
 
@@ -189,137 +258,185 @@ public class ReactNativePaymentsModule extends ReactContextBaseJavaModule implem
         mShowSuccessCallback = successCallback;
         mShowErrorCallback = errorCallback;
 
-        Log.i(REACT_CLASS, "ANDROID PAY SHOW" + options);
+        Log.i(REACT_CLASS, "GOOGLE PAY SHOW" + options);
 
         Boolean shouldRequestShipping = options.hasKey("requestShipping") && options.getBoolean("requestShipping")
-                        || options.hasKey("requestPayerName") && options.getBoolean("requestPayerName")
-                        || options.hasKey("requestPayerPhone") && options.getBoolean("requestPayerPhone");
+                || options.hasKey("requestPayerName") && options.getBoolean("requestPayerName")
+                || options.hasKey("requestPayerPhone") && options.getBoolean("requestPayerPhone");
         Boolean shouldRequestPayerPhone = options.hasKey("requestPayerPhone") && options.getBoolean("requestPayerPhone");
 
-        final PaymentMethodTokenizationParameters parameters = buildTokenizationParametersFromPaymentMethodData(paymentMethodData);
 
-        // TODO: clean up MaskedWalletRequest
         ReadableMap total = details.getMap("total").getMap("amount");
-        final MaskedWalletRequest maskedWalletRequest = MaskedWalletRequest.newBuilder()
-                .setPaymentMethodTokenizationParameters(parameters)
-                .setPhoneNumberRequired(shouldRequestPayerPhone)
-                .setShippingAddressRequired(shouldRequestShipping)
-                .setEstimatedTotalPrice(total.getString("value"))
-                .setCurrencyCode(total.getString("currency"))
-                .build();
-
-        int environment = getEnvironmentFromPaymentMethodData(paymentMethodData);
-        if (mGoogleApiClient == null) {
-            buildGoogleApiClient(getCurrentActivity(), environment);
-        }
 
-        Wallet.Payments.loadMaskedWallet(mGoogleApiClient, maskedWalletRequest, LOAD_MASKED_WALLET_REQUEST_CODE);
-    }
+        try {
+            JSONObject paymentDataRequest = getBaseRequest();
+            paymentDataRequest.put("allowedPaymentMethods", new JSONArray().put(getCardPaymentMethod(paymentMethodData)));
+            paymentDataRequest.put("transactionInfo", getTransactionInfo(total));
+            paymentDataRequest.put("merchantInfo", getMerchantInfo());
 
-    @ReactMethod
-    public void getFullWalletAndroid(
-            String googleTransactionId,
-            ReadableMap paymentMethodData,
-            ReadableMap details,
-            Callback errorCallback,
-            Callback successCallback
-    ) {
-        mGetFullWalletSuccessCallback = successCallback;
-        mGetFullWalletErrorCallback = errorCallback;
 
-        ReadableMap total = details.getMap("total").getMap("amount");
-        Log.i(REACT_CLASS, "ANDROID PAY getFullWalletAndroid" + details.getMap("total").getMap("amount"));
-
-        FullWalletRequest fullWalletRequest = FullWalletRequest.newBuilder()
-                .setGoogleTransactionId(googleTransactionId)
-                .setCart(Cart.newBuilder()
-                        .setCurrencyCode(total.getString("currency"))
-                        .setTotalPrice(total.getString("value"))
-                        .setLineItems(buildLineItems(details.getArray("displayItems")))
-                        .build())
-                .build();
-
-        int environment = getEnvironmentFromPaymentMethodData(paymentMethodData);
-        if (mGoogleApiClient == null) {
-            buildGoogleApiClient(getCurrentActivity(), environment);
+            paymentDataRequest.put("shippingAddressRequired", shouldRequestShipping);
+
+            if (shouldRequestShipping) {
+                JSONObject shippingAddressParameters = new JSONObject();
+                shippingAddressParameters.put("phoneNumberRequired", shouldRequestPayerPhone);
+
+                // JSONArray allowedCountryCodes = new JSONArray(Constants.SHIPPING_SUPPORTED_COUNTRIES);
+                // shippingAddressParameters.put("allowedCountryCodes", allowedCountryCodes);
+                paymentDataRequest.put("shippingAddressParameters", shippingAddressParameters);
+            }
+
+            PaymentDataRequest request =
+                    PaymentDataRequest.fromJson(paymentDataRequest.toString());
+
+            int environment = getEnvironmentFromPaymentMethodData(paymentMethodData);
+            createPaymentsClient(getCurrentActivity(), environment);
+
+
+            AutoResolveHelper.resolveTask(
+                    mPaymentsClient.loadPaymentData(request), getCurrentActivity(), LOAD_MASKED_WALLET_REQUEST_CODE);
+        } catch (Exception e) {
+            errorCallback.invoke(e.getMessage());
         }
 
-        Wallet.Payments.loadFullWallet(mGoogleApiClient, fullWalletRequest, LOAD_FULL_WALLET_REQUEST_CODE);
     }
 
     // Private Method
     // ---------------------------------------------------------------------------------------------
-    private static PaymentMethodTokenizationParameters buildTokenizationParametersFromPaymentMethodData(ReadableMap paymentMethodData) {
-        ReadableMap tokenizationParameters = paymentMethodData.getMap("paymentMethodTokenizationParameters");
+
+    private static JSONObject getBaseRequest() throws JSONException {
+        return new JSONObject().put("apiVersion", 2).put("apiVersionMinor", 0);
+    }
+
+    private static JSONArray getAllowedCardAuthMethods(ReadableArray allowedAuthMethods) {
+        JSONArray res = new JSONArray();
+
+        int size = allowedAuthMethods.size();
+        for (int i = 0; i < size; ++i) {
+            String allowedAuthMethod = allowedAuthMethods.getString(i);
+            switch (allowedAuthMethod) {
+                case "panOnly":
+                    res.put("PAN_ONLY");
+                    break;
+                case "cryptogram3ds":
+                    res.put("CRYPTOGRAM_3DS");
+                    break;
+                default:
+            }
+        }
+
+        return res;
+    }
+
+    private static JSONArray getAllowedCardNetworks(ReadableArray allowedCardNetworks) {
+        JSONArray res = new JSONArray();
+
+        int size = allowedCardNetworks.size();
+        for (int i = 0; i < size; ++i) {
+            String allowedCardNetwork = allowedCardNetworks.getString(i);
+            switch (allowedCardNetwork) {
+                case "visa":
+                    res.put("VISA");
+                    break;
+                case "mastercard":
+                    res.put("MASTERCARD");
+                    break;
+                case "amex":
+                    res.put("AMEX");
+                    break;
+                case "discover":
+                    res.put("DISCOVER");
+                    break;
+                case "interac":
+                    res.put("INTERAC");
+                    break;
+                case "jcb":
+                    res.put("JCB");
+                    break;
+                default:
+                    res.put("OTHER");
+            }
+        }
+
+        return res;
+    }
+
+    private static JSONObject getBaseCardPaymentMethod(ReadableMap paymentMethodData) throws JSONException {
+        JSONObject cardPaymentMethod = new JSONObject();
+        cardPaymentMethod.put("type", "CARD");
+
+        JSONObject parameters = new JSONObject();
+
+        ReadableArray allowedAuthMethods = paymentMethodData.getArray("allowedAuthMethods");
+        if (allowedAuthMethods != null) {
+            parameters.put("allowedAuthMethods", getAllowedCardAuthMethods(allowedAuthMethods));
+        }
+        ReadableArray allowedCardNetworks = paymentMethodData.getArray("supportedNetworks");
+        if (allowedCardNetworks != null) {
+            parameters.put("allowedCardNetworks", getAllowedCardNetworks(allowedCardNetworks));
+        }
+
+        cardPaymentMethod.put("parameters", parameters);
+
+        return cardPaymentMethod;
+    }
+
+    private static JSONObject getGatewayTokenizationSpecification(ReadableMap tokenizationParameters) throws JSONException {
         String tokenizationType = tokenizationParameters.getString("tokenizationType");
+        ReadableMap parameters = tokenizationParameters.getMap("parameters");
 
+        JSONObject res = new JSONObject();
 
         if (tokenizationType.equals("GATEWAY_TOKEN")) {
-            ReadableMap parameters = tokenizationParameters.getMap("parameters");
-            PaymentMethodTokenizationParameters.Builder parametersBuilder = PaymentMethodTokenizationParameters.newBuilder()
-                    .setPaymentMethodTokenizationType(PaymentMethodTokenizationType.PAYMENT_GATEWAY)
-                    .addParameter("gateway", parameters.getString("gateway"));
+            res.put("type", "PAYMENT_GATEWAY");
+
+            JSONObject param = new JSONObject();
+            param.put("gateway", parameters.getString("gateway"));
 
             ReadableMapKeySetIterator iterator = parameters.keySetIterator();
 
             while (iterator.hasNextKey()) {
                 String key = iterator.nextKey();
-
-                parametersBuilder.addParameter(key, parameters.getString(key));
+                param.put(key, parameters.getString(key));
             }
 
-            return parametersBuilder.build();
-
+            res.put("parameters", param);
         } else {
-            String publicKey = tokenizationParameters.getMap("parameters").getString("publicKey");
+            res.put("type", "DIRECT");
+            JSONObject param = new JSONObject();
+            param.put("protocolVersion", "ECv2");
+            param.put("publicKey", parameters.getString("publicKey"));
 
-            return PaymentMethodTokenizationParameters.newBuilder()
-                    .setPaymentMethodTokenizationType(PaymentMethodTokenizationType.NETWORK_TOKEN)
-                    .addParameter("publicKey", publicKey)
-                    .build();
+            res.put("parameters", param);
         }
+
+        return res;
     }
 
-    private static List buildLineItems(ReadableArray displayItems) {
-        List<LineItem> list = new ArrayList<LineItem>();
+    private static JSONObject getCardPaymentMethod(ReadableMap paymentMethodData) throws JSONException {
+        JSONObject cardPaymentMethod = getBaseCardPaymentMethod(paymentMethodData);
 
+        ReadableMap tokenizationParameters = paymentMethodData.getMap("paymentMethodTokenizationParameters");
+        cardPaymentMethod.put("tokenizationSpecification", getGatewayTokenizationSpecification(tokenizationParameters));
 
-        for (int i = 0; i < (displayItems.size() - 1); i++) {
-            ReadableMap displayItem = displayItems.getMap(i);
-            ReadableMap amount = displayItem.getMap("amount");
+        return cardPaymentMethod;
+    }
 
-            list.add(LineItem.newBuilder()
-                    .setCurrencyCode(amount.getString("currency"))
-                    .setDescription(displayItem.getString("label"))
-                    .setQuantity("1")
-                    .setUnitPrice(amount.getString("value"))
-                    .setTotalPrice(amount.getString("value"))
-                    .build());
-        }
+    private static JSONObject getTransactionInfo(ReadableMap total) throws JSONException {
+        JSONObject transactionInfo = new JSONObject();
 
-        Log.i(REACT_CLASS, "ANDROID PAY getFullWalletAndroid" + list);
+        transactionInfo.put("totalPriceStatus", "FINAL");
+        transactionInfo.put("totalPrice", total.getString("value"));
+        transactionInfo.put("currencyCode", total.getString("currency"));
 
-        return list;
+        return transactionInfo;
     }
 
-    private static WritableNativeMap buildAddressFromUserAddress(UserAddress userAddress) {
-        WritableNativeMap address = new WritableNativeMap();
-
-        address.putString("recipient", userAddress.getName());
-        address.putString("organization", userAddress.getCompanyName());
-        address.putString("addressLine", userAddress.getAddress1());
-        address.putString("city", userAddress.getLocality());
-        address.putString("region", userAddress.getAdministrativeArea());
-        address.putString("country", userAddress.getCountryCode());
-        address.putString("postalCode", userAddress.getPostalCode());
-        address.putString("phone", userAddress.getPhoneNumber());
-        address.putNull("languageCode");
-        address.putString("sortingCode", userAddress.getSortingCode());
-        address.putString("dependentLocality", userAddress.getLocality());
-
-        return address;
+    private static JSONObject getMerchantInfo() throws JSONException {
+        return new JSONObject().put("merchantName", "Example Merchant");
     }
 
+
     private void sendEvent(
             ReactApplicationContext reactContext,
             String eventName,
@@ -336,36 +453,9 @@ public class ReactNativePaymentsModule extends ReactContextBaseJavaModule implem
                 : WalletConstants.ENVIRONMENT_PRODUCTION;
     }
 
-    // Google API Client
-    // ---------------------------------------------------------------------------------------------
-    private void buildGoogleApiClient(Activity currentActivity, int environment) {
-        mGoogleApiClient = new GoogleApiClient.Builder(currentActivity)
-                .addConnectionCallbacks(this)
-                .addOnConnectionFailedListener(this)
-                .addApi(Wallet.API, new Wallet.WalletOptions.Builder()
-                        .setEnvironment(environment)
-                        .setTheme(WalletConstants.THEME_LIGHT)
-                        .build())
-                .build();
-        mGoogleApiClient.connect();
-    }
-
-    @Override
-    public void onConnected(Bundle connectionHint) {
-//        mLastLocation = LocationServices.FusedLocationApi.getLastLocation(mGoogleApiClient);
-    }
-
-
-    @Override
-    public void onConnectionFailed(ConnectionResult result) {
-        // Refer to Google Play documentation for what errors can be logged
-        Log.i(REACT_CLASS, "Connection failed: ConnectionResult.getErrorCode() = " + result.getErrorCode());
-    }
-
-    @Override
-    public void onConnectionSuspended(int cause) {
-        // Attempts to reconnect if a disconnect occurs
-        Log.i(REACT_CLASS, "Connection suspended");
-        mGoogleApiClient.connect();
+    protected void createPaymentsClient(Activity activity, int environment) {
+        Wallet.WalletOptions walletOptions =
+                new Wallet.WalletOptions.Builder().setEnvironment(environment).build();
+        mPaymentsClient =  Wallet.getPaymentsClient(activity, walletOptions);
     }
 }
diff --git a/android/src/main/res/values/strings.xml b/android/src/main/res/values/strings.xml
new file mode 100644
index 0000000000000000000000000000000000000000..439eefd27d9f1157d19fe3736b26ab1252769a43
--- /dev/null
+++ b/android/src/main/res/values/strings.xml
@@ -0,0 +1,3 @@
+<resources>
+    <string name="app_name">react-native-payments</string>
+</resources>
\ No newline at end of file
diff --git a/ios/GatewayManager.m b/ios/GatewayManager.m
index d23c4aee20b32a10f6a8a131ed292a887bf7ee16..350d4a412c2028316a21e41ff482324325be5067 100644
--- a/ios/GatewayManager.m
+++ b/ios/GatewayManager.m
@@ -1,8 +1,6 @@
 #import "GatewayManager.h"
 
-#if __has_include(<Stripe/Stripe.h>)
-#import <Stripe/Stripe.h>
-#endif
+@import Stripe;
 
 #if __has_include(<BraintreeApplePay/BraintreeApplePay.h>)
 #import <BraintreeApplePay/BraintreeApplePay.h>
@@ -14,9 +12,7 @@ + (NSArray *)getSupportedGateways
 {
     NSMutableArray *supportedGateways = [NSMutableArray array];
 
-#if __has_include(<Stripe/Stripe.h>)
     [supportedGateways addObject:@"stripe"];
-#endif
 
 #if __has_include(<BraintreeApplePay/BraintreeApplePay.h>)
     [supportedGateways addObject:@"braintree"];
@@ -28,11 +24,9 @@ + (NSArray *)getSupportedGateways
 - (void)configureGateway:(NSDictionary *_Nonnull)gatewayParameters
       merchantIdentifier:(NSString *_Nonnull)merchantId
 {
-#if __has_include(<Stripe/Stripe.h>)
     if ([gatewayParameters[@"gateway"] isEqualToString:@"stripe"]) {
         [self configureStripeGateway:gatewayParameters merchantIdentifier:merchantId];
     }
-#endif
 
 #if __has_include(<BraintreeApplePay/BraintreeApplePay.h>)
     if ([gatewayParameters[@"gateway"] isEqualToString:@"braintree"]) {
@@ -44,9 +38,7 @@ - (void)configureGateway:(NSDictionary *_Nonnull)gatewayParameters
 - (void)createTokenWithPayment:(PKPayment *_Nonnull)payment
                     completion:(void (^_Nullable)(NSString * _Nullable token, NSError * _Nullable error))completion
 {
-#if __has_include(<Stripe/Stripe.h>)
     [self createStripeTokenWithPayment:payment completion:completion];
-#endif
 
 #if __has_include(<BraintreeApplePay/BraintreeApplePay.h>)
     [self createBraintreeTokenWithPayment:payment completion:completion];
@@ -57,16 +49,13 @@ - (void)createTokenWithPayment:(PKPayment *_Nonnull)payment
 - (void)configureStripeGateway:(NSDictionary *_Nonnull)gatewayParameters
             merchantIdentifier:(NSString *_Nonnull)merchantId
 {
-#if __has_include(<Stripe/Stripe.h>)
     NSString *stripePublishableKey = gatewayParameters[@"stripe:publishableKey"];
-    [[STPPaymentConfiguration sharedConfiguration] setPublishableKey:stripePublishableKey];
+    [[STPAPIClient sharedClient] setPublishableKey:stripePublishableKey];
     [[STPPaymentConfiguration sharedConfiguration] setAppleMerchantIdentifier:merchantId];
-#endif
 }
 
 - (void)createStripeTokenWithPayment:(PKPayment *)payment completion:(void (^)(NSString * _Nullable, NSError * _Nullable))completion
 {
-#if __has_include(<Stripe/Stripe.h>)
     [[STPAPIClient sharedClient] createTokenWithPayment:payment completion:^(STPToken * _Nullable token, NSError * _Nullable error)
     {
         if (error) {
@@ -75,7 +64,6 @@ - (void)createStripeTokenWithPayment:(PKPayment *)payment completion:(void (^)(N
             completion(token.tokenId, nil);
         }
     }];
-#endif
 }
 
 // Braintree
diff --git a/ios/ReactNativePayments.m b/ios/ReactNativePayments.m
index c309e3c0d1e21757793115ad0ee6da7dd1e3b1de..c346cbb0c5506fe2a3002b12b54cbb6613497746 100644
--- a/ios/ReactNativePayments.m
+++ b/ios/ReactNativePayments.m
@@ -46,6 +46,8 @@ - (NSDictionary *)constantsToExport
         [self.gatewayManager configureGateway:gatewayParameters merchantIdentifier:merchantId];
     }
     
+    
+    
     self.paymentRequest = [[PKPaymentRequest alloc] init];
     self.paymentRequest.merchantIdentifier = merchantId;
     self.paymentRequest.merchantCapabilities = PKMerchantCapability3DS;
@@ -246,13 +248,13 @@ - (NSArray *_Nonnull)getSupportedNetworksFromMethodData:(NSDictionary *_Nonnull)
     }
     
     if (iOSVersion >= 10.3) {
-        [supportedNetworksMapping setObject:PKPaymentNetworkCarteBancaire forKey:@"cartebancaires"];
+        [supportedNetworksMapping setObject:PKPaymentNetworkCarteBancaires forKey:@"cartebancaires"];
         [supportedNetworksMapping setObject:PKPaymentNetworkIDCredit forKey:@"idcredit"];
         [supportedNetworksMapping setObject:PKPaymentNetworkQuicPay forKey:@"quicpay"];
     }
     
     if (iOSVersion >= 11) {
-        [supportedNetworksMapping setObject:PKPaymentNetworkCarteBancaires forKey:@"cartebancaires"];
+        [supportedNetworksMapping setObject:PKPaymentNetworkCartesBancaires forKey:@"cartebancaires"];
     }
 
     if (iOSVersion >= 12.1) {
@@ -336,11 +338,11 @@ - (void)setRequiredShippingAddressFieldsFromOptions:(NSDictionary *_Nonnull)opti
 {
     // Request Shipping
     if (options[@"requestShipping"]) {
-        self.paymentRequest.requiredShippingAddressFields = PKAddressFieldPostalAddress;
+        self.paymentRequest.requiredShippingContactFields = [NSSet setWithArray:@[PKContactFieldPostalAddress]];
     }
 
     if (options[@"requestBilling"]) {
-        self.paymentRequest.requiredBillingAddressFields = PKAddressFieldPostalAddress;
+        self.paymentRequest.requiredBillingContactFields = [NSSet setWithArray:@[PKContactFieldPostalAddress]];
     }
     
     if (options[@"requestPayerName"]) {
@@ -431,11 +433,11 @@ - (NSDictionary *_Nonnull)paymentMethodToString:(PKPaymentMethod *_Nonnull)payme
 
 - (NSString *_Nonnull)paymentMethodTypeToString:(PKPaymentMethodType)paymentMethodType
 {
-    NSArray *arr = @[@"PKPaymentMethodTypeUnknown",
-                     @"PKPaymentMethodTypeDebit",
-                     @"PKPaymentMethodTypeCredit",
-                     @"PKPaymentMethodTypePrepaid",
-                     @"PKPaymentMethodTypeStore"];
+    NSArray *arr = @[@"unknown",
+                     @"debit",
+                     @"credit",
+                     @"prepaid",
+                     @"store"];
     return (NSString *)[arr objectAtIndex:paymentMethodType];
 }
 
