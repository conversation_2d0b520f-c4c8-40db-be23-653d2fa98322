diff --git a/.DS_Store b/.DS_Store
new file mode 100644
index 0000000000000000000000000000000000000000..08a0a3030dd89baa89275621c513dc3115b9f1cb
--- /dev/null
+++ b/.DS_Store
@@ -0,0 +1 @@
+   Bud1                                                                      o i dbwspbl                                                                                                                                                                           a n d r o i dbwspblob   �bplist00�]ShowStatusBar[ShowToolbar[ShowTabView_ContainerShowSidebar\WindowBounds[ShowSidebar		_{{307, 13}, {920, 436}}	#/;R_klmno�             
               �    a n d r o i dvSrnlong                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  @      �                                        @      �                                          @      �                                          @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   E                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         DSDB                                 `          �                                         @      �                                          @      �                                          @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
\ No newline at end of file
diff --git a/android/src/main/java/com/dylanvann/fastimage/FastImageDiskCacheFactory.java b/android/src/main/java/com/dylanvann/fastimage/FastImageDiskCacheFactory.java
new file mode 100644
index 0000000000000000000000000000000000000000..0db68264b1f11547b72ed5f669c8fdb484aae372
--- /dev/null
+++ b/android/src/main/java/com/dylanvann/fastimage/FastImageDiskCacheFactory.java
@@ -0,0 +1,26 @@
+package com.dylanvann.fastimage;
+
+import com.bumptech.glide.load.engine.cache.DiskCache;
+
+import java.lang.ref.WeakReference;
+
+public class FastImageDiskCacheFactory implements DiskCache.Factory  {
+    private final DiskCache.Factory factory;
+
+    public FastImageDiskCacheFactory(DiskCache.Factory factory) {
+        this.factory = factory;
+    }
+
+    private static WeakReference<DiskCache> diskCache = null;
+
+    public static DiskCache getDiskCache() {
+        return diskCache.get();
+    }
+
+    @Override
+    public DiskCache build() {
+        DiskCache diskCache = factory.build();
+        FastImageDiskCacheFactory.diskCache = new WeakReference<>(diskCache);
+        return diskCache;
+    }
+}
\ No newline at end of file
diff --git a/android/src/main/java/com/dylanvann/fastimage/FastImageGlideModule.java b/android/src/main/java/com/dylanvann/fastimage/FastImageGlideModule.java
index 6708bb8795e13574cb8ce51c69333d11c43f1053..346f8833c3862fdfed15b1ad62321db42d77c3cb 100644
--- a/android/src/main/java/com/dylanvann/fastimage/FastImageGlideModule.java
+++ b/android/src/main/java/com/dylanvann/fastimage/FastImageGlideModule.java
@@ -1,9 +1,18 @@
 package com.dylanvann.fastimage;
 
+import android.content.Context;
+import androidx.annotation.NonNull;
+import com.bumptech.glide.GlideBuilder;
 import com.bumptech.glide.annotation.GlideModule;
+import com.bumptech.glide.load.engine.cache.InternalCacheDiskCacheFactory;
 import com.bumptech.glide.module.AppGlideModule;
 
 // We need an AppGlideModule to be present for progress events to work.
 @GlideModule
 public final class FastImageGlideModule extends AppGlideModule {
+    @Override
+    public void applyOptions(@NonNull Context context, @NonNull GlideBuilder builder) {
+        super.applyOptions(context, builder);
+        builder.setDiskCache(new FastImageDiskCacheFactory(new InternalCacheDiskCacheFactory(context)));
+    }
 }
diff --git a/android/src/main/java/com/dylanvann/fastimage/FastImageRequestListener.java b/android/src/main/java/com/dylanvann/fastimage/FastImageRequestListener.java
index 361417be6c0045457a16281bfcea56ccbf6f21c1..110a9682cd0ba7e1d7c9fb69ae354b9bd66d7fa3 100644
--- a/android/src/main/java/com/dylanvann/fastimage/FastImageRequestListener.java
+++ b/android/src/main/java/com/dylanvann/fastimage/FastImageRequestListener.java
@@ -40,6 +40,7 @@ public class FastImageRequestListener implements RequestListener<Drawable> {
         ThemedReactContext context = (ThemedReactContext) view.getContext();
         RCTEventEmitter eventEmitter = context.getJSModule(RCTEventEmitter.class);
         int viewId = view.getId();
+        view.onLoad();
         eventEmitter.receiveEvent(viewId, REACT_ON_ERROR_EVENT, new WritableNativeMap());
         eventEmitter.receiveEvent(viewId, REACT_ON_LOAD_END_EVENT, new WritableNativeMap());
         return false;
diff --git a/android/src/main/java/com/dylanvann/fastimage/FastImageViewConverter.java b/android/src/main/java/com/dylanvann/fastimage/FastImageViewConverter.java
index d86f66f336ac310a3105fed8b8ac9a259b6c928b..50a6d240c8317eb61ed0c4fbd3f34480c4f37135 100644
--- a/android/src/main/java/com/dylanvann/fastimage/FastImageViewConverter.java
+++ b/android/src/main/java/com/dylanvann/fastimage/FastImageViewConverter.java
@@ -126,6 +126,18 @@ class FastImageViewConverter {
         return options;                
     }
 
+    static RequestOptions getImageResizeOptions(ReadableMap imageSizeOverride) {
+        if (imageSizeOverride != null && imageSizeOverride.hasKey("width") && imageSizeOverride.hasKey("height")) {
+            return new RequestOptions().override(
+                    imageSizeOverride.getInt("width"),
+                    imageSizeOverride.getInt("height")
+            );
+        }
+
+        // No Image Size Override Required
+        return new RequestOptions();
+    }
+
     private static FastImageCacheControl getCacheControl(ReadableMap source) {
         return getValueFromSource("cache", "immutable", FAST_IMAGE_CACHE_CONTROL_MAP, source);
     }
diff --git a/android/src/main/java/com/dylanvann/fastimage/FastImageViewManager.java b/android/src/main/java/com/dylanvann/fastimage/FastImageViewManager.java
index f97ec717af379a27611b5f4b8b6c0e75475ce654..033d52437c04efa3d6639cc27206bded5b3e4b4e 100644
--- a/android/src/main/java/com/dylanvann/fastimage/FastImageViewManager.java
+++ b/android/src/main/java/com/dylanvann/fastimage/FastImageViewManager.java
@@ -3,9 +3,12 @@ package com.dylanvann.fastimage;
 import android.app.Activity;
 import android.content.Context;
 import android.content.ContextWrapper;
+import android.graphics.Color;
 import android.graphics.PorterDuff;
 import android.os.Build;
 
+import androidx.annotation.NonNull;
+
 import com.bumptech.glide.Glide;
 import com.bumptech.glide.RequestManager;
 import com.bumptech.glide.load.model.GlideUrl;
@@ -51,7 +54,9 @@ class FastImageViewManager extends SimpleViewManager<FastImageViewWithUrl> imple
             requestManager = Glide.with(reactContext);
         }
 
-        return new FastImageViewWithUrl(reactContext);
+        FastImageViewWithUrl view =  new FastImageViewWithUrl(reactContext);
+        view.setRequestManager(requestManager);
+        return view;
     }
 
     @ReactProp(name = "source")
@@ -76,6 +81,8 @@ class FastImageViewManager extends SimpleViewManager<FastImageViewWithUrl> imple
 
         // Cancel existing request.
         view.glideUrl = glideUrl;
+        view.imageSource = imageSource;
+        view.source = source;
         if (requestManager != null) {
             requestManager.clear(view);
         }
@@ -89,25 +96,17 @@ class FastImageViewManager extends SimpleViewManager<FastImageViewWithUrl> imple
             List<FastImageViewWithUrl> newViewsForKeys = new ArrayList<>(Collections.singletonList(view));
             VIEWS_FOR_URLS.put(key, newViewsForKeys);
         }
+    }
 
-        ThemedReactContext context = (ThemedReactContext) view.getContext();
-        RCTEventEmitter eventEmitter = context.getJSModule(RCTEventEmitter.class);
-        int viewId = view.getId();
-        eventEmitter.receiveEvent(viewId, REACT_ON_LOAD_START_EVENT, new WritableNativeMap());
+    @ReactProp(name = "resizeImageAndroid")
+    public void setImageResize(FastImageViewWithUrl view, ReadableMap imageSizeOverride) {
+        view.imageSizeOverride = imageSizeOverride;
+    }
 
-        if (requestManager != null) {
-            requestManager
-                    // This will make this work for remote and local images. e.g.
-                    //    - file:///
-                    //    - content://
-                    //    - res:/
-                    //    - android.resource://
-                    //    - data:image/png;base64
-                    .load(imageSource.getSourceForLoad())
-                    .apply(FastImageViewConverter.getOptions(context, imageSource, source))
-                    .listener(new FastImageRequestListener(key))
-                    .into(view);
-        }
+    @Override
+    protected void onAfterUpdateTransaction(@NonNull FastImageViewWithUrl view) {
+        super.onAfterUpdateTransaction(view);
+        view.load();
     }
 
     @ReactProp(name = "tintColor", customType = "Color")
@@ -125,6 +124,16 @@ class FastImageViewManager extends SimpleViewManager<FastImageViewWithUrl> imple
         view.setScaleType(scaleType);
     }
 
+    @ReactProp(name = "imageBackgroundColor")
+    public void setImageBackgroundColor(FastImageViewWithUrl view, @Nullable String imageBackgroundColor) {
+        if (view != null && imageBackgroundColor != null)view.imageBackgroundColor = Color.parseColor(imageBackgroundColor);
+    }
+
+    @ReactProp(name = "borderRadius")
+    public void setBorderRadiusValue(FastImageViewWithUrl view, @Nullable float borderRadius) {
+        view.borderRadius = borderRadius;
+    }
+
     @Override
     public void onDropViewInstance(FastImageViewWithUrl view) {
         // This will cancel existing requests.
diff --git a/android/src/main/java/com/dylanvann/fastimage/FastImageViewModule.java b/android/src/main/java/com/dylanvann/fastimage/FastImageViewModule.java
index 5452c792215a6e5eabe4ef1dad8ac068dbcf9efb..d28e9e8163f0d62afc3e12622b47abe210d0c095 100644
--- a/android/src/main/java/com/dylanvann/fastimage/FastImageViewModule.java
+++ b/android/src/main/java/com/dylanvann/fastimage/FastImageViewModule.java
@@ -1,22 +1,51 @@
 package com.dylanvann.fastimage;
 
 import android.app.Activity;
+import android.content.ComponentCallbacks2;
+import android.content.res.Configuration;
+import android.graphics.drawable.Drawable;
+import android.util.Log;
+
+import androidx.annotation.NonNull;
+import androidx.annotation.Nullable;
 
 import com.bumptech.glide.Glide;
+import com.bumptech.glide.RequestManager;
+import com.bumptech.glide.load.DataSource;
+import com.bumptech.glide.load.Key;
+import com.bumptech.glide.load.engine.GlideException;
+import com.bumptech.glide.load.engine.cache.DiskCache;
 import com.bumptech.glide.load.model.GlideUrl;
+import com.bumptech.glide.manager.RequestTracker;
+import com.bumptech.glide.request.RequestListener;
+import com.bumptech.glide.request.target.Target;
+import com.facebook.react.bridge.Arguments;
+import com.facebook.react.bridge.LifecycleEventListener;
+import com.facebook.react.bridge.Promise;
 import com.facebook.react.bridge.ReactApplicationContext;
 import com.facebook.react.bridge.ReactContextBaseJavaModule;
 import com.facebook.react.bridge.ReactMethod;
 import com.facebook.react.bridge.ReadableArray;
 import com.facebook.react.bridge.ReadableMap;
-import com.facebook.react.views.imagehelper.ImageSource;
+import com.facebook.react.bridge.WritableArray;
+
+import java.io.File;
+import java.io.FileInputStream;
+import java.io.FileOutputStream;
+import java.io.IOException;
+import java.io.OutputStream;
+import java.lang.reflect.Field;
+import java.util.HashMap;
 
-class FastImageViewModule extends ReactContextBaseJavaModule {
+class FastImageViewModule extends ReactContextBaseJavaModule implements ComponentCallbacks2 {
 
     private static final String REACT_CLASS = "FastImageView";
 
     FastImageViewModule(ReactApplicationContext reactContext) {
         super(reactContext);
+        Glide.with(reactContext).load(android.R.drawable.btn_default).preload();
+
+        reactContext.registerComponentCallbacks(this);
     }
 
     @Override
@@ -24,13 +53,25 @@ class FastImageViewModule extends ReactContextBaseJavaModule {
         return REACT_CLASS;
     }
 
+    private void preloadCallback (ReadableArray sources, HashMap<GlideUrl, String> results, Promise promise) {
+        if (results.size() == sources.size()) {
+            WritableArray result = Arguments.createArray();
+            for (int j = 0; j < sources.size(); j++) {
+                final FastImageSource source1 = FastImageViewConverter.getImageSource(getCurrentActivity(), sources.getMap(j));
+                result.pushString(results.get(source1.getGlideUrl()));
+            }
+            promise.resolve(result);
+        }
+    }
+
     @ReactMethod
-    public void preload(final ReadableArray sources) {
+    public void preload(final ReadableArray sources, final Promise promise) {
         final Activity activity = getCurrentActivity();
         if (activity == null) return;
         activity.runOnUiThread(new Runnable() {
             @Override
             public void run() {
+                final HashMap<GlideUrl, String> results = new HashMap<>();
                 for (int i = 0; i < sources.size(); i++) {
                     final ReadableMap source = sources.getMap(i);
                     final FastImageSource imageSource = FastImageViewConverter.getImageSource(activity, source);
@@ -48,9 +89,104 @@ class FastImageViewModule extends ReactContextBaseJavaModule {
                                     imageSource.isResource() ? imageSource.getUri() : imageSource.getGlideUrl()
                             )
                             .apply(FastImageViewConverter.getOptions(activity, imageSource, source))
+                            .addListener(new RequestListener<Drawable>() {
+                                @Override
+                                public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
+                                    if (model instanceof GlideUrl) {
+                                        GlideUrl key = (GlideUrl) model;
+                                        results.put(key, null);
+                                        FastImageViewModule.this.preloadCallback(sources, results, promise);
+                                    }
+                                    return false;
+                                }
+
+                                @Override
+                                public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
+                                    if (model instanceof GlideUrl) {
+                                        GlideUrl key = (GlideUrl) model;
+                                        File cachedFile = FastImageDiskCacheFactory.getDiskCache().get(key);
+                                        if (cachedFile != null && cachedFile.exists()) {
+                                            results.put(key, cachedFile.getAbsolutePath());
+                                        } else {
+                                            results.put(key, null);
+                                        }
+                                        FastImageViewModule.this.preloadCallback(sources, results, promise);
+                                    }
+                                    return false;
+                                }
+                            })
                             .preload();
                 }
             }
         });
     }
+
+    @ReactMethod
+    public void put(String url, final String source, Promise promise) {
+        Key key = new GlideUrl(url);
+        FastImageDiskCacheFactory.getDiskCache().put(key, new DiskCache.Writer() {
+            @Override
+            public boolean write(@NonNull File file) {
+                try {
+                    FileInputStream input = new FileInputStream(source);
+                    OutputStream output = new FileOutputStream(file);
+                    byte[] buffer=new byte[10240];
+                    int size=0;
+                    while((size=input.read(buffer))!=-1){
+                        output.write(buffer, 0, size);
+                    }
+                    output.flush();
+                    output.close();
+                } catch (IOException e) {
+                    e.printStackTrace();
+                    return false;
+                }
+                return true;
+            }
+        });
+        File cachedFile = FastImageDiskCacheFactory.getDiskCache().get(key);
+        if (cachedFile != null && cachedFile.exists()) {
+            promise.resolve(cachedFile.getAbsolutePath());
+        } else {
+            promise.reject("error", "file not found");
+        }
+    }
+
+    @ReactMethod
+    public void cancelLoad(Promise promise) {
+        try {
+            RequestManager requestManager = Glide.with(getCurrentActivity().getApplicationContext());
+            Field f =  requestManager.getClass().getDeclaredField("requestTracker");
+            f.setAccessible(true);
+            RequestTracker tracker = (RequestTracker)f.get(requestManager);
+            tracker.clearRequests();
+        } catch (Exception e) {
+            Log.d(REACT_CLASS, "cancelLoad", e);
+        }
+        promise.resolve(null);
+    }
+
+    @Override
+    public void onTrimMemory(int level) {
+        if (getReactApplicationContextIfActiveOrWarn() == null) return;
+        if (level == TRIM_MEMORY_UI_HIDDEN || level == TRIM_MEMORY_COMPLETE) {
+
+            getReactApplicationContext().runOnUiQueueThread(new Runnable() {
+                @Override
+                public void run() {
+                    Glide.get(getReactApplicationContext()).clearMemory();
+                }
+            });
+        }
+    }
+
+    @Override
+    public void onConfigurationChanged(@NonNull Configuration newConfig) {
+
+    }
+
+    @Override
+    public void onLowMemory() {
+        Glide.get(getReactApplicationContext()).onLowMemory();
+    }
 }
diff --git a/android/src/main/java/com/dylanvann/fastimage/FastImageViewWithUrl.java b/android/src/main/java/com/dylanvann/fastimage/FastImageViewWithUrl.java
index 0b74d92f90cec40562cb7e69fa89e245d241e664..7e49ed6a87833e1b6ba955edfdf089cf1a0281d2 100644
--- a/android/src/main/java/com/dylanvann/fastimage/FastImageViewWithUrl.java
+++ b/android/src/main/java/com/dylanvann/fastimage/FastImageViewWithUrl.java
@@ -1,14 +1,92 @@
 package com.dylanvann.fastimage;
 
+import android.annotation.SuppressLint;
 import android.content.Context;
-import android.widget.ImageView;
+import android.graphics.Paint;
+import android.graphics.drawable.Drawable;
+import android.graphics.drawable.ShapeDrawable;
+import android.graphics.drawable.shapes.RoundRectShape;
 
+import androidx.appcompat.widget.AppCompatImageView;
+
+import com.bumptech.glide.RequestBuilder;
+import com.bumptech.glide.RequestManager;
 import com.bumptech.glide.load.model.GlideUrl;
+import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
+import com.facebook.react.bridge.ReadableMap;
+import com.facebook.react.bridge.WritableNativeMap;
+import com.facebook.react.uimanager.ThemedReactContext;
+import com.facebook.react.uimanager.events.RCTEventEmitter;
+
+import java.lang.ref.WeakReference;
 
-class FastImageViewWithUrl extends ImageView {
+class FastImageViewWithUrl extends AppCompatImageView {
+    public FastImageSource imageSource;
+    public ReadableMap source;
     public GlideUrl glideUrl;
 
+    public ReadableMap imageSizeOverride = null;
+
+    public int imageBackgroundColor = -1;
+    public float borderRadius = 0;
+
+    private static final String REACT_ON_LOAD_START_EVENT = "onFastImageLoadStart";
+
+    private WeakReference<RequestManager> requestManager;
+
     public FastImageViewWithUrl(Context context) {
         super(context);
     }
+
+    public void setRequestManager(RequestManager requestManager) {
+        this.requestManager = new WeakReference<>(requestManager);
+    }
+
+    public void onLoad() {
+        if (imageBackgroundColor != -1) {
+            if (borderRadius == 0) {
+                this.setBackgroundColor(imageBackgroundColor);
+            } else {
+                float r = dip2px(borderRadius);
+                RoundRectShape rr = new RoundRectShape(new float[] { r, r, r, r, r, r, r, r }, null, null);
+                ShapeDrawable drawable = new ShapeDrawable(rr);
+                drawable.getPaint().setColor(imageBackgroundColor);
+                drawable.getPaint().setStyle(Paint.Style.FILL);
+                this.setBackground(drawable);
+            }
+        }
+    }
+
+    @SuppressLint("CheckResult")
+    public void load() {
+        ThemedReactContext context = (ThemedReactContext) this.getContext();
+        RCTEventEmitter eventEmitter = context.getJSModule(RCTEventEmitter.class);
+        int viewId = this.getId();
+
+        eventEmitter.receiveEvent(viewId, REACT_ON_LOAD_START_EVENT, new WritableNativeMap());
+
+        if (requestManager != null && requestManager.get() != null) {
+            // This will make this work for remote and local images. e.g.
+            //    - file:///
+            //    - content://
+            //    - res:/
+            //    - android.resource://
+            //    - data:image/png;base64
+            RequestBuilder<Drawable> builder = requestManager.get()
+                    .load(imageSource.getSourceForLoad())
+                    .placeholder(this.getDrawable())
+                    .apply(FastImageViewConverter.getOptions(context, imageSource, source))
+                    .listener(new FastImageRequestListener(glideUrl.toStringUrl())).dontAnimate();
+
+//            if (imageSizeOverride != null) builder.apply(FastImageViewConverter.getImageResizeOptions(imageSizeOverride));
+            if (borderRadius > 0) builder.transform(new RoundedCorners(dip2px(borderRadius)));
+
+            builder.into(this);
+        }
+    }
+
+    public int dip2px(float dpValue) {
+        final float scale = getContext().getResources().getDisplayMetrics().density;
+        return (int) (dpValue * scale + 0.5f);
+    }
 }
diff --git a/dist/index.cjs.js b/dist/index.cjs.js
index aa79218d45aabbe205a3fc6002918812250bb12c..d0f78becf11668a91081a763043e9a91160819c6 100644
--- a/dist/index.cjs.js
+++ b/dist/index.cjs.js
@@ -94,6 +94,8 @@ FastImage.cacheControl = cacheControl;
 FastImage.priority = priority;
 
 FastImage.preload = sources => FastImageViewNativeModule.preload(sources);
+FastImage.put = (url, source) => FastImageViewNativeModule.put(url, source);
+FastImage.cancelLoad = () => FastImageViewNativeModule.cancelLoad();
 
 const styles = reactNative.StyleSheet.create({
   imageContainer: {
diff --git a/dist/index.d.ts b/dist/index.d.ts
index 0b1afd5e1867df4d2f16675a44c9990761e05f0e..e64e359b5cf23930a4bf7a52057e8817d139539e 100644
--- a/dist/index.d.ts
+++ b/dist/index.d.ts
@@ -95,6 +95,8 @@ interface FastImageStaticProperties {
     priority: typeof priority;
     cacheControl: typeof cacheControl;
     preload: (sources: Source[]) => void;
+    put: (url, source) => void;
+    cancelLoad: () => void;
 }
 declare const FastImage: React.ComponentType<FastImageProps> & FastImageStaticProperties;
 export default FastImage;
diff --git a/dist/index.js b/dist/index.js
index acd3b640e92f672130f8ec333a4bff3503af1950..09f06781e1fe10145d6e9d7372f1943efa8f40b3 100644
--- a/dist/index.js
+++ b/dist/index.js
@@ -87,6 +87,8 @@ FastImage.cacheControl = cacheControl;
 FastImage.priority = priority;
 
 FastImage.preload = sources => FastImageViewNativeModule.preload(sources);
+FastImage.put = (url, source) => FastImageViewNativeModule.put(url, source);
+FastImage.cancelLoad = sources => FastImageViewNativeModule.cancelLoad();
 
 const styles = StyleSheet.create({
   imageContainer: {
diff --git a/ios/FastImage/FFFastImageView.m b/ios/FastImage/FFFastImageView.m
index c5f79b43198641e42254ef10b562c095027f31a6..14b8fe642b89bd3e67e50334e05fe70d9d823fd2 100644
--- a/ios/FastImage/FFFastImageView.m
+++ b/ios/FastImage/FFFastImageView.m
@@ -118,7 +118,7 @@ - (void)reloadImage
 
         // Load base64 images.
         NSString* url = [_source.url absoluteString];
-        if (url && [url hasPrefix:@"data:image"]) {
+        if (url && ([url hasPrefix:@"data:image"] || [url hasPrefix:@"file://"])) {
             if (self.onFastImageLoadStart) {
                 self.onFastImageLoadStart(@{});
                 self.hasSentOnLoadStart = YES;
diff --git a/ios/FastImage/FFFastImageViewManager.m b/ios/FastImage/FFFastImageViewManager.m
index 4cb522078144e428d03c55e00234af2c191d1aee..56207512176f120e473e640cd973f0dadeeea3fe 100644
--- a/ios/FastImage/FFFastImageViewManager.m
+++ b/ios/FastImage/FFFastImageViewManager.m
@@ -2,11 +2,25 @@
 #import "FFFastImageView.h"
 
 #import <SDWebImage/SDWebImagePrefetcher.h>
+#import <SDWebImage/SDImageCache.h>
 
 @implementation FFFastImageViewManager
 
 RCT_EXPORT_MODULE(FastImageView)
 
+- (instancetype) init{
+  self = [super init];
+  if (self) {
+    [[SDWebImagePrefetcher sharedImagePrefetcher] setMaxConcurrentPrefetchCount:20];
+  }
+  return self;
+}
+
++ (BOOL)requiresMainQueueSetup
+{
+  return NO;
+}
+
 - (FFFastImageView*)view {
   return [[FFFastImageView alloc] init];
 }
@@ -20,7 +34,7 @@ - (FFFastImageView*)view {
 RCT_EXPORT_VIEW_PROPERTY(onFastImageLoadEnd, RCTDirectEventBlock)
 RCT_REMAP_VIEW_PROPERTY(tintColor, imageColor, UIColor)
 
-RCT_EXPORT_METHOD(preload:(nonnull NSArray<FFFastImageSource *> *)sources)
+RCT_EXPORT_METHOD(preload:(nonnull NSArray<FFFastImageSource *> *)sources resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject)
 {
     NSMutableArray *urls = [NSMutableArray arrayWithCapacity:sources.count];
 
@@ -31,7 +45,30 @@ - (FFFastImageView*)view {
         [urls setObject:source.url atIndexedSubscript:idx];
     }];
 
-    [[SDWebImagePrefetcher sharedImagePrefetcher] prefetchURLs:urls];
+    [[SDWebImagePrefetcher sharedImagePrefetcher] prefetchURLs:urls progress:^(NSUInteger noOfFinishedUrls, NSUInteger noOfTotalUrls) {
+        // RCTLogInfo(@"preload progress %ld / %ld", noOfFinishedUrls, noOfTotalUrls);
+    } completed:^(NSUInteger noOfFinishedUrls, NSUInteger noOfSkippedUrls) {
+        NSMutableArray *paths = [NSMutableArray arrayWithCapacity:sources.count];
+        __block NSInteger checkFinishCount = 0;
+        SDImageCacheCheckCompletionBlock completionBlock = ^(BOOL isInCache) {
+            NSString* cachePath = [[SDImageCache sharedImageCache] cachePathForKey:sources[checkFinishCount].url.absoluteString];
+            [paths addObject:(isInCache ? cachePath : [NSNull null])];
+            checkFinishCount++;
+            if (checkFinishCount == urls.count) {
+                resolve(paths);
+            }
+        };
+        
+        for (int i = 0; i < urls.count; i++) {
+            [[SDImageCache sharedImageCache] diskImageExistsWithKey:sources[i].url.absoluteString completion:completionBlock];
+        }
+    }];
+}
+
+RCT_EXPORT_METHOD(cancelLoad:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject)
+{
+    [[SDWebImagePrefetcher sharedImagePrefetcher] cancelPrefetching];
+    resolve(nil);
 }
 
 @end
