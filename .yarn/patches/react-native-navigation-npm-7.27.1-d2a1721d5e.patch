diff --git a/lib/android/app/src/main/java/com/reactnativenavigation/react/JsDevReloadHandler.java b/lib/android/app/src/main/java/com/reactnativenavigation/react/JsDevReloadHandler.java
index 412b42faf45ac1c4cc1f27e5586f2c0921948455..c562ccd5511e95caf539310e6606dff32a780fc7 100644
--- a/lib/android/app/src/main/java/com/reactnativenavigation/react/JsDevReloadHandler.java
+++ b/lib/android/app/src/main/java/com/reactnativenavigation/react/JsDevReloadHandler.java
@@ -7,6 +7,7 @@ import android.content.Intent;
 import android.content.IntentFilter;
 import android.view.KeyEvent;
 import android.widget.EditText;
+import android.os.Build;
 
 import com.facebook.react.devsupport.interfaces.DevSupportManager;
 import com.reactnativenavigation.utils.UiUtils;
@@ -49,7 +50,11 @@ public class JsDevReloadHandler extends JsDevReloadHandlerFacade {
     }
 
 	public void onActivityResumed(Activity activity) {
-		activity.registerReceiver(reloadReceiver, new IntentFilter(RELOAD_BROADCAST));
+		if (Build.VERSION.SDK_INT >= 34 && activity.getApplicationInfo().targetSdkVersion >= 34) {
+            activity.registerReceiver(reloadReceiver, new IntentFilter(RELOAD_BROADCAST), Context.RECEIVER_EXPORTED);
+        } else {
+            activity.registerReceiver(reloadReceiver, new IntentFilter(RELOAD_BROADCAST));
+        }
 	}
 
 	public void onActivityPaused(Activity activity) {
diff --git a/lib/android/app/src/main/java/com/reactnativenavigation/react/NavigationModule.java b/lib/android/app/src/main/java/com/reactnativenavigation/react/NavigationModule.java
index a34598c665120e0cdb6406b07afa68a8bcd6fe02..fd5653ed2f34e61fb5e5dc6903ee3ee409737ac0 100644
--- a/lib/android/app/src/main/java/com/reactnativenavigation/react/NavigationModule.java
+++ b/lib/android/app/src/main/java/com/reactnativenavigation/react/NavigationModule.java
@@ -209,6 +209,29 @@ public class NavigationModule extends ReactContextBaseJavaModule {
         handle(() -> navigator().dismissAllOverlays(new NativeCommandListener("dismissAllOverlays", commandId, promise, eventEmitter, now)));
     }
 
+    @ReactMethod
+    public void lockBottomTabs(String commandId, Promise promise) {
+        handle(() -> {
+            int index = navigator().lockBottomTabs();
+            if (index > -1) {
+                promise.resolve(index);
+            } else {
+                promise.reject(new Error());
+            }
+        });
+    }
+
+    @ReactMethod
+    public void unlockBottomTabs(String commandId, Promise promise) {
+        handle(() -> {
+            if (navigator().unlockBottomTabs()) {
+                promise.resolve(true);
+            } else {
+                promise.reject(new Error());
+            }
+        });
+    }
+
     private Navigator navigator() {
         return activity().getNavigator();
     }
diff --git a/lib/android/app/src/main/java/com/reactnativenavigation/viewcontrollers/bottomtabs/BottomTabsController.java b/lib/android/app/src/main/java/com/reactnativenavigation/viewcontrollers/bottomtabs/BottomTabsController.java
index e61df575d94ba90c102fcd701b618eb523f4dc29..0d2e1ccc545d0c7a371ff3fed97893c2e90dc3a8 100644
--- a/lib/android/app/src/main/java/com/reactnativenavigation/viewcontrollers/bottomtabs/BottomTabsController.java
+++ b/lib/android/app/src/main/java/com/reactnativenavigation/viewcontrollers/bottomtabs/BottomTabsController.java
@@ -49,6 +49,7 @@ public class BottomTabsController extends ParentController<BottomTabsLayout> imp
     private final BottomTabsAttacher tabsAttacher;
     private final BottomTabsPresenter presenter;
     private final BottomTabPresenter tabPresenter;
+    private boolean locked = false;
 
     public BottomTabsAnimator getAnimator() {
         return presenter.getAnimator();
@@ -196,6 +197,8 @@ public class BottomTabsController extends ParentController<BottomTabsLayout> imp
 
     @Override
     public boolean onTabSelected(int index, boolean wasSelected) {
+        if (this.locked) return false;
+
         ViewController<?> stack = tabs.get(index);
         BottomTabOptions options = stack.resolveCurrentOptions().bottomTabOptions;
 
@@ -304,6 +307,15 @@ public class BottomTabsController extends ParentController<BottomTabsLayout> imp
         return presenter.getPopAnimation(appearingOptions, disappearingOptions);
     }
 
+    public int lock() {
+        this.locked = true;
+        return getSelectedIndex();
+    }
+
+    public void unlock() {
+        this.locked = false;
+    }
+
     @RestrictTo(RestrictTo.Scope.TESTS)
     public BottomTabs getBottomTabs() {
         return bottomTabs;
diff --git a/lib/android/app/src/main/java/com/reactnativenavigation/viewcontrollers/navigator/Navigator.java b/lib/android/app/src/main/java/com/reactnativenavigation/viewcontrollers/navigator/Navigator.java
index 80e6acfd6246b0e79cca56c638a0f38655bd9084..157583a3fe041c3fad74de2b32fa6e6197bee638 100644
--- a/lib/android/app/src/main/java/com/reactnativenavigation/viewcontrollers/navigator/Navigator.java
+++ b/lib/android/app/src/main/java/com/reactnativenavigation/viewcontrollers/navigator/Navigator.java
@@ -19,6 +19,7 @@ import com.reactnativenavigation.react.CommandListenerAdapter;
 import com.reactnativenavigation.react.events.EventEmitter;
 import com.reactnativenavigation.utils.CompatUtils;
 import com.reactnativenavigation.utils.Functions.Func1;
+import com.reactnativenavigation.viewcontrollers.bottomtabs.BottomTabsController;
 import com.reactnativenavigation.viewcontrollers.child.ChildControllersRegistry;
 import com.reactnativenavigation.viewcontrollers.modal.ModalStack;
 import com.reactnativenavigation.viewcontrollers.overlay.OverlayManager;
@@ -223,6 +224,22 @@ public class Navigator extends ParentController<ViewGroup> {
         overlayManager.dismissAll(overlaysLayout, listener);
     }
 
+    public int lockBottomTabs() {
+        BottomTabsController controller = (BottomTabsController)findController("BottomTabs");
+        if (controller != null) {
+            return controller.lock();
+        }
+        return -1;
+    }
+
+    public boolean unlockBottomTabs() {
+        BottomTabsController controller = (BottomTabsController)findController("BottomTabs");
+        if (controller != null) {
+            controller.unlock();
+        }
+        return true;
+    }
+
     @Nullable
     @Override
     public ViewController<?> findController(String id) {
diff --git a/lib/android/app/src/main/java/com/reactnativenavigation/viewcontrollers/stack/StackController.java b/lib/android/app/src/main/java/com/reactnativenavigation/viewcontrollers/stack/StackController.java
index d812cbea8c651078a32f135747fea10ee262df4c..181112e1653967d94e0e7b869345faded1fde877 100644
--- a/lib/android/app/src/main/java/com/reactnativenavigation/viewcontrollers/stack/StackController.java
+++ b/lib/android/app/src/main/java/com/reactnativenavigation/viewcontrollers/stack/StackController.java
@@ -134,6 +134,8 @@ public class StackController extends ParentController<StackLayout> {
             if (options.fabOptions.hasValue()) {
                 fabPresenter.mergeOptions(options.fabOptions, child, getView());
             }
+        } else if (peek() == child) {
+            presenter.applyInitialChildLayoutOptions(options);
         }
         performOnParentController(parent ->
                 parent.mergeChildOptions(
diff --git a/lib/android/app/src/main/java/com/reactnativenavigation/viewcontrollers/stack/topbar/button/ButtonPresenter.kt b/lib/android/app/src/main/java/com/reactnativenavigation/viewcontrollers/stack/topbar/button/ButtonPresenter.kt
index e1520a4ccebdf32e4c538a69249d3a306a21ec08..b2ec600bd8449fee846f26d94233df8edf5345f3 100644
--- a/lib/android/app/src/main/java/com/reactnativenavigation/viewcontrollers/stack/topbar/button/ButtonPresenter.kt
+++ b/lib/android/app/src/main/java/com/reactnativenavigation/viewcontrollers/stack/topbar/button/ButtonPresenter.kt
@@ -74,7 +74,7 @@ open class ButtonPresenter(private val context: Context, private val button: But
     private fun applyAccessibilityLabel(menuItem: MenuItem) {
         if (button.accessibilityLabel.hasValue()) {
             if (button.component.hasValue()) {
-                menuItem.actionView.contentDescription = button.accessibilityLabel.get()
+                menuItem.actionView?.contentDescription = button.accessibilityLabel.get()
             } else {
                 MenuItemCompat.setContentDescription(menuItem, button.accessibilityLabel.get())
             }
diff --git a/lib/android/app/src/main/java/com/reactnativenavigation/views/animations/BaseViewAnimator.kt b/lib/android/app/src/main/java/com/reactnativenavigation/views/animations/BaseViewAnimator.kt
index 8d6c25607d30e0b717b3d40cd9a4bc4fbf3b76e9..c30fcad0c75a39fabe4f55b8d158bab31d0e7b7c 100644
--- a/lib/android/app/src/main/java/com/reactnativenavigation/views/animations/BaseViewAnimator.kt
+++ b/lib/android/app/src/main/java/com/reactnativenavigation/views/animations/BaseViewAnimator.kt
@@ -43,17 +43,17 @@ open class BaseViewAnimator<T : View>(
     private inner class AnimatorListener(private val startState: AnimationState, private val endVisibility: Int) : AnimatorListenerAdapter() {
         var isCancelled = false
 
-        override fun onAnimationStart(animation: Animator?) {
+        override fun onAnimationStart(animation: Animator) {
             view.resetViewProperties()
             view.visibility = View.VISIBLE
             animationState = startState
         }
 
-        override fun onAnimationCancel(animation: Animator?) {
+        override fun onAnimationCancel(animation: Animator) {
             isCancelled = true
         }
 
-        override fun onAnimationEnd(animation: Animator?) {
+        override fun onAnimationEnd(animation: Animator) {
             if (!isCancelled) {
                 animationState = AnimationState.Idle
                 view.visibility = endVisibility
diff --git a/lib/android/app/src/main/java/com/reactnativenavigation/views/stack/topbar/titlebar/IconBackgroundDrawable.kt b/lib/android/app/src/main/java/com/reactnativenavigation/views/stack/topbar/titlebar/IconBackgroundDrawable.kt
index a469d4c404c9e9d1e2d4e4d4af3cfa35fb069a5f..e3f606a7fb1221d8b9fbbdaf730e225faab798b2 100644
--- a/lib/android/app/src/main/java/com/reactnativenavigation/views/stack/topbar/titlebar/IconBackgroundDrawable.kt
+++ b/lib/android/app/src/main/java/com/reactnativenavigation/views/stack/topbar/titlebar/IconBackgroundDrawable.kt
@@ -68,7 +68,7 @@ class IconBackgroundDrawable(
         super.setBounds(r)
     }
 
-    override fun onBoundsChange(bounds: Rect?) {
+    override fun onBoundsChange(bounds: Rect) {
         if (bounds != null) {
             backgroundRect = Rect((bounds.width() - backgroundWidth) / 2,
                     (bounds.height() - backgroundHeight) / 2,
diff --git a/lib/dist/src/Navigation.js b/lib/dist/src/Navigation.js
index ceabecc4a055e18c3ebaa7a09a0a10468630f938..bd234f6e9bb9a51c6cf78827457ec81fa3072eec 100644
--- a/lib/dist/src/Navigation.js
+++ b/lib/dist/src/Navigation.js
@@ -208,5 +208,17 @@ class NavigationRoot {
     constantsSync() {
         return Constants_1.Constants.getSync();
     }
+    /**
+     * lock bottom tabs, touch event will be ignore
+     */
+    lockBottomTabs() {
+        return this.commands.lockBottomTabs();
+    }
+    /**
+     * unlock bottom tabs, touch event will be recover
+     */
+    unlockBottomTabs() {
+        return this.commands.unlockBottomTabs();
+    }
 }
 exports.NavigationRoot = NavigationRoot;
diff --git a/lib/dist/src/NavigationDelegate.js b/lib/dist/src/NavigationDelegate.js
index e4b83a973ce6a462c95660d77f5b2fedb0e5cc77..4e1c956f69ac584e5f1988cd206a4401249a7f94 100644
--- a/lib/dist/src/NavigationDelegate.js
+++ b/lib/dist/src/NavigationDelegate.js
@@ -139,6 +139,12 @@ class NavigationDelegate {
     getLaunchArgs() {
         return this.concreteNavigation.getLaunchArgs();
     }
+    lockBottomTabs() {
+        return this.concreteNavigation.lockBottomTabs();
+    }
+    unlockBottomTabs() {
+        return this.concreteNavigation.unlockBottomTabs();
+    }
     /**
      * Obtain the events registry instance
      */
diff --git a/lib/dist/src/adapters/NativeCommandsSender.js b/lib/dist/src/adapters/NativeCommandsSender.js
index 3ef9607dc38c09e1e7f0c8b357e643a2ee79b98d..dab792cc584c95671f2705a183f51259454730ce 100644
--- a/lib/dist/src/adapters/NativeCommandsSender.js
+++ b/lib/dist/src/adapters/NativeCommandsSender.js
@@ -52,5 +52,11 @@ class NativeCommandsSender {
     getLaunchArgs(commandId) {
         return this.nativeCommandsModule.getLaunchArgs(commandId);
     }
+    lockBottomTabs(commandId) {
+        return this.nativeCommandsModule.lockBottomTabs(commandId);
+    }
+    unlockBottomTabs(commandId) {
+        return this.nativeCommandsModule.unlockBottomTabs(commandId);
+    }
 }
 exports.NativeCommandsSender = NativeCommandsSender;
diff --git a/lib/dist/src/commands/Commands.js b/lib/dist/src/commands/Commands.js
index 586bcce49bf8e9bac3347ebeabb7787b65aad412..05591be6ac1b7292b33c8c6a5214a48e78113183 100644
--- a/lib/dist/src/commands/Commands.js
+++ b/lib/dist/src/commands/Commands.js
@@ -82,8 +82,8 @@ class Commands {
         const layoutProcessed = this.layoutProcessor.process(layoutCloned, CommandName_1.CommandName.ShowModal);
         const layoutNode = this.layoutTreeParser.parse(layoutProcessed);
         const commandId = this.uniqueIdProvider.generate(CommandName_1.CommandName.ShowModal);
-        this.commandsObserver.notify(CommandName_1.CommandName.ShowModal, { commandId, layout: layoutNode });
         this.layoutTreeCrawler.crawl(layoutNode, CommandName_1.CommandName.ShowModal);
+        this.commandsObserver.notify(CommandName_1.CommandName.ShowModal, { commandId, layout: layoutNode });
         const result = this.nativeCommandsSender.showModal(commandId, layoutNode);
         return result;
     }
@@ -111,8 +111,8 @@ class Commands {
         const layoutProcessed = this.layoutProcessor.process(input, CommandName_1.CommandName.Push);
         const layout = this.layoutTreeParser.parse(layoutProcessed);
         const commandId = this.uniqueIdProvider.generate(CommandName_1.CommandName.Push);
-        this.commandsObserver.notify(CommandName_1.CommandName.Push, { commandId, componentId, layout });
         this.layoutTreeCrawler.crawl(layout, CommandName_1.CommandName.Push);
+        this.commandsObserver.notify(CommandName_1.CommandName.Push, { commandId, componentId, layout });
         const result = this.nativeCommandsSender.push(commandId, componentId, layout);
         return result;
     }
@@ -162,8 +162,8 @@ class Commands {
         const layoutProcessed = this.layoutProcessor.process(input, CommandName_1.CommandName.ShowOverlay);
         const layout = this.layoutTreeParser.parse(layoutProcessed);
         const commandId = this.uniqueIdProvider.generate(CommandName_1.CommandName.ShowOverlay);
-        this.commandsObserver.notify(CommandName_1.CommandName.ShowOverlay, { commandId, layout });
         this.layoutTreeCrawler.crawl(layout, CommandName_1.CommandName.ShowOverlay);
+        this.commandsObserver.notify(CommandName_1.CommandName.ShowOverlay, { commandId, layout });
         const result = this.nativeCommandsSender.showOverlay(commandId, layout);
         return result;
     }
@@ -185,6 +185,18 @@ class Commands {
         this.commandsObserver.notify(CommandName_1.CommandName.GetLaunchArgs, { commandId });
         return result;
     }
+    lockBottomTabs() {
+        const commandId = this.uniqueIdProvider.generate(CommandName_1.CommandName.LockBottomTabs);
+        const result = this.nativeCommandsSender.lockBottomTabs(commandId);
+        this.commandsObserver.notify(CommandName_1.CommandName.LockBottomTabs, { commandId });
+        return result;
+    }
+    unlockBottomTabs() {
+        const commandId = this.uniqueIdProvider.generate(CommandName_1.CommandName.UnlockBottomTabs);
+        const result = this.nativeCommandsSender.unlockBottomTabs(commandId);
+        this.commandsObserver.notify(CommandName_1.CommandName.UnlockBottomTabs, { commandId });
+        return result;
+    }
 }
 exports.Commands = Commands;
 function cloneLayout(layout) {
diff --git a/lib/dist/src/interfaces/CommandName.js b/lib/dist/src/interfaces/CommandName.js
index 2555be1ce6ff012aed449f9fa9833d8858b37417..2eebaa504056068d23cd8cbeda6d7dd9a6a0701a 100644
--- a/lib/dist/src/interfaces/CommandName.js
+++ b/lib/dist/src/interfaces/CommandName.js
@@ -19,4 +19,6 @@ var CommandName;
     CommandName["DismissOverlay"] = "dismissOverlay";
     CommandName["DismissAllOverlays"] = "dismissAllOverlays";
     CommandName["GetLaunchArgs"] = "getLaunchArgs";
+    CommandName["LockBottomTabs"] = "lockBottomTabs";
+    CommandName["UnlockBottomTabs"] = "unlockBottomTabs";
 })(CommandName = exports.CommandName || (exports.CommandName = {}));
diff --git a/lib/ios/RNNBottomTabsController.h b/lib/ios/RNNBottomTabsController.h
index 0f0ebe1bd82366b0f351288d55458441dfd3ab83..e1aa055033f92982ced34c7f9a76c7fcb10bf6d3 100644
--- a/lib/ios/RNNBottomTabsController.h
+++ b/lib/ios/RNNBottomTabsController.h
@@ -28,6 +28,10 @@
 
 - (void)handleTabBarLongPress:(CGPoint)locationInTabBar;
 
+- (NSUInteger)lock;
+
+- (void)unlock;
+
 @property(nonatomic, strong) NSArray *pendingChildViewControllers;
 
 @end
diff --git a/lib/ios/RNNBottomTabsController.m b/lib/ios/RNNBottomTabsController.m
index 1a2b6bf7ac42ffbd64428df7970364e668d3e6ee..dcf19b67cea494454c0e95cd91929a75c2c39977 100644
--- a/lib/ios/RNNBottomTabsController.m
+++ b/lib/ios/RNNBottomTabsController.m
@@ -1,11 +1,13 @@
 #import "RNNBottomTabsController.h"
 #import "UITabBarController+RNNUtils.h"
+#import "RNNUtils.h"
 
 @interface RNNBottomTabsController ()
 @property(nonatomic, strong) BottomTabPresenter *bottomTabPresenter;
 @property(nonatomic, strong) RNNDotIndicatorPresenter *dotIndicatorPresenter;
 @property(nonatomic) BOOL viewWillAppearOnce;
 @property(nonatomic, strong) UILongPressGestureRecognizer *longPressRecognizer;
+@property (nonatomic) BOOL locked;
 
 @end
 
@@ -53,6 +55,8 @@ - (instancetype)initWithLayoutInfo:(RNNLayoutInfo *)layoutInfo
                                                       action:@selector(handleLongPressGesture:)];
     [self.tabBar addGestureRecognizer:self.longPressRecognizer];
 
+    self.locked = NO;
+
     return self;
 }
 
@@ -163,6 +167,17 @@ - (void)handleTabBarLongPress:(CGPoint)locationInTabBar {
     }
 }
 
+- (NSUInteger)lock
+{
+    self.locked = YES;
+    return _currentTabIndex;
+}
+
+- (void)unlock
+{
+    self.locked = NO;
+}
+
 #pragma mark UITabBarControllerDelegate
 
 - (void)tabBarController:(UITabBarController *)tabBarController
@@ -180,6 +195,7 @@ - (void)handleLongPressGesture:(UILongPressGestureRecognizer *)recognizer {
 
 - (BOOL)tabBarController:(UITabBarController *)tabBarController
     shouldSelectViewController:(UIViewController *)viewController {
+    if (_locked) return NO;
     NSUInteger _index = [tabBarController.viewControllers indexOfObject:viewController];
     BOOL isMoreTab = ![tabBarController.viewControllers containsObject:viewController];
 
diff --git a/lib/ios/RNNBridgeModule.m b/lib/ios/RNNBridgeModule.m
index 3a0db31b709f8c745fa7d95c536732069157c265..20d854be9c6fae0b3338b22dd3049478673e7162 100644
--- a/lib/ios/RNNBridgeModule.m
+++ b/lib/ios/RNNBridgeModule.m
@@ -248,4 +248,17 @@ - (instancetype)initWithCommandsHandler:(RNNCommandsHandler *)commandsHandler {
     return [Constants getConstants];
 }
 
+RCT_EXPORT_METHOD(lockBottomTabs:(NSString*)commandId :(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
+    RCTExecuteOnMainQueue(^{
+        resolve(@([self->_commandsHandler lockBottomTabs]));
+    });
+}
+
+RCT_EXPORT_METHOD(unlockBottomTabs:(NSString*)commandId :(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
+    RCTExecuteOnMainQueue(^{
+        [self->_commandsHandler unlockBottomTabs];
+        resolve(nil);
+    });
+}
+
 @end
diff --git a/lib/ios/RNNCommandsHandler.h b/lib/ios/RNNCommandsHandler.h
index 04842f6483cbc8fad20ed25bdab0fa9ce06e3f33..fece428f15e27ac84c981d7fac2b23f257849c3e 100644
--- a/lib/ios/RNNCommandsHandler.h
+++ b/lib/ios/RNNCommandsHandler.h
@@ -84,4 +84,8 @@
 
 - (void)dismissAllOverlays:(NSString *)commandId;
 
+- (NSInteger)lockBottomTabs;
+
+- (void)unlockBottomTabs;
+
 @end
diff --git a/lib/ios/RNNCommandsHandler.m b/lib/ios/RNNCommandsHandler.m
index 61b135fef53f94890b0becdb33eea11777013a61..8f5fd09b06c05a35e770bd9cb610d620ccbc054d 100644
--- a/lib/ios/RNNCommandsHandler.m
+++ b/lib/ios/RNNCommandsHandler.m
@@ -1,10 +1,12 @@
 #import "RNNCommandsHandler.h"
 #import "AnimationObserver.h"
 #import "RNNAssert.h"
+#import "RNNBottomTabsController.h"
 #import "RNNComponentViewController.h"
 #import "RNNConvert.h"
 #import "RNNDefaultOptionsHelper.h"
 #import "RNNErrorHandler.h"
+#import "RNNUtils.h"
 #import "React/RCTI18nUtil.h"
 #import "UINavigationController+RNNCommands.h"
 #import "UIViewController+RNNOptions.h"
@@ -499,6 +501,18 @@ - (void)dismissAllOverlays:(NSString *)commandId {
     [self->_eventEmitter sendOnNavigationCommandCompletion:dismissAllOverlays commandId:commandId];
 }
 
+- (NSInteger)lockBottomTabs
+{
+    RNNBottomTabsController* viewController = (RNNBottomTabsController*)[_layoutManager findComponentForId:@"BottomTabs"];
+    return [viewController lock];
+}
+
+- (void)unlockBottomTabs
+{
+    RNNBottomTabsController* viewController = (RNNBottomTabsController*)[_layoutManager findComponentForId:@"BottomTabs"];
+    [viewController unlock];
+}
+
 #pragma mark - private
 
 - (void)assertReady {
diff --git a/lib/ios/RNNModalHostViewManagerHandler.m b/lib/ios/RNNModalHostViewManagerHandler.m
index f9779ed17a46525f643c252a09a609b1d7f746c0..b0a8c8e13ff28fddb33b61febd83298908b6b6c2 100644
--- a/lib/ios/RNNModalHostViewManagerHandler.m
+++ b/lib/ios/RNNModalHostViewManagerHandler.m
@@ -11,30 +11,6 @@ - (instancetype)initWithModalManager:(RNNModalManager *)modalManager {
 }
 
 - (void)connectModalHostViewManager:(RCTModalHostViewManager *)modalHostViewManager {
-    modalHostViewManager.presentationBlock =
-        ^(UIViewController *reactViewController, UIViewController *viewController, BOOL animated,
-          dispatch_block_t completionBlock) {
-          if (reactViewController.presentedViewController != viewController &&
-              [self->_modalManager topPresentedVC] != viewController) {
-              [self->_modalManager showModal:viewController
-                                    animated:animated
-                                  completion:^(NSString *_Nonnull componentId) {
-                                    if (completionBlock)
-                                        completionBlock();
-                                  }];
-          }
-        };
-
-    modalHostViewManager.dismissalBlock =
-        ^(UIViewController *reactViewController, UIViewController *viewController, BOOL animated,
-          dispatch_block_t completionBlock) {
-          [self->_modalManager dismissModal:viewController
-                                   animated:animated
-                                 completion:^{
-                                   if (completionBlock)
-                                       completionBlock();
-                                 }];
-        };
 }
 
 @end
diff --git a/lib/ios/RNNModalManagerEventHandler.m b/lib/ios/RNNModalManagerEventHandler.m
index dd905ff04520425825de290da6f4358554677fc1..43a8b2b7f4dfb6bc7caa5f124accec9dd72696e5 100644
--- a/lib/ios/RNNModalManagerEventHandler.m
+++ b/lib/ios/RNNModalManagerEventHandler.m
@@ -13,12 +13,12 @@ - (instancetype)initWithEventEmitter:(RNNEventEmitter *)eventEmitter {
 
 - (void)dismissedModal:(UIViewController *)viewController {
     [_eventEmitter
-        sendModalsDismissedEvent:viewController.topMostViewController.layoutInfo.componentId
+        sendModalsDismissedEvent:viewController.layoutInfo.componentId
          numberOfModalsDismissed:@(1)];
 }
 
 - (void)attemptedToDismissModal:(UIViewController *)viewController {
-    [_eventEmitter sendModalAttemptedToDismissEvent:viewController.topMostViewController.layoutInfo
+    [_eventEmitter sendModalAttemptedToDismissEvent:viewController.layoutInfo
                                                         .componentId];
 }
 
diff --git a/lib/ios/RNNScreenTransition.m b/lib/ios/RNNScreenTransition.m
index a93291e3ab294697df9174ad7f2424ae095ee1a4..08e3b7e855a748817c3d020acbba6703aa98875a 100644
--- a/lib/ios/RNNScreenTransition.m
+++ b/lib/ios/RNNScreenTransition.m
@@ -45,7 +45,7 @@ - (BOOL)hasCustomAnimation {
 }
 
 - (BOOL)shouldWaitForRender {
-    return [self.waitForRender withDefault:NO] || self.hasCustomAnimation;
+    return [self.waitForRender withDefault:NO];
 }
 
 - (NSTimeInterval)maxDuration {
diff --git a/lib/ios/UINavigationController+RNNCommands.m b/lib/ios/UINavigationController+RNNCommands.m
index e7a4f2d562d7652496794d80188ad26f7fdf15a9..650401bdf3f5b4a5f05a8a6e311636e1991b23b2 100644
--- a/lib/ios/UINavigationController+RNNCommands.m
+++ b/lib/ios/UINavigationController+RNNCommands.m
@@ -21,7 +21,9 @@ - (void)push:(UIViewController *)newTop
 
     [self
         performBlock:^{
-          [self pushViewController:newTop animated:animated];
+          dispatch_async(dispatch_get_main_queue(), ^{
+            [self pushViewController:newTop animated:animated];
+          });
         }
             animated:animated
           completion:completion];
diff --git a/lib/ios/UITabBarController+RNNOptions.m b/lib/ios/UITabBarController+RNNOptions.m
index 265841c4d61a48daa4de638494027dc172ceace8..1c81b604fef3cf349ae7301a5617a5205d1a207e 100644
--- a/lib/ios/UITabBarController+RNNOptions.m
+++ b/lib/ios/UITabBarController+RNNOptions.m
@@ -35,8 +35,9 @@ - (void)centerTabItems {
 - (void)showTabBar:(BOOL)animated {
     static const CGFloat animationDuration = 0.15;
     const CGRect tabBarVisibleFrame = CGRectMake(
-        self.tabBar.frame.origin.x, self.view.frame.size.height - self.tabBar.frame.size.height,
+        0, self.view.frame.size.height - self.tabBar.frame.size.height,
         self.tabBar.frame.size.width, self.tabBar.frame.size.height);
+    if (self.tabBar.hidden == YES ) self.tabBar.frame = CGRectMake(0, self.view.frame.size.height, self.tabBar.frame.size.width, self.tabBar.frame.size.height);
     self.tabBar.hidden = NO;
     if (!animated) {
         self.tabBar.frame = tabBarVisibleFrame;
