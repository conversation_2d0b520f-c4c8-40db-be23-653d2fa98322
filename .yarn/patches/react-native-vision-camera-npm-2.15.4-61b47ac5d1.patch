diff --git a/ios/Parsers/AVCaptureColorSpace+descriptor.swift b/ios/Parsers/AVCaptureColorSpace+descriptor.swift
index 13a403b14094ec78d6b1f016eff7d689db268fe0..48fa031b061f0b34b59a54ff5d9313d79450d0cd 100644
--- a/ios/Parsers/AVCaptureColorSpace+descriptor.swift
+++ b/ios/Parsers/AVCaptureColorSpace+descriptor.swift
@@ -38,7 +38,7 @@ extension AVCaptureColorSpace {
     case .sRGB:
       return "srgb"
     default:
-      fatalError("AVCaptureDevice.Position has unknown state.")
+      return "unknown"
     }
   }
 }
diff --git a/ios/Parsers/AVCaptureDevice.DeviceType+descriptor.swift b/ios/Parsers/AVCaptureDevice.DeviceType+descriptor.swift
index ae0f96693d93d399c63104c2922cb2322a76f586..ad1b71163477a88967ccd469a4347ac4faa49723 100644
--- a/ios/Parsers/AVCaptureDevice.DeviceType+descriptor.swift
+++ b/ios/Parsers/AVCaptureDevice.DeviceType+descriptor.swift
@@ -32,7 +32,7 @@ extension AVCaptureDevice.DeviceType {
       return "wide-angle-camera"
     default:
       // e.g. `.builtInTrueDepthCamera`
-      fatalError("AVCaptureDevice.Position has unknown state.")
+      return "unknown"
     }
   }
 }
diff --git a/ios/Parsers/AVCaptureDevice.Position+descriptor.swift b/ios/Parsers/AVCaptureDevice.Position+descriptor.swift
index e0445cbd894685b4faca15f9e28aaddf7cbcb474..8afe3b6a19ab7935e35f2b8a16ec91320437aee3 100644
--- a/ios/Parsers/AVCaptureDevice.Position+descriptor.swift
+++ b/ios/Parsers/AVCaptureDevice.Position+descriptor.swift
@@ -19,7 +19,7 @@ extension AVCaptureDevice.Position {
     case .unspecified:
       return "unspecified"
     @unknown default:
-      fatalError("AVCaptureDevice.Position has unknown state.")
+      return "unknown"
     }
   }
 }
