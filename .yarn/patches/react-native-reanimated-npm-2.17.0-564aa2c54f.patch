diff --git a/ios/LayoutReanimation/REAUIManager.mm b/ios/LayoutReanimation/REAUIManager.mm
index 16ed327a7cfa17ec406891d8a40ab02abbe99761..a6afa846fa83527bd4e4195ac8229eb5a5743470 100644
--- a/ios/LayoutReanimation/REAUIManager.mm
+++ b/ios/LayoutReanimation/REAUIManager.mm
@@ -14,6 +14,9 @@
 #if __has_include(<RNScreens/RNSScreen.h>)
 #import <RNScreens/RNSScreen.h>
 #endif
+#if __has_include("AIRMapMarker.h")
+#import "AIRMapMarker.h"
+#endif
 
 @interface RCTUIManager (REA)
 - (void)_manageChildren:(NSNumber *)containerTag
@@ -125,6 +128,9 @@ - (void)_manageChildren:(NSNumber *)containerTag
       if ([toRemoveChild isKindOfClass:[RCTModalHostView class]]
 #if __has_include(<RNScreens/RNSScreen.h>)
           || ([toRemoveChild isKindOfClass:[RNSScreenView class]])
+#endif
+#if __has_include("AIRMapMarker.h")
+          || ([toRemoveChild isKindOfClass:[AIRMapMarker class]])
 #endif
       ) {
         // we don't want layout animations when removing modals or Screens of native-stack since it brings buggy
diff --git a/ios/REANodesManager.m b/ios/REANodesManager.m
index fda5bb2b8f6b02158b1564a3d7c4576b5f9a265a..147a7774f75f5f0f9ad75ad2d0119719f0285344 100644
--- a/ios/REANodesManager.m
+++ b/ios/REANodesManager.m
@@ -92,6 +92,71 @@ - (void)runSyncUIUpdatesWithObserver:(id<RCTUIManagerObserver>)observer
 
 @end
 
+#ifndef RCT_NEW_ARCH_ENABLED
+
+@interface REASyncUpdateObserver : NSObject <RCTUIManagerObserver>
+@end
+
+@implementation REASyncUpdateObserver {
+  volatile void (^_mounting)(void);
+  volatile BOOL _waitTimedOut;
+  dispatch_semaphore_t _semaphore;
+}
+
+- (instancetype)init
+{
+  self = [super init];
+  if (self) {
+    _mounting = nil;
+    _waitTimedOut = NO;
+    _semaphore = dispatch_semaphore_create(0);
+  }
+  return self;
+}
+
+- (void)dealloc
+{
+  RCTAssert(_mounting == nil, @"Mouting block was set but never executed. This may lead to UI inconsistencies");
+}
+
+- (void)unblockUIThread
+{
+  RCTAssertUIManagerQueue();
+  dispatch_semaphore_signal(_semaphore);
+}
+
+- (void)waitAndMountWithTimeout:(NSTimeInterval)timeout
+{
+  RCTAssertMainQueue();
+  long result = dispatch_semaphore_wait(_semaphore, dispatch_time(DISPATCH_TIME_NOW, timeout * NSEC_PER_SEC));
+  if (result != 0) {
+    @synchronized(self) {
+      _waitTimedOut = YES;
+    }
+  }
+  if (_mounting) {
+    _mounting();
+    _mounting = nil;
+  }
+}
+
+- (BOOL)uiManager:(RCTUIManager *)manager performMountingWithBlock:(RCTUIManagerMountingBlock)block
+{
+  RCTAssertUIManagerQueue();
+  @synchronized(self) {
+    if (_waitTimedOut) {
+      return NO;
+    } else {
+      _mounting = block;
+      return YES;
+    }
+  }
+}
+
+@end
+
+#endif
+
 @interface REANodesManager () <RCTUIManagerObserver>
 
 @end
@@ -108,9 +173,6 @@ @implementation REANodesManager {
   NSMutableArray<REANativeAnimationOp> *_operationsInBatch;
   BOOL _tryRunBatchUpdatesSynchronously;
   REAEventHandler _eventHandler;
-  volatile void (^_mounting)(void);
-  NSObject *_syncLayoutUpdatesWaitLock;
-  volatile BOOL _syncLayoutUpdatesWaitTimedOut;
   NSMutableDictionary<NSNumber *, ComponentUpdate *> *_componentUpdateBuffer;
   volatile atomic_bool _shouldFlushUpdateBuffer;
   NSMutableDictionary<NSNumber *, UIView *> *_viewRegistry;
@@ -130,7 +192,6 @@ - (instancetype)initWithModule:(REAModule *)reanimatedModule uiManager:(RCTUIMan
     _operationsInBatch = [NSMutableArray new];
     _componentUpdateBuffer = [NSMutableDictionary new];
     _viewRegistry = [_uiManager valueForKey:@"_viewRegistry"];
-    _syncLayoutUpdatesWaitLock = [NSObject new];
     _shouldFlushUpdateBuffer = false;
   }
 
@@ -232,18 +293,13 @@ - (void)onAnimationFrame:(CADisplayLink *)displayLink
   }
 }
 
+#ifdef RCT_NEW_ARCH_ENABLED
 - (BOOL)uiManager:(RCTUIManager *)manager performMountingWithBlock:(RCTUIManagerMountingBlock)block
 {
-  RCTAssert(_mounting == nil, @"Mouting block is expected to not be set");
-  @synchronized(_syncLayoutUpdatesWaitLock) {
-    if (_syncLayoutUpdatesWaitTimedOut) {
-      return NO;
-    } else {
-      _mounting = block;
-      return YES;
-    }
-  }
+  // This is a stub implementation for the new architecture
+  return NO;
 }
+#endif
 
 - (void)performOperations
 {
@@ -258,8 +314,7 @@ - (void)performOperations
     _tryRunBatchUpdatesSynchronously = NO;
 
     __weak typeof(self) weakSelf = self;
-    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
-    _syncLayoutUpdatesWaitTimedOut = NO;
+    REASyncUpdateObserver *syncUpdateObserver = [REASyncUpdateObserver new];
     RCTExecuteOnUIManagerQueue(^{
       __typeof__(self) strongSelf = weakSelf;
       if (strongSelf == nil) {
@@ -268,7 +323,7 @@ - (void)performOperations
       BOOL canUpdateSynchronously = trySynchronously && ![strongSelf.uiManager hasEnqueuedUICommands];
 
       if (!canUpdateSynchronously) {
-        dispatch_semaphore_signal(semaphore);
+        [syncUpdateObserver unblockUIThread];
       }
 
       for (int i = 0; i < copiedOperationsQueue.count; i++) {
@@ -276,8 +331,8 @@ - (void)performOperations
       }
 
       if (canUpdateSynchronously) {
-        [strongSelf.uiManager runSyncUIUpdatesWithObserver:strongSelf];
-        dispatch_semaphore_signal(semaphore);
+        [strongSelf.uiManager runSyncUIUpdatesWithObserver:syncUpdateObserver];
+        [syncUpdateObserver unblockUIThread];
       }
       // In case canUpdateSynchronously=true we still have to send uiManagerWillPerformMounting event
       // to observers because some components (e.g. TextInput) update their UIViews only on that event.
@@ -288,17 +343,7 @@ - (void)performOperations
       // from CADisplayLink but it is easier to hardcode it for the time being.
       // The reason why we use frame duration here is that if takes longer than one frame to complete layout tasks
       // there is no point of synchronizing layout with the UI interaction as we get that one frame delay anyways.
-      long result = dispatch_semaphore_wait(semaphore, dispatch_time(DISPATCH_TIME_NOW, 16 * NSEC_PER_MSEC));
-      if (result != 0) {
-        @synchronized(_syncLayoutUpdatesWaitLock) {
-          _syncLayoutUpdatesWaitTimedOut = YES;
-        }
-      }
-    }
-
-    if (_mounting) {
-      _mounting();
-      _mounting = nil;
+      [syncUpdateObserver waitAndMountWithTimeout:0.016];
     }
   }
   _wantRunUpdates = NO;
diff --git a/react-native-reanimated.d.ts b/react-native-reanimated.d.ts
index c33d8306ed219100bb76020a5d7b2be3f6e97d57..9bbf0917c2093ee9c969173c2fce45278aab47ef 100644
--- a/react-native-reanimated.d.ts
+++ b/react-native-reanimated.d.ts
@@ -128,7 +128,7 @@ declare module 'react-native-reanimated' {
 
     export type TransformStyleTypes = TransformsStyle['transform'] extends
       | readonly (infer T)[]
-      | undefined
+      | undefined | string
       ? T
       : never;
     export type AdaptTransforms<T> = {
@@ -138,7 +138,7 @@ declare module 'react-native-reanimated' {
 
     export type AnimateStyle<S> = {
       [K in keyof S]: K extends 'transform'
-        ? AnimatedTransform
+        ? AnimatedTransform | string
         : S[K] extends ReadonlyArray<any>
         ? ReadonlyArray<AnimateStyle<S[K][0]>>
         : S[K] extends object
diff --git a/src/reanimated2/platform-specific/checkVersion.ts b/src/reanimated2/platform-specific/checkVersion.ts
index 1b64bb62d62dcede49ab5066596efb2c499e34ef..c3db77173fa207b8bc45f9aabbd3b43894a46b7b 100644
--- a/src/reanimated2/platform-specific/checkVersion.ts
+++ b/src/reanimated2/platform-specific/checkVersion.ts
@@ -4,6 +4,7 @@ import { version as jsVersion } from '../../../package.json';
  * Checks that native and js versions of reanimated match.
  */
 export function checkVersion(): void {
+  // @ts-ignore
   const cppVersion = global._REANIMATED_VERSION_CPP;
   if (cppVersion === undefined) {
     console.error(
