diff --git a/android/src/main/java/community/revteltech/nfc/NfcManager.java b/android/src/main/java/community/revteltech/nfc/NfcManager.java
index 76481b8e47388dd1e563de0897e1039d02884de7..e3fe82d7232a710c1c37fa8072be99527b8dcf68 100644
--- a/android/src/main/java/community/revteltech/nfc/NfcManager.java
+++ b/android/src/main/java/community/revteltech/nfc/NfcManager.java
@@ -1273,14 +1273,18 @@ class NfcManager extends ReactContextBaseJavaModule implements ActivityEventList
     @Override
     public void onNewIntent(Intent intent) {
         Log.d(LOG_TAG, "onNewIntent " + intent);
-        WritableMap nfcTag = parseNfcIntent(intent);
-        if (nfcTag != null) {
-            if (isForegroundEnabled) {
-                sendEvent("NfcManagerDiscoverTag", nfcTag);
-            } else {
-                sendEvent("NfcManagerDiscoverBackgroundTag", nfcTag);
-                bgTag = nfcTag;
+        try {
+            WritableMap nfcTag = parseNfcIntent(intent);
+            if (nfcTag != null) {
+                if (isForegroundEnabled) {
+                    sendEvent("NfcManagerDiscoverTag", nfcTag);
+                } else {
+                    sendEvent("NfcManagerDiscoverBackgroundTag", nfcTag);
+                    bgTag = nfcTag;
+                }
             }
+        } catch (SecurityException e) {
+            
         }
     }
 
