<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" id="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover">
    <title>Membership</title>
    <script>
    <%  
        var PATH = constants.PATH;
        var DEVICE = constants.DEVICE;
        var pageWidth = DEVICE.WIDTH > 900? 900 : DEVICE.WIDTH ;
        var pageHeight = DEVICE.HEIGHT;
        var	pageScale = pageWidth/320;
        var statusbarHeight = DEVICE.STATUS_BAR_HEIGHT;
        var screenHeight = pageHeight - (DEVICE.IOS ? 0 : statusbarHeight);
        var pageTop = DEVICE.TOP_BAR_HEIGHT;
        var pageBottom = DEVICE.MIN_BOTTOM_SPACE;
        var notchHeight = DEVICE.NOTCH_HEIGHT || 0;
    %>
    </script>
    <style>
        @font-face {
            font-family: Melbourne;
            font-weight: normal;
            src: local('Melbourne'), url('<%- constants.FONTPATH %>Melbourne.otf') format('opentype');
        }
        @font-face {
            font-family: Melbourne;
            font-weight: bold;
            src: local('Melbourne_bold'), url('<%- constants.FONTPATH %>Melbourne_bold.otf') format('opentype');
        }

        * {
            box-sizing: border-box;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        }
    
        [v-cloak] {
            display: none;
        }
        
        html {
            width: 100%;
            height: 100%;
            height: <%- pageHeight %>px;
            font-family: "Melbourne", Tahoma, 'Trebuchet MS', Arial, sans-serif;
            font-weight: normal;
            font-size: 10px;
            font-size: <%- 10 * pageScale %>px;
            color: #FFFFFF;
        }
        
        body {
            width: 100%;
            height: 100%;
            height: <%- pageHeight %>px;
            padding: 0;
            margin: 0;
            background-color: #3D3D3D;
        }
        
        h1,
        h2,
        p {
            margin: 0;
        }
        
        h1 {
            font-size: 2.4rem;
            padding-bottom: 0.2rem;
        }
        
        h2 {
            font-size: 2rem;
            padding: 0 0 0.5rem;
            line-height: 1.5em;
            margin: 0 auto;
        }
        
        .active strong {
            text-transform: uppercase;
        }
        
        .expired {
            font-weight: normal;
        }
        
        .tip {
            font-size: 1.4rem;
            opacity: 0.6
        }
        
        #pPage {
            box-sizing: border-box;
            width: 100%;
            height: <%- screenHeight %>px;
            display: -webkit-flex;
            display: flex;
            -webkit-box-pack: center;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-box-align: center;
            -webkit-align-items: center;
            align-items: center;
            padding-top: <%- statusbarHeight %>px;
            opacity: 0;
            transition: opacity 0.4s;
        }
        
        #member {
            text-align: center;
        }
        
        #statusChart {
            margin: 1rem 0 2rem;
        }
        
        svg {
            width: 100%;
            height: 100%;
        }
        
        text {
            text-anchor: middle;
            alignment-baseline: central;
            font-size: 1.6rem;
        }
        
        text:not(.text1) {
            fill: #FFFFFF;
        }

        g.text {opacity: 0;}
        g.success text.text0 {font-size: 2.6rem; transform: translate(0, -0.8rem);}
        g.success text.text1 {font-size: 3.4rem;}
        g.success text.text2 {font-size: 1.4rem; transform: translate(0, 0.8rem);}
        
        g.inprogress text.text0 {font-size: 1.6rem;}
        g.inprogress text.text1 {font-size: 6rem;}
        g.inprogress text.text2 {font-size: 2.4rem;}
        
        #tList {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            max-height: <%- screenHeight %>px;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 0 0 <%- 10*pageScale + pageBottom %>px;
            overflow-y: scroll;
            -webkit-overflow-scrolling: touch;
            z-index: 2;
        }
        
        #tTitle {
            font-size: 1.4rem;
            opacity: 0.8;
            padding: 1rem 2rem 0.5rem;
        }
        
        #arrow {
            position: relative;
            display: inline-block;
            width: 8px;
            height: 8px;
            border: 2px solid #FFFFFF;
            border-width: 2px 2px 0 0;
        }
        
        #arrow.arrow-up {
            margin-bottom: -1px;
            -webkit-transform: rotate(-45deg);
            transform: rotate(-45deg);
        }
        
        #arrow.arrow-down {
            margin-bottom: 2px;
            -webkit-transform: rotate(135deg);
            transform: rotate(135deg);
        }
        
        #tDetail {
            font-size: 1.6rem;
            overflow: hidden;
        }
        
        #tDetail .item {
            width: 100%;
            padding: 0.4rem 2rem;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            justify-content: space-between;
            align-content: flex-start;
            align-items: baseline;
        }
        
        #tDetail .item .amount {
            padding: 0 0.5rem 0 1.5rem;
        }
        
        #tDetail .item .store {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            flex: 1 1 auto;
        }
        
        #tDetail .item .amount,
        #tDetail .item .date {
            text-align: right;
            flex: 0 0 auto;
        }
        
        #mask {
            background-color: rgba(0, 0, 0, 0);
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            z-index: -1;
            pointer-events: none;
        }
         #offlineStatus {
            position: fixed;
            top: 1.8rem;
            left: 1.5rem;
            height: 1.4em;
            line-height: 1.4em;
            padding: 0 0.64rem;
            border-radius: 0.7rem;
            z-index: 99;
            background-color: rgba(255,0,0,0.8);
            text-align: center;
            font-size: 1em;
            color: #FFFFFF;
        }
    </style>
</head>
<body>
    <div id="offlineStatus" v-if="!isOnline" v-cloak>离线</div>
    <div id="pPage">
        <div id="member">
            <div id="header" v-cloak>
                <h1 class="active" v-if="membershipState === 'active'">{{ program.name }}</h1>
                <h2 class="active" v-if="membershipState === 'active'"><strong>{{ tierName }}</strong> 会员</h2>
                <h1 class="expired" v-if="membershipState !== 'active'" v-bind:style="{ paddingBottom: bottomSpace }">{{ program.name }}<br />会籍已于<strong>{{ endTime }}</strong>过期</h1>
            </div>
            <div id="statusChart"></div>
            <div class="tip" v-cloak>{{refreshedAt? refreshedAt + '更新' : ''}}</div>
        </div>
        <div id="mask" v-if="showTransactions"></div>
        <div id="tList" v-if="showTransactions" v-cloak>
            <div id="tTitle" v-if="orders.length !==1">最近一次消费 <span id="arrow" class="arrow-up"></div>
            <div id="tTitle" v-if="orders.length===1">共一次消费</div>
            <div id="tDetail">
                <div class="item" v-for="order in orders"> <span class="store">{{order.storeName}}</span> <span class="amount">{{currency}}{{order.amount}}</span> <span class="date" v-bind:style="{ minWidth: dateWidth }">{{order.purchaseAt}}</span> </div>
        </div>
    </div>
    <script>
        var fontReady = false;
        document.fonts.ready.then(function() {
            fontReady = true;
            showPage();
        });

    </script>
    <script src="<%- PATH.SHARED.RSRC %>appbridge-1.0.min.js"></script>
     <script src="<%- PATH.SHARED.RSRC %>vue-2.6.10.min.js"></script>
     <script src="<%- PATH.SHARED.RSRC %>d3-5.min.js"></script>
     <script src="<%- PATH.SHARED.RSRC %>moment-2.24.0.min.js"></script>
     <script src="<%- PATH.SHARED.RSRC %>appletHelper-1.0.min.js"></script>
     <script src="<%- PATH.SHARED.RSRC %>chart-1.0.min.js"></script>
     <script>
        var statusbarHeight = <%- statusbarHeight %>;
        var pageWidth = <%- pageWidth %>;
        var pageHeight = <%- pageHeight %>;
        var ios = <%- DEVICE.IOS %>;
        var lang = "zh-cn";
        var colors = ['#02F934', '#FFB800', 'FF6F4C', 'D0021B'];
        var currency = '$';
        var stringTemplate = {
           separater: "|",
           unit: ['次', '次'],
           renew: {
               progress: {
                    amount: "%d前消费 | %c%m | 可延续会籍",
                    transaction: "%d前消费 | %m%u | 可延续会籍",
               },
               success: "恭喜你！ | 已获续会资格 | 新会籍%d起有效",
           },
           upgrade: {
                progress: {
                    amount: "%d前消费 | %c%m | 可升级会籍",
                    transaction: "%d前消费 | %m%u | 可升级会籍",
               },
               success: "恭喜你！ | 已获升级资格 | 新会籍%d起有效",
           }
       }

        var widgetsConfig = <%- JSON.stringify(constants.CARDMASTER.widgets || {})  %>;
        var membershipConfig = widgetsConfig.find(function(widget) {return widget.key === 'memberstatus'});
        var transactionConfig = membershipConfig && membershipConfig.param && membershipConfig.param.showTransactions;
        var showDetail = false;
        var networkStatus = { isOnline: true };
        var pageMerge;
        var mergeData = {};
 
        window.addEventListener('data.changed', function () {
            setupPage($data);
        });

        window.addEventListener('network.changed', function(parem) {
            networkStatus.isOnline = parem.detail.isInternetReachable;
        });

        // actions
        $perkd.do('network.info').then(function(status) {
            networkStatus.isOnline = status.isInternetReachable;
        });

        loadPage();

        function loadPage() {
            var networkStatusMerge = new Vue({el: '#offlineStatus', data: networkStatus});
            showPage();
            setupPage($data);
        }
        
        function showPage() {
            if(fontReady) {
                document.getElementById('pPage').style.opacity = 1;
            }
         }

        function updatePage(d){
            if(d.showTransactions){ renderTranscationDetail(d.orders) };
            if(d.chartData && d.chartData.length>0) { renderChart(d.chartData) };
        }

        function setupPage(d) {
            var showTransactions = transactionConfig && d.orders && d.orders.length >= 1;
            var sameYearOrder = showTransactions? d.orders.every(function(o){o.when.paid.getYear === new Date().getYear()}) : false;
            var orders = showTransactions? prepareOrderData(d.orders) : [];
            var tierInfo = (d.toQualify && d.tierLevel) ? prepareTierInfo(d.toQualify, d.program.tierList, d.tierLevel) : {};
            var chartData = prepareChartData(d.toQualify, tierInfo);
            var expiryDate = d.endTime || "<%- constants.CARD.endTime %>";

            mergeData.program = d.program || { name: "<%- constants.CARDMASTER.name %>"};
            mergeData.membershipState = moment(expiryDate) < moment() ? 'expired' : (d.state || 'active');

            mergeData.tierName = tierInfo.tierName;
            mergeData.endTime =  humaneDate(lang, expiryDate, false, false, -1);
            mergeData.refreshedAt = d.refreshedAt? humaneDate(lang, d.refreshedAt, true) : '';
            mergeData.orders = orders;
            mergeData.currency = currency;
            mergeData.showTransactions = showTransactions;
            mergeData.chartData = chartData;
            mergeData.dateWidth = sameYearOrder? '5.5rem' : '8.5rem';
            mergeData.bottomSpace = (chartData && chartData.length>=1)? '1rem' : '8rem';

            if(!pageMerge) {
                pageMerge = new Vue({
                    el: '#pPage', 
                    data: mergeData,
                    mounted: function() { updatePage(mergeData) },
                    updated: function() { updatePage(mergeData) }
                });
            }
        }

        function prepareOrderData(orders) {
            var xOrders = [];
            orders.forEach(function(o, i){
                xOrders.push({
                    purchaseAt: calendarDate(lang, o.when.paid, false, false, 2),
                    amount: formatNumber(o.amount),
                    storeName: (o.acquired.location) ? o.acquired.location.name : '#' + (i+1)
                })
            });
            return xOrders.reverse();
        }

        function prepareTierInfo(toQualify, oriTierList, tierLevel){
            var tierList = oriTierList || [];
            var tierInex = tierList.findIndex(function(t) {return tierLevel === t.level});
            if(tierInex < 0) return;

            var tierInfo = { 
                tierLevel: tierLevel,
                tierIndex: tierInex,
                tierName: tierInex > 0? tierList[tierInex].name : ''
            };
            
            tierList[tierInex].toQualify.forEach(function(q) {
                tierInfo[q.name] = {
                    type: q.name,
                    kind: q.kind,
                    target: q.value,
                    toTier: q.tierLevel,
                };
                if(q.tierLevel !== undefined) {
                    var toTierIndex = tierList.findIndex(function(t){ return q.tierLevel === t.level});
                    tierInfo[q.name].toTierName = tierList[toTierIndex].name;
                }
            })
            
            toQualify.forEach(function(p){
                if (tierInfo[p.name]) {
                    tierInfo[p.name].value = p.value;
                    tierInfo[p.name].formatValue = formatNumber(Math.ceil(p.value));
                    tierInfo[p.name].by = humaneDate(lang, p.by, false, true, -1);
                    var successDate = moment(p.by).add(1, 'days').toString();
                    tierInfo[p.name].on = humaneDate(lang, successDate, false, true, -1);
                }
            })
            return tierInfo;
        }
        
        function prepareChartData(toQualify, tierInfo) {
            var chartData = [];
            var toQualifyArray = [];
            if(tierInfo.upgrade) toQualifyArray.push(tierInfo.upgrade);
            if(tierInfo.renew) toQualifyArray.push(tierInfo.renew);

            toQualifyArray.length > 0 && toQualifyArray.forEach(function(el, i){
                var text = generateChartText(el, el.type);
                chartData.push({
                    color: colors[(el.toTier || tierInfo.tierLevel) - 1] || colors[0],
                    end: el.target ? ((1 - el.value / el.target) * 2 * Math.PI) : 0,
                    text: (el.value == 0) ? text.success : text.progress,
                    class: (el.value == 0) ? "success" : "inprogress"
                });
            })
            return chartData;
       }

        function generateChartText(typeData, type){
            var text = {};
            text.progress = stringTemplate[type].progress[typeData.kind]
                .replace('%c', currency)
                .replace('%m', typeData.formatValue)
                .replace('%u', stringTemplate.unit[typeData.formatValue > 1? 1 : 0])
                .replace('%d', typeData.by)
                .split(stringTemplate.separater);
            text.success = stringTemplate[type].success
                .replace('%d', typeData.on)
                .split(stringTemplate.separater);

            return text;
       }

        function renderChart(chartData) {
            d3.selectAll('#statusChart').selectAll('svg').remove();

            var saleH = d3.select('#tList').node()? d3.select('#tList').node().getBoundingClientRect().height : 0;
            var headerH = d3.select('#header').node().getBoundingClientRect().height;
            var margin = Math.max(0.15 * Math.min(pageWidth, pageHeight - statusbarHeight), 20);
            var size = Math.floor(Math.min(pageWidth - margin, pageHeight - statusbarHeight - margin - headerH - saleH));
            d3.select("#pPage").style("padding-bottom", saleH + 'px');
            d3.select("#statusChart").style("width", size + 'px').style("height", size + 'px');

            new Chart({
                data: chartData,
                element: d3.select("#statusChart"),
                size: size,
                ios: ios
            });
        }

        function renderTranscationDetail(orders) {
            var minDetailH = d3.select('.item').node().getBoundingClientRect().height;
            var maxDetailH = minDetailH * orders.length;

            d3.select("#tDetail").style("height", minDetailH + 'px'); /* get sales overlay original height */
            var saleH = d3.select('#tList').node().getBoundingClientRect().height;

            if (orders.length < 2) return;

            var maxMove = Math.min(pageHeight - saleH, maxDetailH - minDetailH); /* add click event for sales */
            d3.select("#tTitle").on("click", animate); /* add touch event for sales */
            d3.select("#tList").on("touchstart", touchstarted).on("touchmove", touchmoved).on("touchcancel", touchended).on("touchend", touchended);

            /* sales page swipe & click event */
            var startY = 0;
            var movepath = [];

            function touchstarted() {
                var touchobj = d3.event.changedTouches[0];
                startY = touchobj.pageY;
                if (!showDetail) d3.select("#mask").style("z-index", 1);
            }

            function touchmoved() {
                var touchobj = d3.event.changedTouches[0];
                if (movepath.push(touchobj.pageY) > 2) movepath.shift();
                var movePercent = Math.min(Math.abs(startY - touchobj.pageY) / maxMove, 1);
                if (!showDetail && touchobj.pageY - startY < 0) {
                    d3.select("#tDetail").style("height", minDetailH + Math.min(startY - touchobj.pageY, maxDetailH - minDetailH) + "px");
                    d3.select("#mask").style('background-color', "rgba(0,0,0," + 0.5 * movePercent + ")");
                    d3.select("#tList").style('background-color', "rgba(0,0,0," + 0.5 * (1 - movePercent) + ")");
                }
                if (showDetail && touchobj.pageY - startY > 0) {
                    d3.select("#tDetail").style("height", maxDetailH - Math.min(touchobj.pageY - startY, maxDetailH - minDetailH) + "px");
                    d3.select("#mask").style('background-color', "rgba(0,0,0," + 0.5 * (1 - movePercent) + ")");
                    d3.select("#tList").style('background-color', "rgba(0,0,0," + 0.5 * movePercent + ")");
                }
            }

            function touchended() {
                var touchobj = d3.event.changedTouches[0];
                var secondLastPoint = movepath.shift();
                var endPoint = movepath.pop() || secondLastPoint; /* check if user is scrolling instead of swipe up / down */
                var scrolling = (showDetail && ((pageHeight - saleH) < maxDetailH - minDetailH) && d3.select("#tList").property("scrollTop") != 0);
                if (typeof secondLastPoint != "undefined") {
                    if (endPoint == secondLastPoint) secondLastPoint = startY;
                    var direction = (endPoint < secondLastPoint) ? 'up' : 'down';
                    if (scrolling || (showDetail && direction == 'up') || (!showDetail && direction == 'down')) showDetail = !showDetail;
                    animate();
                };
                if (!showDetail) d3.select("#mask").style("z-index", 0);
            }

            function animate() {
                eleAnmation(d3.select("#tDetail"), "height", showDetail, minDetailH + "px", maxDetailH + "px", 400);
                eleAnmation(d3.select("#mask"), "background-color", showDetail, "rgba(0,0,0,0)", "rgba(0,0,0,0.5)", 200);
                eleAnmation(d3.select("#tList"), "background-color", showDetail, "rgba(0,0,0,0.5)", "rgba(0,0,0,0)", 200);
                showDetail = !showDetail;
            }
       }
    </script>
</body>
</html>