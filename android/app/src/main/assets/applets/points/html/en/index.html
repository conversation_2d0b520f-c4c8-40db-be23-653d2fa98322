<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" id="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover">
    <title>Points</title>
    <script>
        <%  
            var PATH = constants.PATH;
            var DEVICE = constants.DEVICE;
            var pageWidth = DEVICE.WIDTH > 900? 900 : DEVICE.WIDTH ;
            var pageHeight = DEVICE.HEIGHT;
            var	pageScale = pageWidth/320;
            var statusbarHeight = DEVICE.STATUS_BAR_HEIGHT;
            var screenHeight = pageHeight - (DEVICE.IOS ? 0 : statusbarHeight);
            var pageTop = DEVICE.TOP_BAR_HEIGHT;
            var pageBottom = DEVICE.MIN_BOTTOM_SPACE;
            var notchHeight = DEVICE.NOTCH_HEIGHT || 0;
        %>
    </script>
    <link rel="stylesheet" href="<%- PATH.SHARED.RSRC %>swiper-4.5.1.min.css">
    <style>
        @font-face {
            font-family: Melbourne;
            font-weight: normal;
            src: local('Melbourne'), url('<%- constants.FONTPATH %>Melbourne.otf') format('opentype');
        }
        @font-face {
            font-family: Melbourne;
            font-weight: bold;
            src: local('Melbourne_bold'), url('<%- constants.FONTPATH %>Melbourne_bold.otf') format('opentype');
        }
        * {
            box-sizing: border-box;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        }
        
        [v-cloak] {
            display: none;
        }

        html {
            width: 100%;
            height: 100%;
            height: <%- pageHeight %>px;
            font-family: "Melbourne", Roboto, Arial, sans-serif;
            font-weight: normal;
            font-size: 10px;
            font-size: <%- 10 * pageScale %>px;
            color: #FFFFFF;
            background-color: #3D3D3D;
        }

        body {
            width: 100%;
            height: 100%;
            height: <%- pageHeight %>px;
            padding: 0;
            margin: 0;
            background-color: #3D3D3D;
        }

        .title {
            font-size: 3.2rem;
            opacity: 0.8;
        }

        .subtitle {
            font-size: 2.8rem;
            opacity: 0.8;
        }

        .title+.subtitle {
            padding-top: 0.3rem;
        }

        .dataValue {
            font-size: 13rem;
            line-height: 1em;
            fill: #02F934;
        }

        .tip {
            padding: 4rem 2rem 0;
            font-size: 1.4rem;
            opacity: 0.6
        }

        .tip+.tip {
            padding-top: 1rem;
        }

        #pPage {
            box-sizing: border-box;
            width: 100%;
            height: 100%;
            height: <%- screenHeight %>px;
            display: -webkit-flex;
            display: flex;
            -webkit-box-pack: center;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-box-align: center;
            -webkit-align-items: center;
            align-items: center;
            padding-top: <%- statusbarHeight %>px;
            opacity: 0;
            transition: opacity 0.4s;
        }

        .expired {
            padding: 2rem 0 8rem;
            font-size: 2.4rem;
            opacity: 1;
        }

        .swiper-container {
            width: 100%;
            z-index: 0;
        }

        .swiper-wrapper {
            align-items: stretch;
        }

        .swiper-slide {
            height: auto;
            padding: 1rem 0 2rem;
            text-align: center;
            /* Center slide text vertically */
            display: -webkit-box;
            display: -webkit-flex;
            display: flex;
            -webkit-box-pack: center;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-box-align: center;
            -webkit-align-items: center;
            align-items: center;
        }

        .swiper-container-horizontal>.swiper-pagination-bullets,
        .swiper-pagination-custom,
        .swiper-pagination-fraction {
            bottom: 0;
        }

        .swiper-pagination-bullet {
            width: 0.36rem;
            height: 0.36rem;
            background: #FFFFFF;
            opacity: 0.3
        }

        .swiper-pagination-bullet-active {
            opacity: 1;
            background: #FFFFFF;
        }

        .swiper-container-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet {
            margin: 0 1rem;
        }

        .chartCanvas {
            margin: auto;
        }

        .chartDesc {
            padding-top: 2.5rem;
        }

        .chartDesc .title {
            font-size: 2.8rem;
        }

        .chartDesc .subtitle {
            padding-bottom: 2rem;
            font-size: 1.8rem;
        }

        svg {
            width: 100%;
            height: 100%;
        }

        text {
            fill: #FFFFFF;
            text-anchor: middle;
            alignment-baseline: central;
        }

        text.text0 {font-size: 10rem;}
        text.text1 {font-size: 2.8rem; transform: translate(0, -0.5rem)}

        g.text-group {opacity: 0;}

        #tList {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            max-height: <%- screenHeight %>px;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 0 0 <%- 10*pageScale + pageBottom %>px;
            overflow-y: scroll;
            -webkit-overflow-scrolling: touch;
            z-index: 2;
        }

        #tTitle {
            font-size: 1.4rem;
            opacity: 0.8;
            padding: 1rem 2rem 0.5rem;
        }

        #arrow {
            position: relative;
            display: inline-block;
            width: 8px;
            height: 8px;
            border: 2px solid #FFFFFF;
            border-width: 2px 2px 0 0;
        }

        #arrow.arrow-up {
            margin-bottom: -1px;
            -webkit-transform: rotate(-45deg);
            transform: rotate(-45deg);
        }

        #arrow.arrow-down {
            margin-bottom: 2px;
            -webkit-transform: rotate(135deg);
            transform: rotate(135deg);
        }

        #tDetail {
            font-size: 1.6rem;
            overflow: hidden;
        }

        #tDetail .item {
            width: 100%;
            padding: 0.4rem 2rem;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            justify-content: space-between;
            align-content: flex-start;
            align-items: baseline;
        }

        #tDetail .item .amount {
            padding: 0 0 0 1.5rem;
        }

        .earn {
            color: #02F934;
        }

        .deduct {
            color: #FF0000;
        }

        #tDetail .item .store {
            padding: 0 0 0 1rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            flex: 1 1 auto;
        }

        #tDetail .item .amount,
        #tDetail .item .date {
            flex: 0 0 auto;
        }

        #mask {
            background-color: rgba(0, 0, 0, 0);
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            z-index: -1;
            pointer-events: none;
       }
       #offlineStatus {
            position: fixed;
            top: 1.8rem;
            left: 1.5rem;
            height: 1.4em;
            line-height: 1.4em;
            padding: 0 0.64rem;
            border-radius: 0.7rem;
            z-index: 99;
            background-color: rgba(255,0,0,0.8);
            text-align: center;
            font-size: 1em;
            color: #FFFFFF;
        }
    </style>
</head>
<body>
	<div id="offlineStatus" v-if="!isOnline" v-cloak>offline</div>
    <div id="pPage">
        <div class="swiper-container">
            <div class="swiper-wrapper">
                <div class="swiper-slide">
                    <div class="slideContent" style="width: 100%; padding: 0 1rem">
                        <div class="title">You have</div>
                        <svg class="pointValue">
                            <g><text class="dataValue">{{ available }}</text></g>
                        </svg>
                        <div class="subtitle">{{ available > 1? 'points' : 'point' }} available</div>
                        <div class="tip" v-if="pointStatus === 'active'">The above points balance is for your reference only. Please check in store for the latest points balance</div>
                        <div class="tip" v-if="pointStatus!=='active'" v-cloak>Your membership &amp; points<br />expired on <strong>{{ endTime }}</strong></div>
                        <div class="tip" v-if="refreshedAt && pointStatus === 'active'" v-cloak>{{ 'updated ' + refreshedAt }}</div>
                    </div>
                </div>
                <template v-for="item in qualified" v-cloak>
                    <div class="swiper-slide" v-if="item.amount !== 0 && pointStatus ==='active'">
                        <div class="slideContent">
                            <div class="chartCanvas"></div>
                            <div class="chartDesc">
                                <div class="title" v-if="pointStatus==='active'">Congrats!</div>
                                <div class="subtitle" v-if="pointStatus==='active'">You can claim this reward</div>
                                <div class="subtitle" v-if="pointStatus!=='active'">Sorry, your points expired</div>
                            </div>
                        </div>
                    </div>
                </template>
                <template v-for="item in toQualify" v-cloak>
                    <div class="swiper-slide" v-if="item.amount !== 0 && pointStatus === 'active'">
                        <div class="slideContent">
                            <div class="chartCanvas"></div>
                            <div class="chartDesc">
                                <div class="title active">Getting there</div>
                                <div class="subtitle">You need another {{ item.pointsGap + (( item.pointsGap > 1)? ' points' : ' point') }}</div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
            <div class="swiper-pagination" :style="{visibility: showPagination ? 'visible' : 'hidden'}" v-cloak></div>
        </div>
        <div id="mask" v-if="showTransactions"></div>
        <div id="tList" v-if="showTransactions" v-cloak>
            <div id="tTitle" v-if="transactions.length > 1">last purchase&nbsp; <span id="arrow" class="arrow-up"></span></div>
            <div id="tTitle" v-if="transactions.length === 1">total 1 transcation</div>
            <div id="tDetail">
                <div class="item" v-for="transaction in transactions">
                    <span class="date" v-bind:style="{ minWidth: dateWidth }">{{transaction.transactedAt}}</span>
                    <span class="store">{{transaction.sourceName}}</span>
                    <span v-bind:class="[transaction.amount > 0 ? 'amount earn' : 'amount deduct']">{{ transaction.displayAmount }}</span>
                </div>
            </div>
        </div>
    </div>
    <script>
        var fontReady = false;
        document.fonts.ready.then(function() {
            fontReady = true;
            showPage();
        });
    </script>
    <script src="<%- PATH.SHARED.RSRC %>appbridge-1.0.min.js"></script>
    <script src="<%- PATH.SHARED.RSRC %>vue-2.6.10.min.js"></script>
    <script src="<%- PATH.SHARED.RSRC %>d3-5.min.js"></script>
    <script src="<%- PATH.SHARED.RSRC %>moment-2.24.0.min.js"></script>
    <script src="<%- PATH.SHARED.RSRC %>appletHelper-1.0.min.js"></script>
    <script src="<%- PATH.SHARED.RSRC %>chart-1.0.min.js"></script>
    <script src="<%- PATH.SHARED.RSRC %>swiper-4.5.1.min.js"></script>
    <script>
        var statusbarHeight = <%- statusbarHeight %>;
        var pageWidth = <%- pageWidth %>;
        var pageHeight = <%- pageHeight %>;
        var ios = <%- DEVICE.IOS %>;
        var pageScale = <%- pageScale %>;
        var widgetsConfig = <%- JSON.stringify(constants.CARDMASTER.widgets || {})  %>;
        var pointConfig = widgetsConfig.find(function(widget){ return widget.type === 'points'});
        var transactionConfig = pointConfig && pointConfig.param && pointConfig.param.showTransactions;

        var lang = "en-gb";
        var color = '#02F934';
        var currency = '$';
        var stringTemplate = {
           separater: "|",
           text: "%c%m | voucher",
       }
       var networkStatus = { isOnline: true };
       var mergeData = {};
       var pageMerge;
       var swiper;
       var renderedCharts = [];

        window.addEventListener('data.changed', function () {
            setupPage($data);
        });
        
        window.addEventListener('network.changed', function(param) {
            networkStatus.isOnline = param.detail.isInternetReachable;
        });

        // actions
        $perkd.do('network.info').then(function(status){
            networkStatus.isOnline = status.isInternetReachable;
        });

        loadPage();

         function loadPage() {
            var networkMerge = new Vue({el: '#offlineStatus', data: networkStatus})
            showPage();
            setupPage($data);
         }

         function showPage() {
            if(fontReady) {
                document.getElementById('pPage').style.opacity = 1;
            }
         }

        function setupPage(d) {
            var expiryDate = d.endTime || "<%- constants.CARD.endTime %>";
            var pointStatus = moment(expiryDate) > moment() ? 'active' : 'expired';
            var qualified = d.qualified || [];
            var toQualify = d.toQualify || [];

            var showTransactions = (transactionConfig && d.transactions && d.transactions.length >= 1);
            var transactions = showTransactions? prepareTranscations(d.transactions) : [];
            var sameYearTransaction = showTransactions? d.transactions.every(function(o){
               return moment(o.transactedAt).year() === moment().year();
            }) : false;

            mergeData.pointStatus = pointStatus;
            mergeData.available = formatNumber(pointStatus === 'active' ? (d.available || 0) : 0);
            mergeData.endTime= humaneDate(lang, expiryDate, false, false, -1);
            mergeData.refreshedAt= d.refreshedAt? humaneDate(lang, d.refreshedAt, true) : '';
            mergeData.qualified= qualified;
            mergeData.toQualify= toQualify;
            mergeData.transactions= transactions;
            mergeData.showTransactions= showTransactions;
            mergeData.dateWidth= sameYearTransaction? '6rem' : '8.5rem';
            mergeData.showPagination= pointStatus === 'active' && (qualified.length > 0 || toQualify.length > 0);
            mergeData.chartData = prepareChartData(qualified, toQualify);


            if(!pageMerge) { 
                pageMerge = new Vue({
                    el: '#pPage', 
                    data: mergeData,
                    mounted: function () {
                        swiper = new Swiper('.swiper-container', {
                            pagination: { el: '.swiper-pagination' },
                            on: {
                                slideChangeTransitionEnd: function () {
                                    var chartEl = d3.select('.swiper-slide-active svg');
                                    renderedCharts[swiper.activeIndex -1].animatePath(chartEl);
                                },
                            }
                        });
                        updatePage(mergeData);
                    },
                    updated: function () {
                        swiper.update();
                        updatePage(mergeData);
                    },
                });
            };
        }

        function updatePage(d){
            adjustPointVaueSize();
            if(d.showTransactions){ renderTranscationDetail(d.transactions) };
            if(d.chartData.length > 0) {  renderChart(d.chartData); };
        }

        function prepareTranscations(transactions) {
            var xTransactions = [];
            transactions.length > 0 &&  transactions.forEach(function(t, i) {
                var source = t.acquired.location || t.acquired.attributedTo || {};
                xTransactions.push({
                    transactedAt: calendarDate(lang, t.transactedAt, false, false, 2),
                    amount: t.amount,
                    displayAmount: ((t.amount > 0) ? '+' : '-') + formatNumber(Math.abs(t.amount)),
                    sourceName: ((source && source.type === 'store') ? source.name : source.type) || ''
                });
            });
                
            return xTransactions.reverse();
        }

        function prepareChartData(qualified, toQualify) {
            var chartData = [];
           
            qualified.forEach(function(q) {
                (q.amount !== 0) && chartData.push(_data(2 * Math.PI, q.amount));
            })
            toQualify.forEach(function(t) {
                (t.amount !== 0)  && chartData.push(_data((1 - t.pointsGap / t.toRedeem) * 2 * Math.PI, t.amount));
            })
            function _data(end, amount) {
                return {
                    color: color,
                    end: end,
                    text: stringTemplate.text.replace('%c', currency).replace('%m', formatNumber(amount)).split(stringTemplate.separater),
                    class: "reward"
                }
            }
            return chartData;
       }

        function renderChart(chartData) {
            d3.selectAll('.chartCanvas').selectAll('svg').remove();

            var tListH = d3.select('#tList').node() ? d3.select('#tList').node().getBoundingClientRect().height : 0;
            var chartDescH = d3.select('.chartDesc').node()? d3.select('.chartDesc').node().getBoundingClientRect().height : 75 * pageScale;
            var margin = Math.max(0.15 * Math.min(pageWidth, pageHeight - statusbarHeight), 20);
            var size = Math.floor(Math.min(pageWidth - margin, pageHeight - statusbarHeight - margin - chartDescH - tListH - 30 * pageScale));
            d3.select("#pPage").style("padding-bottom", tListH + 'px');
            var charts = d3.selectAll(".chartCanvas");

            /******* draw chart *******/
            for (var s = 0; s < chartData.length; s++) {
                var dataItem = [];
                dataItem.push(chartData[s]);
                var chart = new Chart({
                    data: dataItem,
                    element: charts.filter(function(d, i){return i === s}),
                    size: size,
                    ios: ios
                });
                renderedCharts.push(chart);
            }
        }

        function renderTranscationDetail(orders) {
            var minDetailH = d3.select('.item').node().getBoundingClientRect().height;
            var maxDetailH = minDetailH * orders.length;

            d3.select("#tDetail").style("height", minDetailH + 'px'); /* get sales overlay original height */
            var tListH = d3.select('#tList').node().getBoundingClientRect().height;

            if (orders.length < 2) return;

            var maxMove = Math.min(pageHeight - tListH, maxDetailH - minDetailH); /* add click event for sales */
            d3.select("#tTitle").on("click", animate); /* add touch event for sales */
            d3.select("#tList").on("touchstart", touchstarted).on("touchmove", touchmoved).on("touchcancel", touchended).on("touchend", touchended);

            /* sales page swipe & click event */
            var startY = 0;
            var movepath = [];

            function touchstarted() {
                var touchobj = d3.event.changedTouches[0];
                startY = touchobj.pageY;
                if (!showDetail) d3.select("#mask").style("z-index", 1);
            }

            function touchmoved() {
                var touchobj = d3.event.changedTouches[0];
                if (movepath.push(touchobj.pageY) > 2) movepath.shift();
                var movePercent = Math.min(Math.abs(startY - touchobj.pageY) / maxMove, 1);
                if (!showDetail && touchobj.pageY - startY < 0) {
                    d3.select("#tDetail").style("height", minDetailH + Math.min(startY - touchobj.pageY, maxDetailH - minDetailH) + "px");
                    d3.select("#mask").style('background-color', "rgba(0,0,0," + 0.5 * movePercent + ")");
                    d3.select("#tList").style('background-color', "rgba(0,0,0," + 0.5 * (1 - movePercent) + ")");
                }
                if (showDetail && touchobj.pageY - startY > 0) {
                    d3.select("#tDetail").style("height", maxDetailH - Math.min(touchobj.pageY - startY, maxDetailH - minDetailH) + "px");
                    d3.select("#mask").style('background-color', "rgba(0,0,0," + 0.5 * (1 - movePercent) + ")");
                    d3.select("#tList").style('background-color', "rgba(0,0,0," + 0.5 * movePercent + ")");
                }
            }

            function touchended() {
                var touchobj = d3.event.changedTouches[0];
                var secondLastPoint = movepath.shift();
                var endPoint = movepath.pop() || secondLastPoint; /* check if user is scrolling instead of swipe up / down */
                var scrolling = (showDetail && ((pageHeight - saleH) < maxDetailH - minDetailH) && d3.select("#tList").property("scrollTop") != 0);
                if (typeof secondLastPoint != "undefined") {
                    if (endPoint == secondLastPoint) secondLastPoint = startY;
                    var direction = (endPoint < secondLastPoint) ? 'up' : 'down';
                    if (scrolling || (showDetail && direction == 'up') || (!showDetail && direction == 'down')) showDetail = !showDetail;
                    animate();
                };
                if (!showDetail) d3.select("#mask").style("z-index", 0);
            }

            function animate() {
                eleAnmation(d3.select("#tDetail"), "height", showDetail, minDetailH + "px", maxDetailH + "px", 400);
                eleAnmation(d3.select("#mask"), "background-color", showDetail, "rgba(0,0,0,0)", "rgba(0,0,0,0.5)", 200);
                eleAnmation(d3.select("#tList"), "background-color", showDetail, "rgba(0,0,0,0.5)", "rgba(0,0,0,0)", 200);

                showDetail = !showDetail;

                if (showDetail) {
                    d3.select("#mask").style("z-index", 2);
                    d3.select("#arrow").attr('class', 'arrow-down');
                } else {
                    d3.select("#mask").style("z-index", -1);
                    d3.select("#arrow").attr('class', 'arrow-up');
                }
            }
       }

       function adjustPointVaueSize() {
            var pointValueSVGSize = d3.select('svg.pointValue').node().getBoundingClientRect();
            var textScale = pointValueSVGSize.width / d3.select('text.dataValue').node().getComputedTextLength();
            d3.select('svg.pointValue g')
            .attr('transform', 'translate('+ pointValueSVGSize.width/2 + ',' + pointValueSVGSize.height/2 + ')');

            d3.select('text.dataValue')
            .attr("transform", "scale("+ Math.min(1,textScale)+")");
        }
    </script>
</body>
</html>