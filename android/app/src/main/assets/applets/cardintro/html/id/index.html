<!DOCTYPE html>
<html>
<head>
    <script>
        <%  
            var PATH = constants.PATH;
            var DEVICE = constants.DEVICE;
            var pageWidth = DEVICE.WIDTH > 900? 900 : DEVICE.WIDTH ;
            var	pageScale = pageWidth/320;
            var statusbarH = DEVICE.STATUS_BAR_HEIGHT;
            var pageTop = IOS? DEVICE.NAV_BAR_HEIGHT + (DEVICE.HAS_NOTCH ? 20 : 0) : DEVICE.TOP_BAR_HEIGHT;
            var pageBottom = DEVICE.MIN_BOTTOM_SPACE;
            var notchHeight = DEVICE.NOTCH_HEIGHT || 0;
            var baseFontSize = constants.UI.baseFontSize;
            var i18n_button = constants.UI.button;

            var MASTER = constants.CARDMASTER;
            var CARD_IMAGE = MASTER.images[0];
            var description = MASTER.description;
            var cardDescription = description.replace(/(<script>)(.*?)(<\/script>)/g,'');
            if(description.startsWith('<!DOCTYPE html>')) {
                cardDescription = cardDescription.match(/(<div id="frame")(.*?)(<\/div>)/g);
            }
            var squareCharacters  = constants.UI.language === 'zh-Hans' || constants.UI.language === 'zh-Hant' || constants.UI.language === 'zh-Hant-HK';
        %>
    </script>
    <meta charset="UTF-8" />
    <meta name="viewport" id="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover">
    <meta name="format-detection" content="telephone=no" />
    <title><%- MASTER.brand.short || MASTER.brand.long %></title>
    <style type="text/css">
        @font-face {
            font-family: Melbourne;
            font-weight: normal;
            src: local('Melbourne'), url('<%- constants.FONTPATH %>Melbourne.otf') format('opentype');
        }
        @font-face {
            font-family: Melbourne;
            font-weight: bold;
            src: local('Melbourne_bold'), url('<%- constants.FONTPATH %>Melbourne_bold.otf') format('opentype');
        }
        @font-face {
            font-family: picon;
            font-weight: normal;
            src: local('picon'), url('<%- constants.FONTPATH %>picon.ttf') format('truetype');
        }
        <%- constants.FONTCSS %>
    
        * {
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
        }

        html {
            font-family: Melbourne, Roboto, 'Helvetica Neue', Helvetica, Arial, 'PingFang TC', 'Source Han Sans CN', sans-serif;
            font-size: <%- Math.floor(10 * pageScale * 100) / 100 %>px;
        }

        body {
            width: <%=pageWidth %>px;
            max-width: 900px;
            margin: <%= pageTop %>px auto 0;
            padding: 0;
            font-size: <%- baseFontSize %>;
            -webkit-text-size-adjust: 100%;
        }

        #screenOuter,
        #screenInner {
            width: <%=pageWidth %>px;
        }

        #frame p {
            font-size: 1em;
            line-height: inherit;
        }
        
        [v-cloak] {
            display: none !important;
        }
        
        #header {
            display: table;
            margin: 0 1.5rem 1.5rem 1.8rem;
            min-height: 6.6rem;
            color: rgba(255,255,255,0.8);
            line-height: 1.4;
        }
        #header .header-image-container,
        #header .header-info-container {
            display: table-cell;
            vertical-align: top;
        }
        #header .header-image-container {
            max-width: 10.24rem;
        }
        #header .header-info-container {
            padding-left: 1.5rem;
        }
        #header .card-image {
            width: 10.24rem;
            height: 6.4rem;
            border-radius: 0.6rem;
            box-shadow: 0.2rem 0.2rem 0.5rem rgba(0,0,0,0.3);
        }
        #header .with-filter {
            box-shadow: none;
            transform: translateZ(0);
            -webkit-filter: drop-shadow(0.2rem 0.2rem 0.5rem rgba(0,0,0,0.3));
            filter: drop-shadow(0.2rem 0.2rem 0.5rem rgba(0,0,0,0.3));
        }
        #header .irregularShape { 
            max-width: 10.24rem;
            max-height: 6.4rem;
            width: auto;
            height: auto;
        }
        #header .card-brand {
            font-size: 0.875em;
            position: relative;
        }
        #header .card-name {
            margin-top: 0.2rem;
            font-size: <%- !squareCharacters? '1.3em' : '1.6em' %>;
            line-height: 1.2em;
            font-weight: bold;
            color: #FFFFFF;
            position: relative;
        }
       
        #js_content {
            margin: 0 1.8rem 3rem;
            color: rgba(255,255,255,0.8);
            line-height: 1.4
        }
        #menu {
            margin: 0 0 1.5rem 1.8rem;
            color: rgba(255,255,255,0.8);
            line-height: 1.4;
            padding-bottom: <%- pageBottom + 60 * pageScale %>px;
        }
        .menu-title {
            display: none;
            font-size: 2rem;
            font-weight: bold;
            color: #FFFFFF
        }       
        .menu-item {
            position: relative;
            padding-right: 3rem;
            -webkit-user-select: none;
            user-select: none;
        }
        .menu-item + .menu-item {
            border-top: 1px solid rgba(255,255,255,0.12);
        }
        .menu-item:only-child {
            border-top: 1px solid rgba(255,255,255,0.12);
            border-bottom: 1px solid rgba(255,255,255,0.12);
        }
       
        .menu-item .label {
            display: block;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            font-size: 1em;
            line-height: 3em;
        }
        .menu-item .label .picon-info {
            position: relative;
            display: inline-block;
            margin-right: 0.5em;
            top: 0.1em;
        }

        .menu-item .picon-next{
            position: absolute;
            top: 50%;
            right: 1.8rem;
            transform: translateY(-50%);
            font-size: 1.2rem;
        }        
        #actionArea {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 1rem 1.8rem;
            padding-bottom: <%- pageBottom + 10 * pageScale %>px;
            background-color: 'rgba(61,61,61,0.8)'
        }

        #actionArea button,
        #actionArea .button{
            display: block;
            width: 100%;
            background-color: #2990FE;
            -webkit-appearance: none;
            border: none;
            color: #FFFFFF;
            font-family: inherit;
            font-size: 1.25em;
            line-height: 2.2em;
            border-radius: 0.6rem;
            text-align: center;
            -webkit-user-select: none;
            user-select: none;
            outline: none;
        }
</style>
</head>
<body>
    <div id="screenOuter">
        <div id="screenInner">
            <div id="header">
                <div class="header-image-container">
                    <img id="cardImage" alt="<%- MASTER.name %>" class="card-image <%- CARD_IMAGE.transparency? 'with-filter' : '' %> <%- CARD_IMAGE.irregularShape? 'irregularShape' : '' %>"/>
                </div>
                <div class="header-info-container">
                    <div class="card-brand"><%- MASTER.brand.short || MASTER.brand.long %></div>
                    <div class="card-name"><%- MASTER.name %></div>
                </div>
            </div>
            <div id="js_content"><%- cardDescription %></div>
            <div id="menu" v-if="items.length > 0" v-cloak>
                <div class="menu-content">
                    <Template v-for="(item, index) in items">
                        <div class="menu-item" v-on:click="item.function">
                            <span class="label">
                                <span class="picon picon-info" v-bind:class="[item.icon]"></span><span>{{item.label}}</span>
                            </span>
                            <span class="picon picon-next" v-if="item.linkable"></span>
                        </div>
                    </Template>
                </div>
            </div>
            <div id="actionArea">
            	<button v-if="allowRegister" v-cloak onclick="executeFlow()"><%- i18n_button.register %></button>
                <div class="button" v-if="!allowRegister" v-cloak onclick="viewCard()">
                    <span v-if="!differentCardMaster"><%- i18n_button.viewCard %></span>
                    <span v-if="differentCardMaster"><%- i18n_button.view %> {{ navTo.cardName }}</span>
                </div>
            </div>
        </div>
    </div>
    <script src="<%- PATH.SHARED.RSRC %>appbridge-1.0.min.js"></script>
    <script src="<%- PATH.SHARED.RSRC %>vue-2.6.10.min.js"></script>
    <script>
        var ICON = {
            'web.open': 'picon-url-filled',
            'communicate.call': 'picon-phone',
            'communicate.email': 'picon-email',
            'facebook': 'picon-facebook',
            'default': 'picon-info'
        };
        var actionData = {};
        var networkStatus = {isOnline: true};
        var actionAreaMerge;
        var isClicked = false;
        
        window.addEventListener('data.changed', function () { setupData($data) });

        pageSetup();
        
        function pageSetup() {
            setupData($data);

            var details = <%- JSON.stringify(MASTER.details) %>;
            var items = (details && details.menu)? details.menu : [];

            items.forEach(function(item, index) {
                var label = item.label.replace(/\s/g, '').toLowerCase();
                var action = item.object + '.' + item.action;
                var param = item.data;
                items[index].icon = ICON[action] || ICON[label] || ICON['default'];
                items[index].linkable = item.object === 'web';

                items[index].function = function() {
                    perkdDo(action, param)
                };
            });
            var menuMerge = new Vue({ el: '#menu', data: { items: items }});     

            document.getElementById('cardImage').src = "<%- CARD_IMAGE.thumbnail %>";
        }

        function setupData(data){
            var d = data.qualified;
            if(!d || Object.keys(d).length ===0) return;

            var allowRegister = d.join && d.join.quantity > 0;
            var differentCardMaster = !allowRegister && d.existing.cardIds.length === 0;

            actionData.allowRegister = allowRegister;
            actionData.differentCardMaster = differentCardMaster;
            actionData.navTo = allowRegister? {} : {
                cardId: differentCardMaster? d.related && d.related.cards[0].id : d.existing.cardIds[0],
                cardName: differentCardMaster? d.related && d.related.cards[0].cardName : "<%- MASTER.name %>"
            };

            if(!actionAreaMerge) {
                actionAreaMerge = new Vue({
                    el: '#actionArea',
                    data: actionData
                })
            }
        }

        function executeFlow() {
            perkdDo('card.request', {masterId: "<%- MASTER.id %>"});
        };

        function viewCard() {
            var navTo = actionData.navTo.cardId ? { id: actionData.navTo.cardId } : {};
            perkdDo('App.navTo', { route: [{ card: navTo }]});
        }

        function perkdDo(action, param) {
            if(isClicked) return;
            isClicked = true;

            $perkd.do(action, param).then(function(res){ 
                isClicked = false; 
            }).catch(function(err){ 
                isClicked = false; 
                $perkd.do('Track.watch', {message: err.message, error: err, data: { action: action, param: param, source: 'cardintro'}, priority: 'log'})
            });
            $perkd.emit('usage', { name: action, data: param });
        }
    </script>
</body>
</html>