"use strict";(function(){const e=function(){return Math.floor(16777216*(1+Math.random())).toString(16).substring(1)},t=function(){return e()+e()},a={},n={},d={},o=function(e){Object.assign(n,e),Object.keys(n).forEach(function(e){void 0===$data[e]&&Object.defineProperty($data,e,{get:()=>n[e],set(t){n[e]=t},enumerable:!0})}),window.dispatchEvent(new CustomEvent("data.changed"))};window.$perkd={onMessage(e){const t=a[e.id];t?e.error?t.reject(e.error):t.resolve(e.data):"data.changed"===e.name?o(e.data):"bag.changed"===e.name?(Object.assign(d,e.data),window.dispatchEvent(new CustomEvent("bag.changed"))):window.dispatchEvent(new CustomEvent(e.name,{detail:e.data}))},emit(e,a){const n=t(),d=JSON.stringify({id:n,name:e,data:a});return window.ReactNativeWebView.postMessage(d),n},do(e,t){const n=$perkd.emit("do",{action:e,param:t});return new Promise(function(e,t){a[n]={resolve:e,reject:t}})}};class s{save(){$perkd.do("data.save",n)}add(e){Object.defineProperty(this,e,{get:()=>n[e],set(t){n[e]=t},enumerable:!0})}}window.$data=new s;class r{addItems(e){$perkd.do("bag.addItems",{items:e})}updateItems(e){$perkd.do("bag.updateItems",{items:e})}removeItems(e){$perkd.do("bag.removeItems",{items:e})}}window.$bag=new r,["items","amount"].forEach(e=>Object.defineProperty($bag,e,{get:()=>d[e],enumerable:!0})),$perkd.do("init").then(o)})();