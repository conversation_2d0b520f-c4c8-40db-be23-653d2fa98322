"use strict";function getSum(t,e){return t+e}function changeTransform(t,e,r){return t.attr("transform").search(e)>=0?t.attr("transform").replace(e,r):t.attr("transform")+" "+r}function eleAnmation(t,e,r,a,n,i){var s=r?a:n;t.transition().duration(i||0).ease(d3.easeElastic).style(e,s)}class Chart{constructor(t){this.data=t.data,this.element=t.element,this.size=t.size,this.maxRadius=this.size/2,this.ios=t.ios,this.shadow=void 0===t.shadow||t.shadow,this.textEffect=void 0===t.textEffect||t.textEffect,this.noAnimate=t.noAnimate,this.currentId=this.data.length-1,this.draw()}draw(){var t=this;this.gap=2,this.barWidth=Math.min(Math.max(this.size/20,5),15),this.pi=2*Math.PI,this.arc=d3.arc().innerRadius(function(e){return t.maxRadius-t.barWidth-e.index*(t.barWidth+t.gap)}).outerRadius(function(e){return t.maxRadius-e.index*(t.barWidth+t.gap)}).cornerRadius(this.maxRadius/2).startAngle(0).endAngle(this.pi);var e=this.element.style("width",this.size+"px").style("height",this.size+"px").append("svg").attr("width",this.size).attr("height",this.size).attr("overflow","visible"),r=e.selectAll("g").data(this.data).enter().append("g").each(function(t,e){t.index=e}).attr("class","donut").attr("id",function(t,e){return"donut"+e}).attr("transform","translate("+this.size/2+","+this.size/2+")");if(r.append("path").attr("class","background").attr("id",function(t,e){return"background"+e}).style("fill",function(t){return t.color}).attr("d",this.arc).attr("opacity",.2),r.append("path").attr("class","foreground").attr("id",function(t,e){return"foreground"+e}).style("fill",function(t){return t.color}).transition().duration(2500).delay(function(t,e){return 3500*e}).attr("d",this.arc.endAngle(function(t){return t.end})).ease(d3.easeElastic).attrTween("d",function(e){return t.arcTween(e,t.arc)}),this.addText(r),this.ios&&this.shadow&&(this.addShadow(e),r.selectAll("path.foreground").attr("filter","url(#dropshadow)")),this.data.length>1){var a=e.append("rect").attr("width","100%").attr("height","100%").attr("opacity",0),n=!1;a.on("click",function(r){n||(n=!0,t.clickMe(e,t.arc),setTimeout(function(){n=!1},600))})}}addShadow(t){var e=t.append("defs").append("filter").attr("id","dropshadow");e.append("feGaussianBlur").attr("in","SourceAlpha").attr("stdDeviation",5).attr("result","blur"),e.append("feOffset").attr("in","blur").attr("dx",0).attr("dy",0).attr("result","offsetBlur");var r=e.append("feMerge").attr("opacity",.1);r.append("feMergeNode").attr("in","offsetBlur"),r.append("feMergeNode").attr("in","SourceGraphic")}addText(t){var e=this,r=[],a=[],n=this.size,i=2*this.gap;t.append("g").attr("class",function(t){return"text "+t.class}).attr("id",function(t,e){return"text-group"+e}).style("fill",function(t){return t.color}).selectAll("text").data(function(t){return t.text}).enter().append("text").text(function(t){return t.trim()}).attr("class",function(t,e){return"text"+e}).attr("transform",function(t,e){var r=Math.min(1,(n-i*(e+1))/this.getComputedTextLength());return"scale("+r+")"}).attr("dy",function(t,e){0==e&&(a=[]),a.push(this.getBoundingClientRect().height);var n=a[e-1]?a.reduce(getSum)-a[0]/2-a[e]/2:0;return 0==e&&r.push(a),n}),t.select("g.text").attr("transform",function(t,e){return"translate(0,"+(-r[e].reduce(getSum)/2+a[0]/2)+")"}).transition().duration(this.textEffect?1500:0).delay(function(t,e){return 3500*e}).style("opacity",1).transition().duration(this.textEffect?2e3:0).style("opacity",function(t,r){return r==e.data.length-1?1:0})}clickMe(t){var e=this.currentId==this.data.length-1?0:this.currentId+1,r=t.select("#text-group"+this.currentId),a=t.select("#text-group"+e),n=/scale\(.*\)/;r.transition().duration(400).ease(d3.easeSin).attr("transform",changeTransform(r,n,"scale(0,1)")).transition().duration(50).style("opacity",0),a.transition().duration(50).delay(350).style("opacity",1).transition().duration(200).ease(d3.easeBack).attr("transform",changeTransform(a,n,"scale(1,1)")),this.animatePath(t,e),this.currentId=e}arcTween(t,e){if(this.noAnimate)return function(r){return e(t)};var r=d3.interpolateNumber(0,t.end);return function(a){return t.end=r(a),e(t)}}animatePath(t,e){var r=this,a=e||this.currentId,n=t.select("#foreground"+a);n.style("fill",function(t){return t.color}).transition().duration(4e3).delay(350).attr("d",this.arc).ease(d3.easeElasticOut).attrTween("d",function(t){return r.arcTween(t,r.arc)})}destroy(){this.element.select("svg").remove()}}