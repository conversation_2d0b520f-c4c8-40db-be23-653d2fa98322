function formatNumber(e,d){var a=e.toString();if(d){if(a.length>4&&a.length<7)return Math.ceil(e/1e3*10)/10+"K";if(a.length>=7)return Math.ceil(e/1e6*100)/100+"M"}return e.toLocaleString("en-US")}function weekOf(e,d,a){var t=(new Date).getTime(),s=e.clone().subtract(e.isoWeekday()-1,"day").format("x"),_=e.clone().add(7-e.isoWeekday(),"day").format("x");return t>=s&&t<=_?d:a}function hourMinutes(e,d,a){if(!moment(d).isValid())return"";var t,s=moment(d).minutes();switch(e){case"zh-Hans":t=0===s?"Ah[点]":"Ah:mm";break;case"zh-Hant":case"zh-Hant-HK":t=0===s?"Ah[點]":"Ah:mm";break;case"ja":t=`${a?"":", "}Ah:mm`;break;case"ko":t=`${a?"":", "}A h:mm`;break;default:t=(a?"":", ")+(0===s?"ha":"h:mma")}return moment(d).format(t)}function smartRelativeTime(e,d,a,t,s){function _(e,d){var a=o[d+(1===e?"":"s")];return 1===l&&e<10?M.relativeTime.s:M.timeUnit[a].replace("%d",e)}function n(e,d,a,t,s,n,m){var r=[];e&&(l=l||6,r.push(_(e,"year"))),d&&(l=l||5,r.push(_(d,"month"))),a&&(l=l||4,r.push(_(a,"day"))),t&&(!l||l<5)&&(l=l||3,r.push(_(t,"hour"))),s&&(!l||l<4)&&(l=l||2,r.push(_(s,"minute"))),n&&(!l||l<3)&&(l=l||1,r.push(_(n,"second")));var i=m?"future":"past",Y=r.join(o.delimiter);return Y=M.relativeTime[i].replace("%s",Y),Y}function m(e,d,a,t,s,_,n){return{years:e,months:d,days:a,hours:t,minutes:s,seconds:_,firstDateWasLater:n}}var r=e||"en-gb",l=s||0,i=langLookup[r]||langLookup["en-gb"],M=momentConfigs[i];moment.locale()!==r&&moment.updateLocale(r,M);var Y,o={nodiff:"",year:"y",years:"yy",month:"M",months:"MM",day:"d",days:"dd",hour:"h",hours:"hh",minute:"m",minutes:"mm",second:"s",seconds:"ss",delimiter:" "},h=moment(d),u=moment(a);if(h.add(u.utcOffset()-h.utcOffset(),"minutes"),h.isSame(u))return t?m(0,0,0,0,0,0,!1):o.nodiff;if(h.isAfter(u)){var y=h;h=u,u=y,Y=!0}else Y=!1;var L=u.year()-h.year(),D=u.month()-h.month(),p=u.date()-h.date(),k=u.hour()-h.hour(),f=u.minute()-h.minute(),T=u.second()-h.second();if(T<0&&(T=60+T,f--),f<0&&(f=60+f,k--),k<0&&(k=24+k,p--),p<0){var b=moment(u.year()+"-"+(u.month()+1),"YYYY-MM").subtract(1,"M").daysInMonth();p=b<h.date()?b+p+(h.date()-b):b+p,D--}return D<0&&(D=12+D,L--),t?m(L,D,p,k,f,T,Y):n(L,D,p,k,f,T,Y)}function humaneDate(e,d,a,t,s,_){var n=e||"en-gb",m=void 0!==s?s:7;if(!moment(d).isValid())return"-";var r=langLookup[n]||langLookup["en-gb"];moment.locale()!==n&&moment.updateLocale(n,momentConfigs[r]);var l="",i=new Date,M=moment(d).year()!==moment().year(),Y=Math.abs(moment(d).diff(i,"day",!0)),o=a?hourMinutes(r,d,!1):"";return l=-1!==m&&Y<=m?moment(d).isSame(i)||Y<1?smartRelativeTime(n,d,i,!1,_):moment(d).calendar()+o:moment(d).format(M||t?"ll":"l")+o,l}function calendarDate(e,d,a,t,s){var _=e||"en-gb",n=void 0!==s?s:7;if(!moment(d).isValid())return"-";var m=langLookup[_]||langLookup["en-gb"];moment.locale()!==_&&moment.updateLocale(_,momentConfigs[m]);var r="",l=new Date,i=moment(d).year()!==moment().year(),M=Math.abs(moment(d).diff(l,"day",!0)),Y=a?hourMinutes(m,d,!1):"";return r=moment(d).isSame(moment(),"day")?hourMinutes(m,d,!0):M<=n?moment(d).calendar()+Y:moment(d).format(i||t?"ll":"l")+Y,r}var THIS_YEAR=moment().year(),momentConfigs={"en-gb":{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mma",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd, D MMMM YYYY LT",l:"D MMM",ll:"D MMM YYYY",lll:"D MMMM LT",llll:"dddd, D MMMM YYYY LT"},calendar:{sameDay:"[Today]",nextDay:"[Tomorrow]",lastDay:"[Yesterday]",nextWeek(){return weekOf(this,"[on] dddd","[next] dddd")},lastWeek(){return weekOf(this,"[on] dddd","[last] dddd")},sameElse(){return this.year()!==THIS_YEAR?"ll":"l"}},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},ordinal(e){var d=e%10,a=1==~~(e%100/10)?"th":1===d?"st":2===d?"nd":3===d?"rd":"th";return e+a},timeUnit:{s:"1 sec",ss:"%d secs",m:"1 min",mm:"%d mins",h:"1 hour",hh:"%d hours",d:"1 day",dd:"%d days",M:"1 month",MM:"%d months",y:"1 year",yy:"%d years"}},"zh-Hant":{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"Ah點mm",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 LT",LLLL:"YYYY年M月D日dddd LT",l:"M月D日",ll:"YYYY年M月D日",lll:"YYYY年M月D日 LT",llll:"YYYY年M月D日dddd LT"},meridiem(e,d,a){var t=100*e+d;return t<600?"凌晨":t<900?"早上":t<1130?"上午":t<1230?"中午":t<1800?"下午":"晚上"},calendar:{sameDay:"[今天]",nextDay:"[明天]",lastDay:"[昨天]",nextWeek(){return weekOf(this,"dddd","[下]dddd")},lastWeek(){return weekOf(this,"dddd","[上]dddd")},sameElse(){return this.year()!==THIS_YEAR?"ll":"l"}},relativeTime:{future:"%s後",past:"%s前",s:"幾秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"},timeUnit:{s:"1 秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"}},"zh-Hans":{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"Ah点mm",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 LT",LLLL:"YYYY年M月D日dddd LT",l:"M月D日",ll:"YYYY年M月D日",lll:"YYYY年M月D日 LT",llll:"YYYY年M月D日dddd LT"},meridiem(e,d,a){var t=100*e+d;return t<600?"凌晨":t<900?"早上":t<1130?"上午":t<1230?"中午":t<1800?"下午":"晚上"},calendar:{sameDay:"[今天]",nextDay:"[明天]",lastDay:"[昨天]",nextWeek(){return weekOf(this,"dddd","[下]dddd")},lastWeek(){return weekOf(this,"dddd","[上]dddd")},sameElse(){return this.year()!==THIS_YEAR?"ll":"l"}},relativeTime:{future:"%s后",past:"%s前",s:"几秒",ss:"%d 秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},timeUnit:{s:"1 秒",ss:"%d 秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"}},ja:{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"日曜日_月曜日_火曜日_水曜日_木曜日_金曜日_土曜日".split("_"),weekdaysShort:"日_月_火_水_木_金_土".split("_"),weekdaysMin:"日_月_火_水_木_金_土".split("_"),longDateFormat:{LT:"Ah:mm",L:"MMMD日",LL:"YYYY年MMMD日",LLL:"YYYY年MMMD日LT",LLLL:"YYYY年MMMD日ddddLT",l:"MMMD日",ll:"YYYY年MMMD日",lll:"MMMD日LT",llll:"YYYY年MMMD日ddddLT"},meridiem:(e,d,a)=>e<12?"午前":"午後",calendar:{sameDay:"[今日]",nextDay:"[明日]",lastDay:"[昨日]LT",nextWeek(){return weekOf(this,"[今度の]dddd","[次の]dddd")},lastWeek(){return weekOf(this,"[今度の]dddd","[先週の]dddd")},sameElse(){return this.year()!==THIS_YEAR?"ll":"l"}},relativeTime:{future:"%s",past:"%s前",s:"数秒",ss:"%d秒",m:"1分",mm:"%d分",h:"1時間",hh:"%d時間",d:"1日間",dd:"%d日間",M:"1ヶ月",MM:"%dヶ月",y:"1年",yy:"%d年"},timeUnit:{s:"1秒",ss:"%d秒",m:"1分",mm:"%d分",h:"1時間",hh:"%d時間",d:"1日間",dd:"%d日間",M:"1ヶ月",MM:"%dヶ月",y:"1年",yy:"%d年"}},ko:{months:"1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월".split("_"),monthsShort:"1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월".split("_"),weekdays:"일요일_월요일_화요일_수요일_목요일_금요일_토요일".split("_"),weekdaysShort:"일_월_화_수_목_금_토".split("_"),weekdaysMin:"일_월_화_수_목_금_토".split("_"),longDateFormat:{LT:"A h:mm",L:"YYYY.MM.DD.",LL:"YYYY년 MMMM D일",LLL:"YYYY년 MMMM D일 LT",LLLL:"YYYY년 MMMM D일 dddd LT",l:"MMMM D일",ll:"YYYY년 MMMM D일",lll:"YYYY년 MMMM D일 LT",llll:"YYYY년 MMMM D일 dddd LT"},meridiem:(e,d,a)=>e<12?"오전":"오후",calendar:{sameDay:"오늘",nextDay:"내일",lastDay:"어제",nextWeek(){return weekOf(this,"[이번] dddd","[다음주] dddd")},lastWeek(){return weekOf(this,"[이번] dddd","[지난주] dddd")},sameElse(){return this.year()!==THIS_YEAR?"ll":"l"}},relativeTime:{future:"%s 후",past:"%s 전",s:"몇 초",ss:"%d초",m:"1분",mm:"%d분",h:"한 시간",hh:"%d시간",d:"하루",dd:"%d일",M:"한 달",MM:"%d달",y:"일 년",yy:"%d년"},timeUnit:{s:"1초",ss:"%d초",m:"1분",mm:"%d분",h:"1시간",hh:"%d시간",d:"하루",dd:"%d일",M:"1달",MM:"%d달",y:"1년",yy:"%d년"}},id:{months:"Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_November_Desember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Ags_Sep_Okt_Nov_Des".split("_"),weekdays:"Minggu_Senin_Selasa_Rabu_Kamis_Jumat_Sabtu".split("_"),weekdaysShort:"Min_Sen_Sel_Rab_Kam_Jum_Sab".split("_"),weekdaysMin:"Min_Sen_Sel_Rab_Kam_Jum_Sab".split("_"),longDateFormat:{LT:"h:mm a",L:"DD/MM/YYYY",LL:"D MMM YYYY",LLL:"D MMMM YYYY, LT",LLLL:"dddd, D MMMM YYYY, LT",l:"D MMM",ll:"D MMM YYYY",lll:"D MMMM YYYY, LT",llll:"dddd, D MMMM YYYY, LT"},calendar:{sameDay:"[Hari ini]",nextDay:"[Besok]",lastDay:"[Kemarin]",nextWeek(){return weekOf(this,"dddd [ini]","dddd [depan]")},lastWeek(){return weekOf(this,"dddd [ini]","dddd [lalu]")},sameElse(){return this.year()!==THIS_YEAR?"ll":"l"}},relativeTime:{future:"dalam %s",past:"%s yang lalu",s:"beberapa detik",ss:"%d detik",m:"semenit",mm:"%d menit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},timeUnit:{s:"1 detik",ss:"%d detik",m:"1 menit",mm:"%d menit",h:"1 jam",hh:"%d jam",d:"1 hari",dd:"%d hari",M:"1 bulan",MM:"%d bulan",y:"1 tahun",yy:"%d tahun"}},ms:{months:"Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis".split("_"),weekdays:"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu".split("_"),weekdaysShort:"Ahd_Isn_Sel_Rab_Kha_Jum_Sab".split("_"),weekdaysMin:"Ah_Is_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"h.mm a",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMM YYYY",LLL:"D MMMM YYYY [pukul] LT",LLLL:"dddd, D MMMM YYYY [pukul] LT",l:"D MMM",ll:"D MMM YYYY",lll:"D MMMM YYYY, LT",llll:"dddd, D MMMM YYYY, LT"},calendar:{sameDay:"[Hari ini]",nextDay:"[Esok]",lastDay:"[Kelmarin]",nextWeek(){return weekOf(this,"dddd [ini]","dddd [depan]")},lastWeek(){return weekOf(this,"dddd [ini]","dddd [lepas]")},sameElse(){return this.year()!==THIS_YEAR?"ll":"l"}},relativeTime:{future:"dalam %s",past:"%s yang lepas",s:"beberapa saat",ss:"%d saat",m:"seminit",mm:"%d minit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},timeUnit:{s:"1 saat",ss:"%d saat",m:"1 minit",mm:"%d minit",h:"1 jam",hh:"%d jam",d:"1 hari",dd:"%d hari",M:"1 bulan",MM:"%d bulan",y:"1 tahun",yy:"%d tahun"},week:{dow:1,doy:7}}},langLookup={"en-gb":"en-gb",ja:"ja",ko:"ko",id:"id",ms:"ms","zh-cn":"zh-Hans","zh-tw":"zh-Hant"};