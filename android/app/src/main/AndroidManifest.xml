<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="me.perkd">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
    <uses-permission android:name="android.permission.BLUETOOTH"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> 
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> 
    <uses-permission android:name="android.permission.READ_CONTACTS" /> 
    <uses-permission android:name="android.permission.READ_CALENDAR" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove"/>

    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:largeHeap="true"
      android:exported ="true"
      android:localeConfig="@xml/locales_config"
      android:networkSecurityConfig="@xml/network_security_config"
      android:theme="@style/AppTheme">
      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:screenOrientation="portrait"
        android:configChanges="keyboard|keyboardHidden|orientation|screenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustResize"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>

        <intent-filter android:label="Perkd">
          <action android:name="android.intent.action.VIEW" />
          <category android:name="android.intent.category.DEFAULT" />
          <category android:name="android.intent.category.BROWSABLE" />
          <data android:scheme="perkd" />
        </intent-filter>

        <intent-filter android:label="Perkd" android:autoVerify="true">
          <action android:name="android.intent.action.VIEW" />
          <category android:name="android.intent.category.DEFAULT" />
          <category android:name="android.intent.category.BROWSABLE" />
          <data android:scheme="https" />
          <data android:scheme="http" />
          <data android:host="app.perkd.me" />
          <data android:host="menu.perkd.me" />
          <data android:host="card.perkd.me" />
          <data android:host="receipt.perkd.me" />
          <data android:host="qr.perkd.me" />
          <data android:host="perkd.app" />
        </intent-filter>

        <intent-filter>
          <action android:name="android.nfc.action.NDEF_DISCOVERED" />
          <category android:name="android.intent.category.DEFAULT" />
          <data android:scheme="https" />
          <data android:scheme="http" />
          <data android:host="app.perkd.me" />
          <data android:host="menu.perkd.me" />
          <data android:host="card.perkd.me" />
          <data android:host="receipt.perkd.me" />
          <data android:host="qr.perkd.me" />
          <data android:host="perkd.app" />
        </intent-filter>
      </activity>

      <activity android:name="com.reactnativenavigation.NavigationActivity"
         android:exported="true"
         android:windowSoftInputMode="adjustResize" />

      <meta-data
          android:name="com.google.android.gms.wallet.api.enabled"
          android:value="true" />
      <meta-data
          android:name="com.google.android.geo.API_KEY"
          android:value="AIzaSyCon0prFhmOUMbbsz_-vlLo2KSh0pQoKMo"  />

      <meta-data android:name="com.bugsnag.android.API_KEY"
          android:value="c6e0b29f43d58e0741887ab6930fd87b"/>

      <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id"/>

      <meta-data
          android:name="com.google.firebase.messaging.default_notification_icon"
          android:resource="@mipmap/notify_icon" />

      <service android:name="me.perkd.service.DaemonService"
          android:enabled="true"
          android:exported="false"
          android:process=":daemon"/>

      <service android:name="me.perkd.BackgroundService" />

    </application>

    <queries>
      <!-- Specific apps you interact with, eg: -->
      <package android:name="com.eg.android.AlipayGphone" />
      <package android:name="hk.alipay.wallet" />
      <package android:name="com.apaylater.android" />
      <package android:name="jp.naver.line.android" />
      <package android:name="com.grabtaxi.passenger" />
      <package android:name="com.kfit.fave" />
      <package android:name="com.dbs.dbspaylah" />
      <package android:name="com.ocbc.pay" />
      <package android:name="com.uob.mighty.app" />
      <package android:name="com.mol.molwallet" />
      <package android:name="my.com.tngdigital.ewallet" />
      <package android:name="com.nets.netspay" />
      <package android:name="my.com.myboost" />
      <package android:name="hk.com.hsbc.paymefromhsbc" />
      <package android:name="com.jkos.app" />
      <package android:name="com.pxmart.android" />
      <package android:name="com.squareup" />
      <package android:name="com.paypal.android.p2pmobile" />
      <package android:name="com.SingTel.mWallet" />
      <package android:name="com.venmo" />
      <package android:name="com.facebook.katana" />
      <package android:name="com.twitter.android" />
      <package android:name="com.instagram.android" />
      <package android:name="com.tencent.mobileqq" />
      <package android:name="com.linkedin.android" />
      <package android:name="com.sina.weibo" />
      <package android:name="com.facebook.orca" />
      <package android:name="com.whatsapp" />
      <package android:name="com.tencent.mm" />
      <package android:name="com.kakao.talk" />
      <package android:name="com.google.android.apps.maps" />
      <package android:name="com.autonavi.minimap" />
      <package android:name="com.baidu.BaiduMap" />
      <package android:name="com.airbnb.android" />
      <package android:name="com.agoda.mobile.consumer" />
      <package android:name="com.ubercab" />
      <package android:name="com.sdu.didi.psnger" />
      <package android:name="com.gojek.app" />
      <package android:name="ctrip.android.view" />
      <package android:name="com.klook" />
      <package android:name="com.kkday.member" />
      <package android:name="com.tranzmate" />
      <package android:name="com.starbucks.singapore" />
      <package android:name="com.global.foodpanda.android" />
      <package android:name="com.ubercab.eats" />
      <package android:name="com.deliveroo.orderapp" />
      <package android:name="com.google.android.youtube" />
      <package android:name="com.taitung.ttpush" />
      <package android:name="com.hiiir.alley" />
      <package android:name="com.rich.app.designeddriver.user" />
      <package android:name="com.zalora.android" />
      <package android:name="com.shopee.sg" />
      <package android:name="com.shopee.tw" />
      <package android:name="com.shopee.my" />
      <package android:name="com.shopee.id" />
      <package android:name="com.shopee.ph" />
      <package android:name="com.shopee.th" />
      <package android:name="com.shopee.vn" />
      <package android:name="com.shopee.br" />
      <package android:name="com.lazada.android" />
      <package android:name="com.mobile.pomelo" />
      <package android:name="com.asos.app" />
      <package android:name="com.taobao.taobao" />
      <package android:name="com.tmall.wireless" />
      <package android:name="com.amazon.mShop.android.shopping" />
      <package android:name="com.shopify.arrive" />
    </queries>
</manifest>
