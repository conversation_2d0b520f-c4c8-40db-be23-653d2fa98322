package me.perkd.mlkit;

import android.net.Uri;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;
import com.google.mlkit.vision.barcode.BarcodeScanner;
import com.google.mlkit.vision.barcode.BarcodeScanning;
import com.google.mlkit.vision.barcode.BarcodeScannerOptions;
import com.google.mlkit.vision.barcode.common.Barcode;
import com.google.mlkit.vision.common.InputImage;

import java.io.IOException;

public class BarcodeScanningModule extends ReactContextBaseJavaModule {
    private final ReactApplicationContext reactContext;

    BarcodeScanningModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
    }

    @Override
    public String getName() {
        return "BarcodeScanningModule";
    }

    @ReactMethod
    public void processImage(String url, Promise promise) {
        Uri uri = Uri.parse(url);

        InputImage image;
        try {
            image = InputImage.fromFilePath(reactContext, uri);

            BarcodeScannerOptions options =
                    new BarcodeScannerOptions.Builder()
                            .setBarcodeFormats(Barcode.FORMAT_ALL_FORMATS)
                            .build();

            BarcodeScanner scanner = BarcodeScanning.getClient();

            scanner.process(image)
                    .addOnSuccessListener(barcodes -> {
                        WritableArray result = Arguments.createArray();

                        for (Barcode barcode: barcodes) {
                            WritableMap map = Arguments.createMap();

//                            map.putArray("boundingBox", SharedUtils.rectToIntArray(barcode.getBoundingBox()));
//                            map.putArray("cornerPoints", SharedUtils.pointsToIntsList(barcode.getCornerPoints()));
                            map.putInt("format", barcode.getFormat());
                            map.putInt("valueType", barcode.getValueType());
                            map.putString("displayValue", barcode.getDisplayValue());
                            map.putString("rawValue", barcode.getRawValue());

                            result.pushMap(map);
                        }

                        promise.resolve(result);
                    })
                    .addOnFailureListener(e -> {
                        e.printStackTrace();
                        promise.reject("Barcode scanning failed", e);
                    });
        } catch (IOException e) {
            e.printStackTrace();
            promise.reject("Image labeling failed", e);
        }
    }
}
