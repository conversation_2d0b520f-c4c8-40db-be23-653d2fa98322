package me.perkd;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.webkit.WebView;
import android.widget.LinearLayout;

import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.reactnativenavigation.NavigationActivity;

import com.bugsnag.android.Bugsnag;

import me.perkd.common.Constants;
import me.perkd.service.DaemonService;

public class MainActivity extends NavigationActivity {

	private LinearLayout splash;

	@Override
	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		setContentView(this.createSplashLayout());
		getWindow().setBackgroundDrawable(null);

		Bugsnag.start(this);

		if (BuildConfig.DEBUG) {
			WebView.setWebContentsDebuggingEnabled(true);
		}

		try {
			Intent intent = new Intent(this, DaemonService.class);
			startService(intent);
		} catch (Exception e) {e.printStackTrace();}

		LocalBroadcastManager.getInstance(this).registerReceiver(new LaunchedBroadcastReceiver(), new IntentFilter(Constants.LAUNCHED_EVENT_NAME));
	}

	public void launched() {
		if (splash != null)
			splash.setBackgroundColor(ContextCompat.getColor(MainActivity.this, R.color.themeLaunchedColor));
	}

	public LinearLayout createSplashLayout() {
		splash = new LinearLayout(this);
		// splash.setBackgroundColor(Color.TRANSPARENT);
		splash.setBackgroundColor(ContextCompat.getColor(this, R.color.themeColor));
		// splash.setBackgroundColor(Color.parseColor("#2990FE")); // set blue to indicate in launch

		return splash;
	}

	@Override
	public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
		super.onRequestPermissionsResult(requestCode, permissions, grantResults);
	}

	@Override
	public void onNewIntent(Intent intent) {
		super.onNewIntent(intent);
	}

	@Override
	protected void onDestroy() {
		super.onDestroy();
		Intent intent = new Intent(this, DaemonService.class);
		stopService(intent);
		//取消注册
		LocalBroadcastManager.getInstance(this).unregisterReceiver(new LaunchedBroadcastReceiver());
	}

	final class LaunchedBroadcastReceiver extends BroadcastReceiver {

		@Override
		public void onReceive(Context context, Intent intent) {
			if (intent != null && Constants.LAUNCHED_EVENT_NAME.equals(intent.getAction())) {
				launched();
			}
		}
	}
}
