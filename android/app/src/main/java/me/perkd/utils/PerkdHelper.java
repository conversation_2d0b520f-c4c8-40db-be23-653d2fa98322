package me.perkd.utils;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.BroadcastReceiver;
import android.content.ComponentCallbacks2;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.webkit.CookieManager;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.LifecycleEventListener;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;

import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Method;

import me.perkd.common.Constants;
import me.perkd.BackgroundService;
import me.perkd.MainApplication;

public class PerkdHelper extends ReactContextBaseJavaModule implements ComponentCallbacks2, LifecycleEventListener {

    private final ReactApplicationContext reactContext;

    private boolean mainActivityVisible = true;

    private boolean initialized = false;

    private static final String TAG = "PerkdHelper";

    private static final double MB = 1024 * 1024.0;

    BroadcastReceiver receiver;

    public PerkdHelper(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
        this.reactContext.registerComponentCallbacks(this);


        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_LOCALE_CHANGED);

        receiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                Log.i(TAG, "ACTION_LOCALE_CHANGED" + intent.getAction() +  mainActivityVisible);
                if (intent.getAction() != null && !mainActivityVisible) {
                    Log.i(TAG, "ACTION_LOCALE_CHANGED Exit");
                    exit();
                }
            }
        };

        this.reactContext.addLifecycleEventListener(this);
        this.reactContext.registerReceiver(receiver, filter);
    }

    @Override
    public void initialize() {
        super.initialize();
        this.initialized = true;
    }

    @Override
    public void invalidate() {
        super.invalidate();
        this.reactContext.unregisterComponentCallbacks(this);
        this.reactContext.removeLifecycleEventListener(this);
        this.reactContext.unregisterReceiver(receiver);
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    public boolean checkGooglePlayServicesSync() {
        return this.checkGooglePlayServicesHelper() == ConnectionResult.SUCCESS;
    }

    @ReactMethod
    public void checkGooglePlayServices(Promise promise) {
        try {
            promise.resolve(this.checkGooglePlayServicesHelper() == ConnectionResult.SUCCESS);
        } catch(Exception e) {}
    }

    private int checkGooglePlayServicesHelper() {
        return GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(this.getCurrentActivity());
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    public boolean hasFusedLocationProvider() {
        try {
            Class.forName("com.google.android.gms.location.FusedLocationProviderClient");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    @ReactMethod
    public void enterBackground() {
        Intent service = new Intent(reactContext.getApplicationContext(), BackgroundService.class);

        Bundle bundle = new Bundle();
        service.putExtras(bundle);

        reactContext.getApplicationContext().startService(service);
    }

    @ReactMethod
    public void getSignature(Promise promise) {
        try {
            promise.resolve("nQZt9i92PlpR");
        } catch(Exception e) {}
    }

    /**
     * 获取虚拟键高度(无论是否隐藏)
     * @return
     */
    @ReactMethod(isBlockingSynchronousMethod = true)
    public float getNavigationBarHeight(){
        float result = 0;
        int resourceId = reactContext.getResources().getIdentifier("navigation_bar_height","dimen", "android");
        if (resourceId > 0) {
            result = px2dip(reactContext.getResources().getDimensionPixelSize(resourceId));
        }
        return result;
    }

    /**
     * 获取虚拟键高度(无论是否隐藏)
     * @return
     */
    @ReactMethod(isBlockingSynchronousMethod = true)
    public boolean isNavigationBarShown(){
        //虚拟键的view,为空或者不可见时是隐藏状态

        Activity activity = getCurrentActivity();
        if (activity == null) return false;

        View view  = getCurrentActivity().findViewById(android.R.id.navigationBarBackground);
        if(view == null){
            return false;
        }
        int visible = view.getVisibility();
        return visible != View.GONE && visible != View.INVISIBLE;
    }

    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    public float dip2px(float dpValue) {
        final float scale = reactContext.getResources().getDisplayMetrics().density;
        return (int) (dpValue * scale + 0.5f);
    }

    /**
     * 根据手机的分辨率从 px(像素) 的单位 转成为 dp
     */
    public float px2dip(float pxValue) {
        final float scale = reactContext.getResources().getDisplayMetrics().density;
        return (int) (pxValue / scale + 0.5f);
    }
    
    @ReactMethod(isBlockingSynchronousMethod = true)
    public String getSystemProperty(String key, String defaultValue) {
        String value = defaultValue;
        try {
            Class<?> c = Class.forName("android.os.SystemProperties");
            Method get = c.getMethod("get", String.class, String.class);
            value = (String)(get.invoke(c, key, "unknown" ));
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return value;
        }
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    public int getSystemPropertyInt(String key, int defaultValue) {
        int value = defaultValue;
        try {
            Class<?> c = Class.forName("android.os.SystemProperties");
            Method get = c.getMethod("getInt", String.class, int.class);
            value = (int)(get.invoke(c, key, defaultValue ));
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return value;
        }
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    public boolean getSystemPropertyBoolean(String key, boolean defaultValue) {
        boolean value = defaultValue;
        try {
            Class<?> c = Class.forName("android.os.SystemProperties");
            Method get = c.getMethod("getBoolean", String.class, boolean.class);
            value = (boolean)(get.invoke(c, key, defaultValue ));
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return value;
        }
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    public boolean getSamsungIsNotch() {
        boolean result = false;
        try {
            final Resources res = reactContext.getResources();
            final int resId = res.getIdentifier("config_mainBuiltInDisplayCutout", "string", "android");
            final String spec = resId > 0 ? res.getString(resId): null;
            result = spec != null && !TextUtils.isEmpty(spec);
        } catch (Exception e) {
            Log.w(TAG, "Can not update hasDisplayCutout. " +
                    e);
        }
        return result;
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    public boolean getOppoIsNotch() {
        try {
            return reactContext.getPackageManager().hasSystemFeature("com.oppo.feature.screen.heteromorphism");
        } catch (Exception e) {
            return false;
        }
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    public boolean getVivoIsNotch() {
        boolean value = false;
        try {
            Class<?> cls = Class.forName("android.util.FtFeature");
            Method hideMethod = cls.getMethod("isFtFeatureSupport", int.class);
            Object object = cls.newInstance();
            value = (boolean) hideMethod.invoke(object, 0x00000020);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return value;
        }
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    public boolean getHuaweiIsNotch() {
        boolean ret = false;
        try {
            ClassLoader cl = reactContext.getClassLoader();
            Class HwNotchSizeUtil = cl.loadClass("com.huawei.android.util.HwNotchSizeUtil");
            Method get = HwNotchSizeUtil.getMethod("hasNotchInScreen");
            ret = (boolean) get.invoke(HwNotchSizeUtil);
        } catch (ClassNotFoundException e) {
            Log.e("AskSky", "hasNotchInScreen ClassNotFoundException");
        } catch (NoSuchMethodException e) {
            Log.e("AskSky", "hasNotchInScreen NoSuchMethodException");
        } catch (Exception e) {
            Log.e("AskSky", "hasNotchInScreen Exception");
        }
        return ret;
    }

    @NotNull
    @Override
    public String getName() {
        return TAG;
    }
    
    @ReactMethod
    public void getNativeStartupTime(Promise promise){
        try {
            Long startupTime = ((MainApplication)(MainApplication.instance)).getStartupTime();
            promise.resolve(Double.valueOf(startupTime));
        } catch (Exception e) {
            Log.e(TAG, "getNativeStartupTime", e);
            promise.resolve(null);
        }
    }

    @ReactMethod
    public void flushCookie() {
        CookieManager.getInstance().flush();
    }

    @ReactMethod
    public void launched() {
        // LocalBroadcastManager.getInstance(MainApplication.instance).sendBroadcast(new Intent(Constants.LAUNCHED_EVENT_NAME));
    }

    @ReactMethod
    public void exit() {
        android.os.Process.killProcess(android.os.Process.myPid());
    }

    @Override
    public void onTrimMemory(int level) {
        Log.i(TAG, "onTrimMemory" + level);
        Runtime rt = Runtime.getRuntime();
        WritableMap memoryInfo = memoryInfo();
        memoryInfo.putDouble("level", level);

        if (getReactApplicationContextIfActiveOrWarn() == null) return;
        reactContext
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                .emit("onTrimMemory", memoryInfo);
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        Log.i(TAG, "onConfigurationChanged" + newConfig);
    }

    @Override
    public void onLowMemory() {
        Log.i(TAG, "onLowMemory");
        if (getReactApplicationContextIfActiveOrWarn() == null) return;
        reactContext
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                .emit("onLowMemory", memoryInfo());
    }

    @Override
    public void onHostResume() {
        mainActivityVisible = true;
    }

    @Override
    public void onHostPause() {
        mainActivityVisible = false;
    }

    @Override
    public void onHostDestroy() {
        mainActivityVisible = false;
    }

    private WritableMap memoryInfo() {
        Runtime rt = Runtime.getRuntime();
        WritableMap map = Arguments.createMap();
        map.putDouble("total", rt.totalMemory() / MB);
        map.putDouble("max", rt.maxMemory() / MB);
        map.putDouble("free", rt.freeMemory() / MB);

        ActivityManager actMgr = (ActivityManager) getReactApplicationContext().getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo memInfo = new ActivityManager.MemoryInfo();
        if (actMgr != null) {
            actMgr.getMemoryInfo(memInfo);
            map.putDouble("system.total", memInfo.totalMem / MB);
            map.putDouble("system.avail", memInfo.availMem / MB);
        } else {
            System.err.println("Unable to getMemoryInfo. ActivityManager was null");
        }
        return map;
    }
}
