package me.perkd;

import android.content.BroadcastReceiver;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.Context;
import android.os.Build;
import android.os.SystemClock;
import android.webkit.WebView;

import androidx.annotation.Nullable;
//import org.jetbrains.annotations.Nullable;

import com.facebook.react.PackageList;
import com.facebook.react.ReactInstanceManager;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.oblador.keychain.KeychainModuleBuilder;
import com.reactnativenavigation.NavigationApplication;
import com.reactnativenavigation.react.NavigationReactNativeHost;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import me.perkd.utils.PerkdUtilsPackage;
import me.perkd.mlkit.BarcodeScanningPackage;
import com.oblador.keychain.KeychainPackage;


public class MainApplication extends NavigationApplication {

	private Long startupTime;

	public Long getStartupTime() {
		return startupTime;
	}

	private final ReactNativeHost mReactNativeHost = new NavigationReactNativeHost(this) {
		@Override
		public boolean getUseDeveloperSupport() {
			return BuildConfig.DEBUG;
		}

		@Override
		protected List<ReactPackage> getPackages() {
			List<ReactPackage> packages = new PackageList(this).getPackages();
			// Packages that cannot be autolinked yet can be added manually here, for example:
			// packages.add(new MyReactNativePackage());

			packages.add(new PerkdUtilsPackage());
			packages.add(new BarcodeScanningPackage());
			packages.add(new KeychainPackage(new KeychainModuleBuilder().withoutWarmUp()));

			return packages;
		}

		@Override
		protected String getJSMainModuleName() {
			return "index";
		}

		@Nullable
		@Override
		protected String getJSBundleFile() {
			return super.getJSBundleFile();
		}
	};


	@Override
	public ReactNativeHost getReactNativeHost() {
		return mReactNativeHost;
	}

	@Override
	public Intent registerReceiver(@Nullable BroadcastReceiver receiver, IntentFilter filter) {
		if (Build.VERSION.SDK_INT >= 34 && getApplicationInfo().targetSdkVersion >= 34) {
			return super.registerReceiver(receiver, filter, Context.RECEIVER_NOT_EXPORTED);
		} else {
			return super.registerReceiver(receiver, filter);
		}
	}

	@Override
	public void onCreate() {
		super.onCreate();

		initializeFlipper(this, getReactNativeHost().getReactInstanceManager());

		startupTime = SystemClock.uptimeMillis();

		disableWebView();
	}

	/**
	 * Loads Flipper in React Native templates.
	 *
	 * @param context context
	 */
	private static void initializeFlipper(Context context, ReactInstanceManager reactInstanceManager) {
		if (BuildConfig.DEBUG) {
			try {
        /*
         We use reflection here to pick up the class that initializes Flipper,
        since Flipper library is not available in release mode
        */
				Class<?> aClass = Class.forName("me.perkd.ReactNativeFlipper");
				aClass.getMethod("initializeFlipper", Context.class, ReactInstanceManager.class).invoke(null, context, reactInstanceManager);
			} catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
				e.printStackTrace();
			}
		}
	}

	private void disableWebView() {
		// disable webView in sub process
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {

			String processName = MainApplication.getProcessName();
			String packageName = this.getPackageName();

			if (!packageName.equals(this.getPackageName())) {
				WebView.setDataDirectorySuffix(processName);
			}
		}
	}
}