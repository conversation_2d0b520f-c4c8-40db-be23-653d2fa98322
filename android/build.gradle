// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "33.0.0"
        minSdkVersion = 26
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "21.4.7075529"
        supportLibVersion = "30.0.0"
        kotlinVersion = '1.6.21'
        facebookSdkVersion = "13.1.0"
        RNNKotlinVersion = kotlinVersion
        RNNKotlinStdlib = "kotlin-stdlib-jdk8"
    }
    repositories {
        google()
        mavenCentral()
        configurations.all {
            resolutionStrategy {
                force "com.facebook.soloader:soloader:0.10.4+"
            }
        }
    }
    dependencies {
        classpath('com.android.tools.build:gradle:7.4.2')
        classpath 'com.google.gms:google-services:4.3.10'
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

subprojects { subproject ->
    if(subproject['name'] == 'react-native-maskview'){
        subproject.configurations { compile { } }
    }

    subproject.afterEvaluate { project ->
        if (project.hasProperty('android')) {
            project.android {
                // Enable BuildConfig generation if custom BuildConfig fields are detected
                if (!buildFeatures.hasProperty("buildConfig") || !buildFeatures.buildConfig) {
                    buildFeatures.buildConfig = true
                }

                // Inject namespace if it's missing
                if (namespace == null) {
                    def manifestFile = android.sourceSets.main.manifest.srcFile
                    def manifestXml = new XmlSlurper().parse(manifestFile)
                    def packageName = <EMAIL>()
                    namespace = packageName
                }
            }
        }
    }
}

allprojects {
    repositories {
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            // Android JSC is installed from npm
            url("$rootDir/../node_modules/jsc-android/dist")
        }
        mavenCentral {
            // We don't want to fetch react-native from Maven Central as there are
            // older versions over there.
            content {
                excludeGroup "com.facebook.react"
            }
        }
        maven {
            url "$rootDir/../node_modules/@notifee/react-native/android/libs"
        }
        maven {
            // react-native-background-fetch
            url("${project(':react-native-background-fetch').projectDir}/libs")
        }
        jcenter()
        google()
        maven { url 'https://www.jitpack.io' }
    }
}
