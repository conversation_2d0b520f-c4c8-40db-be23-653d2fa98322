{"java.configuration.updateBuildConfiguration": "disabled", "cSpell.words": ["gradlew"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "[typescript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[typescriptreact]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "eslint.format.enable": true, "typescript.tsdk": "node_modules/typescript/lib"}