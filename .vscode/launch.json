{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
    
        {
            "name": "Attach to the React Native iOS - Experimental",
            "cwd": "${workspaceFolder}",
            "type": "reactnativedirect",
            "request": "attach",
            "platform": "ios",
            "port": 9221
        },
        
        {
            "name": "Debug Android",
            "cwd": "${workspaceFolder}",
            "type": "reactnative",
            "request": "launch",
            "platform": "android"
        },
        {
            "name": "Debug iOS",
            "cwd": "${workspaceFolder}",
            "type": "reactnative",
            "request": "attach",
            "platform": "ios",
            "target": "simulator"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch File",
            "program": "${file}"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Program",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "program": "${workspaceFolder}/start"
        }
    ]
}