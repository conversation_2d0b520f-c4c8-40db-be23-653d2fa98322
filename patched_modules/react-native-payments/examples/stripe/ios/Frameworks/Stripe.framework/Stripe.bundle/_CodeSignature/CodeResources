<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NK07MEvt0+HP7U8WXYTSkG2mhzM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3eggt5bctikioMWOhrJGqvywOCc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			sj5Wm1VIaZoeBrT6MLURX1swCss=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			n1o7PtGL1TOo7wRZ8SiYbyjtq2Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PbtT0lxjvJsc/ub401rII6Oysf0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Nxp5BUcUIBJj/2vO62m4y9jxdlo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mLnyV8tV3ZmRXFKBdkTysgfXaUs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>stp_card_amex.png</key>
		<data>
		sk9jX4Apn3xX9ONixp4x2ju5wQM=
		</data>
		<key><EMAIL></key>
		<data>
		60jHxSadPreZdZt7jZTvxhb17Pw=
		</data>
		<key><EMAIL></key>
		<data>
		usx4I3AqXJWMabSAzUyvwKhUn3U=
		</data>
		<key>stp_card_amex_template.png</key>
		<data>
		TnGnQY8KrJl45IqRPQ9EMXJCM5I=
		</data>
		<key><EMAIL></key>
		<data>
		55jt7FCCTTYVPuV4aVPFN2vSykI=
		</data>
		<key><EMAIL></key>
		<data>
		09XBdV8mk7GtfVtrPm+3/cgmGFc=
		</data>
		<key>stp_card_applepay.png</key>
		<data>
		GQKB6tEV+i0BcCHsFXWKmMshjJo=
		</data>
		<key><EMAIL></key>
		<data>
		j4/hnigxQQ6bT5k1GQN/RCCYeHk=
		</data>
		<key><EMAIL></key>
		<data>
		7/d8kDCnhugVtlacj9QMy3AR78c=
		</data>
		<key>stp_card_cvc.png</key>
		<data>
		hds+hnk2tWfhKetfAVdauZdaHsQ=
		</data>
		<key><EMAIL></key>
		<data>
		lYo40bU/z6jD3Py8t5TGrKsTs2w=
		</data>
		<key><EMAIL></key>
		<data>
		urKIxSGeE83wfaj/QWDgjNc98Jc=
		</data>
		<key>stp_card_cvc_amex.png</key>
		<data>
		LChnFX0FsU7zIiA9YVKBQEkdqbM=
		</data>
		<key><EMAIL></key>
		<data>
		wu3sglP+dc7DaNO/IIWzF6YAACU=
		</data>
		<key><EMAIL></key>
		<data>
		2F9GpkQY+nEettR5wFc5GrwGSAA=
		</data>
		<key>stp_card_diners.png</key>
		<data>
		acP/q8hd1jvUjivV+0DpbjdyccM=
		</data>
		<key><EMAIL></key>
		<data>
		b93X8h7+C8CUCn1gvKrNMXMvswg=
		</data>
		<key><EMAIL></key>
		<data>
		HodzoD0Lt0VtIQpN3vkhH+2bzPs=
		</data>
		<key>stp_card_diners_template.png</key>
		<data>
		uDqYJ1nYqRYHwI5hJdxXY2PPON8=
		</data>
		<key><EMAIL></key>
		<data>
		tXDxVavWwo6d1XCe+pLzBCzlCrI=
		</data>
		<key><EMAIL></key>
		<data>
		qa2wivb+4BUZuV0MjtoGyLWatMA=
		</data>
		<key>stp_card_discover.png</key>
		<data>
		ziahmSwOcigNvbGQDF7VJczJpDo=
		</data>
		<key><EMAIL></key>
		<data>
		NbcaYOtaVD4orzv57nODSPPU8kY=
		</data>
		<key><EMAIL></key>
		<data>
		+SJlO67GhacQmO2ymj/UwRgYXfo=
		</data>
		<key>stp_card_discover_template.png</key>
		<data>
		fritjsUVVFP7SHXj6Iu3MznGMJE=
		</data>
		<key><EMAIL></key>
		<data>
		F3S7aSxRpo5qoFJp5Rcro8CJb44=
		</data>
		<key><EMAIL></key>
		<data>
		uO/bJUs8kRhMaFMfUmqZk9Awerk=
		</data>
		<key>stp_card_error.png</key>
		<data>
		dJHtEjCof/xV0FE8VUg/DzlIcDA=
		</data>
		<key><EMAIL></key>
		<data>
		F7TNBbI4SU+fZUty2rekNr656yI=
		</data>
		<key><EMAIL></key>
		<data>
		jm1O1Z3A5ZS9/WvZl5gy6Lla3n8=
		</data>
		<key>stp_card_error_amex.png</key>
		<data>
		fcNpj5t0yPh1zpWYegJ9G9sElAU=
		</data>
		<key><EMAIL></key>
		<data>
		9cHRf+a8+VRD0fbQNHDCz1SNAEE=
		</data>
		<key><EMAIL></key>
		<data>
		redAbFzaqrp3lChrlvbdnBDmnnM=
		</data>
		<key>stp_card_form_back.png</key>
		<data>
		arYL6258S9MXvJjFsCCEt9TDc4c=
		</data>
		<key><EMAIL></key>
		<data>
		7HaunxnPYvF+G2ZSy9PA+ze3ef4=
		</data>
		<key><EMAIL></key>
		<data>
		o4soBcRRvck+BmmhDuQJBCFaYMY=
		</data>
		<key>stp_card_form_front.png</key>
		<data>
		FUvSd23IUAdKk1wB4DKeOyFG3iY=
		</data>
		<key><EMAIL></key>
		<data>
		y/F52HByKH9H1YeRTRTPcANT/jY=
		</data>
		<key><EMAIL></key>
		<data>
		Mo+YJ6OyS3/4c8VH4yVYjQHklq4=
		</data>
		<key>stp_card_jcb.png</key>
		<data>
		9Qqr6gF1fVXQoHQ47se8T+JIksE=
		</data>
		<key><EMAIL></key>
		<data>
		BTiJkm0wsS3pMnUm5uv3Oh1fpj0=
		</data>
		<key><EMAIL></key>
		<data>
		SjeEJrRq5qqUUtLX9nkvKHBUONA=
		</data>
		<key>stp_card_jcb_template.png</key>
		<data>
		gncctvKW/Rzg/w1idi+mYZZtkto=
		</data>
		<key><EMAIL></key>
		<data>
		y32rpZjFkwGvpEZm/aqno0BIpY4=
		</data>
		<key><EMAIL></key>
		<data>
		uDP9Pjd8IblfTi8ryiw32raPoQM=
		</data>
		<key>stp_card_mastercard.png</key>
		<data>
		J5dlu3O/oUROuHVhL6rLXLahvGM=
		</data>
		<key><EMAIL></key>
		<data>
		3D2j3r0BLWaIeO+NoUtjaLDkF3k=
		</data>
		<key><EMAIL></key>
		<data>
		kfpbxYzy8lmKkIOsRbKciEVTZWg=
		</data>
		<key>stp_card_mastercard_template.png</key>
		<data>
		2pRKoYqiI0bSMEXMEgHql6n8EJ0=
		</data>
		<key><EMAIL></key>
		<data>
		OAE0d/QocycHrrkJTKFLhJo9yys=
		</data>
		<key><EMAIL></key>
		<data>
		uF0rsVBHgkVIxcGMiVLJdfqnsUA=
		</data>
		<key>stp_card_unknown.png</key>
		<data>
		ozQ16kISiRJaR/qkdpMjE7nax+4=
		</data>
		<key><EMAIL></key>
		<data>
		60WDmnEXxahflWuE6RWSYGlqbt0=
		</data>
		<key><EMAIL></key>
		<data>
		Touf4iR7hjOSysxfUwV4i8KffLA=
		</data>
		<key>stp_card_visa.png</key>
		<data>
		1l1DgI4Fl56UxmIEG3wJGLqgHoA=
		</data>
		<key><EMAIL></key>
		<data>
		EasaxBK4jHzjQtEET1nV8KU6jI4=
		</data>
		<key><EMAIL></key>
		<data>
		GCIO3SaxQBLDQ0HjQWypMVkYUOc=
		</data>
		<key>stp_card_visa_template.png</key>
		<data>
		VJhgmIyGBZIb79dKkbEGnBwf44s=
		</data>
		<key><EMAIL></key>
		<data>
		S4shcF/6tuIH4ZCOUMuJmBykxq4=
		</data>
		<key><EMAIL></key>
		<data>
		5mns9YpSOMAnW1cytzW1k0eAzkU=
		</data>
		<key>stp_icon_add.png</key>
		<data>
		WVhVJrpxz+hy8upNdPrmx3XDxV0=
		</data>
		<key><EMAIL></key>
		<data>
		7pN2kVv1eafTYzjeakFEgQoF68Q=
		</data>
		<key><EMAIL></key>
		<data>
		I2/zUOCVYrCFXA6Qc5u+U6bhl58=
		</data>
		<key>stp_icon_checkmark.png</key>
		<data>
		92/pr1m6QRRxBvTJx3Iox4ikoYU=
		</data>
		<key><EMAIL></key>
		<data>
		a5QcogSP6Nk7eSuf5HL1GkFrBMM=
		</data>
		<key><EMAIL></key>
		<data>
		3+MbJQzrSylhATDPNGHm2GdpgDw=
		</data>
		<key>stp_icon_chevron_left.png</key>
		<data>
		IWVe/clQNuM92ukNcgKDJckhKjs=
		</data>
		<key><EMAIL></key>
		<data>
		ha62lDWfDNOKxJWKdwLw9lb/2SQ=
		</data>
		<key><EMAIL></key>
		<data>
		TRoSf2ndHfxTngE/PjIIliFtvdY=
		</data>
		<key>stp_shipping_form.png</key>
		<data>
		sHk6zeVOeieab+Rl2UlmQ3bmEFw=
		</data>
		<key><EMAIL></key>
		<data>
		QV5/dCpHS8EIN9KoSadU8bKUsAE=
		</data>
		<key><EMAIL></key>
		<data>
		nmDDwoJPaMVz7b0hIOy008ZVKgU=
		</data>
		<key>zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			J4X660kl+OXIx7q7BRgdau+XkjU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NK07MEvt0+HP7U8WXYTSkG2mhzM=
			</data>
			<key>hash2</key>
			<data>
			7Wfot+OSlz9AWTrUW8AasULomdjMYMlgU3O+dUHL8hI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3eggt5bctikioMWOhrJGqvywOCc=
			</data>
			<key>hash2</key>
			<data>
			61xO1RXnZm7Zwhe7vNesyPkR2L4O4HHlB3CO4y4zcK4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			sj5Wm1VIaZoeBrT6MLURX1swCss=
			</data>
			<key>hash2</key>
			<data>
			G7VVNsBO8/+NBi5UkmHJ3vAqStqGl0k6DXeWaG/hCy8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			n1o7PtGL1TOo7wRZ8SiYbyjtq2Y=
			</data>
			<key>hash2</key>
			<data>
			ZbzB4nAXnMMUA8N2QGSrakcIhC8ivn7mVZ0poeL6csI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PbtT0lxjvJsc/ub401rII6Oysf0=
			</data>
			<key>hash2</key>
			<data>
			EU5vg4xEGruxD+YVTQmm9F2wNCp3qCrRTWjAaM+B/q8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Nxp5BUcUIBJj/2vO62m4y9jxdlo=
			</data>
			<key>hash2</key>
			<data>
			mmRGXyXI7kQw8+JL/0G++xUddtT0la/8keqIbEwHGBU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mLnyV8tV3ZmRXFKBdkTysgfXaUs=
			</data>
			<key>hash2</key>
			<data>
			BC6kyvhOBW3REO14PeHBxkvwhm4q0YGjaFVxpbiBfx4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>stp_card_amex.png</key>
		<dict>
			<key>hash</key>
			<data>
			sk9jX4Apn3xX9ONixp4x2ju5wQM=
			</data>
			<key>hash2</key>
			<data>
			RoVHLa4oxomkmUqoRRUhOvZ5MZb6QR6iNovJCv8JK4c=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			60jHxSadPreZdZt7jZTvxhb17Pw=
			</data>
			<key>hash2</key>
			<data>
			Gm9ZzIG/zoGw5kLcjXCB6TRSgViyelD8JtvG70U1ejI=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			usx4I3AqXJWMabSAzUyvwKhUn3U=
			</data>
			<key>hash2</key>
			<data>
			cMXLbjoYZBr9biKGg4y0ABLO7hfDweM4ZAyUSvlq9S4=
			</data>
		</dict>
		<key>stp_card_amex_template.png</key>
		<dict>
			<key>hash</key>
			<data>
			TnGnQY8KrJl45IqRPQ9EMXJCM5I=
			</data>
			<key>hash2</key>
			<data>
			k9MfgcP/4WEA+L4oVENIc+1blURSVjoYBQReG9YtxSQ=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			55jt7FCCTTYVPuV4aVPFN2vSykI=
			</data>
			<key>hash2</key>
			<data>
			QVeT6Abw5uC36D/jcyM+jDAtzs9OnKg75eowHhYH6KI=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			09XBdV8mk7GtfVtrPm+3/cgmGFc=
			</data>
			<key>hash2</key>
			<data>
			RFMzLkYdwnRsnRNFdpKjfu6bPyWSpUVvEmL8b6O+v48=
			</data>
		</dict>
		<key>stp_card_applepay.png</key>
		<dict>
			<key>hash</key>
			<data>
			GQKB6tEV+i0BcCHsFXWKmMshjJo=
			</data>
			<key>hash2</key>
			<data>
			HLrGc2o9i5ZNHHUhvv6l6WyroG2dpCe9jdap80aBDXg=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			j4/hnigxQQ6bT5k1GQN/RCCYeHk=
			</data>
			<key>hash2</key>
			<data>
			oFMc/57Mx9D5aIgBxA/Bl019bF0NEw3sOXhbQ2ep67c=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			7/d8kDCnhugVtlacj9QMy3AR78c=
			</data>
			<key>hash2</key>
			<data>
			akjOHJsFBEhkH5fALYlIeb4z+jZkHBm6wSIloqNVfD0=
			</data>
		</dict>
		<key>stp_card_cvc.png</key>
		<dict>
			<key>hash</key>
			<data>
			hds+hnk2tWfhKetfAVdauZdaHsQ=
			</data>
			<key>hash2</key>
			<data>
			sbUse0EopZC3QOn13BEhDQtHDcEgOXOu2HgiCwjDJvM=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			lYo40bU/z6jD3Py8t5TGrKsTs2w=
			</data>
			<key>hash2</key>
			<data>
			1ebjYqw3WsXOx2RN4OsR40VU9MW2WwPmpOclDMRAR/0=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			urKIxSGeE83wfaj/QWDgjNc98Jc=
			</data>
			<key>hash2</key>
			<data>
			rV0yaAqwfhP+G5z+Vc6LZX98jnYZHMgcZAiK8V6nbkg=
			</data>
		</dict>
		<key>stp_card_cvc_amex.png</key>
		<dict>
			<key>hash</key>
			<data>
			LChnFX0FsU7zIiA9YVKBQEkdqbM=
			</data>
			<key>hash2</key>
			<data>
			WvaVewkqN/uVx8yny2QEIG2G+BQjoYsyFegQrQUBSjw=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			wu3sglP+dc7DaNO/IIWzF6YAACU=
			</data>
			<key>hash2</key>
			<data>
			DBzg8P0I1KGFueZ6HmUjJBIarUMaiJwpG5KC1y0OWFw=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			2F9GpkQY+nEettR5wFc5GrwGSAA=
			</data>
			<key>hash2</key>
			<data>
			LYnVskjdEjFiUOCGl9WfPjMz0S3ZSw6wuzzPDLYhf4s=
			</data>
		</dict>
		<key>stp_card_diners.png</key>
		<dict>
			<key>hash</key>
			<data>
			acP/q8hd1jvUjivV+0DpbjdyccM=
			</data>
			<key>hash2</key>
			<data>
			+H6FFxdNlhTSZqx0c0dAdPrivfOzqpsTpYWFWd5PXck=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			b93X8h7+C8CUCn1gvKrNMXMvswg=
			</data>
			<key>hash2</key>
			<data>
			JvwMePC2L2jGsYdPSIDNWd6H/O7iaBXzNTWMn6cbwGk=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			HodzoD0Lt0VtIQpN3vkhH+2bzPs=
			</data>
			<key>hash2</key>
			<data>
			hv1k8hrNG7zInldxfWH5FtYnXvuUF06GsORf853fXj0=
			</data>
		</dict>
		<key>stp_card_diners_template.png</key>
		<dict>
			<key>hash</key>
			<data>
			uDqYJ1nYqRYHwI5hJdxXY2PPON8=
			</data>
			<key>hash2</key>
			<data>
			a01yc7Jw2opFCjunuugihmoHTo+ohDFfKsqPud56Ge8=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			tXDxVavWwo6d1XCe+pLzBCzlCrI=
			</data>
			<key>hash2</key>
			<data>
			nF3SBFrP6VDlNuZ5Et8wo5qHHyQw9brDnOObtMpcp6Q=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			qa2wivb+4BUZuV0MjtoGyLWatMA=
			</data>
			<key>hash2</key>
			<data>
			GfT4o2iQ/Es5Vr8o+qw783+fFvgQGtpesF93QtDjnMc=
			</data>
		</dict>
		<key>stp_card_discover.png</key>
		<dict>
			<key>hash</key>
			<data>
			ziahmSwOcigNvbGQDF7VJczJpDo=
			</data>
			<key>hash2</key>
			<data>
			3Qk7ZWM4/+qsbHWZtTSmx1D/lPNhinLfgMl7mDAf0qI=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			NbcaYOtaVD4orzv57nODSPPU8kY=
			</data>
			<key>hash2</key>
			<data>
			a5vV6phDbE3t8cGD/BWjj9gvea7V/a9IRjiLSx2XcJc=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			+SJlO67GhacQmO2ymj/UwRgYXfo=
			</data>
			<key>hash2</key>
			<data>
			XJ+V1GdVvMF8sz3oc3F+Gh2gK4/sVJvyPsBEEFqnpfU=
			</data>
		</dict>
		<key>stp_card_discover_template.png</key>
		<dict>
			<key>hash</key>
			<data>
			fritjsUVVFP7SHXj6Iu3MznGMJE=
			</data>
			<key>hash2</key>
			<data>
			SXyz0o+4nqM+ISgi8OnzTU3OJXEXzuk945rMQLhKR/o=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			F3S7aSxRpo5qoFJp5Rcro8CJb44=
			</data>
			<key>hash2</key>
			<data>
			2Y0RPUFtsy7sTiioLLG5eFoj04GpdUBEQkK2GZsQqiU=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			uO/bJUs8kRhMaFMfUmqZk9Awerk=
			</data>
			<key>hash2</key>
			<data>
			Ov7ve9EGkDHJl9K9QcSImY+nojjOIfsKZ/18zs0IDYU=
			</data>
		</dict>
		<key>stp_card_error.png</key>
		<dict>
			<key>hash</key>
			<data>
			dJHtEjCof/xV0FE8VUg/DzlIcDA=
			</data>
			<key>hash2</key>
			<data>
			4lNTsQM6fEgHKPg5wmrpGYjsY1/6xYRCoyhBThXeWuQ=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			F7TNBbI4SU+fZUty2rekNr656yI=
			</data>
			<key>hash2</key>
			<data>
			P+tB1cLzj2YonF4tuXCNJ4mDBN0TS6wb/rmvi5viHoE=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			jm1O1Z3A5ZS9/WvZl5gy6Lla3n8=
			</data>
			<key>hash2</key>
			<data>
			5/WJsQyqV/XBQ9os/3U9UrPaZP3R8HzKf4SozlpNinY=
			</data>
		</dict>
		<key>stp_card_error_amex.png</key>
		<dict>
			<key>hash</key>
			<data>
			fcNpj5t0yPh1zpWYegJ9G9sElAU=
			</data>
			<key>hash2</key>
			<data>
			Td7EWoxio1cQ9jJoB0bs3d9Kygm5yLKz/IE/a4HFsDY=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			9cHRf+a8+VRD0fbQNHDCz1SNAEE=
			</data>
			<key>hash2</key>
			<data>
			SZ9c6B2OwFbjbabRK0F0rdc2kyfQ4A2Wf4EjbLTn640=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			redAbFzaqrp3lChrlvbdnBDmnnM=
			</data>
			<key>hash2</key>
			<data>
			9HLHkRWk+g3KQmA/0D3j7TzUJikBkyfRbl4R6dvjUDk=
			</data>
		</dict>
		<key>stp_card_form_back.png</key>
		<dict>
			<key>hash</key>
			<data>
			arYL6258S9MXvJjFsCCEt9TDc4c=
			</data>
			<key>hash2</key>
			<data>
			tW15uRznSo9drAeBI/1aJ8Bbe1NI5DJDp9dNoSkYjzw=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			7HaunxnPYvF+G2ZSy9PA+ze3ef4=
			</data>
			<key>hash2</key>
			<data>
			ao1NRHUnIrIDM968vYojlPbmCvscHPfhBBdPqobLtoM=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			o4soBcRRvck+BmmhDuQJBCFaYMY=
			</data>
			<key>hash2</key>
			<data>
			MI+kt9hpWqXcndil4sENgMOFTB8h+MrXRj9PKxU+DX0=
			</data>
		</dict>
		<key>stp_card_form_front.png</key>
		<dict>
			<key>hash</key>
			<data>
			FUvSd23IUAdKk1wB4DKeOyFG3iY=
			</data>
			<key>hash2</key>
			<data>
			pz+4+iuj1VaqHeygNjHMUysh8i7wKsM3Z8Z1WwoXuyw=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			y/F52HByKH9H1YeRTRTPcANT/jY=
			</data>
			<key>hash2</key>
			<data>
			gfpo36XBs2ZSNgrO2WqN9A4L1jGCl01sjs3FZ0TM+j0=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			Mo+YJ6OyS3/4c8VH4yVYjQHklq4=
			</data>
			<key>hash2</key>
			<data>
			frhe+FddLL+GNW6Jz7R10b0ClPaCp6Iz4+vD5q5M6UM=
			</data>
		</dict>
		<key>stp_card_jcb.png</key>
		<dict>
			<key>hash</key>
			<data>
			9Qqr6gF1fVXQoHQ47se8T+JIksE=
			</data>
			<key>hash2</key>
			<data>
			wz3qJLNOAOGFNiMoCwiDztWVrI9Hf0WSO7IjxYVEIlk=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			BTiJkm0wsS3pMnUm5uv3Oh1fpj0=
			</data>
			<key>hash2</key>
			<data>
			9hIS1l2WHr7FUnq3nl+dtOfF/MMs+PY1kDlnP7Ldo44=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			SjeEJrRq5qqUUtLX9nkvKHBUONA=
			</data>
			<key>hash2</key>
			<data>
			KMgA9SZWoeTDaZ0fWiIuiG4jcgKsEshpVtppY6/JYIw=
			</data>
		</dict>
		<key>stp_card_jcb_template.png</key>
		<dict>
			<key>hash</key>
			<data>
			gncctvKW/Rzg/w1idi+mYZZtkto=
			</data>
			<key>hash2</key>
			<data>
			sB4bSAMo/fXeeSTrJCDd1tTmry7f1BG8WBbJudldMZ0=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			y32rpZjFkwGvpEZm/aqno0BIpY4=
			</data>
			<key>hash2</key>
			<data>
			Se8aTQPK+OitSTNtMjfq9dizRgAbHmkC6f+oISiYc/o=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			uDP9Pjd8IblfTi8ryiw32raPoQM=
			</data>
			<key>hash2</key>
			<data>
			MElHqS4nSZi0YTAPuCSC5F1EYztKro6mnoMqjagRzAA=
			</data>
		</dict>
		<key>stp_card_mastercard.png</key>
		<dict>
			<key>hash</key>
			<data>
			J5dlu3O/oUROuHVhL6rLXLahvGM=
			</data>
			<key>hash2</key>
			<data>
			vh8OC+6ZXcnfxP7aJM6Mjdwo3Om3a9TjgxWKWYd+aec=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			3D2j3r0BLWaIeO+NoUtjaLDkF3k=
			</data>
			<key>hash2</key>
			<data>
			lvSv3U31AxHSfHRkQcttlHBdEqweqsDBJdEJNRfxIvM=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			kfpbxYzy8lmKkIOsRbKciEVTZWg=
			</data>
			<key>hash2</key>
			<data>
			Kaq6UXoBvqbtpa8jm0cy0w0tvsUPoA3+rvOIjDQftKc=
			</data>
		</dict>
		<key>stp_card_mastercard_template.png</key>
		<dict>
			<key>hash</key>
			<data>
			2pRKoYqiI0bSMEXMEgHql6n8EJ0=
			</data>
			<key>hash2</key>
			<data>
			3Gd+OTtu6LUjSE2IXf4Sz6EwwBlEVYZYDIcAjs4yW3M=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			OAE0d/QocycHrrkJTKFLhJo9yys=
			</data>
			<key>hash2</key>
			<data>
			9FWWBtds6ENXeKPmz/V1N48qM4Bo4dYCqZMn64Wt5Pc=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			uF0rsVBHgkVIxcGMiVLJdfqnsUA=
			</data>
			<key>hash2</key>
			<data>
			fpk6Bssrfl5k/cM4EuxPVfLvP8mqNLXXL3RbDorvmZ0=
			</data>
		</dict>
		<key>stp_card_unknown.png</key>
		<dict>
			<key>hash</key>
			<data>
			ozQ16kISiRJaR/qkdpMjE7nax+4=
			</data>
			<key>hash2</key>
			<data>
			XFCCHi3ebw7ND2MSOGf2bPDnAvW95YWzCY45jbG8wmI=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			60WDmnEXxahflWuE6RWSYGlqbt0=
			</data>
			<key>hash2</key>
			<data>
			fFCszDcnkhnDsa56UgsTALAjFA+THRZ+oDNHkbjMNS4=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			Touf4iR7hjOSysxfUwV4i8KffLA=
			</data>
			<key>hash2</key>
			<data>
			JUGZfIiJrdFxAKuBUL2YqABk7oCDqPk1q7gddnVq8B8=
			</data>
		</dict>
		<key>stp_card_visa.png</key>
		<dict>
			<key>hash</key>
			<data>
			1l1DgI4Fl56UxmIEG3wJGLqgHoA=
			</data>
			<key>hash2</key>
			<data>
			7gF6MG+QPq19WP4Sqs9+yWsCXw0afcIsGYWltbJCa1w=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			EasaxBK4jHzjQtEET1nV8KU6jI4=
			</data>
			<key>hash2</key>
			<data>
			PIqNvOZefbpT3fdbqc8qh4CZCRQhFnLfi9f0gs9/kKk=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			GCIO3SaxQBLDQ0HjQWypMVkYUOc=
			</data>
			<key>hash2</key>
			<data>
			2eqkpaM4g6c8w6gvKUyLfSnak5dxQdclxk7L55vQhZI=
			</data>
		</dict>
		<key>stp_card_visa_template.png</key>
		<dict>
			<key>hash</key>
			<data>
			VJhgmIyGBZIb79dKkbEGnBwf44s=
			</data>
			<key>hash2</key>
			<data>
			6pthMxrymD43uva5VSeXY6PW2vKAEgkBraQAPzSM7R8=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			S4shcF/6tuIH4ZCOUMuJmBykxq4=
			</data>
			<key>hash2</key>
			<data>
			doiSADsWq6p16C/fymgR2KRAg4kdLPGEnOmbHqWOwKo=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			5mns9YpSOMAnW1cytzW1k0eAzkU=
			</data>
			<key>hash2</key>
			<data>
			z71hmhHMY0ls785OusHSORztw7/H103OquucbgiW2QQ=
			</data>
		</dict>
		<key>stp_icon_add.png</key>
		<dict>
			<key>hash</key>
			<data>
			WVhVJrpxz+hy8upNdPrmx3XDxV0=
			</data>
			<key>hash2</key>
			<data>
			KJYAh7qg+8JbMwmTBbZLFhMau2SklzbaAviJgjpwvZs=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			7pN2kVv1eafTYzjeakFEgQoF68Q=
			</data>
			<key>hash2</key>
			<data>
			98cHqn/HmXfoSYI5eaXeMkudlfZNpD/4ZNYH7Ki0u7A=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			I2/zUOCVYrCFXA6Qc5u+U6bhl58=
			</data>
			<key>hash2</key>
			<data>
			J8xfcgpCtm9HUZOTLGPlhKKrc6GOh2uREHzMZz/PHIM=
			</data>
		</dict>
		<key>stp_icon_checkmark.png</key>
		<dict>
			<key>hash</key>
			<data>
			92/pr1m6QRRxBvTJx3Iox4ikoYU=
			</data>
			<key>hash2</key>
			<data>
			eDdEyfY25owftLteyZOlqPW5lzd2cLYh6rwyaDxGLn8=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			a5QcogSP6Nk7eSuf5HL1GkFrBMM=
			</data>
			<key>hash2</key>
			<data>
			qNbjCroBAbV5ASZ2ps1eGx/UmG3s7AS142FjfsR0p88=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			3+MbJQzrSylhATDPNGHm2GdpgDw=
			</data>
			<key>hash2</key>
			<data>
			lI4yHTWBdu8egr5JSrKa+cTysfuej0rn2XASbDfeakE=
			</data>
		</dict>
		<key>stp_icon_chevron_left.png</key>
		<dict>
			<key>hash</key>
			<data>
			IWVe/clQNuM92ukNcgKDJckhKjs=
			</data>
			<key>hash2</key>
			<data>
			tuQW96fNeJ2bw5o5s02TenZxfjCqLUMz5XZxgBU+cGs=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			ha62lDWfDNOKxJWKdwLw9lb/2SQ=
			</data>
			<key>hash2</key>
			<data>
			pHEMFmc2lqFtzqZ3rziTJrJmZqHdJUc+FOP4IuIipvc=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			TRoSf2ndHfxTngE/PjIIliFtvdY=
			</data>
			<key>hash2</key>
			<data>
			uHUKHOeK54snLaR7pm2swlhKGpQ9dRRmj+3NskphI44=
			</data>
		</dict>
		<key>stp_shipping_form.png</key>
		<dict>
			<key>hash</key>
			<data>
			sHk6zeVOeieab+Rl2UlmQ3bmEFw=
			</data>
			<key>hash2</key>
			<data>
			F57k1WE/vYNPQ+RkVxwsT3wrg58PzmKpRt5IC5Pkwas=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			QV5/dCpHS8EIN9KoSadU8bKUsAE=
			</data>
			<key>hash2</key>
			<data>
			hJudOuU623zf4C17sEo8ZDHcwGLdK0tGquuVwSRmf7g=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			nmDDwoJPaMVz7b0hIOy008ZVKgU=
			</data>
			<key>hash2</key>
			<data>
			QL1PnjZXC/UmoVdYEy+oJzmchonnpA1wuFRVfdD+qQE=
			</data>
		</dict>
		<key>zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			J4X660kl+OXIx7q7BRgdau+XkjU=
			</data>
			<key>hash2</key>
			<data>
			G7XPE++OueMIeoFiWSR8FfmwLVM0uP78G4BC9bomYg0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
