import re

# To learn about <PERSON> see [Doc<PERSON>](https://buckbuild.com/).
# To run your application with <PERSON>:
# - install Buck
# - `npm start` - to start the packager
# - `cd android`
# - `keytool -genkey -v -keystore keystores/debug.keystore -storepass android -alias androiddebugkey -keypass android -dname "CN=Android Debug,O=Android,C=US"`
# - `./gradlew :app:copyDownloadableDepsToLibs` - make all Gradle compile dependencies available to Buck
# - `buck install -r android/app` - compile, install and run application
#

lib_deps = []
for jarfile in glob(['libs/*.jar']):
  name = 'jars__' + re.sub(r'^.*/([^/]+)\.jar$', r'\1', jarfile)
  lib_deps.append(':' + name)
  prebuilt_jar(
    name = name,
    binary_jar = jarfile,
  )

for aarfile in glob(['libs/*.aar']):
  name = 'aars__' + re.sub(r'^.*/([^/]+)\.aar$', r'\1', aarfile)
  lib_deps.append(':' + name)
  android_prebuilt_aar(
    name = name,
    aar = aarfile,
  )

android_library(
  name = 'all-libs',
  exported_deps = lib_deps
)

android_library(
  name = 'app-code',
  srcs = glob([
    'src/main/java/**/*.java',
  ]),
  deps = [
    ':all-libs',
    ':build_config',
    ':res',
  ],
)

android_build_config(
  name = 'build_config',
  package = 'com.stripeexample',
)

android_resource(
  name = 'res',
  res = 'src/main/res',
  package = 'com.stripeexample',
)

android_binary(
  name = 'app',
  package_type = 'debug',
  manifest = 'src/main/AndroidManifest.xml',
  keystore = '//android/keystores:debug',
  deps = [
    ':app-code',
  ],
)
