// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		00C302E51ABCBA2D00DB3ED1 /* libRCTActionSheet.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302AC1ABCB8CE00DB3ED1 /* libRCTActionSheet.a */; };
		00C302E71ABCBA2D00DB3ED1 /* libRCTGeolocation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302BA1ABCB90400DB3ED1 /* libRCTGeolocation.a */; };
		00C302E81ABCBA2D00DB3ED1 /* libRCTImage.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302C01ABCB91800DB3ED1 /* libRCTImage.a */; };
		00C302E91ABCBA2D00DB3ED1 /* libRCTNetwork.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302DC1ABCB9D200DB3ED1 /* libRCTNetwork.a */; };
		00C302EA1ABCBA2D00DB3ED1 /* libRCTVibration.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302E41ABCB9EE00DB3ED1 /* libRCTVibration.a */; };
		00E356F31AD99517003FC87E /* exampleTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* exampleTests.m */; };
		133E29F31AD74F7200F7D852 /* libRCTLinking.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 78C398B91ACF4ADC00677621 /* libRCTLinking.a */; };
		139105C61AF99C1200B5F7CC /* libRCTSettings.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 139105C11AF99BAD00B5F7CC /* libRCTSettings.a */; };
		139FDEF61B0652A700C62182 /* libRCTWebSocket.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 139FDEF41B06529B00C62182 /* libRCTWebSocket.a */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		13B07FBD1A68108700A75B9A /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB11A68108700A75B9A /* LaunchScreen.xib */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		140ED2AC1D01E1AD002B40FF /* libReact.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 146834041AC3E56700842450 /* libReact.a */; };
		146834051AC3E58100842450 /* libReact.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 146834041AC3E56700842450 /* libReact.a */; };
		2D02E4BC1E0B4A80006451C7 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		2D02E4BD1E0B4A84006451C7 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		2D02E4BF1E0B4AB3006451C7 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		2D02E4C21E0B4AEC006451C7 /* libRCTAnimation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5E9157351DD0AC6500FF2AA8 /* libRCTAnimation.a */; };
		2D02E4C31E0B4AEC006451C7 /* libRCTImage-tvOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3DAD3E841DF850E9000B6D8A /* libRCTImage-tvOS.a */; };
		2D02E4C41E0B4AEC006451C7 /* libRCTLinking-tvOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3DAD3E881DF850E9000B6D8A /* libRCTLinking-tvOS.a */; };
		2D02E4C51E0B4AEC006451C7 /* libRCTNetwork-tvOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3DAD3E8C1DF850E9000B6D8A /* libRCTNetwork-tvOS.a */; };
		2D02E4C61E0B4AEC006451C7 /* libRCTSettings-tvOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3DAD3E901DF850E9000B6D8A /* libRCTSettings-tvOS.a */; };
		2D02E4C71E0B4AEC006451C7 /* libRCTText-tvOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3DAD3E941DF850E9000B6D8A /* libRCTText-tvOS.a */; };
		2D02E4C81E0B4AEC006451C7 /* libRCTWebSocket-tvOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3DAD3E991DF850E9000B6D8A /* libRCTWebSocket-tvOS.a */; };
		2D16E6881FA4F8E400B85C8A /* libReact.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 2D16E6891FA4F8E400B85C8A /* libReact.a */; };
		2DCD954D1E0B4F2C00145EB5 /* exampleTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* exampleTests.m */; };
		2DF0FFEE2056DD460020B375 /* libReact.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3DAD3EA31DF850E9000B6D8A /* libReact.a */; };
		5E9157361DD0AC6A00FF2AA8 /* libRCTAnimation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5E9157331DD0AC6500FF2AA8 /* libRCTAnimation.a */; };
		7771F46320C886B20051AC63 /* libReactNativePayments.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7771F46220C886970051AC63 /* libReactNativePayments.a */; };
		832341BD1AAA6AB300B99B32 /* libRCTText.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 832341B51AAA6A8300B99B32 /* libRCTText.a */; };
		ADBDB9381DFEBF1600ED6528 /* libRCTBlob.a in Frameworks */ = {isa = PBXBuildFile; fileRef = ADBDB9271DFEBF0700ED6528 /* libRCTBlob.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00C302AB1ABCB8CE00DB3ED1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302A71ABCB8CE00DB3ED1 /* RCTActionSheet.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RCTActionSheet;
		};
		00C302B91ABCB90400DB3ED1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302B51ABCB90400DB3ED1 /* RCTGeolocation.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RCTGeolocation;
		};
		00C302BF1ABCB91800DB3ED1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 58B5115D1A9E6B3D00147676;
			remoteInfo = RCTImage;
		};
		00C302DB1ABCB9D200DB3ED1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 58B511DB1A9E6C8500147676;
			remoteInfo = RCTNetwork;
		};
		00C302E31ABCB9EE00DB3ED1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302DF1ABCB9EE00DB3ED1 /* RCTVibration.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 832C81801AAF6DEF007FA2F7;
			remoteInfo = RCTVibration;
		};
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = example;
		};
		139105C01AF99BAD00B5F7CC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RCTSettings;
		};
		139FDEF31B06529B00C62182 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3C86DF461ADF2C930047B81A;
			remoteInfo = RCTWebSocket;
		};
		146834031AC3E56700842450 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 83CBBA2E1A601D0E00E9B192;
			remoteInfo = React;
		};
		2D02E4911E0B4A5D006451C7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2D02E47A1E0B4A5D006451C7;
			remoteInfo = "example-tvOS";
		};
		2D16E6711FA4F8DC00B85C8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = ADBDB91F1DFEBF0600ED6528 /* RCTBlob.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = ADD01A681E09402E00F6D226;
			remoteInfo = "RCTBlob-tvOS";
		};
		2D16E6831FA4F8DC00B85C8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3DBE0D001F3B181A0099AA32;
			remoteInfo = fishhook;
		};
		2D16E6851FA4F8DC00B85C8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3DBE0D0D1F3B181C0099AA32;
			remoteInfo = "fishhook-tvOS";
		};
		2DF0FFDE2056DD460020B375 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = EBF21BDC1FC498900052F4D5;
			remoteInfo = jsinspector;
		};
		2DF0FFE02056DD460020B375 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = EBF21BFA1FC4989A0052F4D5;
			remoteInfo = "jsinspector-tvOS";
		};
		2DF0FFE22056DD460020B375 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 139D7ECE1E25DB7D00323FB7;
			remoteInfo = "third-party";
		};
		2DF0FFE42056DD460020B375 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D383D3C1EBD27B6005632C8;
			remoteInfo = "third-party-tvOS";
		};
		2DF0FFE62056DD460020B375 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 139D7E881E25C6D100323FB7;
			remoteInfo = "double-conversion";
		};
		2DF0FFE82056DD460020B375 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D383D621EBD27B9005632C8;
			remoteInfo = "double-conversion-tvOS";
		};
		2DF0FFEA2056DD460020B375 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 9936F3131F5F2E4B0010BF04;
			remoteInfo = privatedata;
		};
		2DF0FFEC2056DD460020B375 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 9936F32F1F5F2E5B0010BF04;
			remoteInfo = "privatedata-tvOS";
		};
		3DAD3E831DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A283A1D9B042B00D4039D;
			remoteInfo = "RCTImage-tvOS";
		};
		3DAD3E871DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28471D9B043800D4039D;
			remoteInfo = "RCTLinking-tvOS";
		};
		3DAD3E8B1DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28541D9B044C00D4039D;
			remoteInfo = "RCTNetwork-tvOS";
		};
		3DAD3E8F1DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28611D9B046600D4039D;
			remoteInfo = "RCTSettings-tvOS";
		};
		3DAD3E931DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A287B1D9B048500D4039D;
			remoteInfo = "RCTText-tvOS";
		};
		3DAD3E981DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28881D9B049200D4039D;
			remoteInfo = "RCTWebSocket-tvOS";
		};
		3DAD3EA21DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28131D9B038B00D4039D;
			remoteInfo = "React-tvOS";
		};
		3DAD3EA41DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D3C059A1DE3340900C268FA;
			remoteInfo = yoga;
		};
		3DAD3EA61DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D3C06751DE3340C00C268FA;
			remoteInfo = "yoga-tvOS";
		};
		3DAD3EA81DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D3CD9251DE5FBEC00167DC4;
			remoteInfo = cxxreact;
		};
		3DAD3EAA1DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D3CD9321DE5FBEE00167DC4;
			remoteInfo = "cxxreact-tvOS";
		};
		3DAD3EAC1DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D3CD90B1DE5FBD600167DC4;
			remoteInfo = jschelpers;
		};
		3DAD3EAE1DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D3CD9181DE5FBD800167DC4;
			remoteInfo = "jschelpers-tvOS";
		};
		5E9157321DD0AC6500FF2AA8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 5E91572D1DD0AC6500FF2AA8 /* RCTAnimation.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RCTAnimation;
		};
		5E9157341DD0AC6500FF2AA8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 5E91572D1DD0AC6500FF2AA8 /* RCTAnimation.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28201D9B03D100D4039D;
			remoteInfo = "RCTAnimation-tvOS";
		};
		7771F46120C886970051AC63 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7771F43820C886970051AC63 /* ReactNativePayments.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = ReactNativePayments;
		};
		78C398B81ACF4ADC00677621 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RCTLinking;
		};
		832341B41AAA6A8300B99B32 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 58B5119B1A9E6C1200147676;
			remoteInfo = RCTText;
		};
		ADBDB9261DFEBF0700ED6528 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = ADBDB91F1DFEBF0600ED6528 /* RCTBlob.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 358F4ED71D1E81A9004DF814;
			remoteInfo = RCTBlob;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		008F07F21AC5B25A0029DE68 /* main.jsbundle */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = main.jsbundle; sourceTree = "<group>"; };
		00C302A71ABCB8CE00DB3ED1 /* RCTActionSheet.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTActionSheet.xcodeproj; path = "../node_modules/react-native/Libraries/ActionSheetIOS/RCTActionSheet.xcodeproj"; sourceTree = "<group>"; };
		00C302B51ABCB90400DB3ED1 /* RCTGeolocation.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTGeolocation.xcodeproj; path = "../node_modules/react-native/Libraries/Geolocation/RCTGeolocation.xcodeproj"; sourceTree = "<group>"; };
		00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTImage.xcodeproj; path = "../node_modules/react-native/Libraries/Image/RCTImage.xcodeproj"; sourceTree = "<group>"; };
		00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTNetwork.xcodeproj; path = "../node_modules/react-native/Libraries/Network/RCTNetwork.xcodeproj"; sourceTree = "<group>"; };
		00C302DF1ABCB9EE00DB3ED1 /* RCTVibration.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTVibration.xcodeproj; path = "../node_modules/react-native/Libraries/Vibration/RCTVibration.xcodeproj"; sourceTree = "<group>"; };
		00E356EE1AD99517003FC87E /* exampleTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = exampleTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* exampleTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = exampleTests.m; sourceTree = "<group>"; };
		139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTSettings.xcodeproj; path = "../node_modules/react-native/Libraries/Settings/RCTSettings.xcodeproj"; sourceTree = "<group>"; };
		139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTWebSocket.xcodeproj; path = "../node_modules/react-native/Libraries/WebSocket/RCTWebSocket.xcodeproj"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* example.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = example.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = example/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AppDelegate.m; path = example/AppDelegate.m; sourceTree = "<group>"; };
		13B07FB21A68108700A75B9A /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/LaunchScreen.xib; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = example/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = example/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = example/main.m; sourceTree = "<group>"; };
		146833FF1AC3E56700842450 /* React.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = React.xcodeproj; path = "../node_modules/react-native/React/React.xcodeproj"; sourceTree = "<group>"; };
		2D02E47B1E0B4A5D006451C7 /* example-tvOS.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "example-tvOS.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		2D02E4901E0B4A5D006451C7 /* example-tvOSTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "example-tvOSTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		2D16E6891FA4F8E400B85C8A /* libReact.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libReact.a; sourceTree = BUILT_PRODUCTS_DIR; };
		5E91572D1DD0AC6500FF2AA8 /* RCTAnimation.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTAnimation.xcodeproj; path = "../node_modules/react-native/Libraries/NativeAnimation/RCTAnimation.xcodeproj"; sourceTree = "<group>"; };
		7771F43820C886970051AC63 /* ReactNativePayments.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = ReactNativePayments.xcodeproj; path = ../../../lib/ios/ReactNativePayments.xcodeproj; sourceTree = "<group>"; };
		78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTLinking.xcodeproj; path = "../node_modules/react-native/Libraries/LinkingIOS/RCTLinking.xcodeproj"; sourceTree = "<group>"; };
		832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTText.xcodeproj; path = "../node_modules/react-native/Libraries/Text/RCTText.xcodeproj"; sourceTree = "<group>"; };
		ADBDB91F1DFEBF0600ED6528 /* RCTBlob.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTBlob.xcodeproj; path = "../node_modules/react-native/Libraries/Blob/RCTBlob.xcodeproj"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				140ED2AC1D01E1AD002B40FF /* libReact.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7771F46320C886B20051AC63 /* libReactNativePayments.a in Frameworks */,
				ADBDB9381DFEBF1600ED6528 /* libRCTBlob.a in Frameworks */,
				5E9157361DD0AC6A00FF2AA8 /* libRCTAnimation.a in Frameworks */,
				146834051AC3E58100842450 /* libReact.a in Frameworks */,
				5E9157361DD0AC6A00FF2AA8 /* libRCTAnimation.a in Frameworks */,
				00C302E51ABCBA2D00DB3ED1 /* libRCTActionSheet.a in Frameworks */,
				00C302E71ABCBA2D00DB3ED1 /* libRCTGeolocation.a in Frameworks */,
				00C302E81ABCBA2D00DB3ED1 /* libRCTImage.a in Frameworks */,
				133E29F31AD74F7200F7D852 /* libRCTLinking.a in Frameworks */,
				00C302E91ABCBA2D00DB3ED1 /* libRCTNetwork.a in Frameworks */,
				139105C61AF99C1200B5F7CC /* libRCTSettings.a in Frameworks */,
				832341BD1AAA6AB300B99B32 /* libRCTText.a in Frameworks */,
				00C302EA1ABCBA2D00DB3ED1 /* libRCTVibration.a in Frameworks */,
				139FDEF61B0652A700C62182 /* libRCTWebSocket.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E4781E0B4A5D006451C7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2D16E6881FA4F8E400B85C8A /* libReact.a in Frameworks */,
				2D02E4C21E0B4AEC006451C7 /* libRCTAnimation.a in Frameworks */,
				2D02E4C31E0B4AEC006451C7 /* libRCTImage-tvOS.a in Frameworks */,
				2D02E4C41E0B4AEC006451C7 /* libRCTLinking-tvOS.a in Frameworks */,
				2D02E4C51E0B4AEC006451C7 /* libRCTNetwork-tvOS.a in Frameworks */,
				2D02E4C61E0B4AEC006451C7 /* libRCTSettings-tvOS.a in Frameworks */,
				2D02E4C71E0B4AEC006451C7 /* libRCTText-tvOS.a in Frameworks */,
				2D02E4C81E0B4AEC006451C7 /* libRCTWebSocket-tvOS.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E48D1E0B4A5D006451C7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2DF0FFEE2056DD460020B375 /* libReact.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00C302A81ABCB8CE00DB3ED1 /* Products */ = {
			isa = PBXGroup;
			children = (
				00C302AC1ABCB8CE00DB3ED1 /* libRCTActionSheet.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		00C302B61ABCB90400DB3ED1 /* Products */ = {
			isa = PBXGroup;
			children = (
				00C302BA1ABCB90400DB3ED1 /* libRCTGeolocation.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		00C302BC1ABCB91800DB3ED1 /* Products */ = {
			isa = PBXGroup;
			children = (
				00C302C01ABCB91800DB3ED1 /* libRCTImage.a */,
				3DAD3E841DF850E9000B6D8A /* libRCTImage-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		00C302D41ABCB9D200DB3ED1 /* Products */ = {
			isa = PBXGroup;
			children = (
				00C302DC1ABCB9D200DB3ED1 /* libRCTNetwork.a */,
				3DAD3E8C1DF850E9000B6D8A /* libRCTNetwork-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		00C302E01ABCB9EE00DB3ED1 /* Products */ = {
			isa = PBXGroup;
			children = (
				00C302E41ABCB9EE00DB3ED1 /* libRCTVibration.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		00E356EF1AD99517003FC87E /* exampleTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* exampleTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = exampleTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		139105B71AF99BAD00B5F7CC /* Products */ = {
			isa = PBXGroup;
			children = (
				139105C11AF99BAD00B5F7CC /* libRCTSettings.a */,
				3DAD3E901DF850E9000B6D8A /* libRCTSettings-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		139FDEE71B06529A00C62182 /* Products */ = {
			isa = PBXGroup;
			children = (
				139FDEF41B06529B00C62182 /* libRCTWebSocket.a */,
				3DAD3E991DF850E9000B6D8A /* libRCTWebSocket-tvOS.a */,
				2D16E6841FA4F8DC00B85C8A /* libfishhook.a */,
				2D16E6861FA4F8DC00B85C8A /* libfishhook-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* example */ = {
			isa = PBXGroup;
			children = (
				008F07F21AC5B25A0029DE68 /* main.jsbundle */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.m */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				13B07FB11A68108700A75B9A /* LaunchScreen.xib */,
				13B07FB71A68108700A75B9A /* main.m */,
			);
			name = example;
			sourceTree = "<group>";
		};
		146834001AC3E56700842450 /* Products */ = {
			isa = PBXGroup;
			children = (
				146834041AC3E56700842450 /* libReact.a */,
				3DAD3EA31DF850E9000B6D8A /* libReact.a */,
				3DAD3EA51DF850E9000B6D8A /* libyoga.a */,
				3DAD3EA71DF850E9000B6D8A /* libyoga.a */,
				3DAD3EA91DF850E9000B6D8A /* libcxxreact.a */,
				3DAD3EAB1DF850E9000B6D8A /* libcxxreact.a */,
				3DAD3EAD1DF850E9000B6D8A /* libjschelpers.a */,
				3DAD3EAF1DF850E9000B6D8A /* libjschelpers.a */,
				2DF0FFDF2056DD460020B375 /* libjsinspector.a */,
				2DF0FFE12056DD460020B375 /* libjsinspector-tvOS.a */,
				2DF0FFE32056DD460020B375 /* libthird-party.a */,
				2DF0FFE52056DD460020B375 /* libthird-party.a */,
				2DF0FFE72056DD460020B375 /* libdouble-conversion.a */,
				2DF0FFE92056DD460020B375 /* libdouble-conversion.a */,
				2DF0FFEB2056DD460020B375 /* libprivatedata.a */,
				2DF0FFED2056DD460020B375 /* libprivatedata-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				2D16E6891FA4F8E400B85C8A /* libReact.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		5E91572E1DD0AC6500FF2AA8 /* Products */ = {
			isa = PBXGroup;
			children = (
				5E9157331DD0AC6500FF2AA8 /* libRCTAnimation.a */,
				5E9157351DD0AC6500FF2AA8 /* libRCTAnimation.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7771F43920C886970051AC63 /* Products */ = {
			isa = PBXGroup;
			children = (
				7771F46220C886970051AC63 /* libReactNativePayments.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		78C398B11ACF4ADC00677621 /* Products */ = {
			isa = PBXGroup;
			children = (
				78C398B91ACF4ADC00677621 /* libRCTLinking.a */,
				3DAD3E881DF850E9000B6D8A /* libRCTLinking-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
				7771F43820C886970051AC63 /* ReactNativePayments.xcodeproj */,
				5E91572D1DD0AC6500FF2AA8 /* RCTAnimation.xcodeproj */,
				146833FF1AC3E56700842450 /* React.xcodeproj */,
				00C302A71ABCB8CE00DB3ED1 /* RCTActionSheet.xcodeproj */,
				ADBDB91F1DFEBF0600ED6528 /* RCTBlob.xcodeproj */,
				00C302B51ABCB90400DB3ED1 /* RCTGeolocation.xcodeproj */,
				00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */,
				78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */,
				00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */,
				139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */,
				832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */,
				00C302DF1ABCB9EE00DB3ED1 /* RCTVibration.xcodeproj */,
				139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */,
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		832341B11AAA6A8300B99B32 /* Products */ = {
			isa = PBXGroup;
			children = (
				832341B51AAA6A8300B99B32 /* libRCTText.a */,
				3DAD3E941DF850E9000B6D8A /* libRCTText-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* example */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* exampleTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* example.app */,
				00E356EE1AD99517003FC87E /* exampleTests.xctest */,
				2D02E47B1E0B4A5D006451C7 /* example-tvOS.app */,
				2D02E4901E0B4A5D006451C7 /* example-tvOSTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		ADBDB9201DFEBF0600ED6528 /* Products */ = {
			isa = PBXGroup;
			children = (
				ADBDB9271DFEBF0700ED6528 /* libRCTBlob.a */,
				2D16E6721FA4F8DC00B85C8A /* libRCTBlob-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* exampleTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "exampleTests" */;
			buildPhases = (
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = exampleTests;
			productName = exampleTests;
			productReference = 00E356EE1AD99517003FC87E /* exampleTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* example */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "example" */;
			buildPhases = (
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = example;
			productName = "Hello World";
			productReference = 13B07F961A680F5B00A75B9A /* example.app */;
			productType = "com.apple.product-type.application";
		};
		2D02E47A1E0B4A5D006451C7 /* example-tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2D02E4BA1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "example-tvOS" */;
			buildPhases = (
				2D02E4771E0B4A5D006451C7 /* Sources */,
				2D02E4781E0B4A5D006451C7 /* Frameworks */,
				2D02E4791E0B4A5D006451C7 /* Resources */,
				2D02E4CB1E0B4B27006451C7 /* Bundle React Native Code And Images */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "example-tvOS";
			productName = "example-tvOS";
			productReference = 2D02E47B1E0B4A5D006451C7 /* example-tvOS.app */;
			productType = "com.apple.product-type.application";
		};
		2D02E48F1E0B4A5D006451C7 /* example-tvOSTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2D02E4BB1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "example-tvOSTests" */;
			buildPhases = (
				2D02E48C1E0B4A5D006451C7 /* Sources */,
				2D02E48D1E0B4A5D006451C7 /* Frameworks */,
				2D02E48E1E0B4A5D006451C7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2D02E4921E0B4A5D006451C7 /* PBXTargetDependency */,
			);
			name = "example-tvOSTests";
			productName = "example-tvOSTests";
			productReference = 2D02E4901E0B4A5D006451C7 /* example-tvOSTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0610;
				ORGANIZATIONNAME = Facebook;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					2D02E47A1E0B4A5D006451C7 = {
						CreatedOnToolsVersion = 8.2.1;
						ProvisioningStyle = Automatic;
					};
					2D02E48F1E0B4A5D006451C7 = {
						CreatedOnToolsVersion = 8.2.1;
						ProvisioningStyle = Automatic;
						TestTargetID = 2D02E47A1E0B4A5D006451C7;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "example" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 00C302A81ABCB8CE00DB3ED1 /* Products */;
					ProjectRef = 00C302A71ABCB8CE00DB3ED1 /* RCTActionSheet.xcodeproj */;
				},
				{
					ProductGroup = 5E91572E1DD0AC6500FF2AA8 /* Products */;
					ProjectRef = 5E91572D1DD0AC6500FF2AA8 /* RCTAnimation.xcodeproj */;
				},
				{
					ProductGroup = ADBDB9201DFEBF0600ED6528 /* Products */;
					ProjectRef = ADBDB91F1DFEBF0600ED6528 /* RCTBlob.xcodeproj */;
				},
				{
					ProductGroup = 00C302B61ABCB90400DB3ED1 /* Products */;
					ProjectRef = 00C302B51ABCB90400DB3ED1 /* RCTGeolocation.xcodeproj */;
				},
				{
					ProductGroup = 00C302BC1ABCB91800DB3ED1 /* Products */;
					ProjectRef = 00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */;
				},
				{
					ProductGroup = 78C398B11ACF4ADC00677621 /* Products */;
					ProjectRef = 78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */;
				},
				{
					ProductGroup = 00C302D41ABCB9D200DB3ED1 /* Products */;
					ProjectRef = 00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */;
				},
				{
					ProductGroup = 139105B71AF99BAD00B5F7CC /* Products */;
					ProjectRef = 139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */;
				},
				{
					ProductGroup = 832341B11AAA6A8300B99B32 /* Products */;
					ProjectRef = 832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */;
				},
				{
					ProductGroup = 00C302E01ABCB9EE00DB3ED1 /* Products */;
					ProjectRef = 00C302DF1ABCB9EE00DB3ED1 /* RCTVibration.xcodeproj */;
				},
				{
					ProductGroup = 139FDEE71B06529A00C62182 /* Products */;
					ProjectRef = 139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */;
				},
				{
					ProductGroup = 146834001AC3E56700842450 /* Products */;
					ProjectRef = 146833FF1AC3E56700842450 /* React.xcodeproj */;
				},
				{
					ProductGroup = 7771F43920C886970051AC63 /* Products */;
					ProjectRef = 7771F43820C886970051AC63 /* ReactNativePayments.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* example */,
				00E356ED1AD99517003FC87E /* exampleTests */,
				2D02E47A1E0B4A5D006451C7 /* example-tvOS */,
				2D02E48F1E0B4A5D006451C7 /* example-tvOSTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		00C302AC1ABCB8CE00DB3ED1 /* libRCTActionSheet.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTActionSheet.a;
			remoteRef = 00C302AB1ABCB8CE00DB3ED1 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		00C302BA1ABCB90400DB3ED1 /* libRCTGeolocation.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTGeolocation.a;
			remoteRef = 00C302B91ABCB90400DB3ED1 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		00C302C01ABCB91800DB3ED1 /* libRCTImage.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTImage.a;
			remoteRef = 00C302BF1ABCB91800DB3ED1 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		00C302DC1ABCB9D200DB3ED1 /* libRCTNetwork.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTNetwork.a;
			remoteRef = 00C302DB1ABCB9D200DB3ED1 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		00C302E41ABCB9EE00DB3ED1 /* libRCTVibration.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTVibration.a;
			remoteRef = 00C302E31ABCB9EE00DB3ED1 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		139105C11AF99BAD00B5F7CC /* libRCTSettings.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTSettings.a;
			remoteRef = 139105C01AF99BAD00B5F7CC /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		139FDEF41B06529B00C62182 /* libRCTWebSocket.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTWebSocket.a;
			remoteRef = 139FDEF31B06529B00C62182 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		146834041AC3E56700842450 /* libReact.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libReact.a;
			remoteRef = 146834031AC3E56700842450 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		2D16E6721FA4F8DC00B85C8A /* libRCTBlob-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTBlob-tvOS.a";
			remoteRef = 2D16E6711FA4F8DC00B85C8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		2D16E6841FA4F8DC00B85C8A /* libfishhook.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libfishhook.a;
			remoteRef = 2D16E6831FA4F8DC00B85C8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		2D16E6861FA4F8DC00B85C8A /* libfishhook-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libfishhook-tvOS.a";
			remoteRef = 2D16E6851FA4F8DC00B85C8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		2DF0FFDF2056DD460020B375 /* libjsinspector.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libjsinspector.a;
			remoteRef = 2DF0FFDE2056DD460020B375 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		2DF0FFE12056DD460020B375 /* libjsinspector-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libjsinspector-tvOS.a";
			remoteRef = 2DF0FFE02056DD460020B375 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		2DF0FFE32056DD460020B375 /* libthird-party.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libthird-party.a";
			remoteRef = 2DF0FFE22056DD460020B375 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		2DF0FFE52056DD460020B375 /* libthird-party.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libthird-party.a";
			remoteRef = 2DF0FFE42056DD460020B375 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		2DF0FFE72056DD460020B375 /* libdouble-conversion.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libdouble-conversion.a";
			remoteRef = 2DF0FFE62056DD460020B375 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		2DF0FFE92056DD460020B375 /* libdouble-conversion.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libdouble-conversion.a";
			remoteRef = 2DF0FFE82056DD460020B375 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		2DF0FFEB2056DD460020B375 /* libprivatedata.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libprivatedata.a;
			remoteRef = 2DF0FFEA2056DD460020B375 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		2DF0FFED2056DD460020B375 /* libprivatedata-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libprivatedata-tvOS.a";
			remoteRef = 2DF0FFEC2056DD460020B375 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E841DF850E9000B6D8A /* libRCTImage-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTImage-tvOS.a";
			remoteRef = 3DAD3E831DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E881DF850E9000B6D8A /* libRCTLinking-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTLinking-tvOS.a";
			remoteRef = 3DAD3E871DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E8C1DF850E9000B6D8A /* libRCTNetwork-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTNetwork-tvOS.a";
			remoteRef = 3DAD3E8B1DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E901DF850E9000B6D8A /* libRCTSettings-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTSettings-tvOS.a";
			remoteRef = 3DAD3E8F1DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E941DF850E9000B6D8A /* libRCTText-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTText-tvOS.a";
			remoteRef = 3DAD3E931DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E991DF850E9000B6D8A /* libRCTWebSocket-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTWebSocket-tvOS.a";
			remoteRef = 3DAD3E981DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EA31DF850E9000B6D8A /* libReact.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libReact.a;
			remoteRef = 3DAD3EA21DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EA51DF850E9000B6D8A /* libyoga.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libyoga.a;
			remoteRef = 3DAD3EA41DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EA71DF850E9000B6D8A /* libyoga.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libyoga.a;
			remoteRef = 3DAD3EA61DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EA91DF850E9000B6D8A /* libcxxreact.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libcxxreact.a;
			remoteRef = 3DAD3EA81DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EAB1DF850E9000B6D8A /* libcxxreact.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libcxxreact.a;
			remoteRef = 3DAD3EAA1DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EAD1DF850E9000B6D8A /* libjschelpers.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libjschelpers.a;
			remoteRef = 3DAD3EAC1DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EAF1DF850E9000B6D8A /* libjschelpers.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libjschelpers.a;
			remoteRef = 3DAD3EAE1DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		5E9157331DD0AC6500FF2AA8 /* libRCTAnimation.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTAnimation.a;
			remoteRef = 5E9157321DD0AC6500FF2AA8 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		5E9157351DD0AC6500FF2AA8 /* libRCTAnimation.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTAnimation.a;
			remoteRef = 5E9157341DD0AC6500FF2AA8 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7771F46220C886970051AC63 /* libReactNativePayments.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libReactNativePayments.a;
			remoteRef = 7771F46120C886970051AC63 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		78C398B91ACF4ADC00677621 /* libRCTLinking.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTLinking.a;
			remoteRef = 78C398B81ACF4ADC00677621 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		832341B51AAA6A8300B99B32 /* libRCTText.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTText.a;
			remoteRef = 832341B41AAA6A8300B99B32 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		ADBDB9271DFEBF0700ED6528 /* libRCTBlob.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTBlob.a;
			remoteRef = ADBDB9261DFEBF0700ED6528 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				13B07FBD1A68108700A75B9A /* LaunchScreen.xib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E4791E0B4A5D006451C7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2D02E4BD1E0B4A84006451C7 /* Images.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E48E1E0B4A5D006451C7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export NODE_BINARY=node\n../node_modules/react-native/scripts/react-native-xcode.sh";
		};
		2D02E4CB1E0B4B27006451C7 /* Bundle React Native Code And Images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native Code And Images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export NODE_BINARY=node\n../node_modules/react-native/scripts/react-native-xcode.sh";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				00E356F31AD99517003FC87E /* exampleTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E4771E0B4A5D006451C7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2D02E4BF1E0B4AB3006451C7 /* main.m in Sources */,
				2D02E4BC1E0B4A80006451C7 /* AppDelegate.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E48C1E0B4A5D006451C7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2DCD954D1E0B4F2C00145EB5 /* exampleTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* example */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
		2D02E4921E0B4A5D006451C7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2D02E47A1E0B4A5D006451C7 /* example-tvOS */;
			targetProxy = 2D02E4911E0B4A5D006451C7 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		13B07FB11A68108700A75B9A /* LaunchScreen.xib */ = {
			isa = PBXVariantGroup;
			children = (
				13B07FB21A68108700A75B9A /* Base */,
			);
			name = LaunchScreen.xib;
			path = example;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = exampleTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/example.app/example";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = exampleTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/example.app/example";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = NO;
				HEADER_SEARCH_PATHS = "$(SRCROOT)/../../lib/ios";
				INFOPLIST_FILE = example/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_NAME = example;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CURRENT_PROJECT_VERSION = 1;
				HEADER_SEARCH_PATHS = "$(SRCROOT)/../../lib/ios";
				INFOPLIST_FILE = example/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_NAME = example;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		2D02E4971E0B4A5E006451C7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "App Icon & Top Shelf Image";
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "example-tvOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.facebook.REACT.example-tvOS";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 9.2;
			};
			name = Debug;
		};
		2D02E4981E0B4A5E006451C7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "App Icon & Top Shelf Image";
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "example-tvOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.facebook.REACT.example-tvOS";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 9.2;
			};
			name = Release;
		};
		2D02E4991E0B4A5E006451C7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "example-tvOSTests/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.facebook.REACT.example-tvOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/example-tvOS.app/example-tvOS";
				TVOS_DEPLOYMENT_TARGET = 10.1;
			};
			name = Debug;
		};
		2D02E49A1E0B4A5E006451C7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "example-tvOSTests/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.facebook.REACT.example-tvOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/example-tvOS.app/example-tvOS";
				TVOS_DEPLOYMENT_TARGET = 10.1;
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "exampleTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "example" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D02E4BA1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "example-tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D02E4971E0B4A5E006451C7 /* Debug */,
				2D02E4981E0B4A5E006451C7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D02E4BB1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "example-tvOSTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D02E4991E0B4A5E006451C7 /* Debug */,
				2D02E49A1E0B4A5E006451C7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "example" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
