{"name": "react-native-payments-native-example", "version": "0.0.1", "private": true, "scripts": {"start": "node node_modules/react-native/local-cli/cli.js start", "test": "jest", "run:packager": "yarn run haul start", "run:ios": "react-native run-ios", "run:android": "react-native run-android", "run:demo": "yarn && react-native run-ios --configuration=Release", "build:ios": "yarn run haul bundle -- --platform=ios --bundle-output=ios/main.jsbundle --assets-dest=ios/build/assets", "build:android": "yarn run haul bundle -- --platform=android --bundle-output=android/build/index.bundle --assets-dest=android/build/assets"}, "dependencies": {"react": "~15.4.0-rc.4", "react-native": "0.41.0"}, "devDependencies": {"babel-jest": "20.0.3", "babel-preset-react-native": "2.0.0", "haul": "^1.0.0-beta.1", "jest": "20.0.4", "react-native-payments": "file:../..", "react-test-renderer": "~15.4.0-rc.4"}, "jest": {"preset": "react-native"}, "gitDir": "../../../../"}