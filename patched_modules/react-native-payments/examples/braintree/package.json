{"name": "BraintreeExample", "version": "0.0.1", "private": true, "scripts": {"start": "node node_modules/react-native/local-cli/cli.js start", "test": "jest", "run:packager": "yarn run haul start -- --platform ios", "run:ios": "react-native run-ios"}, "dependencies": {"react": "~15.4.0-rc.4", "react-native": "0.41.0", "react-native-payments": "0.1.2", "react-native-payments-addon-braintree": "4.8.4"}, "devDependencies": {"babel-jest": "20.0.3", "babel-preset-react-native": "2.1.0", "haul": "^1.0.0-beta.1", "jest": "20.0.4", "react-test-renderer": "~15.4.0-rc.4"}, "jest": {"preset": "react-native"}}