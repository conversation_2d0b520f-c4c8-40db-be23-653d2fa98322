# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


abab@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/abab/-/abab-1.0.3.tgz#b81de5f7274ec4e756d797cd834f303642724e5d"

abbrev@1:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/abbrev/-/abbrev-1.1.0.tgz#d0554c2256636e2f56e7c2e5ad183f859428d81f"

absolute-path@^0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/absolute-path/-/absolute-path-0.0.0.tgz#a78762fbdadfb5297be99b15d35a785b2f095bf7"

accepts@~1.2.12, accepts@~1.2.13:
  version "1.2.13"
  resolved "https://registry.yarnpkg.com/accepts/-/accepts-1.2.13.tgz#e5f1f3928c6d95fd96558c36ec3d9d0de4a6ecea"
  dependencies:
    mime-types "~2.1.6"
    negotiator "0.5.3"

accepts@~1.3.0, accepts@~1.3.3:
  version "1.3.3"
  resolved "https://registry.yarnpkg.com/accepts/-/accepts-1.3.3.tgz#c3ca7434938648c3e0d9c1e328dd68b622c284ca"
  dependencies:
    mime-types "~2.1.11"
    negotiator "0.6.1"

acorn-dynamic-import@^2.0.0:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/acorn-dynamic-import/-/acorn-dynamic-import-2.0.2.tgz#c752bd210bef679501b6c6cb7fc84f8f47158cc4"
  dependencies:
    acorn "^4.0.3"

acorn-globals@^3.1.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/acorn-globals/-/acorn-globals-3.1.0.tgz#fd8270f71fbb4996b004fa880ee5d46573a731bf"
  dependencies:
    acorn "^4.0.4"

acorn@^4.0.3, acorn@^4.0.4:
  version "4.0.13"
  resolved "https://registry.yarnpkg.com/acorn/-/acorn-4.0.13.tgz#105495ae5361d697bd195c825192e1ad7f253787"

acorn@^5.0.0:
  version "5.1.1"
  resolved "https://registry.yarnpkg.com/acorn/-/acorn-5.1.1.tgz#53fe161111f912ab999ee887a90a0bc52822fd75"

ajv-keywords@^1.1.1:
  version "1.5.1"
  resolved "https://registry.yarnpkg.com/ajv-keywords/-/ajv-keywords-1.5.1.tgz#314dd0a4b3368fad3dfcdc54ede6171b886daf3c"

ajv@^4.7.0, ajv@^4.9.1:
  version "4.11.8"
  resolved "https://registry.yarnpkg.com/ajv/-/ajv-4.11.8.tgz#82ffb02b29e662ae53bdc20af15947706739c536"
  dependencies:
    co "^4.6.0"
    json-stable-stringify "^1.0.1"

align-text@^0.1.1, align-text@^0.1.3:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/align-text/-/align-text-0.1.4.tgz#0cd90a561093f35d0a99256c22b7069433fad117"
  dependencies:
    kind-of "^3.0.2"
    longest "^1.0.1"
    repeat-string "^1.5.2"

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/amdefine/-/amdefine-1.0.1.tgz#4a5282ac164729e93619bcfd3ad151f817ce91f5"

ansi-escapes@^1.1.0, ansi-escapes@^1.4.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/ansi-escapes/-/ansi-escapes-1.4.0.tgz#d3a8a83b319aa67793662b13e761c7911422306e"

ansi-escapes@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/ansi-escapes/-/ansi-escapes-2.0.0.tgz#5bae52be424878dd9783e8910e3fc2922e83c81b"

ansi-regex@^2.0.0, ansi-regex@^2.1.1:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-3.0.0.tgz#ed0317c322064f79466c02966bddb605ab37d998"

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"

ansi-styles@^3.0.0, ansi-styles@^3.1.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-3.1.0.tgz#09c202d5c917ec23188caa5c9cb9179cd9547750"
  dependencies:
    color-convert "^1.0.0"

ansi@^0.3.0, ansi@~0.3.1:
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/ansi/-/ansi-0.3.1.tgz#0c42d4fb17160d5a9af1e484bace1c66922c1b21"

anymatch@^1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/anymatch/-/anymatch-1.3.0.tgz#a3e52fa39168c825ff57b0248126ce5a8ff95507"
  dependencies:
    arrify "^1.0.0"
    micromatch "^2.1.5"

append-transform@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/append-transform/-/append-transform-0.4.0.tgz#d76ebf8ca94d276e247a36bad44a4b74ab611991"
  dependencies:
    default-require-extensions "^1.0.0"

aproba@^1.0.3:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/aproba/-/aproba-1.1.2.tgz#45c6629094de4e96f693ef7eab74ae079c240fc1"

are-we-there-yet@~1.1.2:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/are-we-there-yet/-/are-we-there-yet-1.1.4.tgz#bb5dca382bb94f05e15194373d16fd3ba1ca110d"
  dependencies:
    delegates "^1.0.0"
    readable-stream "^2.0.6"

argparse@^1.0.7:
  version "1.0.9"
  resolved "https://registry.yarnpkg.com/argparse/-/argparse-1.0.9.tgz#73d83bc263f86e97f8cc4f6bae1b0e90a7d22c86"
  dependencies:
    sprintf-js "~1.0.2"

arr-diff@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/arr-diff/-/arr-diff-2.0.0.tgz#8f3b827f955a8bd669697e4a4256ac3ceae356cf"
  dependencies:
    arr-flatten "^1.0.1"

arr-flatten@^1.0.1:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/arr-flatten/-/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"

array-differ@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/array-differ/-/array-differ-1.0.0.tgz#eff52e3758249d33be402b8bb8e564bb2b5d4031"

array-equal@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/array-equal/-/array-equal-1.0.0.tgz#8c2a5ef2472fd9ea742b04c77a75093ba2757c93"

array-filter@~0.0.0:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/array-filter/-/array-filter-0.0.1.tgz#7da8cf2e26628ed732803581fd21f67cacd2eeec"

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/array-flatten/-/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"

array-map@~0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/array-map/-/array-map-0.0.0.tgz#88a2bab73d1cf7bcd5c1b118a003f66f665fa662"

array-reduce@~0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/array-reduce/-/array-reduce-0.0.0.tgz#173899d3ffd1c7d9383e4479525dbe278cab5f2b"

array-union@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/array-union/-/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.1, array-uniq@^1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/array-uniq/-/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"

array-unique@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/array-unique/-/array-unique-0.2.1.tgz#a1d97ccafcbc2625cc70fadceb36a50c58b01a53"

arrify@^1.0.0, arrify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/arrify/-/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"

art@^0.10.0:
  version "0.10.1"
  resolved "https://registry.yarnpkg.com/art/-/art-0.10.1.tgz#38541883e399225c5e193ff246e8f157cf7b2146"

asap@~2.0.3:
  version "2.0.6"
  resolved "https://registry.yarnpkg.com/asap/-/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"

asn1.js@^4.0.0:
  version "4.9.1"
  resolved "https://registry.yarnpkg.com/asn1.js/-/asn1.js-4.9.1.tgz#48ba240b45a9280e94748990ba597d216617fd40"
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

asn1@~0.2.3:
  version "0.2.3"
  resolved "https://registry.yarnpkg.com/asn1/-/asn1-0.2.3.tgz#dac8787713c9966849fc8180777ebe9c1ddf3b86"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/assert-plus/-/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"

assert-plus@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/assert-plus/-/assert-plus-0.2.0.tgz#d74e1b87e7affc0db8aadb7021f3fe48101ab234"

assert@^1.1.1:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/assert/-/assert-1.4.1.tgz#99912d591836b5a6f5b345c0f07eefc08fc65d91"
  dependencies:
    util "0.10.3"

async-each@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/async-each/-/async-each-1.0.1.tgz#19d386a1d9edc6e7c1c85d388aedbcc56d33602d"

async@1.5.0:
  version "1.5.0"
  resolved "https://registry.yarnpkg.com/async/-/async-1.5.0.tgz#2796642723573859565633fc6274444bee2f8ce3"

async@^1.4.0:
  version "1.5.2"
  resolved "https://registry.yarnpkg.com/async/-/async-1.5.2.tgz#ec6a61ae56480c0c3cb241c95618e20892f9672a"

async@^2.0.1, async@^2.1.2, async@^2.1.4:
  version "2.5.0"
  resolved "https://registry.yarnpkg.com/async/-/async-2.5.0.tgz#843190fd6b7357a0b9e1c956edddd5ec8462b54d"
  dependencies:
    lodash "^4.14.0"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"

aws-sign2@~0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/aws-sign2/-/aws-sign2-0.6.0.tgz#14342dd38dbcc94d0e5b87d763cd63612c0e794f"

aws4@^1.2.1:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/aws4/-/aws4-1.6.0.tgz#83ef5ca860b2b32e4a0deedee8c771b9db57471e"

babel-code-frame@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-code-frame/-/babel-code-frame-6.22.0.tgz#027620bee567a88c32561574e7fd0801d33118e4"
  dependencies:
    chalk "^1.1.0"
    esutils "^2.0.2"
    js-tokens "^3.0.0"

babel-core@^6.0.0, babel-core@^6.21.0, babel-core@^6.24.0, babel-core@^6.24.1, babel-core@^6.7.2:
  version "6.25.0"
  resolved "https://registry.yarnpkg.com/babel-core/-/babel-core-6.25.0.tgz#7dd42b0463c742e9d5296deb3ec67a9322dad729"
  dependencies:
    babel-code-frame "^6.22.0"
    babel-generator "^6.25.0"
    babel-helpers "^6.24.1"
    babel-messages "^6.23.0"
    babel-register "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.25.0"
    babel-traverse "^6.25.0"
    babel-types "^6.25.0"
    babylon "^6.17.2"
    convert-source-map "^1.1.0"
    debug "^2.1.1"
    json5 "^0.5.0"
    lodash "^4.2.0"
    minimatch "^3.0.2"
    path-is-absolute "^1.0.0"
    private "^0.1.6"
    slash "^1.0.0"
    source-map "^0.5.0"

babel-generator@^6.18.0, babel-generator@^6.21.0, babel-generator@^6.25.0:
  version "6.25.0"
  resolved "https://registry.yarnpkg.com/babel-generator/-/babel-generator-6.25.0.tgz#33a1af70d5f2890aeb465a4a7793c1df6a9ea9fc"
  dependencies:
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-types "^6.25.0"
    detect-indent "^4.0.0"
    jsesc "^1.3.0"
    lodash "^4.2.0"
    source-map "^0.5.0"
    trim-right "^1.0.1"

babel-helper-builder-binary-assignment-operator-visitor@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.24.1.tgz#cce4517ada356f4220bcae8a02c2b346f9a56664"
  dependencies:
    babel-helper-explode-assignable-expression "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-builder-react-jsx@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-builder-react-jsx/-/babel-helper-builder-react-jsx-6.24.1.tgz#0ad7917e33c8d751e646daca4e77cc19377d2cbc"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"
    esutils "^2.0.0"

babel-helper-call-delegate@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-call-delegate/-/babel-helper-call-delegate-6.24.1.tgz#ece6aacddc76e41c3461f88bfc575bd0daa2df8d"
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-define-map@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-define-map/-/babel-helper-define-map-6.24.1.tgz#7a9747f258d8947d32d515f6aa1c7bd02204a080"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"
    lodash "^4.2.0"

babel-helper-explode-assignable-expression@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.24.1.tgz#f25b82cf7dc10433c55f70592d5746400ac22caa"
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-function-name@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-function-name/-/babel-helper-function-name-6.24.1.tgz#d3475b8c03ed98242a25b48351ab18399d3580a9"
  dependencies:
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-get-function-arity@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-get-function-arity/-/babel-helper-get-function-arity-6.24.1.tgz#8f7782aa93407c41d3aa50908f89b031b1b6853d"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-hoist-variables@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-hoist-variables/-/babel-helper-hoist-variables-6.24.1.tgz#1ecb27689c9d25513eadbc9914a73f5408be7a76"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-optimise-call-expression@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-optimise-call-expression/-/babel-helper-optimise-call-expression-6.24.1.tgz#f7a13427ba9f73f8f4fa993c54a97882d1244257"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-regex@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-regex/-/babel-helper-regex-6.24.1.tgz#d36e22fab1008d79d88648e32116868128456ce8"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"
    lodash "^4.2.0"

babel-helper-remap-async-to-generator@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-remap-async-to-generator/-/babel-helper-remap-async-to-generator-6.24.1.tgz#5ec581827ad723fecdd381f1c928390676e4551b"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-replace-supers@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-replace-supers/-/babel-helper-replace-supers-6.24.1.tgz#bf6dbfe43938d17369a213ca8a8bf74b6a90ab1a"
  dependencies:
    babel-helper-optimise-call-expression "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helpers@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helpers/-/babel-helpers-6.24.1.tgz#3471de9caec388e5c850e597e58a26ddf37602b2"
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-jest@20.0.3, babel-jest@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/babel-jest/-/babel-jest-20.0.3.tgz#e4a03b13dc10389e140fc645d09ffc4ced301671"
  dependencies:
    babel-core "^6.0.0"
    babel-plugin-istanbul "^4.0.0"
    babel-preset-jest "^20.0.3"

babel-loader@^6.4.1:
  version "6.4.1"
  resolved "https://registry.yarnpkg.com/babel-loader/-/babel-loader-6.4.1.tgz#0b34112d5b0748a8dcdbf51acf6f9bd42d50b8ca"
  dependencies:
    find-cache-dir "^0.1.1"
    loader-utils "^0.2.16"
    mkdirp "^0.5.1"
    object-assign "^4.0.1"

babel-messages@^6.23.0:
  version "6.23.0"
  resolved "https://registry.yarnpkg.com/babel-messages/-/babel-messages-6.23.0.tgz#f3cdf4703858035b2a2951c6ec5edf6c62f2630e"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-check-es2015-constants@^6.22.0, babel-plugin-check-es2015-constants@^6.5.0, babel-plugin-check-es2015-constants@^6.7.2, babel-plugin-check-es2015-constants@^6.8.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-check-es2015-constants/-/babel-plugin-check-es2015-constants-6.22.0.tgz#35157b101426fd2ffd3da3f75c7d1e91835bbf8a"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-external-helpers@^6.18.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-external-helpers/-/babel-plugin-external-helpers-6.22.0.tgz#2285f48b02bd5dede85175caf8c62e86adccefa1"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-istanbul@^4.0.0:
  version "4.1.4"
  resolved "https://registry.yarnpkg.com/babel-plugin-istanbul/-/babel-plugin-istanbul-4.1.4.tgz#18dde84bf3ce329fddf3f4103fae921456d8e587"
  dependencies:
    find-up "^2.1.0"
    istanbul-lib-instrument "^1.7.2"
    test-exclude "^4.1.1"

babel-plugin-jest-hoist@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-20.0.3.tgz#afedc853bd3f8dc3548ea671fbe69d03cc2c1767"

babel-plugin-react-transform@2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/babel-plugin-react-transform/-/babel-plugin-react-transform-2.0.2.tgz#515bbfa996893981142d90b1f9b1635de2995109"
  dependencies:
    lodash "^4.6.1"

babel-plugin-syntax-async-functions@^6.5.0, babel-plugin-syntax-async-functions@^6.8.0:
  version "6.13.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-syntax-async-functions/-/babel-plugin-syntax-async-functions-6.13.0.tgz#cad9cad1191b5ad634bf30ae0872391e0647be95"

babel-plugin-syntax-class-properties@^6.5.0, babel-plugin-syntax-class-properties@^6.8.0:
  version "6.13.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-syntax-class-properties/-/babel-plugin-syntax-class-properties-6.13.0.tgz#d7eb23b79a317f8543962c505b827c7d6cac27de"

babel-plugin-syntax-exponentiation-operator@^6.8.0:
  version "6.13.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-syntax-exponentiation-operator/-/babel-plugin-syntax-exponentiation-operator-6.13.0.tgz#9ee7e8337290da95288201a6a57f4170317830de"

babel-plugin-syntax-flow@^6.18.0, babel-plugin-syntax-flow@^6.5.0, babel-plugin-syntax-flow@^6.8.0:
  version "6.18.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-syntax-flow/-/babel-plugin-syntax-flow-6.18.0.tgz#4c3ab20a2af26aa20cd25995c398c4eb70310c8d"

babel-plugin-syntax-jsx@^6.5.0, babel-plugin-syntax-jsx@^6.8.0:
  version "6.18.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-syntax-jsx/-/babel-plugin-syntax-jsx-6.18.0.tgz#0af32a9a6e13ca7a3fd5069e62d7b0f58d0d8946"

babel-plugin-syntax-object-rest-spread@^6.5.0, babel-plugin-syntax-object-rest-spread@^6.8.0:
  version "6.13.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-syntax-object-rest-spread/-/babel-plugin-syntax-object-rest-spread-6.13.0.tgz#fd6536f2bce13836ffa3a5458c4903a597bb3bf5"

babel-plugin-syntax-trailing-function-commas@^6.20.0, babel-plugin-syntax-trailing-function-commas@^6.22.0, babel-plugin-syntax-trailing-function-commas@^6.5.0, babel-plugin-syntax-trailing-function-commas@^6.8.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-syntax-trailing-function-commas/-/babel-plugin-syntax-trailing-function-commas-6.22.0.tgz#ba0360937f8d06e40180a43fe0d5616fff532cf3"

babel-plugin-transform-async-to-generator@^6.22.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-async-to-generator/-/babel-plugin-transform-async-to-generator-6.24.1.tgz#6536e378aff6cb1d5517ac0e40eb3e9fc8d08761"
  dependencies:
    babel-helper-remap-async-to-generator "^6.24.1"
    babel-plugin-syntax-async-functions "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-class-properties@^6.5.0, babel-plugin-transform-class-properties@^6.6.0, babel-plugin-transform-class-properties@^6.8.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.24.1.tgz#6a79763ea61d33d36f37b611aa9def81a81b46ac"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-plugin-syntax-class-properties "^6.8.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-arrow-functions@^6.22.0, babel-plugin-transform-es2015-arrow-functions@^6.5.0, babel-plugin-transform-es2015-arrow-functions@^6.5.2, babel-plugin-transform-es2015-arrow-functions@^6.8.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-arrow-functions/-/babel-plugin-transform-es2015-arrow-functions-6.22.0.tgz#452692cb711d5f79dc7f85e440ce41b9f244d221"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoped-functions@^6.22.0, babel-plugin-transform-es2015-block-scoped-functions@^6.6.5, babel-plugin-transform-es2015-block-scoped-functions@^6.8.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-block-scoped-functions/-/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz#bbc51b49f964d70cb8d8e0b94e820246ce3a6141"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoping@^6.23.0, babel-plugin-transform-es2015-block-scoping@^6.5.0, babel-plugin-transform-es2015-block-scoping@^6.7.1, babel-plugin-transform-es2015-block-scoping@^6.8.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-block-scoping/-/babel-plugin-transform-es2015-block-scoping-6.24.1.tgz#76c295dc3a4741b1665adfd3167215dcff32a576"
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"
    lodash "^4.2.0"

babel-plugin-transform-es2015-classes@^6.23.0, babel-plugin-transform-es2015-classes@^6.5.0, babel-plugin-transform-es2015-classes@^6.6.5, babel-plugin-transform-es2015-classes@^6.8.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-classes/-/babel-plugin-transform-es2015-classes-6.24.1.tgz#5a4c58a50c9c9461e564b4b2a3bfabc97a2584db"
  dependencies:
    babel-helper-define-map "^6.24.1"
    babel-helper-function-name "^6.24.1"
    babel-helper-optimise-call-expression "^6.24.1"
    babel-helper-replace-supers "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-computed-properties@^6.22.0, babel-plugin-transform-es2015-computed-properties@^6.5.0, babel-plugin-transform-es2015-computed-properties@^6.6.5, babel-plugin-transform-es2015-computed-properties@^6.8.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-computed-properties/-/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz#6fe2a8d16895d5634f4cd999b6d3480a308159b3"
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-destructuring@6.x, babel-plugin-transform-es2015-destructuring@^6.23.0, babel-plugin-transform-es2015-destructuring@^6.5.0, babel-plugin-transform-es2015-destructuring@^6.6.5, babel-plugin-transform-es2015-destructuring@^6.8.0:
  version "6.23.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-destructuring/-/babel-plugin-transform-es2015-destructuring-6.23.0.tgz#997bb1f1ab967f682d2b0876fe358d60e765c56d"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-duplicate-keys@^6.22.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-duplicate-keys/-/babel-plugin-transform-es2015-duplicate-keys-6.24.1.tgz#73eb3d310ca969e3ef9ec91c53741a6f1576423e"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-for-of@^6.23.0, babel-plugin-transform-es2015-for-of@^6.5.0, babel-plugin-transform-es2015-for-of@^6.6.0, babel-plugin-transform-es2015-for-of@^6.8.0:
  version "6.23.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-for-of/-/babel-plugin-transform-es2015-for-of-6.23.0.tgz#f47c95b2b613df1d3ecc2fdb7573623c75248691"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-function-name@6.x, babel-plugin-transform-es2015-function-name@^6.22.0, babel-plugin-transform-es2015-function-name@^6.5.0, babel-plugin-transform-es2015-function-name@^6.8.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-function-name/-/babel-plugin-transform-es2015-function-name-6.24.1.tgz#834c89853bc36b1af0f3a4c5dbaa94fd8eacaa8b"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-literals@^6.22.0, babel-plugin-transform-es2015-literals@^6.5.0, babel-plugin-transform-es2015-literals@^6.8.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-literals/-/babel-plugin-transform-es2015-literals-6.22.0.tgz#4f54a02d6cd66cf915280019a31d31925377ca2e"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-modules-amd@^6.22.0, babel-plugin-transform-es2015-modules-amd@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz#3b3e54017239842d6d19c3011c4bd2f00a00d154"
  dependencies:
    babel-plugin-transform-es2015-modules-commonjs "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-modules-commonjs@6.x, babel-plugin-transform-es2015-modules-commonjs@^6.23.0, babel-plugin-transform-es2015-modules-commonjs@^6.24.1, babel-plugin-transform-es2015-modules-commonjs@^6.5.0, babel-plugin-transform-es2015-modules-commonjs@^6.7.0, babel-plugin-transform-es2015-modules-commonjs@^6.8.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.24.1.tgz#d3e310b40ef664a36622200097c6d440298f2bfe"
  dependencies:
    babel-plugin-transform-strict-mode "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-modules-systemjs@^6.23.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz#ff89a142b9119a906195f5f106ecf305d9407d23"
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-modules-umd@^6.23.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.24.1.tgz#ac997e6285cd18ed6176adb607d602344ad38468"
  dependencies:
    babel-plugin-transform-es2015-modules-amd "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-object-super@^6.22.0, babel-plugin-transform-es2015-object-super@^6.6.5, babel-plugin-transform-es2015-object-super@^6.8.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-object-super/-/babel-plugin-transform-es2015-object-super-6.24.1.tgz#24cef69ae21cb83a7f8603dad021f572eb278f8d"
  dependencies:
    babel-helper-replace-supers "^6.24.1"
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-parameters@6.x, babel-plugin-transform-es2015-parameters@^6.23.0, babel-plugin-transform-es2015-parameters@^6.5.0, babel-plugin-transform-es2015-parameters@^6.7.0, babel-plugin-transform-es2015-parameters@^6.8.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-parameters/-/babel-plugin-transform-es2015-parameters-6.24.1.tgz#57ac351ab49caf14a97cd13b09f66fdf0a625f2b"
  dependencies:
    babel-helper-call-delegate "^6.24.1"
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-shorthand-properties@6.x, babel-plugin-transform-es2015-shorthand-properties@^6.22.0, babel-plugin-transform-es2015-shorthand-properties@^6.5.0, babel-plugin-transform-es2015-shorthand-properties@^6.8.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-shorthand-properties/-/babel-plugin-transform-es2015-shorthand-properties-6.24.1.tgz#24f875d6721c87661bbd99a4622e51f14de38aa0"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-spread@6.x, babel-plugin-transform-es2015-spread@^6.22.0, babel-plugin-transform-es2015-spread@^6.5.0, babel-plugin-transform-es2015-spread@^6.6.5, babel-plugin-transform-es2015-spread@^6.8.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-spread/-/babel-plugin-transform-es2015-spread-6.22.0.tgz#d6d68a99f89aedc4536c81a542e8dd9f1746f8d1"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-sticky-regex@6.x, babel-plugin-transform-es2015-sticky-regex@^6.22.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-sticky-regex/-/babel-plugin-transform-es2015-sticky-regex-6.24.1.tgz#00c1cdb1aca71112cdf0cf6126c2ed6b457ccdbc"
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-template-literals@^6.22.0, babel-plugin-transform-es2015-template-literals@^6.5.0, babel-plugin-transform-es2015-template-literals@^6.6.5, babel-plugin-transform-es2015-template-literals@^6.8.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-template-literals/-/babel-plugin-transform-es2015-template-literals-6.22.0.tgz#a84b3450f7e9f8f1f6839d6d687da84bb1236d8d"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-typeof-symbol@^6.23.0:
  version "6.23.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-typeof-symbol/-/babel-plugin-transform-es2015-typeof-symbol-6.23.0.tgz#dec09f1cddff94b52ac73d505c84df59dcceb372"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-unicode-regex@6.x, babel-plugin-transform-es2015-unicode-regex@^6.22.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-unicode-regex/-/babel-plugin-transform-es2015-unicode-regex-6.24.1.tgz#d38b12f42ea7323f729387f18a7c5ae1faeb35e9"
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    regexpu-core "^2.0.0"

babel-plugin-transform-es3-member-expression-literals@^6.5.0, babel-plugin-transform-es3-member-expression-literals@^6.8.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es3-member-expression-literals/-/babel-plugin-transform-es3-member-expression-literals-6.22.0.tgz#733d3444f3ecc41bef8ed1a6a4e09657b8969ebb"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es3-property-literals@^6.5.0, babel-plugin-transform-es3-property-literals@^6.8.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es3-property-literals/-/babel-plugin-transform-es3-property-literals-6.22.0.tgz#b2078d5842e22abf40f73e8cde9cd3711abd5758"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-exponentiation-operator@^6.22.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.24.1.tgz#2ab0c9c7f3098fa48907772bb813fe41e8de3a0e"
  dependencies:
    babel-helper-builder-binary-assignment-operator-visitor "^6.24.1"
    babel-plugin-syntax-exponentiation-operator "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-flow-strip-types@^6.21.0, babel-plugin-transform-flow-strip-types@^6.22.0, babel-plugin-transform-flow-strip-types@^6.5.0, babel-plugin-transform-flow-strip-types@^6.7.0, babel-plugin-transform-flow-strip-types@^6.8.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-flow-strip-types/-/babel-plugin-transform-flow-strip-types-6.22.0.tgz#84cb672935d43714fdc32bce84568d87441cf7cf"
  dependencies:
    babel-plugin-syntax-flow "^6.18.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-object-assign@^6.5.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-object-assign/-/babel-plugin-transform-object-assign-6.22.0.tgz#f99d2f66f1a0b0d498e346c5359684740caa20ba"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-object-rest-spread@^6.20.2, babel-plugin-transform-object-rest-spread@^6.23.0, babel-plugin-transform-object-rest-spread@^6.5.0, babel-plugin-transform-object-rest-spread@^6.6.5, babel-plugin-transform-object-rest-spread@^6.8.0:
  version "6.23.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-object-rest-spread/-/babel-plugin-transform-object-rest-spread-6.23.0.tgz#875d6bc9be761c58a2ae3feee5dc4895d8c7f921"
  dependencies:
    babel-plugin-syntax-object-rest-spread "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-react-display-name@^6.5.0, babel-plugin-transform-react-display-name@^6.8.0:
  version "6.25.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-react-display-name/-/babel-plugin-transform-react-display-name-6.25.0.tgz#67e2bf1f1e9c93ab08db96792e05392bf2cc28d1"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-react-jsx-source@^6.5.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-react-jsx-source/-/babel-plugin-transform-react-jsx-source-6.22.0.tgz#66ac12153f5cd2d17b3c19268f4bf0197f44ecd6"
  dependencies:
    babel-plugin-syntax-jsx "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-react-jsx@^6.5.0, babel-plugin-transform-react-jsx@^6.8.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-react-jsx/-/babel-plugin-transform-react-jsx-6.24.1.tgz#840a028e7df460dfc3a2d29f0c0d91f6376e66a3"
  dependencies:
    babel-helper-builder-react-jsx "^6.24.1"
    babel-plugin-syntax-jsx "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-regenerator@^6.22.0, babel-plugin-transform-regenerator@^6.5.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-regenerator/-/babel-plugin-transform-regenerator-6.24.1.tgz#b8da305ad43c3c99b4848e4fe4037b770d23c418"
  dependencies:
    regenerator-transform "0.9.11"

babel-plugin-transform-strict-mode@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-strict-mode/-/babel-plugin-transform-strict-mode-6.24.1.tgz#d5faf7aa578a65bbe591cf5edae04a0c67020758"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-polyfill@^6.20.0:
  version "6.23.0"
  resolved "https://registry.yarnpkg.com/babel-polyfill/-/babel-polyfill-6.23.0.tgz#8364ca62df8eafb830499f699177466c3b03499d"
  dependencies:
    babel-runtime "^6.22.0"
    core-js "^2.4.0"
    regenerator-runtime "^0.10.0"

babel-preset-env@^1.2.2:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/babel-preset-env/-/babel-preset-env-1.6.0.tgz#2de1c782a780a0a5d605d199c957596da43c44e4"
  dependencies:
    babel-plugin-check-es2015-constants "^6.22.0"
    babel-plugin-syntax-trailing-function-commas "^6.22.0"
    babel-plugin-transform-async-to-generator "^6.22.0"
    babel-plugin-transform-es2015-arrow-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoped-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoping "^6.23.0"
    babel-plugin-transform-es2015-classes "^6.23.0"
    babel-plugin-transform-es2015-computed-properties "^6.22.0"
    babel-plugin-transform-es2015-destructuring "^6.23.0"
    babel-plugin-transform-es2015-duplicate-keys "^6.22.0"
    babel-plugin-transform-es2015-for-of "^6.23.0"
    babel-plugin-transform-es2015-function-name "^6.22.0"
    babel-plugin-transform-es2015-literals "^6.22.0"
    babel-plugin-transform-es2015-modules-amd "^6.22.0"
    babel-plugin-transform-es2015-modules-commonjs "^6.23.0"
    babel-plugin-transform-es2015-modules-systemjs "^6.23.0"
    babel-plugin-transform-es2015-modules-umd "^6.23.0"
    babel-plugin-transform-es2015-object-super "^6.22.0"
    babel-plugin-transform-es2015-parameters "^6.23.0"
    babel-plugin-transform-es2015-shorthand-properties "^6.22.0"
    babel-plugin-transform-es2015-spread "^6.22.0"
    babel-plugin-transform-es2015-sticky-regex "^6.22.0"
    babel-plugin-transform-es2015-template-literals "^6.22.0"
    babel-plugin-transform-es2015-typeof-symbol "^6.23.0"
    babel-plugin-transform-es2015-unicode-regex "^6.22.0"
    babel-plugin-transform-exponentiation-operator "^6.22.0"
    babel-plugin-transform-regenerator "^6.22.0"
    browserslist "^2.1.2"
    invariant "^2.2.2"
    semver "^5.3.0"

babel-preset-es2015-node@^6.1.1:
  version "6.1.1"
  resolved "https://registry.yarnpkg.com/babel-preset-es2015-node/-/babel-preset-es2015-node-6.1.1.tgz#60b23157024b0cfebf3a63554cb05ee035b4e55f"
  dependencies:
    babel-plugin-transform-es2015-destructuring "6.x"
    babel-plugin-transform-es2015-function-name "6.x"
    babel-plugin-transform-es2015-modules-commonjs "6.x"
    babel-plugin-transform-es2015-parameters "6.x"
    babel-plugin-transform-es2015-shorthand-properties "6.x"
    babel-plugin-transform-es2015-spread "6.x"
    babel-plugin-transform-es2015-sticky-regex "6.x"
    babel-plugin-transform-es2015-unicode-regex "6.x"
    semver "5.x"

babel-preset-fbjs@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/babel-preset-fbjs/-/babel-preset-fbjs-1.0.0.tgz#c972e5c9b301d4ec9e7971f4aec3e14ac017a8b0"
  dependencies:
    babel-plugin-check-es2015-constants "^6.7.2"
    babel-plugin-syntax-flow "^6.5.0"
    babel-plugin-syntax-object-rest-spread "^6.5.0"
    babel-plugin-syntax-trailing-function-commas "^6.5.0"
    babel-plugin-transform-class-properties "^6.6.0"
    babel-plugin-transform-es2015-arrow-functions "^6.5.2"
    babel-plugin-transform-es2015-block-scoped-functions "^6.6.5"
    babel-plugin-transform-es2015-block-scoping "^6.7.1"
    babel-plugin-transform-es2015-classes "^6.6.5"
    babel-plugin-transform-es2015-computed-properties "^6.6.5"
    babel-plugin-transform-es2015-destructuring "^6.6.5"
    babel-plugin-transform-es2015-for-of "^6.6.0"
    babel-plugin-transform-es2015-literals "^6.5.0"
    babel-plugin-transform-es2015-modules-commonjs "^6.7.0"
    babel-plugin-transform-es2015-object-super "^6.6.5"
    babel-plugin-transform-es2015-parameters "^6.7.0"
    babel-plugin-transform-es2015-shorthand-properties "^6.5.0"
    babel-plugin-transform-es2015-spread "^6.6.5"
    babel-plugin-transform-es2015-template-literals "^6.6.5"
    babel-plugin-transform-es3-member-expression-literals "^6.5.0"
    babel-plugin-transform-es3-property-literals "^6.5.0"
    babel-plugin-transform-flow-strip-types "^6.7.0"
    babel-plugin-transform-object-rest-spread "^6.6.5"
    object-assign "^4.0.1"

babel-preset-fbjs@^2.1.0:
  version "2.1.4"
  resolved "https://registry.yarnpkg.com/babel-preset-fbjs/-/babel-preset-fbjs-2.1.4.tgz#22f358e6654073acf61e47a052a777d7bccf03af"
  dependencies:
    babel-plugin-check-es2015-constants "^6.8.0"
    babel-plugin-syntax-class-properties "^6.8.0"
    babel-plugin-syntax-flow "^6.8.0"
    babel-plugin-syntax-jsx "^6.8.0"
    babel-plugin-syntax-object-rest-spread "^6.8.0"
    babel-plugin-syntax-trailing-function-commas "^6.8.0"
    babel-plugin-transform-class-properties "^6.8.0"
    babel-plugin-transform-es2015-arrow-functions "^6.8.0"
    babel-plugin-transform-es2015-block-scoped-functions "^6.8.0"
    babel-plugin-transform-es2015-block-scoping "^6.8.0"
    babel-plugin-transform-es2015-classes "^6.8.0"
    babel-plugin-transform-es2015-computed-properties "^6.8.0"
    babel-plugin-transform-es2015-destructuring "^6.8.0"
    babel-plugin-transform-es2015-for-of "^6.8.0"
    babel-plugin-transform-es2015-function-name "^6.8.0"
    babel-plugin-transform-es2015-literals "^6.8.0"
    babel-plugin-transform-es2015-modules-commonjs "^6.8.0"
    babel-plugin-transform-es2015-object-super "^6.8.0"
    babel-plugin-transform-es2015-parameters "^6.8.0"
    babel-plugin-transform-es2015-shorthand-properties "^6.8.0"
    babel-plugin-transform-es2015-spread "^6.8.0"
    babel-plugin-transform-es2015-template-literals "^6.8.0"
    babel-plugin-transform-es3-member-expression-literals "^6.8.0"
    babel-plugin-transform-es3-property-literals "^6.8.0"
    babel-plugin-transform-flow-strip-types "^6.8.0"
    babel-plugin-transform-object-rest-spread "^6.8.0"
    babel-plugin-transform-react-display-name "^6.8.0"
    babel-plugin-transform-react-jsx "^6.8.0"

babel-preset-jest@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/babel-preset-jest/-/babel-preset-jest-20.0.3.tgz#cbacaadecb5d689ca1e1de1360ebfc66862c178a"
  dependencies:
    babel-plugin-jest-hoist "^20.0.3"

babel-preset-react-native@2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/babel-preset-react-native/-/babel-preset-react-native-2.1.0.tgz#9013ebd82da1c88102bf588810ff59e209ca2b8a"
  dependencies:
    babel-plugin-check-es2015-constants "^6.5.0"
    babel-plugin-react-transform "2.0.2"
    babel-plugin-syntax-async-functions "^6.5.0"
    babel-plugin-syntax-class-properties "^6.5.0"
    babel-plugin-syntax-flow "^6.5.0"
    babel-plugin-syntax-jsx "^6.5.0"
    babel-plugin-syntax-trailing-function-commas "^6.5.0"
    babel-plugin-transform-class-properties "^6.5.0"
    babel-plugin-transform-es2015-arrow-functions "^6.5.0"
    babel-plugin-transform-es2015-block-scoping "^6.5.0"
    babel-plugin-transform-es2015-classes "^6.5.0"
    babel-plugin-transform-es2015-computed-properties "^6.5.0"
    babel-plugin-transform-es2015-destructuring "^6.5.0"
    babel-plugin-transform-es2015-for-of "^6.5.0"
    babel-plugin-transform-es2015-function-name "^6.5.0"
    babel-plugin-transform-es2015-literals "^6.5.0"
    babel-plugin-transform-es2015-modules-commonjs "^6.5.0"
    babel-plugin-transform-es2015-parameters "^6.5.0"
    babel-plugin-transform-es2015-shorthand-properties "^6.5.0"
    babel-plugin-transform-es2015-spread "^6.5.0"
    babel-plugin-transform-es2015-template-literals "^6.5.0"
    babel-plugin-transform-flow-strip-types "^6.5.0"
    babel-plugin-transform-object-assign "^6.5.0"
    babel-plugin-transform-object-rest-spread "^6.5.0"
    babel-plugin-transform-react-display-name "^6.5.0"
    babel-plugin-transform-react-jsx "^6.5.0"
    babel-plugin-transform-react-jsx-source "^6.5.0"
    babel-plugin-transform-regenerator "^6.5.0"
    react-transform-hmr "^1.0.4"

babel-preset-react-native@^1.9.1:
  version "1.9.2"
  resolved "https://registry.yarnpkg.com/babel-preset-react-native/-/babel-preset-react-native-1.9.2.tgz#b22addd2e355ff3b39671b79be807e52dfa145f2"
  dependencies:
    babel-plugin-check-es2015-constants "^6.5.0"
    babel-plugin-react-transform "2.0.2"
    babel-plugin-syntax-async-functions "^6.5.0"
    babel-plugin-syntax-class-properties "^6.5.0"
    babel-plugin-syntax-flow "^6.5.0"
    babel-plugin-syntax-jsx "^6.5.0"
    babel-plugin-syntax-trailing-function-commas "^6.5.0"
    babel-plugin-transform-class-properties "^6.5.0"
    babel-plugin-transform-es2015-arrow-functions "^6.5.0"
    babel-plugin-transform-es2015-block-scoping "^6.5.0"
    babel-plugin-transform-es2015-classes "^6.5.0"
    babel-plugin-transform-es2015-computed-properties "^6.5.0"
    babel-plugin-transform-es2015-destructuring "^6.5.0"
    babel-plugin-transform-es2015-for-of "^6.5.0"
    babel-plugin-transform-es2015-function-name "^6.5.0"
    babel-plugin-transform-es2015-literals "^6.5.0"
    babel-plugin-transform-es2015-modules-commonjs "^6.5.0"
    babel-plugin-transform-es2015-parameters "^6.5.0"
    babel-plugin-transform-es2015-shorthand-properties "^6.5.0"
    babel-plugin-transform-es2015-spread "^6.5.0"
    babel-plugin-transform-es2015-template-literals "^6.5.0"
    babel-plugin-transform-flow-strip-types "^6.5.0"
    babel-plugin-transform-object-assign "^6.5.0"
    babel-plugin-transform-object-rest-spread "^6.5.0"
    babel-plugin-transform-react-display-name "^6.5.0"
    babel-plugin-transform-react-jsx "^6.5.0"
    babel-plugin-transform-react-jsx-source "^6.5.0"
    babel-plugin-transform-regenerator "^6.5.0"
    react-transform-hmr "^1.0.4"

babel-register@^6.18.0, babel-register@^6.24.0, babel-register@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-register/-/babel-register-6.24.1.tgz#7e10e13a2f71065bdfad5a1787ba45bca6ded75f"
  dependencies:
    babel-core "^6.24.1"
    babel-runtime "^6.22.0"
    core-js "^2.4.0"
    home-or-tmp "^2.0.0"
    lodash "^4.2.0"
    mkdirp "^0.5.1"
    source-map-support "^0.4.2"

babel-runtime@^6.18.0, babel-runtime@^6.20.0, babel-runtime@^6.22.0:
  version "6.23.0"
  resolved "https://registry.yarnpkg.com/babel-runtime/-/babel-runtime-6.23.0.tgz#0a9489f144de70efb3ce4300accdb329e2fc543b"
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.10.0"

babel-template@^6.16.0, babel-template@^6.24.1, babel-template@^6.25.0:
  version "6.25.0"
  resolved "https://registry.yarnpkg.com/babel-template/-/babel-template-6.25.0.tgz#665241166b7c2aa4c619d71e192969552b10c071"
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.25.0"
    babel-types "^6.25.0"
    babylon "^6.17.2"
    lodash "^4.2.0"

babel-traverse@^6.18.0, babel-traverse@^6.21.0, babel-traverse@^6.24.1, babel-traverse@^6.25.0:
  version "6.25.0"
  resolved "https://registry.yarnpkg.com/babel-traverse/-/babel-traverse-6.25.0.tgz#2257497e2fcd19b89edc13c4c91381f9512496f1"
  dependencies:
    babel-code-frame "^6.22.0"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-types "^6.25.0"
    babylon "^6.17.2"
    debug "^2.2.0"
    globals "^9.0.0"
    invariant "^2.2.0"
    lodash "^4.2.0"

babel-types@^6.18.0, babel-types@^6.19.0, babel-types@^6.21.0, babel-types@^6.24.1, babel-types@^6.25.0:
  version "6.25.0"
  resolved "https://registry.yarnpkg.com/babel-types/-/babel-types-6.25.0.tgz#70afb248d5660e5d18f811d91c8303b54134a18e"
  dependencies:
    babel-runtime "^6.22.0"
    esutils "^2.0.2"
    lodash "^4.2.0"
    to-fast-properties "^1.0.1"

babylon@^6.14.1, babylon@^6.17.2, babylon@^6.17.4:
  version "6.17.4"
  resolved "https://registry.yarnpkg.com/babylon/-/babylon-6.17.4.tgz#3e8b7402b88d22c3423e137a1577883b15ff869a"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/balanced-match/-/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"

base64-js@0.0.8:
  version "0.0.8"
  resolved "https://registry.yarnpkg.com/base64-js/-/base64-js-0.0.8.tgz#1101e9544f4a76b1bc3b26d452ca96d7a35e7978"

base64-js@^1.0.2, base64-js@^1.1.2:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/base64-js/-/base64-js-1.2.1.tgz#a91947da1f4a516ea38e5b4ec0ec3773675e0886"

base64-url@1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/base64-url/-/base64-url-1.2.1.tgz#199fd661702a0e7b7dcae6e0698bb089c52f6d78"

basic-auth-connect@1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/basic-auth-connect/-/basic-auth-connect-1.0.0.tgz#fdb0b43962ca7b40456a7c2bb48fe173da2d2122"

basic-auth@~1.0.3:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/basic-auth/-/basic-auth-1.0.4.tgz#030935b01de7c9b94a824b29f3fccb750d3a5290"

basic-auth@~1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/basic-auth/-/basic-auth-1.1.0.tgz#45221ee429f7ee1e5035be3f51533f1cdfd29884"

batch@0.5.3:
  version "0.5.3"
  resolved "https://registry.yarnpkg.com/batch/-/batch-0.5.3.tgz#3f3414f380321743bfc1042f9a83ff1d5824d464"

bcrypt-pbkdf@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.1.tgz#63bc5dcb61331b92bc05fd528953c33462a06f8d"
  dependencies:
    tweetnacl "^0.14.3"

beeper@^1.0.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/beeper/-/beeper-1.1.1.tgz#e6d5ea8c5dad001304a70b22638447f69cb2f809"

big.js@^3.1.3:
  version "3.1.3"
  resolved "https://registry.yarnpkg.com/big.js/-/big.js-3.1.3.tgz#4cada2193652eb3ca9ec8e55c9015669c9806978"

binary-extensions@^1.0.0:
  version "1.8.0"
  resolved "https://registry.yarnpkg.com/binary-extensions/-/binary-extensions-1.8.0.tgz#48ec8d16df4377eae5fa5884682480af4d95c774"

block-stream@*:
  version "0.0.9"
  resolved "https://registry.yarnpkg.com/block-stream/-/block-stream-0.0.9.tgz#13ebfe778a03205cfe03751481ebb4b3300c126a"
  dependencies:
    inherits "~2.0.0"

bn.js@^4.0.0, bn.js@^4.1.0, bn.js@^4.1.1, bn.js@^4.4.0:
  version "4.11.7"
  resolved "https://registry.yarnpkg.com/bn.js/-/bn.js-4.11.7.tgz#ddb048e50d9482790094c13eb3fcfc833ce7ab46"

body-parser@~1.13.3:
  version "1.13.3"
  resolved "https://registry.yarnpkg.com/body-parser/-/body-parser-1.13.3.tgz#c08cf330c3358e151016a05746f13f029c97fa97"
  dependencies:
    bytes "2.1.0"
    content-type "~1.0.1"
    debug "~2.2.0"
    depd "~1.0.1"
    http-errors "~1.3.1"
    iconv-lite "0.4.11"
    on-finished "~2.3.0"
    qs "4.0.0"
    raw-body "~2.1.2"
    type-is "~1.6.6"

boom@2.x.x:
  version "2.10.1"
  resolved "https://registry.yarnpkg.com/boom/-/boom-2.10.1.tgz#39c8918ceff5799f83f9492a848f625add0c766f"
  dependencies:
    hoek "2.x.x"

bplist-creator@0.0.4:
  version "0.0.4"
  resolved "https://registry.yarnpkg.com/bplist-creator/-/bplist-creator-0.0.4.tgz#4ac0496782e127a85c1d2026a4f5eb22a7aff991"
  dependencies:
    stream-buffers "~0.2.3"

bplist-parser@0.0.6:
  version "0.0.6"
  resolved "https://registry.yarnpkg.com/bplist-parser/-/bplist-parser-0.0.6.tgz#38da3471817df9d44ab3892e27707bbbd75a11b9"

brace-expansion@^1.1.7:
  version "1.1.8"
  resolved "https://registry.yarnpkg.com/brace-expansion/-/brace-expansion-1.1.8.tgz#c07b211c7c952ec1f8efd51a77ef0d1d3990a292"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^1.8.2:
  version "1.8.5"
  resolved "https://registry.yarnpkg.com/braces/-/braces-1.8.5.tgz#ba77962e12dff969d6b76711e914b737857bf6a7"
  dependencies:
    expand-range "^1.8.1"
    preserve "^0.2.0"
    repeat-element "^1.1.2"

brorand@^1.0.1:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/brorand/-/brorand-1.1.0.tgz#12c25efe40a45e3c323eb8675a0a0ce57b22371f"

browser-resolve@^1.11.2:
  version "1.11.2"
  resolved "https://registry.yarnpkg.com/browser-resolve/-/browser-resolve-1.11.2.tgz#8ff09b0a2c421718a1051c260b32e48f442938ce"
  dependencies:
    resolve "1.1.7"

browserify-aes@^1.0.0, browserify-aes@^1.0.4:
  version "1.0.6"
  resolved "https://registry.yarnpkg.com/browserify-aes/-/browserify-aes-1.0.6.tgz#5e7725dbdef1fd5930d4ebab48567ce451c48a0a"
  dependencies:
    buffer-xor "^1.0.2"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.0"
    inherits "^2.0.1"

browserify-cipher@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/browserify-cipher/-/browserify-cipher-1.0.0.tgz#9988244874bf5ed4e28da95666dcd66ac8fc363a"
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/browserify-des/-/browserify-des-1.0.0.tgz#daa277717470922ed2fe18594118a175439721dd"
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"

browserify-rsa@^4.0.0:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/browserify-rsa/-/browserify-rsa-4.0.1.tgz#21e0abfaf6f2029cf2fafb133567a701d4135524"
  dependencies:
    bn.js "^4.1.0"
    randombytes "^2.0.1"

browserify-sign@^4.0.0:
  version "4.0.4"
  resolved "https://registry.yarnpkg.com/browserify-sign/-/browserify-sign-4.0.4.tgz#aa4eb68e5d7b658baa6bf6a57e630cbd7a93d298"
  dependencies:
    bn.js "^4.1.1"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.2"
    elliptic "^6.0.0"
    inherits "^2.0.1"
    parse-asn1 "^5.0.0"

browserify-zlib@^0.1.4:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/browserify-zlib/-/browserify-zlib-0.1.4.tgz#bb35f8a519f600e0fa6b8485241c979d0141fb2d"
  dependencies:
    pako "~0.2.0"

browserslist@^2.1.2:
  version "2.1.5"
  resolved "https://registry.yarnpkg.com/browserslist/-/browserslist-2.1.5.tgz#e882550df3d1cd6d481c1a3e0038f2baf13a4711"
  dependencies:
    caniuse-lite "^1.0.30000684"
    electron-to-chromium "^1.3.14"

bser@1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/bser/-/bser-1.0.2.tgz#381116970b2a6deea5646dd15dd7278444b56169"
  dependencies:
    node-int64 "^0.4.0"

bser@^1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/bser/-/bser-1.0.3.tgz#d63da19ee17330a0e260d2a34422b21a89520317"
  dependencies:
    node-int64 "^0.4.0"

bser@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/bser/-/bser-2.0.0.tgz#9ac78d3ed5d915804fd87acb158bc797147a1719"
  dependencies:
    node-int64 "^0.4.0"

buffer-xor@^1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/buffer-xor/-/buffer-xor-1.0.3.tgz#26e61ed1422fb70dd42e6e36729ed51d855fe8d9"

buffer@^4.3.0:
  version "4.9.1"
  resolved "https://registry.yarnpkg.com/buffer/-/buffer-4.9.1.tgz#6d1bb601b07a4efced97094132093027c95bc298"
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

builtin-modules@^1.0.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/builtin-modules/-/builtin-modules-1.1.1.tgz#270f076c5a72c02f5b65a47df94c5fe3a278892f"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz#85982878e21b98e1c66425e03d0174788f569ee8"

bytes@2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/bytes/-/bytes-2.1.0.tgz#ac93c410e2ffc9cc7cf4b464b38289067f5e47b4"

bytes@2.4.0:
  version "2.4.0"
  resolved "https://registry.yarnpkg.com/bytes/-/bytes-2.4.0.tgz#7d97196f9d5baf7f6935e25985549edd2a6c2339"

callsites@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/callsites/-/callsites-2.0.0.tgz#06eb84f00eea413da86affefacbffb36093b3c50"

camelcase-keys@^4.0.0:
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/camelcase-keys/-/camelcase-keys-4.1.0.tgz#214d348cc5457f39316a2c31cc3e37246325e73f"
  dependencies:
    camelcase "^4.1.0"
    map-obj "^2.0.0"
    quick-lru "^1.0.0"

camelcase@^1.0.2:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/camelcase/-/camelcase-1.2.1.tgz#9bb5304d2e0b56698b2c758b08a3eaa9daa58a39"

camelcase@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/camelcase/-/camelcase-3.0.0.tgz#32fc4b9fcdaf845fcdf7e73bb97cac2261f0ab0a"

camelcase@^4.1.0:
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/camelcase/-/camelcase-4.1.0.tgz#d545635be1e33c542649c69173e5de6acfae34dd"

caniuse-lite@^1.0.30000684:
  version "1.0.30000701"
  resolved "https://registry.yarnpkg.com/caniuse-lite/-/caniuse-lite-1.0.30000701.tgz#9d673cf6b74dcb3d5c21d213176b011ac6a45baa"

case-sensitive-paths-webpack-plugin@^2.0.0:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/case-sensitive-paths-webpack-plugin/-/case-sensitive-paths-webpack-plugin-2.1.1.tgz#3d29ced8c1f124bf6f53846fb3f5894731fdc909"

caseless@~0.12.0:
  version "0.12.0"
  resolved "https://registry.yarnpkg.com/caseless/-/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"

center-align@^0.1.1:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/center-align/-/center-align-0.1.3.tgz#aa0d32629b6ee972200411cbd4461c907bc2b7ad"
  dependencies:
    align-text "^0.1.3"
    lazy-cache "^1.0.3"

chalk@1.1.3, chalk@^1.0.0, chalk@^1.1.0, chalk@^1.1.1, chalk@^1.1.3:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/chalk/-/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/chalk/-/chalk-2.0.1.tgz#dbec49436d2ae15f536114e76d14656cdbc0f44d"
  dependencies:
    ansi-styles "^3.1.0"
    escape-string-regexp "^1.0.5"
    supports-color "^4.0.0"

chokidar@^1.7.0:
  version "1.7.0"
  resolved "https://registry.yarnpkg.com/chokidar/-/chokidar-1.7.0.tgz#798e689778151c8076b4b360e5edd28cda2bb468"
  dependencies:
    anymatch "^1.3.0"
    async-each "^1.0.0"
    glob-parent "^2.0.0"
    inherits "^2.0.1"
    is-binary-path "^1.0.0"
    is-glob "^2.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.0.0"
  optionalDependencies:
    fsevents "^1.0.0"

ci-info@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/ci-info/-/ci-info-1.0.0.tgz#dc5285f2b4e251821683681c381c3388f46ec534"

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/cipher-base/-/cipher-base-1.0.4.tgz#8760e4ecc272f4c363532f926d874aae2c1397de"
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

clap@^1.1.3:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/clap/-/clap-1.2.0.tgz#59c90fe3e137104746ff19469a27a634ff68c857"
  dependencies:
    chalk "^1.1.3"

clear@^0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/clear/-/clear-0.0.1.tgz#e5186e229d99448179c130311b6f9d30bff6b0ba"

cli-cursor@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/cli-cursor/-/cli-cursor-1.0.2.tgz#64da3f7d56a54412e59794bd62dc35295e8f2987"
  dependencies:
    restore-cursor "^1.0.1"

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/cli-cursor/-/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  dependencies:
    restore-cursor "^2.0.0"

cli-spinners@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/cli-spinners/-/cli-spinners-1.0.0.tgz#ef987ed3d48391ac3dab9180b406a742180d6e6a"

cli-width@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/cli-width/-/cli-width-2.1.0.tgz#b234ca209b29ef66fc518d9b98d5847b00edf00a"

cliui@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/cliui/-/cliui-2.1.0.tgz#4b475760ff80264c762c3a1719032e91c7fea0d1"
  dependencies:
    center-align "^0.1.1"
    right-align "^0.1.1"
    wordwrap "0.0.2"

cliui@^3.2.0:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/cliui/-/cliui-3.2.0.tgz#120601537a916d29940f934da3b48d585a39213d"
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wrap-ansi "^2.0.0"

clone-stats@^0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/clone-stats/-/clone-stats-0.0.1.tgz#b88f94a82cf38b8791d58046ea4029ad88ca99d1"

clone@^1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/clone/-/clone-1.0.2.tgz#260b7a99ebb1edfe247538175f783243cb19d149"

co@^4.6.0:
  version "4.6.0"
  resolved "https://registry.yarnpkg.com/co/-/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/code-point-at/-/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"

color-convert@^1.0.0:
  version "1.9.0"
  resolved "https://registry.yarnpkg.com/color-convert/-/color-convert-1.9.0.tgz#1accf97dd739b983bf994d56fec8f95853641b7a"
  dependencies:
    color-name "^1.1.1"

color-name@^1.1.1:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/color-name/-/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"

combined-stream@^1.0.5, combined-stream@~1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/combined-stream/-/combined-stream-1.0.5.tgz#938370a57b4a51dea2c77c15d5c5fdf895164009"
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.9.0:
  version "2.11.0"
  resolved "https://registry.yarnpkg.com/commander/-/commander-2.11.0.tgz#157152fd1e7a6c8d98a5b715cf376df928004563"

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/commondir/-/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"

compressible@~2.0.5:
  version "2.0.10"
  resolved "https://registry.yarnpkg.com/compressible/-/compressible-2.0.10.tgz#feda1c7f7617912732b29bf8cf26252a20b9eecd"
  dependencies:
    mime-db ">= 1.27.0 < 2"

compression@~1.5.2:
  version "1.5.2"
  resolved "https://registry.yarnpkg.com/compression/-/compression-1.5.2.tgz#b03b8d86e6f8ad29683cba8df91ddc6ffc77b395"
  dependencies:
    accepts "~1.2.12"
    bytes "2.1.0"
    compressible "~2.0.5"
    debug "~2.2.0"
    on-headers "~1.0.0"
    vary "~1.0.1"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"

connect-timeout@~1.6.2:
  version "1.6.2"
  resolved "https://registry.yarnpkg.com/connect-timeout/-/connect-timeout-1.6.2.tgz#de9a5ec61e33a12b6edaab7b5f062e98c599b88e"
  dependencies:
    debug "~2.2.0"
    http-errors "~1.3.1"
    ms "0.7.1"
    on-headers "~1.0.0"

connect@^2.8.3:
  version "2.30.2"
  resolved "https://registry.yarnpkg.com/connect/-/connect-2.30.2.tgz#8da9bcbe8a054d3d318d74dfec903b5c39a1b609"
  dependencies:
    basic-auth-connect "1.0.0"
    body-parser "~1.13.3"
    bytes "2.1.0"
    compression "~1.5.2"
    connect-timeout "~1.6.2"
    content-type "~1.0.1"
    cookie "0.1.3"
    cookie-parser "~1.3.5"
    cookie-signature "1.0.6"
    csurf "~1.8.3"
    debug "~2.2.0"
    depd "~1.0.1"
    errorhandler "~1.4.2"
    express-session "~1.11.3"
    finalhandler "0.4.0"
    fresh "0.3.0"
    http-errors "~1.3.1"
    method-override "~2.3.5"
    morgan "~1.6.1"
    multiparty "3.3.2"
    on-headers "~1.0.0"
    parseurl "~1.3.0"
    pause "0.1.0"
    qs "4.0.0"
    response-time "~2.3.1"
    serve-favicon "~2.3.0"
    serve-index "~1.7.2"
    serve-static "~1.10.0"
    type-is "~1.6.6"
    utils-merge "1.0.0"
    vhost "~3.0.1"

console-browserify@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/console-browserify/-/console-browserify-1.1.0.tgz#f0241c45730a9fc6323b206dbf38edc741d0bb10"
  dependencies:
    date-now "^0.1.4"

console-control-strings@^1.0.0, console-control-strings@~1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/console-control-strings/-/console-control-strings-1.1.0.tgz#3d7cf4464db6446ea644bf4b39507f9851008e8e"

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/constants-browserify/-/constants-browserify-1.0.0.tgz#c20b96d8c617748aaf1c16021760cd27fcb8cb75"

content-disposition@0.5.2:
  version "0.5.2"
  resolved "https://registry.yarnpkg.com/content-disposition/-/content-disposition-0.5.2.tgz#0cf68bb9ddf5f2be7961c3a85178cb85dba78cb4"

content-type-parser@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/content-type-parser/-/content-type-parser-1.0.1.tgz#c3e56988c53c65127fb46d4032a3a900246fdc94"

content-type@~1.0.1, content-type@~1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/content-type/-/content-type-1.0.2.tgz#b7d113aee7a8dd27bd21133c4dc2529df1721eed"

convert-source-map@^1.1.0, convert-source-map@^1.4.0:
  version "1.5.0"
  resolved "https://registry.yarnpkg.com/convert-source-map/-/convert-source-map-1.5.0.tgz#9acd70851c6d5dfdd93d9282e5edf94a03ff46b5"

cookie-parser@~1.3.5:
  version "1.3.5"
  resolved "https://registry.yarnpkg.com/cookie-parser/-/cookie-parser-1.3.5.tgz#9d755570fb5d17890771227a02314d9be7cf8356"
  dependencies:
    cookie "0.1.3"
    cookie-signature "1.0.6"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.yarnpkg.com/cookie-signature/-/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"

cookie@0.1.3:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/cookie/-/cookie-0.1.3.tgz#e734a5c1417fce472d5aef82c381cabb64d1a435"

cookie@0.3.1:
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/cookie/-/cookie-0.3.1.tgz#e7e0a1f9ef43b4c8ba925c5c5a96e806d16873bb"

core-js@^1.0.0:
  version "1.2.7"
  resolved "https://registry.yarnpkg.com/core-js/-/core-js-1.2.7.tgz#652294c14651db28fa93bd2d5ff2983a4f08c636"

core-js@^2.2.2, core-js@^2.4.0:
  version "2.4.1"
  resolved "https://registry.yarnpkg.com/core-js/-/core-js-2.4.1.tgz#4de911e667b0eae9124e34254b53aea6fc618d3e"

core-util-is@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/core-util-is/-/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"

crc@3.3.0:
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/crc/-/crc-3.3.0.tgz#fa622e1bc388bf257309082d6b65200ce67090ba"

create-ecdh@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/create-ecdh/-/create-ecdh-4.0.0.tgz#888c723596cdf7612f6498233eebd7a35301737d"
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.0.0"

create-hash@^1.1.0, create-hash@^1.1.1, create-hash@^1.1.2:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/create-hash/-/create-hash-1.1.3.tgz#606042ac8b9262750f483caddab0f5819172d8fd"
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    sha.js "^2.4.0"

create-hmac@^1.1.0, create-hmac@^1.1.2, create-hmac@^1.1.4:
  version "1.1.6"
  resolved "https://registry.yarnpkg.com/create-hmac/-/create-hmac-1.1.6.tgz#acb9e221a4e17bdb076e90657c42b93e3726cf06"
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

cross-spawn@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/cross-spawn/-/cross-spawn-3.0.1.tgz#1256037ecb9f0c5f79e3d6ef135e30770184b982"
  dependencies:
    lru-cache "^4.0.1"
    which "^1.2.9"

cryptiles@2.x.x:
  version "2.0.5"
  resolved "https://registry.yarnpkg.com/cryptiles/-/cryptiles-2.0.5.tgz#3bdfecdc608147c1c67202fa291e7dca59eaa3b8"
  dependencies:
    boom "2.x.x"

crypto-browserify@^3.11.0:
  version "3.11.1"
  resolved "https://registry.yarnpkg.com/crypto-browserify/-/crypto-browserify-3.11.1.tgz#948945efc6757a400d6e5e5af47194d10064279f"
  dependencies:
    browserify-cipher "^1.0.0"
    browserify-sign "^4.0.0"
    create-ecdh "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.0"
    diffie-hellman "^5.0.0"
    inherits "^2.0.1"
    pbkdf2 "^3.0.3"
    public-encrypt "^4.0.0"
    randombytes "^2.0.0"

csrf@~3.0.0:
  version "3.0.6"
  resolved "https://registry.yarnpkg.com/csrf/-/csrf-3.0.6.tgz#b61120ddceeafc91e76ed5313bb5c0b2667b710a"
  dependencies:
    rndm "1.2.0"
    tsscmp "1.0.5"
    uid-safe "2.1.4"

cssom@0.3.x, "cssom@>= 0.3.2 < 0.4.0":
  version "0.3.2"
  resolved "https://registry.yarnpkg.com/cssom/-/cssom-0.3.2.tgz#b8036170c79f07a90ff2f16e22284027a243848b"

"cssstyle@>= 0.2.37 < 0.3.0":
  version "0.2.37"
  resolved "https://registry.yarnpkg.com/cssstyle/-/cssstyle-0.2.37.tgz#541097234cb2513c83ceed3acddc27ff27987d54"
  dependencies:
    cssom "0.3.x"

csurf@~1.8.3:
  version "1.8.3"
  resolved "https://registry.yarnpkg.com/csurf/-/csurf-1.8.3.tgz#23f2a13bf1d8fce1d0c996588394442cba86a56a"
  dependencies:
    cookie "0.1.3"
    cookie-signature "1.0.6"
    csrf "~3.0.0"
    http-errors "~1.3.1"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "https://registry.yarnpkg.com/dashdash/-/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  dependencies:
    assert-plus "^1.0.0"

date-now@^0.1.4:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/date-now/-/date-now-0.1.4.tgz#eaf439fd4d4848ad74e5cc7dbef200672b9e345b"

dateformat@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/dateformat/-/dateformat-2.0.0.tgz#2743e3abb5c3fc2462e527dca445e04e9f4dee17"

debug@2.6.7:
  version "2.6.7"
  resolved "https://registry.yarnpkg.com/debug/-/debug-2.6.7.tgz#92bad1f6d05bbb6bba22cca88bcd0ec894c2861e"
  dependencies:
    ms "2.0.0"

debug@2.6.8, debug@^2.1.1, debug@^2.2.0, debug@^2.6.3:
  version "2.6.8"
  resolved "https://registry.yarnpkg.com/debug/-/debug-2.6.8.tgz#e731531ca2ede27d188222427da17821d68ff4fc"
  dependencies:
    ms "2.0.0"

debug@~2.2.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/debug/-/debug-2.2.0.tgz#f87057e995b1a1f6ae6a4960664137bc56f039da"
  dependencies:
    ms "0.7.1"

decamelize@^1.0.0, decamelize@^1.1.1, decamelize@^1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/decamelize/-/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"

dedent@^0.7.0:
  version "0.7.0"
  resolved "https://registry.yarnpkg.com/dedent/-/dedent-0.7.0.tgz#2495ddbaf6eb874abb0e1be9df22d2e5a544326c"

deep-extend@~0.4.0:
  version "0.4.2"
  resolved "https://registry.yarnpkg.com/deep-extend/-/deep-extend-0.4.2.tgz#48b699c27e334bf89f10892be432f6e4c7d34a7f"

deep-is@~0.1.3:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/deep-is/-/deep-is-0.1.3.tgz#b369d6fb5dbc13eecf524f91b070feedc357cf34"

default-require-extensions@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/default-require-extensions/-/default-require-extensions-1.0.0.tgz#f37ea15d3e13ffd9b437d33e1a75b5fb97874cb8"
  dependencies:
    strip-bom "^2.0.0"

define-properties@^1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/define-properties/-/define-properties-1.1.2.tgz#83a73f2fea569898fb737193c8f873caf6d45c94"
  dependencies:
    foreach "^2.0.5"
    object-keys "^1.0.8"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/delegates/-/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"

denodeify@^1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/denodeify/-/denodeify-1.2.1.tgz#3a36287f5034e699e7577901052c2e6c94251631"

depd@1.1.0, depd@~1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/depd/-/depd-1.1.0.tgz#e1bd82c6aab6ced965b97b88b17ed3e528ca18c3"

depd@~1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/depd/-/depd-1.0.1.tgz#80aec64c9d6d97e65cc2a9caa93c0aa6abf73aaa"

des.js@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/des.js/-/des.js-1.0.0.tgz#c074d2e2aa6a8a9a07dbd61f9a15c2cd83ec8ecc"
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

destroy@~1.0.4:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/destroy/-/destroy-1.0.4.tgz#978857442c44749e4206613e37946205826abd80"

detect-indent@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/detect-indent/-/detect-indent-4.0.0.tgz#f76d064352cdf43a1cb6ce619c4ee3a9475de208"
  dependencies:
    repeating "^2.0.0"

diff@^3.2.0:
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/diff/-/diff-3.3.0.tgz#056695150d7aa93237ca7e378ac3b1682b7963b9"

diffie-hellman@^5.0.0:
  version "5.0.2"
  resolved "https://registry.yarnpkg.com/diffie-hellman/-/diffie-hellman-5.0.2.tgz#b5835739270cfe26acf632099fded2a07f209e5e"
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

dlv@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/dlv/-/dlv-1.1.0.tgz#fee1a7c43f63be75f3f679e85262da5f102764a7"

dom-walk@^0.1.0:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/dom-walk/-/dom-walk-0.1.1.tgz#672226dc74c8f799ad35307df936aba11acd6018"

domain-browser@^1.1.1:
  version "1.1.7"
  resolved "https://registry.yarnpkg.com/domain-browser/-/domain-browser-1.1.7.tgz#867aa4b093faa05f1de08c06f4d7b21fdf8698bc"

duplexer2@0.0.2:
  version "0.0.2"
  resolved "https://registry.yarnpkg.com/duplexer2/-/duplexer2-0.0.2.tgz#c614dcf67e2fb14995a91711e5a617e8a60a31db"
  dependencies:
    readable-stream "~1.1.9"

ecc-jsbn@~0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/ecc-jsbn/-/ecc-jsbn-0.1.1.tgz#0fc73a9ed5f0d53c38193398523ef7e543777505"
  dependencies:
    jsbn "~0.1.0"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/ee-first/-/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"

electron-to-chromium@^1.3.14:
  version "1.3.15"
  resolved "https://registry.yarnpkg.com/electron-to-chromium/-/electron-to-chromium-1.3.15.tgz#08397934891cbcfaebbd18b82a95b5a481138369"

elliptic@^6.0.0:
  version "6.4.0"
  resolved "https://registry.yarnpkg.com/elliptic/-/elliptic-6.4.0.tgz#cac9af8762c85836187003c8dfe193e5e2eae5df"
  dependencies:
    bn.js "^4.4.0"
    brorand "^1.0.1"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.0"

emojis-list@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/emojis-list/-/emojis-list-2.1.0.tgz#4daa4d9db00f9819880c79fa457ae5b09a1fd389"

encodeurl@~1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/encodeurl/-/encodeurl-1.0.1.tgz#79e3d58655346909fe6f0f45a5de68103b294d20"

encoding@^0.1.11:
  version "0.1.12"
  resolved "https://registry.yarnpkg.com/encoding/-/encoding-0.1.12.tgz#538b66f3ee62cd1ab51ec323829d1f9480c74beb"
  dependencies:
    iconv-lite "~0.4.13"

enhanced-resolve@^3.3.0:
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/enhanced-resolve/-/enhanced-resolve-3.3.0.tgz#950964ecc7f0332a42321b673b38dc8ff15535b3"
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.4.0"
    object-assign "^4.0.1"
    tapable "^0.2.5"

errno@^0.1.3, errno@^0.1.4:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/errno/-/errno-0.1.4.tgz#b896e23a9e5e8ba33871fc996abd3635fc9a1c7d"
  dependencies:
    prr "~0.0.0"

error-ex@^1.2.0:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/error-ex/-/error-ex-1.3.1.tgz#f855a86ce61adc4e8621c3cda21e7a7612c3a8dc"
  dependencies:
    is-arrayish "^0.2.1"

errorhandler@~1.4.2:
  version "1.4.3"
  resolved "https://registry.yarnpkg.com/errorhandler/-/errorhandler-1.4.3.tgz#b7b70ed8f359e9db88092f2d20c0f831420ad83f"
  dependencies:
    accepts "~1.3.0"
    escape-html "~1.0.3"

es6-error@^4.0.2:
  version "4.0.2"
  resolved "https://registry.yarnpkg.com/es6-error/-/es6-error-4.0.2.tgz#eec5c726eacef51b7f6b73c20db6e1b13b069c98"

escape-html@1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/escape-html/-/escape-html-1.0.2.tgz#d77d32fa98e38c2f41ae85e9278e0e0e6ba1022c"

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/escape-html/-/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"

escodegen@^1.6.1:
  version "1.8.1"
  resolved "https://registry.yarnpkg.com/escodegen/-/escodegen-1.8.1.tgz#5a5b53af4693110bebb0867aa3430dd3b70a1018"
  dependencies:
    esprima "^2.7.1"
    estraverse "^1.9.1"
    esutils "^2.0.2"
    optionator "^0.8.1"
  optionalDependencies:
    source-map "~0.2.0"

eslint-plugin-prettier@^2.0.1:
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/eslint-plugin-prettier/-/eslint-plugin-prettier-2.1.2.tgz#4b90f4ee7f92bfbe2e926017e1ca40eb628965ea"
  dependencies:
    fast-diff "^1.1.1"
    jest-docblock "^20.0.1"

esprima@^2.7.1:
  version "2.7.3"
  resolved "https://registry.yarnpkg.com/esprima/-/esprima-2.7.3.tgz#96e3b70d5779f6ad49cd032673d1c312767ba581"

esprima@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/esprima/-/esprima-4.0.0.tgz#4499eddcd1110e0b218bacf2fa7f7f59f55ca804"

estraverse@^1.9.1:
  version "1.9.3"
  resolved "https://registry.yarnpkg.com/estraverse/-/estraverse-1.9.3.tgz#af67f2dc922582415950926091a4005d29c9bb44"

esutils@^2.0.0, esutils@^2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/esutils/-/esutils-2.0.2.tgz#0abf4f1caa5bcb1f7a9d8acc6dea4faaa04bac9b"

etag@~1.7.0:
  version "1.7.0"
  resolved "https://registry.yarnpkg.com/etag/-/etag-1.7.0.tgz#03d30b5f67dd6e632d2945d30d6652731a34d5d8"

etag@~1.8.0:
  version "1.8.0"
  resolved "https://registry.yarnpkg.com/etag/-/etag-1.8.0.tgz#6f631aef336d6c46362b51764044ce216be3c051"

event-target-shim@^1.0.5:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/event-target-shim/-/event-target-shim-1.1.1.tgz#a86e5ee6bdaa16054475da797ccddf0c55698491"

events@^1.0.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/events/-/events-1.1.1.tgz#9ebdb7635ad099c70dcc4c2a1f5004288e8bd924"

evp_bytestokey@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/evp_bytestokey/-/evp_bytestokey-1.0.0.tgz#497b66ad9fef65cd7c08a6180824ba1476b66e53"
  dependencies:
    create-hash "^1.1.1"

exec-sh@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/exec-sh/-/exec-sh-0.2.0.tgz#14f75de3f20d286ef933099b2ce50a90359cef10"
  dependencies:
    merge "^1.1.3"

exit-hook@^1.0.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/exit-hook/-/exit-hook-1.1.1.tgz#f05ca233b48c05d54fff07765df8507e95c02ff8"

expand-brackets@^0.1.4:
  version "0.1.5"
  resolved "https://registry.yarnpkg.com/expand-brackets/-/expand-brackets-0.1.5.tgz#df07284e342a807cd733ac5af72411e581d1177b"
  dependencies:
    is-posix-bracket "^0.1.0"

expand-range@^1.8.1:
  version "1.8.2"
  resolved "https://registry.yarnpkg.com/expand-range/-/expand-range-1.8.2.tgz#a299effd335fe2721ebae8e257ec79644fc85337"
  dependencies:
    fill-range "^2.1.0"

express-session@~1.11.3:
  version "1.11.3"
  resolved "https://registry.yarnpkg.com/express-session/-/express-session-1.11.3.tgz#5cc98f3f5ff84ed835f91cbf0aabd0c7107400af"
  dependencies:
    cookie "0.1.3"
    cookie-signature "1.0.6"
    crc "3.3.0"
    debug "~2.2.0"
    depd "~1.0.1"
    on-headers "~1.0.0"
    parseurl "~1.3.0"
    uid-safe "~2.0.0"
    utils-merge "1.0.0"

express@^4.15.2:
  version "4.15.3"
  resolved "https://registry.yarnpkg.com/express/-/express-4.15.3.tgz#bab65d0f03aa80c358408972fc700f916944b662"
  dependencies:
    accepts "~1.3.3"
    array-flatten "1.1.1"
    content-disposition "0.5.2"
    content-type "~1.0.2"
    cookie "0.3.1"
    cookie-signature "1.0.6"
    debug "2.6.7"
    depd "~1.1.0"
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    etag "~1.8.0"
    finalhandler "~1.0.3"
    fresh "0.5.0"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.1"
    path-to-regexp "0.1.7"
    proxy-addr "~1.1.4"
    qs "6.4.0"
    range-parser "~1.2.0"
    send "0.15.3"
    serve-static "1.12.3"
    setprototypeof "1.0.3"
    statuses "~1.3.1"
    type-is "~1.6.15"
    utils-merge "1.0.0"
    vary "~1.1.1"

extend@~3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/extend/-/extend-3.0.1.tgz#a755ea7bc1adfcc5a31ce7e762dbaadc5e636444"

external-editor@^2.0.4:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/external-editor/-/external-editor-2.0.4.tgz#1ed9199da9cbfe2ef2f7a31b2fde8b0d12368972"
  dependencies:
    iconv-lite "^0.4.17"
    jschardet "^1.4.2"
    tmp "^0.0.31"

extglob@^0.3.1:
  version "0.3.2"
  resolved "https://registry.yarnpkg.com/extglob/-/extglob-0.3.2.tgz#2e18ff3d2f49ab2765cec9023f011daa8d8349a1"
  dependencies:
    is-extglob "^1.0.0"

extsprintf@1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/extsprintf/-/extsprintf-1.0.2.tgz#e1080e0658e300b06294990cc70e1502235fd550"

fancy-log@^1.1.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/fancy-log/-/fancy-log-1.3.0.tgz#45be17d02bb9917d60ccffd4995c999e6c8c9948"
  dependencies:
    chalk "^1.1.1"
    time-stamp "^1.0.0"

fast-diff@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/fast-diff/-/fast-diff-1.1.1.tgz#0aea0e4e605b6a2189f0e936d4b7fbaf1b7cfd9b"

fast-levenshtein@~2.0.4:
  version "2.0.6"
  resolved "https://registry.yarnpkg.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"

fb-watchman@^1.8.0, fb-watchman@^1.9.0:
  version "1.9.2"
  resolved "https://registry.yarnpkg.com/fb-watchman/-/fb-watchman-1.9.2.tgz#a24cf47827f82d38fb59a69ad70b76e3b6ae7383"
  dependencies:
    bser "1.0.2"

fb-watchman@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/fb-watchman/-/fb-watchman-2.0.0.tgz#54e9abf7dfa2f26cd9b1636c588c1afc05de5d58"
  dependencies:
    bser "^2.0.0"

fbjs-scripts@^0.7.0:
  version "0.7.1"
  resolved "https://registry.yarnpkg.com/fbjs-scripts/-/fbjs-scripts-0.7.1.tgz#4f115e218e243e3addbf0eddaac1e3c62f703fac"
  dependencies:
    babel-core "^6.7.2"
    babel-preset-fbjs "^1.0.0"
    core-js "^1.0.0"
    cross-spawn "^3.0.1"
    gulp-util "^3.0.4"
    object-assign "^4.0.1"
    semver "^5.1.0"
    through2 "^2.0.0"

fbjs@^0.8.4, fbjs@^0.8.5:
  version "0.8.12"
  resolved "https://registry.yarnpkg.com/fbjs/-/fbjs-0.8.12.tgz#10b5d92f76d45575fd63a217d4ea02bea2f8ed04"
  dependencies:
    core-js "^1.0.0"
    isomorphic-fetch "^2.1.1"
    loose-envify "^1.0.0"
    object-assign "^4.1.0"
    promise "^7.1.1"
    setimmediate "^1.0.5"
    ua-parser-js "^0.7.9"

figures@^1.3.5:
  version "1.7.0"
  resolved "https://registry.yarnpkg.com/figures/-/figures-1.7.0.tgz#cbe1e3affcf1cd44b80cadfed28dc793a9701d2e"
  dependencies:
    escape-string-regexp "^1.0.5"
    object-assign "^4.1.0"

figures@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/figures/-/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
  dependencies:
    escape-string-regexp "^1.0.5"

filename-regex@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/filename-regex/-/filename-regex-2.0.1.tgz#c1c4b9bee3e09725ddb106b75c1e301fe2f18b26"

fileset@^2.0.2:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/fileset/-/fileset-2.0.3.tgz#8e7548a96d3cc2327ee5e674168723a333bba2a0"
  dependencies:
    glob "^7.0.3"
    minimatch "^3.0.3"

fill-range@^2.1.0:
  version "2.2.3"
  resolved "https://registry.yarnpkg.com/fill-range/-/fill-range-2.2.3.tgz#50b77dfd7e469bc7492470963699fe7a8485a723"
  dependencies:
    is-number "^2.1.0"
    isobject "^2.0.0"
    randomatic "^1.1.3"
    repeat-element "^1.1.2"
    repeat-string "^1.5.2"

finalhandler@0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/finalhandler/-/finalhandler-0.4.0.tgz#965a52d9e8d05d2b857548541fb89b53a2497d9b"
  dependencies:
    debug "~2.2.0"
    escape-html "1.0.2"
    on-finished "~2.3.0"
    unpipe "~1.0.0"

finalhandler@~1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/finalhandler/-/finalhandler-1.0.3.tgz#ef47e77950e999780e86022a560e3217e0d0cc89"
  dependencies:
    debug "2.6.7"
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.1"
    statuses "~1.3.1"
    unpipe "~1.0.0"

find-cache-dir@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/find-cache-dir/-/find-cache-dir-0.1.1.tgz#c8defae57c8a52a8a784f9e31c57c742e993a0b9"
  dependencies:
    commondir "^1.0.1"
    mkdirp "^0.5.1"
    pkg-dir "^1.0.0"

find-up@^1.0.0:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/find-up/-/find-up-1.1.2.tgz#6b2e9822b1a2ce0a60ab64d610eccad53cb24d0f"
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/find-up/-/find-up-2.1.0.tgz#45d1b7e506c717ddd482775a2b77920a3c0c57a7"
  dependencies:
    locate-path "^2.0.0"

for-in@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/for-in/-/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"

for-own@^0.1.4:
  version "0.1.5"
  resolved "https://registry.yarnpkg.com/for-own/-/for-own-0.1.5.tgz#5265c681a4f294dabbf17c9509b6763aa84510ce"
  dependencies:
    for-in "^1.0.1"

foreach@^2.0.5:
  version "2.0.5"
  resolved "https://registry.yarnpkg.com/foreach/-/foreach-2.0.5.tgz#0bee005018aeb260d0a3af3ae658dd0136ec1b99"

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/forever-agent/-/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"

form-data@~2.1.1:
  version "2.1.4"
  resolved "https://registry.yarnpkg.com/form-data/-/form-data-2.1.4.tgz#33c183acf193276ecaa98143a69e94bfee1750d1"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.5"
    mime-types "^2.1.12"

forwarded@~0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/forwarded/-/forwarded-0.1.0.tgz#19ef9874c4ae1c297bcf078fde63a09b66a84363"

fresh@0.3.0:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/fresh/-/fresh-0.3.0.tgz#651f838e22424e7566de161d8358caa199f83d4f"

fresh@0.5.0:
  version "0.5.0"
  resolved "https://registry.yarnpkg.com/fresh/-/fresh-0.5.0.tgz#f474ca5e6a9246d6fd8e0953cfa9b9c805afa78e"

fs-extra@^0.26.2:
  version "0.26.7"
  resolved "https://registry.yarnpkg.com/fs-extra/-/fs-extra-0.26.7.tgz#9ae1fdd94897798edab76d0918cf42d0c3184fa9"
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^2.1.0"
    klaw "^1.0.0"
    path-is-absolute "^1.0.0"
    rimraf "^2.2.8"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"

fsevents@^1.0.0:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/fsevents/-/fsevents-1.1.2.tgz#3282b713fb3ad80ede0e9fcf4611b5aa6fc033f4"
  dependencies:
    nan "^2.3.0"
    node-pre-gyp "^0.6.36"

fstream-ignore@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/fstream-ignore/-/fstream-ignore-1.0.5.tgz#9c31dae34767018fe1d249b24dada67d092da105"
  dependencies:
    fstream "^1.0.0"
    inherits "2"
    minimatch "^3.0.0"

fstream@^1.0.0, fstream@^1.0.10, fstream@^1.0.2:
  version "1.0.11"
  resolved "https://registry.yarnpkg.com/fstream/-/fstream-1.0.11.tgz#5c1fb1f117477114f0632a0eb4b71b3cb0fd3171"
  dependencies:
    graceful-fs "^4.1.2"
    inherits "~2.0.0"
    mkdirp ">=0.5 0"
    rimraf "2"

function-bind@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/function-bind/-/function-bind-1.1.0.tgz#16176714c801798e4e8f2cf7f7529467bb4a5771"

gauge@~1.2.5:
  version "1.2.7"
  resolved "https://registry.yarnpkg.com/gauge/-/gauge-1.2.7.tgz#e9cec5483d3d4ee0ef44b60a7d99e4935e136d93"
  dependencies:
    ansi "^0.3.0"
    has-unicode "^2.0.0"
    lodash.pad "^4.1.0"
    lodash.padend "^4.1.0"
    lodash.padstart "^4.1.0"

gauge@~2.7.3:
  version "2.7.4"
  resolved "https://registry.yarnpkg.com/gauge/-/gauge-2.7.4.tgz#2c03405c7538c39d7eb37b317022e325fb018bf7"
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wide-align "^1.1.0"

get-caller-file@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/get-caller-file/-/get-caller-file-1.0.2.tgz#f702e63127e7e231c160a80c1554acb70d5047e5"

getpass@^0.1.1:
  version "0.1.7"
  resolved "https://registry.yarnpkg.com/getpass/-/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  dependencies:
    assert-plus "^1.0.0"

glob-base@^0.3.0:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/glob-base/-/glob-base-0.3.0.tgz#dbb164f6221b1c0b1ccf82aea328b497df0ea3c4"
  dependencies:
    glob-parent "^2.0.0"
    is-glob "^2.0.0"

glob-parent@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/glob-parent/-/glob-parent-2.0.0.tgz#81383d72db054fcccf5336daa902f182f6edbb28"
  dependencies:
    is-glob "^2.0.0"

glob@^5.0.15:
  version "5.0.15"
  resolved "https://registry.yarnpkg.com/glob/-/glob-5.0.15.tgz#1bc936b9e02f4a603fcc222ecf7633d30b8b93b1"
  dependencies:
    inflight "^1.0.4"
    inherits "2"
    minimatch "2 || 3"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^7.0.3, glob@^7.0.5, glob@^7.1.1:
  version "7.1.2"
  resolved "https://registry.yarnpkg.com/glob/-/glob-7.1.2.tgz#c19c9df9a028702d678612384a6552404c636d15"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global@^4.3.0:
  version "4.3.2"
  resolved "https://registry.yarnpkg.com/global/-/global-4.3.2.tgz#e76989268a6c74c38908b1305b10fc0e394e9d0f"
  dependencies:
    min-document "^2.19.0"
    process "~0.5.1"

globals@^9.0.0:
  version "9.18.0"
  resolved "https://registry.yarnpkg.com/globals/-/globals-9.18.0.tgz#aa3896b3e69b487f17e31ed2143d69a8e30c2d8a"

glogg@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/glogg/-/glogg-1.0.0.tgz#7fe0f199f57ac906cf512feead8f90ee4a284fc5"
  dependencies:
    sparkles "^1.0.0"

graceful-fs@^4.1.11, graceful-fs@^4.1.2, graceful-fs@^4.1.3, graceful-fs@^4.1.6, graceful-fs@^4.1.9:
  version "4.1.11"
  resolved "https://registry.yarnpkg.com/graceful-fs/-/graceful-fs-4.1.11.tgz#0e8bdfe4d1ddb8854d64e04ea7c00e2a026e5658"

growly@^1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/growly/-/growly-1.3.0.tgz#f10748cbe76af964b7c96c93c6bcc28af120c081"

gulp-util@^3.0.4:
  version "3.0.8"
  resolved "https://registry.yarnpkg.com/gulp-util/-/gulp-util-3.0.8.tgz#0054e1e744502e27c04c187c3ecc505dd54bbb4f"
  dependencies:
    array-differ "^1.0.0"
    array-uniq "^1.0.2"
    beeper "^1.0.0"
    chalk "^1.0.0"
    dateformat "^2.0.0"
    fancy-log "^1.1.0"
    gulplog "^1.0.0"
    has-gulplog "^0.1.0"
    lodash._reescape "^3.0.0"
    lodash._reevaluate "^3.0.0"
    lodash._reinterpolate "^3.0.0"
    lodash.template "^3.0.0"
    minimist "^1.1.0"
    multipipe "^0.1.2"
    object-assign "^3.0.0"
    replace-ext "0.0.1"
    through2 "^2.0.0"
    vinyl "^0.5.0"

gulplog@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/gulplog/-/gulplog-1.0.0.tgz#e28c4d45d05ecbbed818363ce8f9c5926229ffe5"
  dependencies:
    glogg "^1.0.0"

handlebars@^4.0.3:
  version "4.0.10"
  resolved "https://registry.yarnpkg.com/handlebars/-/handlebars-4.0.10.tgz#3d30c718b09a3d96f23ea4cc1f403c4d3ba9ff4f"
  dependencies:
    async "^1.4.0"
    optimist "^0.6.1"
    source-map "^0.4.4"
  optionalDependencies:
    uglify-js "^2.6"

"happypack@github:amireh/happypack#3256a3380dde2e06e3ad2ca3b41e9a81fd4f9673":
  version "3.0.3"
  resolved "https://codeload.github.com/amireh/happypack/tar.gz/3256a3380dde2e06e3ad2ca3b41e9a81fd4f9673"
  dependencies:
    async "1.5.0"
    json-stringify-safe "5.0.1"
    loader-utils "0.2.16"
    mkdirp "0.5.1"
    serialize-error "^2.1.0"

har-schema@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/har-schema/-/har-schema-1.0.5.tgz#d263135f43307c02c602afc8fe95970c0151369e"

har-validator@~4.2.1:
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/har-validator/-/har-validator-4.2.1.tgz#33481d0f1bbff600dd203d75812a6a5fba002e2a"
  dependencies:
    ajv "^4.9.1"
    har-schema "^1.0.5"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/has-ansi/-/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  dependencies:
    ansi-regex "^2.0.0"

has-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/has-flag/-/has-flag-1.0.0.tgz#9d9e793165ce017a00f00418c43f942a7b1d11fa"

has-flag@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/has-flag/-/has-flag-2.0.0.tgz#e8207af1cc7b30d446cc70b734b5e8be18f88d51"

has-gulplog@^0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/has-gulplog/-/has-gulplog-0.1.0.tgz#6414c82913697da51590397dafb12f22967811ce"
  dependencies:
    sparkles "^1.0.0"

has-unicode@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/has-unicode/-/has-unicode-2.0.1.tgz#e0e6fe6a28cf51138855e086d1691e771de2a8b9"

hash-base@^2.0.0:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/hash-base/-/hash-base-2.0.2.tgz#66ea1d856db4e8a5470cadf6fce23ae5244ef2e1"
  dependencies:
    inherits "^2.0.1"

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/hash.js/-/hash.js-1.1.3.tgz#340dedbe6290187151c1ea1d777a3448935df846"
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.0"

hasha@^2.2.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/hasha/-/hasha-2.2.0.tgz#78d7cbfc1e6d66303fe79837365984517b2f6ee1"
  dependencies:
    is-stream "^1.0.1"
    pinkie-promise "^2.0.0"

haul@^1.0.0-beta.1:
  version "1.0.0-beta.2"
  resolved "https://registry.yarnpkg.com/haul/-/haul-1.0.0-beta.2.tgz#d8fe6d66d5f6e2419e145e425003489fef54931b"
  dependencies:
    babel-core "^6.24.0"
    babel-loader "^6.4.1"
    babel-plugin-transform-flow-strip-types "^6.22.0"
    babel-plugin-transform-object-rest-spread "^6.23.0"
    babel-preset-env "^1.2.2"
    babel-preset-react-native "^1.9.1"
    babel-register "^6.24.0"
    camelcase-keys "^4.0.0"
    case-sensitive-paths-webpack-plugin "^2.0.0"
    chalk "1.1.3"
    clear "^0.0.1"
    cliui "^3.2.0"
    decamelize "^1.2.0"
    dedent "^0.7.0"
    dlv "^1.1.0"
    escape-string-regexp "^1.0.5"
    eslint-plugin-prettier "^2.0.1"
    express "^4.15.2"
    happypack "github:amireh/happypack#3256a3380dde2e06e3ad2ca3b41e9a81fd4f9673"
    hasha "^2.2.0"
    image-size "^0.5.1"
    inquirer "^3.0.6"
    loader-utils "^1.1.0"
    minimist "^1.2.0"
    morgan "^1.8.1"
    open-in-editor "^2.2.0"
    opn "^4.0.2"
    ora "^1.2.0"
    progress-bar-webpack-plugin "^1.9.3"
    resolve "^1.3.3"
    source-map "^0.5.6"
    strip-ansi "^3.0.1"
    webpack "^2.3.1"
    webpack-dev-middleware "^1.10.1"
    ws "^2.2.2"

hawk@~3.1.3:
  version "3.1.3"
  resolved "https://registry.yarnpkg.com/hawk/-/hawk-3.1.3.tgz#078444bd7c1640b0fe540d2c9b73d59678e8e1c4"
  dependencies:
    boom "2.x.x"
    cryptiles "2.x.x"
    hoek "2.x.x"
    sntp "1.x.x"

hmac-drbg@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/hmac-drbg/-/hmac-drbg-1.0.1.tgz#d2745701025a6c775a6c545793ed502fc0c649a1"
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

hoek@2.x.x:
  version "2.16.3"
  resolved "https://registry.yarnpkg.com/hoek/-/hoek-2.16.3.tgz#20bb7403d3cea398e91dc4710a8ff1b8274a25ed"

home-or-tmp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/home-or-tmp/-/home-or-tmp-2.0.0.tgz#e36c3f2d2cae7d746a857e38d18d5f32a7882db8"
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.1"

hosted-git-info@^2.1.4:
  version "2.5.0"
  resolved "https://registry.yarnpkg.com/hosted-git-info/-/hosted-git-info-2.5.0.tgz#6d60e34b3abbc8313062c3b798ef8d901a07af3c"

html-encoding-sniffer@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/html-encoding-sniffer/-/html-encoding-sniffer-1.0.1.tgz#79bf7a785ea495fe66165e734153f363ff5437da"
  dependencies:
    whatwg-encoding "^1.0.1"

http-errors@~1.3.1:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/http-errors/-/http-errors-1.3.1.tgz#****************************************"
  dependencies:
    inherits "~2.0.1"
    statuses "1"

http-errors@~1.6.1:
  version "1.6.1"
  resolved "https://registry.yarnpkg.com/http-errors/-/http-errors-1.6.1.tgz#5f8b8ed98aca545656bf572997387f904a722257"
  dependencies:
    depd "1.1.0"
    inherits "2.0.3"
    setprototypeof "1.0.3"
    statuses ">= 1.3.1 < 2"

http-signature@~1.1.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/http-signature/-/http-signature-1.1.1.tgz#df72e267066cd0ac67fb76adf8e134a8fbcf91bf"
  dependencies:
    assert-plus "^0.2.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-browserify@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/https-browserify/-/https-browserify-0.0.1.tgz#3f91365cabe60b77ed0ebba24b454e3e09d95a82"

iconv-lite@0.4.11:
  version "0.4.11"
  resolved "https://registry.yarnpkg.com/iconv-lite/-/iconv-lite-0.4.11.tgz#2ecb42fd294744922209a2e7c404dac8793d8ade"

iconv-lite@0.4.13:
  version "0.4.13"
  resolved "https://registry.yarnpkg.com/iconv-lite/-/iconv-lite-0.4.13.tgz#1f88aba4ab0b1508e8312acc39345f36e992e2f2"

iconv-lite@^0.4.17, iconv-lite@~0.4.13:
  version "0.4.18"
  resolved "https://registry.yarnpkg.com/iconv-lite/-/iconv-lite-0.4.18.tgz#23d8656b16aae6742ac29732ea8f0336a4789cf2"

ieee754@^1.1.4:
  version "1.1.8"
  resolved "https://registry.yarnpkg.com/ieee754/-/ieee754-1.1.8.tgz#be33d40ac10ef1926701f6f08a2d86fbfd1ad3e4"

image-size@^0.3.5:
  version "0.3.5"
  resolved "https://registry.yarnpkg.com/image-size/-/image-size-0.3.5.tgz#83240eab2fb5b00b04aab8c74b0471e9cba7ad8c"

image-size@^0.5.1:
  version "0.5.5"
  resolved "https://registry.yarnpkg.com/image-size/-/image-size-0.5.5.tgz#09dfd4ab9d20e29eb1c3e80b8990378df9e3cb9c"

immutable@~3.7.6:
  version "3.7.6"
  resolved "https://registry.yarnpkg.com/immutable/-/immutable-3.7.6.tgz#13b4d3cb12befa15482a26fe1b2ebae640071e4b"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/imurmurhash/-/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"

indexof@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/indexof/-/indexof-0.0.1.tgz#82dc336d232b9062179d05ab3293a66059fd435d"

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.yarnpkg.com/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.3, inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.0, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"

inherits@2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.1.tgz#b17d08d326b4423e568eff719f91b0b1cbdf69f1"

ini@~1.3.0:
  version "1.3.4"
  resolved "https://registry.yarnpkg.com/ini/-/ini-1.3.4.tgz#0537cb79daf59b59a1a517dff706c86ec039162e"

inquirer@^0.12.0:
  version "0.12.0"
  resolved "https://registry.yarnpkg.com/inquirer/-/inquirer-0.12.0.tgz#1ef2bfd63504df0bc75785fff8c2c41df12f077e"
  dependencies:
    ansi-escapes "^1.1.0"
    ansi-regex "^2.0.0"
    chalk "^1.0.0"
    cli-cursor "^1.0.1"
    cli-width "^2.0.0"
    figures "^1.3.5"
    lodash "^4.3.0"
    readline2 "^1.0.1"
    run-async "^0.1.0"
    rx-lite "^3.1.2"
    string-width "^1.0.1"
    strip-ansi "^3.0.0"
    through "^2.3.6"

inquirer@^3.0.6:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/inquirer/-/inquirer-3.2.0.tgz#45b44c2160c729d7578c54060b3eed94487bb42b"
  dependencies:
    ansi-escapes "^2.0.0"
    chalk "^2.0.0"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^2.0.4"
    figures "^2.0.0"
    lodash "^4.3.0"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rx-lite "^4.0.8"
    rx-lite-aggregates "^4.0.8"
    string-width "^2.1.0"
    strip-ansi "^4.0.0"
    through "^2.3.6"

interpret@^1.0.0:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/interpret/-/interpret-1.0.3.tgz#cbc35c62eeee73f19ab7b10a801511401afc0f90"

invariant@^2.2.0, invariant@^2.2.2:
  version "2.2.2"
  resolved "https://registry.yarnpkg.com/invariant/-/invariant-2.2.2.tgz#9e1f56ac0acdb6bf303306f338be3b204ae60360"
  dependencies:
    loose-envify "^1.0.0"

invert-kv@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/invert-kv/-/invert-kv-1.0.0.tgz#104a8e4aaca6d3d8cd157a8ef8bfab2d7a3ffdb6"

ipaddr.js@1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/ipaddr.js/-/ipaddr.js-1.3.0.tgz#1e03a52fdad83a8bbb2b25cbf4998b4cffcd3dec"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/is-arrayish/-/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/is-binary-path/-/is-binary-path-1.0.1.tgz#75f16642b480f187a711c814161fd3a4a7655898"
  dependencies:
    binary-extensions "^1.0.0"

is-buffer@^1.1.5:
  version "1.1.5"
  resolved "https://registry.yarnpkg.com/is-buffer/-/is-buffer-1.1.5.tgz#1f3b26ef613b214b88cbca23cc6c01d87961eecc"

is-builtin-module@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-builtin-module/-/is-builtin-module-1.0.0.tgz#540572d34f7ac3119f8f76c30cbc1b1e037affbe"
  dependencies:
    builtin-modules "^1.0.0"

is-ci@^1.0.10:
  version "1.0.10"
  resolved "https://registry.yarnpkg.com/is-ci/-/is-ci-1.0.10.tgz#f739336b2632365061a9d48270cd56ae3369318e"
  dependencies:
    ci-info "^1.0.0"

is-dotfile@^1.0.0:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/is-dotfile/-/is-dotfile-1.0.3.tgz#a6a2f32ffd2dfb04f5ca25ecd0f6b83cf798a1e1"

is-equal-shallow@^0.1.3:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/is-equal-shallow/-/is-equal-shallow-0.1.3.tgz#2238098fc221de0bcfa5d9eac4c45d638aa1c534"
  dependencies:
    is-primitive "^2.0.0"

is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/is-extendable/-/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"

is-extglob@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-extglob/-/is-extglob-1.0.0.tgz#ac468177c4943405a092fc8f29760c6ffc6206c0"

is-finite@^1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/is-finite/-/is-finite-1.0.2.tgz#cc6677695602be550ef11e8b4aa6305342b6d0aa"
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"

is-glob@^2.0.0, is-glob@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/is-glob/-/is-glob-2.0.1.tgz#d096f926a3ded5600f3fdfd91198cb0888c2d863"
  dependencies:
    is-extglob "^1.0.0"

is-number@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/is-number/-/is-number-2.1.0.tgz#01fcbbb393463a548f2f466cce16dece49db908f"
  dependencies:
    kind-of "^3.0.2"

is-number@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/is-number/-/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  dependencies:
    kind-of "^3.0.2"

is-posix-bracket@^0.1.0:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/is-posix-bracket/-/is-posix-bracket-0.1.1.tgz#3334dc79774368e92f016e6fbc0a88f5cd6e6bc4"

is-primitive@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/is-primitive/-/is-primitive-2.0.0.tgz#207bab91638499c07b2adf240a41a87210034575"

is-promise@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/is-promise/-/is-promise-2.1.0.tgz#79a2a9ece7f096e80f36d2b2f3bc16c1ff4bf3fa"

is-stream@^1.0.1:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/is-stream/-/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-typedarray/-/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/is-utf8/-/is-utf8-0.2.1.tgz#4b0da1442104d1b336340e80797e865cf39f7d72"

isarray@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/isarray/-/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"

isemail@1.x.x:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/isemail/-/isemail-1.2.0.tgz#be03df8cc3e29de4d2c5df6501263f1fa4595e9a"

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/isexe/-/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"

isobject@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/isobject/-/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  dependencies:
    isarray "1.0.0"

isomorphic-fetch@^2.1.1:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/isomorphic-fetch/-/isomorphic-fetch-2.2.1.tgz#611ae1acf14f5e81f729507472819fe9733558a9"
  dependencies:
    node-fetch "^1.0.1"
    whatwg-fetch ">=0.10.0"

isstream@~0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/isstream/-/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"

istanbul-api@^1.1.1:
  version "1.1.11"
  resolved "https://registry.yarnpkg.com/istanbul-api/-/istanbul-api-1.1.11.tgz#fcc0b461e2b3bda71e305155138238768257d9de"
  dependencies:
    async "^2.1.4"
    fileset "^2.0.2"
    istanbul-lib-coverage "^1.1.1"
    istanbul-lib-hook "^1.0.7"
    istanbul-lib-instrument "^1.7.4"
    istanbul-lib-report "^1.1.1"
    istanbul-lib-source-maps "^1.2.1"
    istanbul-reports "^1.1.1"
    js-yaml "^3.7.0"
    mkdirp "^0.5.1"
    once "^1.4.0"

istanbul-lib-coverage@^1.0.1, istanbul-lib-coverage@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/istanbul-lib-coverage/-/istanbul-lib-coverage-1.1.1.tgz#73bfb998885299415c93d38a3e9adf784a77a9da"

istanbul-lib-hook@^1.0.7:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/istanbul-lib-hook/-/istanbul-lib-hook-1.0.7.tgz#dd6607f03076578fe7d6f2a630cf143b49bacddc"
  dependencies:
    append-transform "^0.4.0"

istanbul-lib-instrument@^1.4.2, istanbul-lib-instrument@^1.7.2, istanbul-lib-instrument@^1.7.4:
  version "1.7.4"
  resolved "https://registry.yarnpkg.com/istanbul-lib-instrument/-/istanbul-lib-instrument-1.7.4.tgz#e9fd920e4767f3d19edc765e2d6b3f5ccbd0eea8"
  dependencies:
    babel-generator "^6.18.0"
    babel-template "^6.16.0"
    babel-traverse "^6.18.0"
    babel-types "^6.18.0"
    babylon "^6.17.4"
    istanbul-lib-coverage "^1.1.1"
    semver "^5.3.0"

istanbul-lib-report@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/istanbul-lib-report/-/istanbul-lib-report-1.1.1.tgz#f0e55f56655ffa34222080b7a0cd4760e1405fc9"
  dependencies:
    istanbul-lib-coverage "^1.1.1"
    mkdirp "^0.5.1"
    path-parse "^1.0.5"
    supports-color "^3.1.2"

istanbul-lib-source-maps@^1.1.0, istanbul-lib-source-maps@^1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.2.1.tgz#a6fe1acba8ce08eebc638e572e294d267008aa0c"
  dependencies:
    debug "^2.6.3"
    istanbul-lib-coverage "^1.1.1"
    mkdirp "^0.5.1"
    rimraf "^2.6.1"
    source-map "^0.5.3"

istanbul-reports@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/istanbul-reports/-/istanbul-reports-1.1.1.tgz#042be5c89e175bc3f86523caab29c014e77fee4e"
  dependencies:
    handlebars "^4.0.3"

jest-changed-files@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/jest-changed-files/-/jest-changed-files-20.0.3.tgz#9394d5cc65c438406149bef1bf4d52b68e03e3f8"

jest-cli@^20.0.4:
  version "20.0.4"
  resolved "https://registry.yarnpkg.com/jest-cli/-/jest-cli-20.0.4.tgz#e532b19d88ae5bc6c417e8b0593a6fe954b1dc93"
  dependencies:
    ansi-escapes "^1.4.0"
    callsites "^2.0.0"
    chalk "^1.1.3"
    graceful-fs "^4.1.11"
    is-ci "^1.0.10"
    istanbul-api "^1.1.1"
    istanbul-lib-coverage "^1.0.1"
    istanbul-lib-instrument "^1.4.2"
    istanbul-lib-source-maps "^1.1.0"
    jest-changed-files "^20.0.3"
    jest-config "^20.0.4"
    jest-docblock "^20.0.3"
    jest-environment-jsdom "^20.0.3"
    jest-haste-map "^20.0.4"
    jest-jasmine2 "^20.0.4"
    jest-message-util "^20.0.3"
    jest-regex-util "^20.0.3"
    jest-resolve-dependencies "^20.0.3"
    jest-runtime "^20.0.4"
    jest-snapshot "^20.0.3"
    jest-util "^20.0.3"
    micromatch "^2.3.11"
    node-notifier "^5.0.2"
    pify "^2.3.0"
    slash "^1.0.0"
    string-length "^1.0.1"
    throat "^3.0.0"
    which "^1.2.12"
    worker-farm "^1.3.1"
    yargs "^7.0.2"

jest-config@^20.0.4:
  version "20.0.4"
  resolved "https://registry.yarnpkg.com/jest-config/-/jest-config-20.0.4.tgz#e37930ab2217c913605eff13e7bd763ec48faeea"
  dependencies:
    chalk "^1.1.3"
    glob "^7.1.1"
    jest-environment-jsdom "^20.0.3"
    jest-environment-node "^20.0.3"
    jest-jasmine2 "^20.0.4"
    jest-matcher-utils "^20.0.3"
    jest-regex-util "^20.0.3"
    jest-resolve "^20.0.4"
    jest-validate "^20.0.3"
    pretty-format "^20.0.3"

jest-diff@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/jest-diff/-/jest-diff-20.0.3.tgz#81f288fd9e675f0fb23c75f1c2b19445fe586617"
  dependencies:
    chalk "^1.1.3"
    diff "^3.2.0"
    jest-matcher-utils "^20.0.3"
    pretty-format "^20.0.3"

jest-docblock@^20.0.1, jest-docblock@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/jest-docblock/-/jest-docblock-20.0.3.tgz#17bea984342cc33d83c50fbe1545ea0efaa44712"

jest-environment-jsdom@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/jest-environment-jsdom/-/jest-environment-jsdom-20.0.3.tgz#048a8ac12ee225f7190417713834bb999787de99"
  dependencies:
    jest-mock "^20.0.3"
    jest-util "^20.0.3"
    jsdom "^9.12.0"

jest-environment-node@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/jest-environment-node/-/jest-environment-node-20.0.3.tgz#d488bc4612af2c246e986e8ae7671a099163d403"
  dependencies:
    jest-mock "^20.0.3"
    jest-util "^20.0.3"

jest-haste-map@18.0.0:
  version "18.0.0"
  resolved "https://registry.yarnpkg.com/jest-haste-map/-/jest-haste-map-18.0.0.tgz#707d3b5ae3bcbda971c39e8b911d20ad8502c748"
  dependencies:
    fb-watchman "^1.9.0"
    graceful-fs "^4.1.6"
    multimatch "^2.1.0"
    sane "~1.4.1"
    worker-farm "^1.3.1"

jest-haste-map@^20.0.4:
  version "20.0.5"
  resolved "https://registry.yarnpkg.com/jest-haste-map/-/jest-haste-map-20.0.5.tgz#abad74efb1a005974a7b6517e11010709cab9112"
  dependencies:
    fb-watchman "^2.0.0"
    graceful-fs "^4.1.11"
    jest-docblock "^20.0.3"
    micromatch "^2.3.11"
    sane "~1.6.0"
    worker-farm "^1.3.1"

jest-jasmine2@^20.0.4:
  version "20.0.4"
  resolved "https://registry.yarnpkg.com/jest-jasmine2/-/jest-jasmine2-20.0.4.tgz#fcc5b1411780d911d042902ef1859e852e60d5e1"
  dependencies:
    chalk "^1.1.3"
    graceful-fs "^4.1.11"
    jest-diff "^20.0.3"
    jest-matcher-utils "^20.0.3"
    jest-matchers "^20.0.3"
    jest-message-util "^20.0.3"
    jest-snapshot "^20.0.3"
    once "^1.4.0"
    p-map "^1.1.1"

jest-matcher-utils@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/jest-matcher-utils/-/jest-matcher-utils-20.0.3.tgz#b3a6b8e37ca577803b0832a98b164f44b7815612"
  dependencies:
    chalk "^1.1.3"
    pretty-format "^20.0.3"

jest-matchers@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/jest-matchers/-/jest-matchers-20.0.3.tgz#ca69db1c32db5a6f707fa5e0401abb55700dfd60"
  dependencies:
    jest-diff "^20.0.3"
    jest-matcher-utils "^20.0.3"
    jest-message-util "^20.0.3"
    jest-regex-util "^20.0.3"

jest-message-util@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/jest-message-util/-/jest-message-util-20.0.3.tgz#6aec2844306fcb0e6e74d5796c1006d96fdd831c"
  dependencies:
    chalk "^1.1.3"
    micromatch "^2.3.11"
    slash "^1.0.0"

jest-mock@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/jest-mock/-/jest-mock-20.0.3.tgz#8bc070e90414aa155c11a8d64c869a0d5c71da59"

jest-regex-util@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/jest-regex-util/-/jest-regex-util-20.0.3.tgz#85bbab5d133e44625b19faf8c6aa5122d085d762"

jest-resolve-dependencies@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/jest-resolve-dependencies/-/jest-resolve-dependencies-20.0.3.tgz#6e14a7b717af0f2cb3667c549de40af017b1723a"
  dependencies:
    jest-regex-util "^20.0.3"

jest-resolve@^20.0.4:
  version "20.0.4"
  resolved "https://registry.yarnpkg.com/jest-resolve/-/jest-resolve-20.0.4.tgz#9448b3e8b6bafc15479444c6499045b7ffe597a5"
  dependencies:
    browser-resolve "^1.11.2"
    is-builtin-module "^1.0.0"
    resolve "^1.3.2"

jest-runtime@^20.0.4:
  version "20.0.4"
  resolved "https://registry.yarnpkg.com/jest-runtime/-/jest-runtime-20.0.4.tgz#a2c802219c4203f754df1404e490186169d124d8"
  dependencies:
    babel-core "^6.0.0"
    babel-jest "^20.0.3"
    babel-plugin-istanbul "^4.0.0"
    chalk "^1.1.3"
    convert-source-map "^1.4.0"
    graceful-fs "^4.1.11"
    jest-config "^20.0.4"
    jest-haste-map "^20.0.4"
    jest-regex-util "^20.0.3"
    jest-resolve "^20.0.4"
    jest-util "^20.0.3"
    json-stable-stringify "^1.0.1"
    micromatch "^2.3.11"
    strip-bom "3.0.0"
    yargs "^7.0.2"

jest-snapshot@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/jest-snapshot/-/jest-snapshot-20.0.3.tgz#5b847e1adb1a4d90852a7f9f125086e187c76566"
  dependencies:
    chalk "^1.1.3"
    jest-diff "^20.0.3"
    jest-matcher-utils "^20.0.3"
    jest-util "^20.0.3"
    natural-compare "^1.4.0"
    pretty-format "^20.0.3"

jest-util@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/jest-util/-/jest-util-20.0.3.tgz#0c07f7d80d82f4e5a67c6f8b9c3fe7f65cfd32ad"
  dependencies:
    chalk "^1.1.3"
    graceful-fs "^4.1.11"
    jest-message-util "^20.0.3"
    jest-mock "^20.0.3"
    jest-validate "^20.0.3"
    leven "^2.1.0"
    mkdirp "^0.5.1"

jest-validate@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/jest-validate/-/jest-validate-20.0.3.tgz#d0cfd1de4f579f298484925c280f8f1d94ec3cab"
  dependencies:
    chalk "^1.1.3"
    jest-matcher-utils "^20.0.3"
    leven "^2.1.0"
    pretty-format "^20.0.3"

jest@20.0.4:
  version "20.0.4"
  resolved "https://registry.yarnpkg.com/jest/-/jest-20.0.4.tgz#3dd260c2989d6dad678b1e9cc4d91944f6d602ac"
  dependencies:
    jest-cli "^20.0.4"

joi@^6.6.1:
  version "6.10.1"
  resolved "https://registry.yarnpkg.com/joi/-/joi-6.10.1.tgz#4d50c318079122000fe5f16af1ff8e1917b77e06"
  dependencies:
    hoek "2.x.x"
    isemail "1.x.x"
    moment "2.x.x"
    topo "1.x.x"

js-tokens@^3.0.0:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/js-tokens/-/js-tokens-3.0.2.tgz#9866df395102130e38f7f996bceb65443209c25b"

js-yaml@^3.7.0:
  version "3.9.0"
  resolved "https://registry.yarnpkg.com/js-yaml/-/js-yaml-3.9.0.tgz#4ffbbf25c2ac963b8299dc74da7e3740de1c18ce"
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/jsbn/-/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"

jschardet@^1.4.2:
  version "1.5.0"
  resolved "https://registry.yarnpkg.com/jschardet/-/jschardet-1.5.0.tgz#a61f310306a5a71188e1b1acd08add3cfbb08b1e"

jsdom@^9.12.0:
  version "9.12.0"
  resolved "https://registry.yarnpkg.com/jsdom/-/jsdom-9.12.0.tgz#e8c546fffcb06c00d4833ca84410fed7f8a097d4"
  dependencies:
    abab "^1.0.3"
    acorn "^4.0.4"
    acorn-globals "^3.1.0"
    array-equal "^1.0.0"
    content-type-parser "^1.0.1"
    cssom ">= 0.3.2 < 0.4.0"
    cssstyle ">= 0.2.37 < 0.3.0"
    escodegen "^1.6.1"
    html-encoding-sniffer "^1.0.1"
    nwmatcher ">= 1.3.9 < 2.0.0"
    parse5 "^1.5.1"
    request "^2.79.0"
    sax "^1.2.1"
    symbol-tree "^3.2.1"
    tough-cookie "^2.3.2"
    webidl-conversions "^4.0.0"
    whatwg-encoding "^1.0.1"
    whatwg-url "^4.3.0"
    xml-name-validator "^2.0.1"

jsesc@^1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/jsesc/-/jsesc-1.3.0.tgz#46c3fec8c1892b12b0833db9bc7622176dbab34b"

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://registry.yarnpkg.com/jsesc/-/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"

json-loader@^0.5.4:
  version "0.5.4"
  resolved "https://registry.yarnpkg.com/json-loader/-/json-loader-0.5.4.tgz#8baa1365a632f58a3c46d20175fc6002c96e37de"

json-schema@0.2.3:
  version "0.2.3"
  resolved "https://registry.yarnpkg.com/json-schema/-/json-schema-0.2.3.tgz#b480c892e59a2f05954ce727bd3f2a4e882f9e13"

json-stable-stringify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/json-stable-stringify/-/json-stable-stringify-1.0.1.tgz#9a759d39c5f2ff503fd5300646ed445f88c4f9af"
  dependencies:
    jsonify "~0.0.0"

json-stringify-safe@5.0.1, json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "https://registry.yarnpkg.com/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"

json5@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/json5/-/json5-0.4.0.tgz#054352e4c4c80c86c0923877d449de176a732c8d"

json5@^0.5.0, json5@^0.5.1:
  version "0.5.1"
  resolved "https://registry.yarnpkg.com/json5/-/json5-0.5.1.tgz#1eade7acc012034ad84e2396767ead9fa5495821"

jsonfile@^2.1.0:
  version "2.4.0"
  resolved "https://registry.yarnpkg.com/jsonfile/-/jsonfile-2.4.0.tgz#3736a2b428b87bbda0cc83b53fa3d633a35c2ae8"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonify@~0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/jsonify/-/jsonify-0.0.0.tgz#2c74b6ee41d93ca51b7b5aaee8f503631d252a73"

jsprim@^1.2.2:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/jsprim/-/jsprim-1.4.0.tgz#a3b87e40298d8c380552d8cc7628a0bb95a22918"
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.0.2"
    json-schema "0.2.3"
    verror "1.3.6"

kind-of@^3.0.2:
  version "3.2.2"
  resolved "https://registry.yarnpkg.com/kind-of/-/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/kind-of/-/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  dependencies:
    is-buffer "^1.1.5"

klaw@^1.0.0:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/klaw/-/klaw-1.3.1.tgz#4088433b46b3b1ba259d78785d8e96f73ba02439"
  optionalDependencies:
    graceful-fs "^4.1.9"

lazy-cache@^1.0.3:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/lazy-cache/-/lazy-cache-1.0.4.tgz#a1d78fc3a50474cb80845d3b3b6e1da49a446e8e"

lcid@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/lcid/-/lcid-1.0.0.tgz#308accafa0bc483a3867b4b6f2b9506251d1b835"
  dependencies:
    invert-kv "^1.0.0"

left-pad@^1.1.3:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/left-pad/-/left-pad-1.1.3.tgz#612f61c033f3a9e08e939f1caebeea41b6f3199a"

leven@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/leven/-/leven-2.1.0.tgz#c2e7a9f772094dee9d34202ae8acce4687875580"

levn@~0.3.0:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/levn/-/levn-0.3.0.tgz#3b09924edf9f083c0490fdd4c0bc4421e04764ee"
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

load-json-file@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/load-json-file/-/load-json-file-1.1.0.tgz#956905708d58b4bab4c2261b04f59f31c99374c0"
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    strip-bom "^2.0.0"

loader-runner@^2.3.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/loader-runner/-/loader-runner-2.3.0.tgz#f482aea82d543e07921700d5a46ef26fdac6b8a2"

loader-utils@0.2.16:
  version "0.2.16"
  resolved "https://registry.yarnpkg.com/loader-utils/-/loader-utils-0.2.16.tgz#f08632066ed8282835dff88dfb52704765adee6d"
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

loader-utils@^0.2.16:
  version "0.2.17"
  resolved "https://registry.yarnpkg.com/loader-utils/-/loader-utils-0.2.17.tgz#f86e6374d43205a6e6c60e9196f17c0299bfb348"
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

loader-utils@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/loader-utils/-/loader-utils-1.1.0.tgz#c98aef488bcceda2ffb5e2de646d6a754429f5cd"
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/locate-path/-/locate-path-2.0.0.tgz#2b568b265eec944c6d9c0de9c3dbbbca0354cd8e"
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

lodash._basecopy@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/lodash._basecopy/-/lodash._basecopy-3.0.1.tgz#8da0e6a876cf344c0ad8a54882111dd3c5c7ca36"

lodash._basetostring@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/lodash._basetostring/-/lodash._basetostring-3.0.1.tgz#d1861d877f824a52f669832dcaf3ee15566a07d5"

lodash._basevalues@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/lodash._basevalues/-/lodash._basevalues-3.0.0.tgz#5b775762802bde3d3297503e26300820fdf661b7"

lodash._getnative@^3.0.0:
  version "3.9.1"
  resolved "https://registry.yarnpkg.com/lodash._getnative/-/lodash._getnative-3.9.1.tgz#570bc7dede46d61cdcde687d65d3eecbaa3aaff5"

lodash._isiterateecall@^3.0.0:
  version "3.0.9"
  resolved "https://registry.yarnpkg.com/lodash._isiterateecall/-/lodash._isiterateecall-3.0.9.tgz#5203ad7ba425fae842460e696db9cf3e6aac057c"

lodash._reescape@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/lodash._reescape/-/lodash._reescape-3.0.0.tgz#2b1d6f5dfe07c8a355753e5f27fac7f1cde1616a"

lodash._reevaluate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/lodash._reevaluate/-/lodash._reevaluate-3.0.0.tgz#58bc74c40664953ae0b124d806996daca431e2ed"

lodash._reinterpolate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/lodash._reinterpolate/-/lodash._reinterpolate-3.0.0.tgz#0ccf2d89166af03b3663c796538b75ac6e114d9d"

lodash._root@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/lodash._root/-/lodash._root-3.0.1.tgz#fba1c4524c19ee9a5f8136b4609f017cf4ded692"

lodash.escape@^3.0.0:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/lodash.escape/-/lodash.escape-3.2.0.tgz#995ee0dc18c1b48cc92effae71a10aab5b487698"
  dependencies:
    lodash._root "^3.0.0"

lodash.isarguments@^3.0.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/lodash.isarguments/-/lodash.isarguments-3.1.0.tgz#2f573d85c6a24289ff00663b491c1d338ff3458a"

lodash.isarray@^3.0.0:
  version "3.0.4"
  resolved "https://registry.yarnpkg.com/lodash.isarray/-/lodash.isarray-3.0.4.tgz#79e4eb88c36a8122af86f844aa9bcd851b5fbb55"

lodash.keys@^3.0.0:
  version "3.1.2"
  resolved "https://registry.yarnpkg.com/lodash.keys/-/lodash.keys-3.1.2.tgz#4dbc0472b156be50a0b286855d1bd0b0c656098a"
  dependencies:
    lodash._getnative "^3.0.0"
    lodash.isarguments "^3.0.0"
    lodash.isarray "^3.0.0"

lodash.pad@^4.1.0:
  version "4.5.1"
  resolved "https://registry.yarnpkg.com/lodash.pad/-/lodash.pad-4.5.1.tgz#4330949a833a7c8da22cc20f6a26c4d59debba70"

lodash.padend@^4.1.0:
  version "4.6.1"
  resolved "https://registry.yarnpkg.com/lodash.padend/-/lodash.padend-4.6.1.tgz#53ccba047d06e158d311f45da625f4e49e6f166e"

lodash.padstart@^4.1.0:
  version "4.6.1"
  resolved "https://registry.yarnpkg.com/lodash.padstart/-/lodash.padstart-4.6.1.tgz#d2e3eebff0d9d39ad50f5cbd1b52a7bce6bb611b"

lodash.restparam@^3.0.0:
  version "3.6.1"
  resolved "https://registry.yarnpkg.com/lodash.restparam/-/lodash.restparam-3.6.1.tgz#936a4e309ef330a7645ed4145986c85ae5b20805"

lodash.template@^3.0.0:
  version "3.6.2"
  resolved "https://registry.yarnpkg.com/lodash.template/-/lodash.template-3.6.2.tgz#f8cdecc6169a255be9098ae8b0c53d378931d14f"
  dependencies:
    lodash._basecopy "^3.0.0"
    lodash._basetostring "^3.0.0"
    lodash._basevalues "^3.0.0"
    lodash._isiterateecall "^3.0.0"
    lodash._reinterpolate "^3.0.0"
    lodash.escape "^3.0.0"
    lodash.keys "^3.0.0"
    lodash.restparam "^3.0.0"
    lodash.templatesettings "^3.0.0"

lodash.templatesettings@^3.0.0:
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/lodash.templatesettings/-/lodash.templatesettings-3.1.1.tgz#fb307844753b66b9f1afa54e262c745307dba8e5"
  dependencies:
    lodash._reinterpolate "^3.0.0"
    lodash.escape "^3.0.0"

lodash@^3.5.0:
  version "3.10.1"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-3.10.1.tgz#5bf45e8e49ba4189e17d482789dfd15bd140b7b6"

lodash@^4.14.0, lodash@^4.16.6, lodash@^4.2.0, lodash@^4.3.0, lodash@^4.6.1:
  version "4.17.4"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-4.17.4.tgz#78203a4d1c328ae1d86dca6460e369b57f4055ae"

log-symbols@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/log-symbols/-/log-symbols-1.0.2.tgz#376ff7b58ea3086a0f09facc74617eca501e1a18"
  dependencies:
    chalk "^1.0.0"

longest@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/longest/-/longest-1.0.1.tgz#30a0b2da38f73770e8294a0d22e6625ed77d0097"

loose-envify@^1.0.0, loose-envify@^1.1.0:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/loose-envify/-/loose-envify-1.3.1.tgz#d1a8ad33fa9ce0e713d65fdd0ac8b748d478c848"
  dependencies:
    js-tokens "^3.0.0"

lru-cache@^4.0.1:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/lru-cache/-/lru-cache-4.1.1.tgz#622e32e82488b49279114a4f9ecf45e7cd6bba55"
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

makeerror@1.0.x:
  version "1.0.11"
  resolved "https://registry.yarnpkg.com/makeerror/-/makeerror-1.0.11.tgz#e01a5c9109f2af79660e4e8b9587790184f5a96c"
  dependencies:
    tmpl "1.0.x"

map-obj@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/map-obj/-/map-obj-2.0.0.tgz#a65cd29087a92598b8791257a523e021222ac1f9"

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/media-typer/-/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"

memory-fs@^0.4.0, memory-fs@~0.4.1:
  version "0.4.1"
  resolved "https://registry.yarnpkg.com/memory-fs/-/memory-fs-0.4.1.tgz#3a9a20b8462523e447cfbc7e8bb80ed667bfc552"
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/merge-descriptors/-/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"

merge@^1.1.3:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/merge/-/merge-1.2.0.tgz#7531e39d4949c281a66b8c5a6e0265e8b05894da"

method-override@~2.3.5:
  version "2.3.9"
  resolved "https://registry.yarnpkg.com/method-override/-/method-override-2.3.9.tgz#bd151f2ce34cf01a76ca400ab95c012b102d8f71"
  dependencies:
    debug "2.6.8"
    methods "~1.1.2"
    parseurl "~1.3.1"
    vary "~1.1.1"

methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/methods/-/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"

micromatch@^2.1.5, micromatch@^2.3.11:
  version "2.3.11"
  resolved "https://registry.yarnpkg.com/micromatch/-/micromatch-2.3.11.tgz#86677c97d1720b363431d04d0d15293bd38c1565"
  dependencies:
    arr-diff "^2.0.0"
    array-unique "^0.2.1"
    braces "^1.8.2"
    expand-brackets "^0.1.4"
    extglob "^0.3.1"
    filename-regex "^2.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.1"
    kind-of "^3.0.2"
    normalize-path "^2.0.1"
    object.omit "^2.0.0"
    parse-glob "^3.0.4"
    regex-cache "^0.4.2"

miller-rabin@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/miller-rabin/-/miller-rabin-4.0.0.tgz#4a62fb1d42933c05583982f4c716f6fb9e6c6d3d"
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

"mime-db@>= 1.27.0 < 2", mime-db@~1.27.0:
  version "1.27.0"
  resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.27.0.tgz#820f572296bbd20ec25ed55e5b5de869e5436eb1"

mime-db@~1.23.0:
  version "1.23.0"
  resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.23.0.tgz#a31b4070adaea27d732ea333740a64d0ec9a6659"

mime-types@2.1.11, mime-types@~2.1.7, mime-types@~2.1.9:
  version "2.1.11"
  resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.11.tgz#c259c471bda808a85d6cd193b430a5fae4473b3c"
  dependencies:
    mime-db "~1.23.0"

mime-types@^2.1.12, mime-types@~2.1.11, mime-types@~2.1.15, mime-types@~2.1.6:
  version "2.1.15"
  resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.15.tgz#a4ebf5064094569237b8cf70046776d09fc92aed"
  dependencies:
    mime-db "~1.27.0"

mime@1.3.4:
  version "1.3.4"
  resolved "https://registry.yarnpkg.com/mime/-/mime-1.3.4.tgz#115f9e3b6b3daf2959983cb38f149a2d40eb5d53"

mime@^1.3.4:
  version "1.3.6"
  resolved "https://registry.yarnpkg.com/mime/-/mime-1.3.6.tgz#591d84d3653a6b0b4a3b9df8de5aa8108e72e5e0"

mimic-fn@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/mimic-fn/-/mimic-fn-1.1.0.tgz#e667783d92e89dbd342818b5230b9d62a672ad18"

min-document@^2.19.0:
  version "2.19.0"
  resolved "https://registry.yarnpkg.com/min-document/-/min-document-2.19.0.tgz#7bd282e3f5842ed295bb748cdd9f1ffa2c824685"
  dependencies:
    dom-walk "^0.1.0"

minimalistic-assert@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/minimalistic-assert/-/minimalistic-assert-1.0.0.tgz#702be2dda6b37f4836bcb3f5db56641b64a1d3d3"

minimalistic-crypto-utils@^1.0.0, minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz#f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a"

"minimatch@2 || 3", minimatch@^3.0.0, minimatch@^3.0.2, minimatch@^3.0.3, minimatch@^3.0.4:
  version "3.0.4"
  resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  dependencies:
    brace-expansion "^1.1.7"

minimist@0.0.8, minimist@~0.0.1:
  version "0.0.8"
  resolved "https://registry.yarnpkg.com/minimist/-/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"

minimist@^1.1.0, minimist@^1.1.1, minimist@^1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/minimist/-/minimist-1.2.0.tgz#a35008b20f41383eec1fb914f4cd5df79a264284"

mkdirp@0.5.1, "mkdirp@>=0.5 0", mkdirp@^0.5.1, mkdirp@~0.5.0:
  version "0.5.1"
  resolved "https://registry.yarnpkg.com/mkdirp/-/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
  dependencies:
    minimist "0.0.8"

moment@2.x.x:
  version "2.18.1"
  resolved "https://registry.yarnpkg.com/moment/-/moment-2.18.1.tgz#c36193dd3ce1c2eed2adb7c802dbbc77a81b1c0f"

morgan@^1.8.1:
  version "1.8.2"
  resolved "https://registry.yarnpkg.com/morgan/-/morgan-1.8.2.tgz#784ac7734e4a453a9c6e6e8680a9329275c8b687"
  dependencies:
    basic-auth "~1.1.0"
    debug "2.6.8"
    depd "~1.1.0"
    on-finished "~2.3.0"
    on-headers "~1.0.1"

morgan@~1.6.1:
  version "1.6.1"
  resolved "https://registry.yarnpkg.com/morgan/-/morgan-1.6.1.tgz#5fd818398c6819cba28a7cd6664f292fe1c0bbf2"
  dependencies:
    basic-auth "~1.0.3"
    debug "~2.2.0"
    depd "~1.0.1"
    on-finished "~2.3.0"
    on-headers "~1.0.0"

ms@0.7.1:
  version "0.7.1"
  resolved "https://registry.yarnpkg.com/ms/-/ms-0.7.1.tgz#9cd13c03adbff25b65effde7ce864ee952017098"

ms@0.7.2:
  version "0.7.2"
  resolved "https://registry.yarnpkg.com/ms/-/ms-0.7.2.tgz#ae25cf2512b3885a1d95d7f037868d8431124765"

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/ms/-/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"

multimatch@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/multimatch/-/multimatch-2.1.0.tgz#9c7906a22fb4c02919e2f5f75161b4cdbd4b2a2b"
  dependencies:
    array-differ "^1.0.0"
    array-union "^1.0.1"
    arrify "^1.0.0"
    minimatch "^3.0.0"

multiparty@3.3.2:
  version "3.3.2"
  resolved "https://registry.yarnpkg.com/multiparty/-/multiparty-3.3.2.tgz#35de6804dc19643e5249f3d3e3bdc6c8ce301d3f"
  dependencies:
    readable-stream "~1.1.9"
    stream-counter "~0.2.0"

multipipe@^0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/multipipe/-/multipipe-0.1.2.tgz#2a8f2ddf70eed564dff2d57f1e1a137d9f05078b"
  dependencies:
    duplexer2 "0.0.2"

mute-stream@0.0.5:
  version "0.0.5"
  resolved "https://registry.yarnpkg.com/mute-stream/-/mute-stream-0.0.5.tgz#8fbfabb0a98a253d3184331f9e8deb7372fac6c0"

mute-stream@0.0.7:
  version "0.0.7"
  resolved "https://registry.yarnpkg.com/mute-stream/-/mute-stream-0.0.7.tgz#3075ce93bc21b8fab43e1bc4da7e8115ed1e7bab"

nan@^2.3.0:
  version "2.6.2"
  resolved "https://registry.yarnpkg.com/nan/-/nan-2.6.2.tgz#e4ff34e6c95fdfb5aecc08de6596f43605a7db45"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/natural-compare/-/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"

negotiator@0.5.3:
  version "0.5.3"
  resolved "https://registry.yarnpkg.com/negotiator/-/negotiator-0.5.3.tgz#269d5c476810ec92edbe7b6c2f28316384f9a7e8"

negotiator@0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/negotiator/-/negotiator-0.6.1.tgz#2b327184e8992101177b28563fb5e7102acd0ca9"

node-fetch@^1.0.1, node-fetch@^1.3.3:
  version "1.7.1"
  resolved "https://registry.yarnpkg.com/node-fetch/-/node-fetch-1.7.1.tgz#899cb3d0a3c92f952c47f1b876f4c8aeabd400d5"
  dependencies:
    encoding "^0.1.11"
    is-stream "^1.0.1"

node-int64@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/node-int64/-/node-int64-0.4.0.tgz#87a9065cdb355d3182d8f94ce11188b825c68a3b"

node-libs-browser@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/node-libs-browser/-/node-libs-browser-2.0.0.tgz#a3a59ec97024985b46e958379646f96c4b616646"
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.1.4"
    buffer "^4.3.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "^3.11.0"
    domain-browser "^1.1.1"
    events "^1.0.0"
    https-browserify "0.0.1"
    os-browserify "^0.2.0"
    path-browserify "0.0.0"
    process "^0.11.0"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.0.5"
    stream-browserify "^2.0.1"
    stream-http "^2.3.1"
    string_decoder "^0.10.25"
    timers-browserify "^2.0.2"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.10.3"
    vm-browserify "0.0.4"

node-notifier@^5.0.2:
  version "5.1.2"
  resolved "https://registry.yarnpkg.com/node-notifier/-/node-notifier-5.1.2.tgz#2fa9e12605fa10009d44549d6fcd8a63dde0e4ff"
  dependencies:
    growly "^1.3.0"
    semver "^5.3.0"
    shellwords "^0.1.0"
    which "^1.2.12"

node-pre-gyp@^0.6.36:
  version "0.6.36"
  resolved "https://registry.yarnpkg.com/node-pre-gyp/-/node-pre-gyp-0.6.36.tgz#db604112cb74e0d477554e9b505b17abddfab786"
  dependencies:
    mkdirp "^0.5.1"
    nopt "^4.0.1"
    npmlog "^4.0.2"
    rc "^1.1.7"
    request "^2.81.0"
    rimraf "^2.6.1"
    semver "^5.3.0"
    tar "^2.2.1"
    tar-pack "^3.4.0"

node-uuid@1.4.7:
  version "1.4.7"
  resolved "https://registry.yarnpkg.com/node-uuid/-/node-uuid-1.4.7.tgz#6da5a17668c4b3dd59623bda11cf7fa4c1f60a6f"

nopt@^4.0.1:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/nopt/-/nopt-4.0.1.tgz#d0d4685afd5415193c8c7505602d0d17cd64474d"
  dependencies:
    abbrev "1"
    osenv "^0.1.4"

normalize-package-data@^2.3.2:
  version "2.4.0"
  resolved "https://registry.yarnpkg.com/normalize-package-data/-/normalize-package-data-2.4.0.tgz#12f95a307d58352075a04907b84ac8be98ac012f"
  dependencies:
    hosted-git-info "^2.1.4"
    is-builtin-module "^1.0.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.0.1:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/normalize-path/-/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
  dependencies:
    remove-trailing-separator "^1.0.1"

npmlog@^2.0.4:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/npmlog/-/npmlog-2.0.4.tgz#98b52530f2514ca90d09ec5b22c8846722375692"
  dependencies:
    ansi "~0.3.1"
    are-we-there-yet "~1.1.2"
    gauge "~1.2.5"

npmlog@^4.0.2:
  version "4.1.2"
  resolved "https://registry.yarnpkg.com/npmlog/-/npmlog-4.1.2.tgz#08a7f2a8bf734604779a9efa4ad5cc717abb954b"
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.7.3"
    set-blocking "~2.0.0"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/number-is-nan/-/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"

"nwmatcher@>= 1.3.9 < 2.0.0":
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/nwmatcher/-/nwmatcher-1.4.1.tgz#7ae9b07b0ea804db7e25f05cb5fe4097d4e4949f"

oauth-sign@~0.8.1:
  version "0.8.2"
  resolved "https://registry.yarnpkg.com/oauth-sign/-/oauth-sign-0.8.2.tgz#46a6ab7f0aead8deae9ec0565780b7d4efeb9d43"

object-assign@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/object-assign/-/object-assign-3.0.0.tgz#9bedd5ca0897949bca47e7ff408062d549f587f2"

object-assign@^4.0.1, object-assign@^4.1.0:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"

object-keys@^1.0.10, object-keys@^1.0.8:
  version "1.0.11"
  resolved "https://registry.yarnpkg.com/object-keys/-/object-keys-1.0.11.tgz#c54601778ad560f1142ce0e01bcca8b56d13426d"

object.assign@^4.0.1:
  version "4.0.4"
  resolved "https://registry.yarnpkg.com/object.assign/-/object.assign-4.0.4.tgz#b1c9cc044ef1b9fe63606fc141abbb32e14730cc"
  dependencies:
    define-properties "^1.1.2"
    function-bind "^1.1.0"
    object-keys "^1.0.10"

object.omit@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/object.omit/-/object.omit-2.0.1.tgz#1a9c744829f39dbb858c76ca3579ae2a54ebd1fa"
  dependencies:
    for-own "^0.1.4"
    is-extendable "^0.1.1"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/on-finished/-/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.0, on-headers@~1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/on-headers/-/on-headers-1.0.1.tgz#928f5d0f470d49342651ea6794b0857c100693f7"

once@^1.3.0, once@^1.3.3, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  dependencies:
    wrappy "1"

onetime@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/onetime/-/onetime-1.1.0.tgz#a1f7838f8314c516f05ecefcbc4ccfe04b4ed789"

onetime@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/onetime/-/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  dependencies:
    mimic-fn "^1.0.0"

open-in-editor@^2.2.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/open-in-editor/-/open-in-editor-2.2.0.tgz#c5b21aa76f6acd4cbbd3c3b2e77dccb4b75a2020"
  dependencies:
    clap "^1.1.3"
    os-homedir "~1.0.2"

opn@^3.0.2:
  version "3.0.3"
  resolved "https://registry.yarnpkg.com/opn/-/opn-3.0.3.tgz#b6d99e7399f78d65c3baaffef1fb288e9b85243a"
  dependencies:
    object-assign "^4.0.1"

opn@^4.0.2:
  version "4.0.2"
  resolved "https://registry.yarnpkg.com/opn/-/opn-4.0.2.tgz#7abc22e644dff63b0a96d5ab7f2790c0f01abc95"
  dependencies:
    object-assign "^4.0.1"
    pinkie-promise "^2.0.0"

optimist@^0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/optimist/-/optimist-0.6.1.tgz#da3ea74686fa21a19a111c326e90eb15a0196686"
  dependencies:
    minimist "~0.0.1"
    wordwrap "~0.0.2"

optionator@^0.8.1:
  version "0.8.2"
  resolved "https://registry.yarnpkg.com/optionator/-/optionator-0.8.2.tgz#364c5e409d3f4d6301d6c0b4c05bba50180aeb64"
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.4"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    wordwrap "~1.0.0"

options@>=0.0.5:
  version "0.0.6"
  resolved "https://registry.yarnpkg.com/options/-/options-0.0.6.tgz#ec22d312806bb53e731773e7cdaefcf1c643128f"

ora@^1.2.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/ora/-/ora-1.3.0.tgz#80078dd2b92a934af66a3ad72a5b910694ede51a"
  dependencies:
    chalk "^1.1.1"
    cli-cursor "^2.1.0"
    cli-spinners "^1.0.0"
    log-symbols "^1.0.2"

os-browserify@^0.2.0:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/os-browserify/-/os-browserify-0.2.1.tgz#63fc4ccee5d2d7763d26bbf8601078e6c2e0044f"

os-homedir@^1.0.0, os-homedir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/os-homedir/-/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"

os-locale@^1.4.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/os-locale/-/os-locale-1.4.0.tgz#20f9f17ae29ed345e8bde583b13d2009803c14d9"
  dependencies:
    lcid "^1.0.0"

os-tmpdir@^1.0.0, os-tmpdir@^1.0.1, os-tmpdir@~1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/os-tmpdir/-/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"

osenv@^0.1.4:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/osenv/-/osenv-0.1.4.tgz#42fe6d5953df06c8064be6f176c3d05aaaa34644"
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.0"

p-limit@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/p-limit/-/p-limit-1.1.0.tgz#b07ff2d9a5d88bec806035895a2bab66a27988bc"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/p-locate/-/p-locate-2.0.0.tgz#20a0103b222a70c8fd39cc2e580680f3dde5ec43"
  dependencies:
    p-limit "^1.1.0"

p-map@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/p-map/-/p-map-1.1.1.tgz#05f5e4ae97a068371bc2a5cc86bfbdbc19c4ae7a"

pako@~0.2.0:
  version "0.2.9"
  resolved "https://registry.yarnpkg.com/pako/-/pako-0.2.9.tgz#f3f7522f4ef782348da8161bad9ecfd51bf83a75"

parse-asn1@^5.0.0:
  version "5.1.0"
  resolved "https://registry.yarnpkg.com/parse-asn1/-/parse-asn1-5.1.0.tgz#37c4f9b7ed3ab65c74817b5f2480937fbf97c712"
  dependencies:
    asn1.js "^4.0.0"
    browserify-aes "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.0"
    pbkdf2 "^3.0.3"

parse-glob@^3.0.4:
  version "3.0.4"
  resolved "https://registry.yarnpkg.com/parse-glob/-/parse-glob-3.0.4.tgz#b2c376cfb11f35513badd173ef0bb6e3a388391c"
  dependencies:
    glob-base "^0.3.0"
    is-dotfile "^1.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.0"

parse-json@^2.2.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/parse-json/-/parse-json-2.2.0.tgz#f480f40434ef80741f8469099f8dea18f55a4dc9"
  dependencies:
    error-ex "^1.2.0"

parse5@^1.5.1:
  version "1.5.1"
  resolved "https://registry.yarnpkg.com/parse5/-/parse5-1.5.1.tgz#9b7f3b0de32be78dc2401b17573ccaf0f6f59d94"

parseurl@~1.3.0, parseurl@~1.3.1:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/parseurl/-/parseurl-1.3.1.tgz#c8ab8c9223ba34888aa64a297b28853bec18da56"

path-browserify@0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/path-browserify/-/path-browserify-0.0.0.tgz#a0b870729aae214005b7d5032ec2cbbb0fb4451a"

path-exists@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/path-exists/-/path-exists-2.1.0.tgz#0feb6c64f0fc518d9a754dd5efb62c7022761f4b"
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/path-exists/-/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"

path-parse@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/path-parse/-/path-parse-1.0.5.tgz#3c1adf871ea9cd6c9431b6ea2bd74a0ff055c4c1"

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.yarnpkg.com/path-to-regexp/-/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"

path-type@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/path-type/-/path-type-1.1.0.tgz#59c44f7ee491da704da415da5a4070ba4f8fe441"
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

pause@0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/pause/-/pause-0.1.0.tgz#ebc8a4a8619ff0b8a81ac1513c3434ff469fdb74"

pbkdf2@^3.0.3:
  version "3.0.12"
  resolved "https://registry.yarnpkg.com/pbkdf2/-/pbkdf2-3.0.12.tgz#be36785c5067ea48d806ff923288c5f750b6b8a2"
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

pegjs@0.9.0:
  version "0.9.0"
  resolved "https://registry.yarnpkg.com/pegjs/-/pegjs-0.9.0.tgz#f6aefa2e3ce56169208e52179dfe41f89141a369"

performance-now@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/performance-now/-/performance-now-0.2.0.tgz#33ef30c5c77d4ea21c5a53869d91b56d8f2555e5"

pify@^2.0.0, pify@^2.3.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/pify/-/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/pinkie-promise/-/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/pinkie/-/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"

pkg-dir@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/pkg-dir/-/pkg-dir-1.0.0.tgz#7a4b508a8d5bb2d629d447056ff4e9c9314cf3d4"
  dependencies:
    find-up "^1.0.0"

plist@1.2.0, plist@^1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/plist/-/plist-1.2.0.tgz#084b5093ddc92506e259f874b8d9b1afb8c79593"
  dependencies:
    base64-js "0.0.8"
    util-deprecate "1.0.2"
    xmlbuilder "4.0.0"
    xmldom "0.1.x"

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/prelude-ls/-/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"

preserve@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/preserve/-/preserve-0.2.0.tgz#815ed1f6ebc65926f865b310c0713bcb3315ce4b"

pretty-format@^20.0.3:
  version "20.0.3"
  resolved "https://registry.yarnpkg.com/pretty-format/-/pretty-format-20.0.3.tgz#020e350a560a1fe1a98dc3beb6ccffb386de8b14"
  dependencies:
    ansi-regex "^2.1.1"
    ansi-styles "^3.0.0"

private@^0.1.6:
  version "0.1.7"
  resolved "https://registry.yarnpkg.com/private/-/private-0.1.7.tgz#68ce5e8a1ef0a23bb570cc28537b5332aba63ef1"

process-nextick-args@~1.0.6:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/process-nextick-args/-/process-nextick-args-1.0.7.tgz#150e20b756590ad3f91093f25a4f2ad8bff30ba3"

process@^0.11.0:
  version "0.11.10"
  resolved "https://registry.yarnpkg.com/process/-/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"

process@~0.5.1:
  version "0.5.2"
  resolved "https://registry.yarnpkg.com/process/-/process-0.5.2.tgz#1638d8a8e34c2f440a91db95ab9aeb677fc185cf"

progress-bar-webpack-plugin@^1.9.3:
  version "1.10.0"
  resolved "https://registry.yarnpkg.com/progress-bar-webpack-plugin/-/progress-bar-webpack-plugin-1.10.0.tgz#e0b1063aa03c79e298a9340598590bb61efef9a4"
  dependencies:
    chalk "^1.1.1"
    object.assign "^4.0.1"
    progress "^1.1.8"

progress@^1.1.8:
  version "1.1.8"
  resolved "https://registry.yarnpkg.com/progress/-/progress-1.1.8.tgz#e260c78f6161cdd9b0e56cc3e0a85de17c7a57be"

promise@^7.1.1:
  version "7.3.1"
  resolved "https://registry.yarnpkg.com/promise/-/promise-7.3.1.tgz#064b72602b18f90f29192b8b1bc418ffd1ebd3bf"
  dependencies:
    asap "~2.0.3"

proxy-addr@~1.1.4:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/proxy-addr/-/proxy-addr-1.1.4.tgz#27e545f6960a44a627d9b44467e35c1b6b4ce2f3"
  dependencies:
    forwarded "~0.1.0"
    ipaddr.js "1.3.0"

prr@~0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/prr/-/prr-0.0.0.tgz#1a84b85908325501411853d0081ee3fa86e2926a"

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/pseudomap/-/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"

public-encrypt@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/public-encrypt/-/public-encrypt-4.0.0.tgz#39f699f3a46560dd5ebacbca693caf7c65c18cc6"
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"

punycode@^1.2.4, punycode@^1.4.1:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"

qs@4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/qs/-/qs-4.0.0.tgz#c31d9b74ec27df75e543a86c78728ed8d4623607"

qs@6.4.0, qs@~6.4.0:
  version "6.4.0"
  resolved "https://registry.yarnpkg.com/qs/-/qs-6.4.0.tgz#13e26d28ad6b0ffaa91312cd3bf708ed351e7233"

querystring-es3@^0.2.0:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/querystring-es3/-/querystring-es3-0.2.1.tgz#9ec61f79049875707d69414596fd907a4d711e73"

querystring@0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/querystring/-/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"

quick-lru@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/quick-lru/-/quick-lru-1.0.0.tgz#7fa80304ab72c1f81cef738739cd47d7cc0c8bff"

random-bytes@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/random-bytes/-/random-bytes-1.0.0.tgz#4f68a1dc0ae58bd3fb95848c30324db75d64360b"

randomatic@^1.1.3:
  version "1.1.7"
  resolved "https://registry.yarnpkg.com/randomatic/-/randomatic-1.1.7.tgz#c7abe9cc8b87c0baa876b19fde83fd464797e38c"
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

randombytes@^2.0.0, randombytes@^2.0.1:
  version "2.0.5"
  resolved "https://registry.yarnpkg.com/randombytes/-/randombytes-2.0.5.tgz#dc009a246b8d09a177b4b7a0ae77bc570f4b1b79"
  dependencies:
    safe-buffer "^5.1.0"

range-parser@^1.0.3, range-parser@~1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/range-parser/-/range-parser-1.2.0.tgz#f49be6b487894ddc40dcc94a322f611092e00d5e"

range-parser@~1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/range-parser/-/range-parser-1.0.3.tgz#6872823535c692e2c2a0103826afd82c2e0ff175"

raw-body@~2.1.2:
  version "2.1.7"
  resolved "https://registry.yarnpkg.com/raw-body/-/raw-body-2.1.7.tgz#adfeace2e4fb3098058014d08c072dcc59758774"
  dependencies:
    bytes "2.4.0"
    iconv-lite "0.4.13"
    unpipe "1.0.0"

rc@^1.1.7:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/rc/-/rc-1.2.1.tgz#2e03e8e42ee450b8cb3dce65be1bf8974e1dfd95"
  dependencies:
    deep-extend "~0.4.0"
    ini "~1.3.0"
    minimist "^1.2.0"
    strip-json-comments "~2.0.1"

react-clone-referenced-element@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/react-clone-referenced-element/-/react-clone-referenced-element-1.0.1.tgz#2bba8c69404c5e4a944398600bcc4c941f860682"

react-deep-force-update@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/react-deep-force-update/-/react-deep-force-update-1.0.1.tgz#f911b5be1d2a6fe387507dd6e9a767aa2924b4c7"

react-native-payments-addon-braintree@4.8.4:
  version "4.8.4"
  resolved "https://registry.yarnpkg.com/react-native-payments-addon-braintree/-/react-native-payments-addon-braintree-4.8.4.tgz#f6b1fac554ec05c75af4669b071735104f1b8559"

react-native-payments@0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/react-native-payments/-/react-native-payments-0.1.2.tgz#442be4a927bc9790aa42da5c33560b14109ca832"
  dependencies:
    es6-error "^4.0.2"
    uuid "^3.1.0"
    validator "^7.0.0"

react-native@0.41.0:
  version "0.41.0"
  resolved "https://registry.yarnpkg.com/react-native/-/react-native-0.41.0.tgz#09ea967b182885f302724f14587145da3b2d2e22"
  dependencies:
    absolute-path "^0.0.0"
    art "^0.10.0"
    async "^2.0.1"
    babel-core "^6.21.0"
    babel-generator "^6.21.0"
    babel-plugin-external-helpers "^6.18.0"
    babel-plugin-syntax-trailing-function-commas "^6.20.0"
    babel-plugin-transform-flow-strip-types "^6.21.0"
    babel-plugin-transform-object-rest-spread "^6.20.2"
    babel-polyfill "^6.20.0"
    babel-preset-es2015-node "^6.1.1"
    babel-preset-fbjs "^2.1.0"
    babel-preset-react-native "^1.9.1"
    babel-register "^6.18.0"
    babel-runtime "^6.20.0"
    babel-traverse "^6.21.0"
    babel-types "^6.21.0"
    babylon "^6.14.1"
    base64-js "^1.1.2"
    bser "^1.0.2"
    chalk "^1.1.1"
    commander "^2.9.0"
    connect "^2.8.3"
    core-js "^2.2.2"
    debug "^2.2.0"
    denodeify "^1.2.1"
    event-target-shim "^1.0.5"
    fbjs "^0.8.5"
    fbjs-scripts "^0.7.0"
    fs-extra "^0.26.2"
    glob "^5.0.15"
    graceful-fs "^4.1.3"
    image-size "^0.3.5"
    immutable "~3.7.6"
    imurmurhash "^0.1.4"
    inquirer "^0.12.0"
    jest-haste-map "18.0.0"
    joi "^6.6.1"
    json-stable-stringify "^1.0.1"
    json5 "^0.4.0"
    left-pad "^1.1.3"
    lodash "^4.16.6"
    mime "^1.3.4"
    mime-types "2.1.11"
    minimist "^1.2.0"
    mkdirp "^0.5.1"
    node-fetch "^1.3.3"
    npmlog "^2.0.4"
    opn "^3.0.2"
    optimist "^0.6.1"
    plist "^1.2.0"
    promise "^7.1.1"
    react-clone-referenced-element "^1.0.1"
    react-timer-mixin "^0.13.2"
    react-transform-hmr "^1.0.4"
    rebound "^0.0.13"
    regenerator-runtime "^0.9.5"
    request "^2.79.0"
    rimraf "^2.5.4"
    sane "~1.4.1"
    semver "^5.0.3"
    shell-quote "1.6.1"
    source-map "^0.5.6"
    stacktrace-parser "^0.1.3"
    temp "0.8.3"
    throat "^3.0.0"
    uglify-js "^2.6.2"
    whatwg-fetch "^1.0.0"
    wordwrap "^1.0.0"
    worker-farm "^1.3.1"
    write-file-atomic "^1.2.0"
    ws "^1.1.0"
    xcode "^0.8.9"
    xmldoc "^0.4.0"
    yargs "^6.4.0"

react-proxy@^1.1.7:
  version "1.1.8"
  resolved "https://registry.yarnpkg.com/react-proxy/-/react-proxy-1.1.8.tgz#9dbfd9d927528c3aa9f444e4558c37830ab8c26a"
  dependencies:
    lodash "^4.6.1"
    react-deep-force-update "^1.0.0"

react-test-renderer@~15.4.0-rc.4:
  version "15.4.2"
  resolved "https://registry.yarnpkg.com/react-test-renderer/-/react-test-renderer-15.4.2.tgz#27e1dff5d26d0e830f99614c487622bc831416f3"
  dependencies:
    fbjs "^0.8.4"
    object-assign "^4.1.0"

react-timer-mixin@^0.13.2:
  version "0.13.3"
  resolved "https://registry.yarnpkg.com/react-timer-mixin/-/react-timer-mixin-0.13.3.tgz#0da8b9f807ec07dc3e854d082c737c65605b3d22"

react-transform-hmr@^1.0.4:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/react-transform-hmr/-/react-transform-hmr-1.0.4.tgz#e1a40bd0aaefc72e8dfd7a7cda09af85066397bb"
  dependencies:
    global "^4.3.0"
    react-proxy "^1.1.7"

react@~15.4.0-rc.4:
  version "15.4.2"
  resolved "https://registry.yarnpkg.com/react/-/react-15.4.2.tgz#41f7991b26185392ba9bae96c8889e7e018397ef"
  dependencies:
    fbjs "^0.8.4"
    loose-envify "^1.1.0"
    object-assign "^4.1.0"

read-pkg-up@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/read-pkg-up/-/read-pkg-up-1.0.1.tgz#9d63c13276c065918d57f002a57f40a1b643fb02"
  dependencies:
    find-up "^1.0.0"
    read-pkg "^1.0.0"

read-pkg@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/read-pkg/-/read-pkg-1.1.0.tgz#f5ffaa5ecd29cb31c0474bca7d756b6bb29e3f28"
  dependencies:
    load-json-file "^1.0.0"
    normalize-package-data "^2.3.2"
    path-type "^1.0.0"

readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.0.5, readable-stream@^2.0.6, readable-stream@^2.1.4, readable-stream@^2.1.5, readable-stream@^2.2.6:
  version "2.3.3"
  resolved "https://registry.yarnpkg.com/readable-stream/-/readable-stream-2.3.3.tgz#368f2512d79f9d46fdfc71349ae7878bbc1eb95c"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~1.0.6"
    safe-buffer "~5.1.1"
    string_decoder "~1.0.3"
    util-deprecate "~1.0.1"

readable-stream@~1.1.8, readable-stream@~1.1.9:
  version "1.1.14"
  resolved "https://registry.yarnpkg.com/readable-stream/-/readable-stream-1.1.14.tgz#7cf4c54ef648e3813084c636dd2079e166c081d9"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readdirp@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/readdirp/-/readdirp-2.1.0.tgz#4ed0ad060df3073300c48440373f72d1cc642d78"
  dependencies:
    graceful-fs "^4.1.2"
    minimatch "^3.0.2"
    readable-stream "^2.0.2"
    set-immediate-shim "^1.0.1"

readline2@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/readline2/-/readline2-1.0.1.tgz#41059608ffc154757b715d9989d199ffbf372e35"
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    mute-stream "0.0.5"

rebound@^0.0.13:
  version "0.0.13"
  resolved "https://registry.yarnpkg.com/rebound/-/rebound-0.0.13.tgz#4a225254caf7da756797b19c5817bf7a7941fac1"

regenerate@^1.2.1:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/regenerate/-/regenerate-1.3.2.tgz#d1941c67bad437e1be76433add5b385f95b19260"

regenerator-runtime@^0.10.0:
  version "0.10.5"
  resolved "https://registry.yarnpkg.com/regenerator-runtime/-/regenerator-runtime-0.10.5.tgz#336c3efc1220adcedda2c9fab67b5a7955a33658"

regenerator-runtime@^0.9.5:
  version "0.9.6"
  resolved "https://registry.yarnpkg.com/regenerator-runtime/-/regenerator-runtime-0.9.6.tgz#d33eb95d0d2001a4be39659707c51b0cb71ce029"

regenerator-transform@0.9.11:
  version "0.9.11"
  resolved "https://registry.yarnpkg.com/regenerator-transform/-/regenerator-transform-0.9.11.tgz#3a7d067520cb7b7176769eb5ff868691befe1283"
  dependencies:
    babel-runtime "^6.18.0"
    babel-types "^6.19.0"
    private "^0.1.6"

regex-cache@^0.4.2:
  version "0.4.3"
  resolved "https://registry.yarnpkg.com/regex-cache/-/regex-cache-0.4.3.tgz#9b1a6c35d4d0dfcef5711ae651e8e9d3d7114145"
  dependencies:
    is-equal-shallow "^0.1.3"
    is-primitive "^2.0.0"

regexpu-core@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/regexpu-core/-/regexpu-core-2.0.0.tgz#49d038837b8dcf8bfa5b9a42139938e6ea2ae240"
  dependencies:
    regenerate "^1.2.1"
    regjsgen "^0.2.0"
    regjsparser "^0.1.4"

regjsgen@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/regjsgen/-/regjsgen-0.2.0.tgz#6c016adeac554f75823fe37ac05b92d5a4edb1f7"

regjsparser@^0.1.4:
  version "0.1.5"
  resolved "https://registry.yarnpkg.com/regjsparser/-/regjsparser-0.1.5.tgz#7ee8f84dc6fa792d3fd0ae228d24bd949ead205c"
  dependencies:
    jsesc "~0.5.0"

remove-trailing-separator@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/remove-trailing-separator/-/remove-trailing-separator-1.0.2.tgz#69b062d978727ad14dc6b56ba4ab772fd8d70511"

repeat-element@^1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/repeat-element/-/repeat-element-1.1.2.tgz#ef089a178d1483baae4d93eb98b4f9e4e11d990a"

repeat-string@^1.5.2:
  version "1.6.1"
  resolved "https://registry.yarnpkg.com/repeat-string/-/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"

repeating@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/repeating/-/repeating-2.0.1.tgz#5214c53a926d3552707527fbab415dbc08d06dda"
  dependencies:
    is-finite "^1.0.0"

replace-ext@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/replace-ext/-/replace-ext-0.0.1.tgz#29bbd92078a739f0bcce2b4ee41e837953522924"

request@^2.79.0, request@^2.81.0:
  version "2.81.0"
  resolved "https://registry.yarnpkg.com/request/-/request-2.81.0.tgz#c6928946a0e06c5f8d6f8a9333469ffda46298a0"
  dependencies:
    aws-sign2 "~0.6.0"
    aws4 "^1.2.1"
    caseless "~0.12.0"
    combined-stream "~1.0.5"
    extend "~3.0.0"
    forever-agent "~0.6.1"
    form-data "~2.1.1"
    har-validator "~4.2.1"
    hawk "~3.1.3"
    http-signature "~1.1.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.7"
    oauth-sign "~0.8.1"
    performance-now "^0.2.0"
    qs "~6.4.0"
    safe-buffer "^5.0.1"
    stringstream "~0.0.4"
    tough-cookie "~2.3.0"
    tunnel-agent "^0.6.0"
    uuid "^3.0.0"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/require-directory/-/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"

require-main-filename@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/require-main-filename/-/require-main-filename-1.0.1.tgz#97f717b69d48784f5f526a6c5aa8ffdda055a4d1"

resolve@1.1.7:
  version "1.1.7"
  resolved "https://registry.yarnpkg.com/resolve/-/resolve-1.1.7.tgz#203114d82ad2c5ed9e8e0411b3932875e889e97b"

resolve@^1.3.2, resolve@^1.3.3:
  version "1.3.3"
  resolved "https://registry.yarnpkg.com/resolve/-/resolve-1.3.3.tgz#655907c3469a8680dc2de3a275a8fdd69691f0e5"
  dependencies:
    path-parse "^1.0.5"

response-time@~2.3.1:
  version "2.3.2"
  resolved "https://registry.yarnpkg.com/response-time/-/response-time-2.3.2.tgz#ffa71bab952d62f7c1d49b7434355fbc68dffc5a"
  dependencies:
    depd "~1.1.0"
    on-headers "~1.0.1"

restore-cursor@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/restore-cursor/-/restore-cursor-1.0.1.tgz#34661f46886327fed2991479152252df92daa541"
  dependencies:
    exit-hook "^1.0.0"
    onetime "^1.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/restore-cursor/-/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

right-align@^0.1.1:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/right-align/-/right-align-0.1.3.tgz#61339b722fe6a3515689210d24e14c96148613ef"
  dependencies:
    align-text "^0.1.1"

rimraf@2, rimraf@^2.2.8, rimraf@^2.5.1, rimraf@^2.5.4, rimraf@^2.6.1:
  version "2.6.1"
  resolved "https://registry.yarnpkg.com/rimraf/-/rimraf-2.6.1.tgz#c2338ec643df7a1b7fe5c54fa86f57428a55f33d"
  dependencies:
    glob "^7.0.5"

rimraf@~2.2.6:
  version "2.2.8"
  resolved "https://registry.yarnpkg.com/rimraf/-/rimraf-2.2.8.tgz#e439be2aaee327321952730f99a8929e4fc50582"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/ripemd160/-/ripemd160-2.0.1.tgz#0f4584295c53a3628af7e6d79aca21ce57d1c6e7"
  dependencies:
    hash-base "^2.0.0"
    inherits "^2.0.1"

rndm@1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/rndm/-/rndm-1.2.0.tgz#f33fe9cfb52bbfd520aa18323bc65db110a1b76c"

run-async@^0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/run-async/-/run-async-0.1.0.tgz#c8ad4a5e110661e402a7d21b530e009f25f8e389"
  dependencies:
    once "^1.3.0"

run-async@^2.2.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/run-async/-/run-async-2.3.0.tgz#0371ab4ae0bdd720d4166d7dfda64ff7a445a6c0"
  dependencies:
    is-promise "^2.1.0"

rx-lite-aggregates@^4.0.8:
  version "4.0.8"
  resolved "https://registry.yarnpkg.com/rx-lite-aggregates/-/rx-lite-aggregates-4.0.8.tgz#753b87a89a11c95467c4ac1626c4efc4e05c67be"
  dependencies:
    rx-lite "*"

rx-lite@*, rx-lite@^4.0.8:
  version "4.0.8"
  resolved "https://registry.yarnpkg.com/rx-lite/-/rx-lite-4.0.8.tgz#0b1e11af8bc44836f04a6407e92da42467b79444"

rx-lite@^3.1.2:
  version "3.1.2"
  resolved "https://registry.yarnpkg.com/rx-lite/-/rx-lite-3.1.2.tgz#19ce502ca572665f3b647b10939f97fd1615f102"

safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.1"
  resolved "https://registry.yarnpkg.com/safe-buffer/-/safe-buffer-5.1.1.tgz#893312af69b2123def71f57889001671eeb2c853"

safe-buffer@~5.0.1:
  version "5.0.1"
  resolved "https://registry.yarnpkg.com/safe-buffer/-/safe-buffer-5.0.1.tgz#d263ca54696cd8a306b5ca6551e92de57918fbe7"

sane@~1.4.1:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/sane/-/sane-1.4.1.tgz#88f763d74040f5f0c256b6163db399bf110ac715"
  dependencies:
    exec-sh "^0.2.0"
    fb-watchman "^1.8.0"
    minimatch "^3.0.2"
    minimist "^1.1.1"
    walker "~1.0.5"
    watch "~0.10.0"

sane@~1.6.0:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/sane/-/sane-1.6.0.tgz#9610c452307a135d29c1fdfe2547034180c46775"
  dependencies:
    anymatch "^1.3.0"
    exec-sh "^0.2.0"
    fb-watchman "^1.8.0"
    minimatch "^3.0.2"
    minimist "^1.1.1"
    walker "~1.0.5"
    watch "~0.10.0"

sax@^1.2.1:
  version "1.2.4"
  resolved "https://registry.yarnpkg.com/sax/-/sax-1.2.4.tgz#2816234e2378bddc4e5354fab5caa895df7100d9"

sax@~1.1.1:
  version "1.1.6"
  resolved "https://registry.yarnpkg.com/sax/-/sax-1.1.6.tgz#5d616be8a5e607d54e114afae55b7eaf2fcc3240"

"semver@2 || 3 || 4 || 5", semver@5.x, semver@^5.0.3, semver@^5.1.0, semver@^5.3.0:
  version "5.3.0"
  resolved "https://registry.yarnpkg.com/semver/-/semver-5.3.0.tgz#9b2ce5d3de02d17c6012ad326aa6b4d0cf54f94f"

send@0.13.2:
  version "0.13.2"
  resolved "https://registry.yarnpkg.com/send/-/send-0.13.2.tgz#765e7607c8055452bba6f0b052595350986036de"
  dependencies:
    debug "~2.2.0"
    depd "~1.1.0"
    destroy "~1.0.4"
    escape-html "~1.0.3"
    etag "~1.7.0"
    fresh "0.3.0"
    http-errors "~1.3.1"
    mime "1.3.4"
    ms "0.7.1"
    on-finished "~2.3.0"
    range-parser "~1.0.3"
    statuses "~1.2.1"

send@0.15.3:
  version "0.15.3"
  resolved "https://registry.yarnpkg.com/send/-/send-0.15.3.tgz#5013f9f99023df50d1bd9892c19e3defd1d53309"
  dependencies:
    debug "2.6.7"
    depd "~1.1.0"
    destroy "~1.0.4"
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    etag "~1.8.0"
    fresh "0.5.0"
    http-errors "~1.6.1"
    mime "1.3.4"
    ms "2.0.0"
    on-finished "~2.3.0"
    range-parser "~1.2.0"
    statuses "~1.3.1"

serialize-error@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/serialize-error/-/serialize-error-2.1.0.tgz#50b679d5635cdf84667bdc8e59af4e5b81d5f60a"

serve-favicon@~2.3.0:
  version "2.3.2"
  resolved "https://registry.yarnpkg.com/serve-favicon/-/serve-favicon-2.3.2.tgz#dd419e268de012ab72b319d337f2105013f9381f"
  dependencies:
    etag "~1.7.0"
    fresh "0.3.0"
    ms "0.7.2"
    parseurl "~1.3.1"

serve-index@~1.7.2:
  version "1.7.3"
  resolved "https://registry.yarnpkg.com/serve-index/-/serve-index-1.7.3.tgz#7a057fc6ee28dc63f64566e5fa57b111a86aecd2"
  dependencies:
    accepts "~1.2.13"
    batch "0.5.3"
    debug "~2.2.0"
    escape-html "~1.0.3"
    http-errors "~1.3.1"
    mime-types "~2.1.9"
    parseurl "~1.3.1"

serve-static@1.12.3:
  version "1.12.3"
  resolved "https://registry.yarnpkg.com/serve-static/-/serve-static-1.12.3.tgz#9f4ba19e2f3030c547f8af99107838ec38d5b1e2"
  dependencies:
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    parseurl "~1.3.1"
    send "0.15.3"

serve-static@~1.10.0:
  version "1.10.3"
  resolved "https://registry.yarnpkg.com/serve-static/-/serve-static-1.10.3.tgz#ce5a6ecd3101fed5ec09827dac22a9c29bfb0535"
  dependencies:
    escape-html "~1.0.3"
    parseurl "~1.3.1"
    send "0.13.2"

set-blocking@^2.0.0, set-blocking@~2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/set-blocking/-/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"

set-immediate-shim@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/set-immediate-shim/-/set-immediate-shim-1.0.1.tgz#4b2b1b27eb808a9f8dcc481a58e5e56f599f3f61"

setimmediate@^1.0.4, setimmediate@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/setimmediate/-/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"

setprototypeof@1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/setprototypeof/-/setprototypeof-1.0.3.tgz#66567e37043eeb4f04d91bd658c0cbefb55b8e04"

sha.js@^2.4.0, sha.js@^2.4.8:
  version "2.4.8"
  resolved "https://registry.yarnpkg.com/sha.js/-/sha.js-2.4.8.tgz#37068c2c476b6baf402d14a49c67f597921f634f"
  dependencies:
    inherits "^2.0.1"

shell-quote@1.6.1:
  version "1.6.1"
  resolved "https://registry.yarnpkg.com/shell-quote/-/shell-quote-1.6.1.tgz#f4781949cce402697127430ea3b3c5476f481767"
  dependencies:
    array-filter "~0.0.0"
    array-map "~0.0.0"
    array-reduce "~0.0.0"
    jsonify "~0.0.0"

shellwords@^0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/shellwords/-/shellwords-0.1.0.tgz#66afd47b6a12932d9071cbfd98a52e785cd0ba14"

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/signal-exit/-/signal-exit-3.0.2.tgz#b5fdc08f1287ea1178628e415e25132b73646c6d"

simple-plist@0.1.4:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/simple-plist/-/simple-plist-0.1.4.tgz#10eb51b47e33c556eb8ec46d5ee64d64e717db5d"
  dependencies:
    bplist-creator "0.0.4"
    bplist-parser "0.0.6"
    plist "1.2.0"

slash@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/slash/-/slash-1.0.0.tgz#c41f2f6c39fc16d1cd17ad4b5d896114ae470d55"

slide@^1.1.5:
  version "1.1.6"
  resolved "https://registry.yarnpkg.com/slide/-/slide-1.1.6.tgz#56eb027d65b4d2dce6cb2e2d32c4d4afc9e1d707"

sntp@1.x.x:
  version "1.0.9"
  resolved "https://registry.yarnpkg.com/sntp/-/sntp-1.0.9.tgz#6541184cc90aeea6c6e7b35e2659082443c66198"
  dependencies:
    hoek "2.x.x"

source-list-map@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/source-list-map/-/source-list-map-2.0.0.tgz#aaa47403f7b245a92fbc97ea08f250d6087ed085"

source-map-support@^0.4.2:
  version "0.4.15"
  resolved "https://registry.yarnpkg.com/source-map-support/-/source-map-support-0.4.15.tgz#03202df65c06d2bd8c7ec2362a193056fef8d3b1"
  dependencies:
    source-map "^0.5.6"

source-map@^0.4.4:
  version "0.4.4"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.4.4.tgz#eba4f5da9c0dc999de68032d8b4f76173652036b"
  dependencies:
    amdefine ">=0.0.4"

source-map@^0.5.0, source-map@^0.5.3, source-map@^0.5.6, source-map@~0.5.1, source-map@~0.5.3:
  version "0.5.6"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.5.6.tgz#75ce38f52bf0733c5a7f0c118d81334a2bb5f412"

source-map@~0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.2.0.tgz#dab73fbcfc2ba819b4de03bd6f6eaa48164b3f9d"
  dependencies:
    amdefine ">=0.0.4"

sparkles@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/sparkles/-/sparkles-1.0.0.tgz#1acbbfb592436d10bbe8f785b7cc6f82815012c3"

spdx-correct@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/spdx-correct/-/spdx-correct-1.0.2.tgz#4b3073d933ff51f3912f03ac5519498a4150db40"
  dependencies:
    spdx-license-ids "^1.0.2"

spdx-expression-parse@~1.0.0:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/spdx-expression-parse/-/spdx-expression-parse-1.0.4.tgz#9bdf2f20e1f40ed447fbe273266191fced51626c"

spdx-license-ids@^1.0.2:
  version "1.2.2"
  resolved "https://registry.yarnpkg.com/spdx-license-ids/-/spdx-license-ids-1.2.2.tgz#c9df7a3424594ade6bd11900d596696dc06bac57"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"

sshpk@^1.7.0:
  version "1.13.1"
  resolved "https://registry.yarnpkg.com/sshpk/-/sshpk-1.13.1.tgz#512df6da6287144316dc4c18fe1cf1d940739be3"
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    dashdash "^1.12.0"
    getpass "^0.1.1"
  optionalDependencies:
    bcrypt-pbkdf "^1.0.0"
    ecc-jsbn "~0.1.1"
    jsbn "~0.1.0"
    tweetnacl "~0.14.0"

stacktrace-parser@^0.1.3:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/stacktrace-parser/-/stacktrace-parser-0.1.4.tgz#01397922e5f62ecf30845522c95c4fe1d25e7d4e"

statuses@1, "statuses@>= 1.3.1 < 2", statuses@~1.3.1:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/statuses/-/statuses-1.3.1.tgz#faf51b9eb74aaef3b3acf4ad5f61abf24cb7b93e"

statuses@~1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/statuses/-/statuses-1.2.1.tgz#dded45cc18256d51ed40aec142489d5c61026d28"

stream-browserify@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/stream-browserify/-/stream-browserify-2.0.1.tgz#66266ee5f9bdb9940a4e4514cafb43bb71e5c9db"
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-buffers@~0.2.3:
  version "0.2.6"
  resolved "https://registry.yarnpkg.com/stream-buffers/-/stream-buffers-0.2.6.tgz#181c08d5bb3690045f69401b9ae6a7a0cf3313fc"

stream-counter@~0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/stream-counter/-/stream-counter-0.2.0.tgz#ded266556319c8b0e222812b9cf3b26fa7d947de"
  dependencies:
    readable-stream "~1.1.8"

stream-http@^2.3.1:
  version "2.7.2"
  resolved "https://registry.yarnpkg.com/stream-http/-/stream-http-2.7.2.tgz#40a050ec8dc3b53b33d9909415c02c0bf1abfbad"
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.2.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

string-length@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/string-length/-/string-length-1.0.1.tgz#56970fb1c38558e9e70b728bf3de269ac45adfac"
  dependencies:
    strip-ansi "^3.0.0"

string-width@^1.0.1, string-width@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/string-width/-/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

string-width@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/string-width/-/string-width-2.1.0.tgz#030664561fc146c9423ec7d978fe2457437fe6d0"
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string_decoder@^0.10.25, string_decoder@~0.10.x:
  version "0.10.31"
  resolved "https://registry.yarnpkg.com/string_decoder/-/string_decoder-0.10.31.tgz#62e203bc41766c6c28c9fc84301dab1c5310fa94"

string_decoder@~1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/string_decoder/-/string_decoder-1.0.3.tgz#0fc67d7c141825de94282dd536bec6b9bce860ab"
  dependencies:
    safe-buffer "~5.1.0"

stringstream@~0.0.4:
  version "0.0.5"
  resolved "https://registry.yarnpkg.com/stringstream/-/stringstream-0.0.5.tgz#4e484cd4de5a0bbbee18e46307710a8a81621878"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  dependencies:
    ansi-regex "^3.0.0"

strip-bom@3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/strip-bom/-/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/strip-bom/-/strip-bom-2.0.0.tgz#6219a85616520491f35788bdbf1447a99c7e6b0e"
  dependencies:
    is-utf8 "^0.2.0"

strip-json-comments@~2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/strip-json-comments/-/strip-json-comments-2.0.1.tgz#3c531942e908c2697c0ec344858c286c7ca0a60a"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"

supports-color@^3.1.0, supports-color@^3.1.2:
  version "3.2.3"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-3.2.3.tgz#65ac0504b3954171d8a64946b2ae3cbb8a5f54f6"
  dependencies:
    has-flag "^1.0.0"

supports-color@^4.0.0:
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-4.2.0.tgz#ad986dc7eb2315d009b4d77c8169c2231a684037"
  dependencies:
    has-flag "^2.0.0"

symbol-tree@^3.2.1:
  version "3.2.2"
  resolved "https://registry.yarnpkg.com/symbol-tree/-/symbol-tree-3.2.2.tgz#ae27db38f660a7ae2e1c3b7d1bc290819b8519e6"

tapable@^0.2.5, tapable@~0.2.5:
  version "0.2.6"
  resolved "https://registry.yarnpkg.com/tapable/-/tapable-0.2.6.tgz#206be8e188860b514425375e6f1ae89bfb01fd8d"

tar-pack@^3.4.0:
  version "3.4.0"
  resolved "https://registry.yarnpkg.com/tar-pack/-/tar-pack-3.4.0.tgz#23be2d7f671a8339376cbdb0b8fe3fdebf317984"
  dependencies:
    debug "^2.2.0"
    fstream "^1.0.10"
    fstream-ignore "^1.0.5"
    once "^1.3.3"
    readable-stream "^2.1.4"
    rimraf "^2.5.1"
    tar "^2.2.1"
    uid-number "^0.0.6"

tar@^2.2.1:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/tar/-/tar-2.2.1.tgz#8e4d2a256c0e2185c6b18ad694aec968b83cb1d1"
  dependencies:
    block-stream "*"
    fstream "^1.0.2"
    inherits "2"

temp@0.8.3:
  version "0.8.3"
  resolved "https://registry.yarnpkg.com/temp/-/temp-0.8.3.tgz#e0c6bc4d26b903124410e4fed81103014dfc1f59"
  dependencies:
    os-tmpdir "^1.0.0"
    rimraf "~2.2.6"

test-exclude@^4.1.1:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/test-exclude/-/test-exclude-4.1.1.tgz#4d84964b0966b0087ecc334a2ce002d3d9341e26"
  dependencies:
    arrify "^1.0.1"
    micromatch "^2.3.11"
    object-assign "^4.1.0"
    read-pkg-up "^1.0.1"
    require-main-filename "^1.0.1"

throat@^3.0.0:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/throat/-/throat-3.2.0.tgz#50cb0670edbc40237b9e347d7e1f88e4620af836"

through2@^2.0.0:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/through2/-/through2-2.0.3.tgz#0004569b37c7c74ba39c43f3ced78d1ad94140be"
  dependencies:
    readable-stream "^2.1.5"
    xtend "~4.0.1"

through@^2.3.6:
  version "2.3.8"
  resolved "https://registry.yarnpkg.com/through/-/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"

time-stamp@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/time-stamp/-/time-stamp-1.1.0.tgz#764a5a11af50561921b133f3b44e618687e0f5c3"

timers-browserify@^2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/timers-browserify/-/timers-browserify-2.0.2.tgz#ab4883cf597dcd50af211349a00fbca56ac86b86"
  dependencies:
    setimmediate "^1.0.4"

tmp@^0.0.31:
  version "0.0.31"
  resolved "https://registry.yarnpkg.com/tmp/-/tmp-0.0.31.tgz#8f38ab9438e17315e5dbd8b3657e8bfb277ae4a7"
  dependencies:
    os-tmpdir "~1.0.1"

tmpl@1.0.x:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/tmpl/-/tmpl-1.0.4.tgz#23640dd7b42d00433911140820e5cf440e521dd1"

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz#7d229b1fcc637e466ca081180836a7aabff83f43"

to-fast-properties@^1.0.1:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/to-fast-properties/-/to-fast-properties-1.0.3.tgz#b83571fa4d8c25b82e231b06e3a3055de4ca1a47"

topo@1.x.x:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/topo/-/topo-1.1.0.tgz#e9d751615d1bb87dc865db182fa1ca0a5ef536d5"
  dependencies:
    hoek "2.x.x"

tough-cookie@^2.3.2, tough-cookie@~2.3.0:
  version "2.3.2"
  resolved "https://registry.yarnpkg.com/tough-cookie/-/tough-cookie-2.3.2.tgz#f081f76e4c85720e6c37a5faced737150d84072a"
  dependencies:
    punycode "^1.4.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.yarnpkg.com/tr46/-/tr46-0.0.3.tgz#8184fd347dac9cdc185992f3a6622e14b9d9ab6a"

trim-right@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/trim-right/-/trim-right-1.0.1.tgz#cb2e1203067e0c8de1f614094b9fe45704ea6003"

tsscmp@1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/tsscmp/-/tsscmp-1.0.5.tgz#7dc4a33af71581ab4337da91d85ca5427ebd9a97"

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/tty-browserify/-/tty-browserify-0.0.0.tgz#a157ba402da24e9bf957f9aa69d524eed42901a6"

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/tunnel-agent/-/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "https://registry.yarnpkg.com/tweetnacl/-/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"

type-check@~0.3.2:
  version "0.3.2"
  resolved "https://registry.yarnpkg.com/type-check/-/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
  dependencies:
    prelude-ls "~1.1.2"

type-is@~1.6.15, type-is@~1.6.6:
  version "1.6.15"
  resolved "https://registry.yarnpkg.com/type-is/-/type-is-1.6.15.tgz#cab10fb4909e441c82842eafe1ad646c81804410"
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.15"

ua-parser-js@^0.7.9:
  version "0.7.13"
  resolved "https://registry.yarnpkg.com/ua-parser-js/-/ua-parser-js-0.7.13.tgz#cd9dd2f86493b3f44dbeeef3780fda74c5ee14be"

uglify-js@^2.6, uglify-js@^2.6.2, uglify-js@^2.8.27:
  version "2.8.29"
  resolved "https://registry.yarnpkg.com/uglify-js/-/uglify-js-2.8.29.tgz#29c5733148057bb4e1f75df35b7a9cb72e6a59dd"
  dependencies:
    source-map "~0.5.1"
    yargs "~3.10.0"
  optionalDependencies:
    uglify-to-browserify "~1.0.0"

uglify-to-browserify@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz#6e0924d6bda6b5afe349e39a6d632850a0f882b7"

uid-number@^0.0.6:
  version "0.0.6"
  resolved "https://registry.yarnpkg.com/uid-number/-/uid-number-0.0.6.tgz#0ea10e8035e8eb5b8e4449f06da1c730663baa81"

uid-safe@2.1.4:
  version "2.1.4"
  resolved "https://registry.yarnpkg.com/uid-safe/-/uid-safe-2.1.4.tgz#3ad6f38368c6d4c8c75ec17623fb79aa1d071d81"
  dependencies:
    random-bytes "~1.0.0"

uid-safe@~2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/uid-safe/-/uid-safe-2.0.0.tgz#a7f3c6ca64a1f6a5d04ec0ef3e4c3d5367317137"
  dependencies:
    base64-url "1.2.1"

ultron@1.0.x:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/ultron/-/ultron-1.0.2.tgz#ace116ab557cd197386a4e88f4685378c8b2e4fa"

ultron@~1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/ultron/-/ultron-1.1.0.tgz#b07a2e6a541a815fc6a34ccd4533baec307ca864"

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/unpipe/-/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"

url@^0.11.0:
  version "0.11.0"
  resolved "https://registry.yarnpkg.com/url/-/url-0.11.0.tgz#3838e97cfc60521eb73c525a8e55bfdd9e2e28f1"
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

util-deprecate@1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"

util@0.10.3, util@^0.10.3:
  version "0.10.3"
  resolved "https://registry.yarnpkg.com/util/-/util-0.10.3.tgz#7afb1afe50805246489e3db7fe0ed379336ac0f9"
  dependencies:
    inherits "2.0.1"

utils-merge@1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/utils-merge/-/utils-merge-1.0.0.tgz#0294fb922bb9375153541c4f7096231f287c8af8"

uuid@^3.0.0, uuid@^3.1.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/uuid/-/uuid-3.1.0.tgz#3dd3d3e790abc24d7b0d3a034ffababe28ebbc04"

validate-npm-package-license@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/validate-npm-package-license/-/validate-npm-package-license-3.0.1.tgz#2804babe712ad3379459acfbe24746ab2c303fbc"
  dependencies:
    spdx-correct "~1.0.0"
    spdx-expression-parse "~1.0.0"

validator@^7.0.0:
  version "7.2.0"
  resolved "https://registry.yarnpkg.com/validator/-/validator-7.2.0.tgz#a63dcbaba51d4350bf8df20988e0d5a54d711791"

vary@~1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/vary/-/vary-1.0.1.tgz#99e4981566a286118dfb2b817357df7993376d10"

vary@~1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/vary/-/vary-1.1.1.tgz#67535ebb694c1d52257457984665323f587e8d37"

verror@1.3.6:
  version "1.3.6"
  resolved "https://registry.yarnpkg.com/verror/-/verror-1.3.6.tgz#cff5df12946d297d2baaefaa2689e25be01c005c"
  dependencies:
    extsprintf "1.0.2"

vhost@~3.0.1:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/vhost/-/vhost-3.0.2.tgz#2fb1decd4c466aa88b0f9341af33dc1aff2478d5"

vinyl@^0.5.0:
  version "0.5.3"
  resolved "https://registry.yarnpkg.com/vinyl/-/vinyl-0.5.3.tgz#b0455b38fc5e0cf30d4325132e461970c2091cde"
  dependencies:
    clone "^1.0.0"
    clone-stats "^0.0.1"
    replace-ext "0.0.1"

vm-browserify@0.0.4:
  version "0.0.4"
  resolved "https://registry.yarnpkg.com/vm-browserify/-/vm-browserify-0.0.4.tgz#5d7ea45bbef9e4a6ff65f95438e0a87c357d5a73"
  dependencies:
    indexof "0.0.1"

walker@~1.0.5:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/walker/-/walker-1.0.7.tgz#2f7f9b8fd10d677262b18a884e28d19618e028fb"
  dependencies:
    makeerror "1.0.x"

watch@~0.10.0:
  version "0.10.0"
  resolved "https://registry.yarnpkg.com/watch/-/watch-0.10.0.tgz#77798b2da0f9910d595f1ace5b0c2258521f21dc"

watchpack@^1.3.1:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/watchpack/-/watchpack-1.4.0.tgz#4a1472bcbb952bd0a9bb4036801f954dfb39faac"
  dependencies:
    async "^2.1.2"
    chokidar "^1.7.0"
    graceful-fs "^4.1.2"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/webidl-conversions/-/webidl-conversions-3.0.1.tgz#24534275e2a7bc6be7bc86611cc16ae0a5654871"

webidl-conversions@^4.0.0:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/webidl-conversions/-/webidl-conversions-4.0.1.tgz#8015a17ab83e7e1b311638486ace81da6ce206a0"

webpack-dev-middleware@^1.10.1:
  version "1.11.0"
  resolved "https://registry.yarnpkg.com/webpack-dev-middleware/-/webpack-dev-middleware-1.11.0.tgz#09691d0973a30ad1f82ac73a12e2087f0a4754f9"
  dependencies:
    memory-fs "~0.4.1"
    mime "^1.3.4"
    path-is-absolute "^1.0.0"
    range-parser "^1.0.3"

webpack-sources@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/webpack-sources/-/webpack-sources-1.0.1.tgz#c7356436a4d13123be2e2426a05d1dad9cbe65cf"
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.5.3"

webpack@^2.3.1:
  version "2.7.0"
  resolved "https://registry.yarnpkg.com/webpack/-/webpack-2.7.0.tgz#b2a1226804373ffd3d03ea9c6bd525067034f6b1"
  dependencies:
    acorn "^5.0.0"
    acorn-dynamic-import "^2.0.0"
    ajv "^4.7.0"
    ajv-keywords "^1.1.1"
    async "^2.1.2"
    enhanced-resolve "^3.3.0"
    interpret "^1.0.0"
    json-loader "^0.5.4"
    json5 "^0.5.1"
    loader-runner "^2.3.0"
    loader-utils "^0.2.16"
    memory-fs "~0.4.1"
    mkdirp "~0.5.0"
    node-libs-browser "^2.0.0"
    source-map "^0.5.3"
    supports-color "^3.1.0"
    tapable "~0.2.5"
    uglify-js "^2.8.27"
    watchpack "^1.3.1"
    webpack-sources "^1.0.1"
    yargs "^6.0.0"

whatwg-encoding@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/whatwg-encoding/-/whatwg-encoding-1.0.1.tgz#3c6c451a198ee7aec55b1ec61d0920c67801a5f4"
  dependencies:
    iconv-lite "0.4.13"

whatwg-fetch@>=0.10.0, whatwg-fetch@^1.0.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/whatwg-fetch/-/whatwg-fetch-1.1.1.tgz#ac3c9d39f320c6dce5339969d054ef43dd333319"

whatwg-url@^4.3.0:
  version "4.8.0"
  resolved "https://registry.yarnpkg.com/whatwg-url/-/whatwg-url-4.8.0.tgz#d2981aa9148c1e00a41c5a6131166ab4683bbcc0"
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which-module@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/which-module/-/which-module-1.0.0.tgz#bba63ca861948994ff307736089e3b96026c2a4f"

which@^1.2.12, which@^1.2.9:
  version "1.2.14"
  resolved "https://registry.yarnpkg.com/which/-/which-1.2.14.tgz#9a87c4378f03e827cecaf1acdf56c736c01c14e5"
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.0:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/wide-align/-/wide-align-1.1.2.tgz#571e0f1b0604636ebc0dfc21b0339bbe31341710"
  dependencies:
    string-width "^1.0.2"

window-size@0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/window-size/-/window-size-0.1.0.tgz#5438cd2ea93b202efa3a19fe8887aee7c94f9c9d"

wordwrap@0.0.2:
  version "0.0.2"
  resolved "https://registry.yarnpkg.com/wordwrap/-/wordwrap-0.0.2.tgz#b79669bb42ecb409f83d583cad52ca17eaa1643f"

wordwrap@^1.0.0, wordwrap@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/wordwrap/-/wordwrap-1.0.0.tgz#27584810891456a4171c8d0226441ade90cbcaeb"

wordwrap@~0.0.2:
  version "0.0.3"
  resolved "https://registry.yarnpkg.com/wordwrap/-/wordwrap-0.0.3.tgz#a3d5da6cd5c0bc0008d37234bbaf1bed63059107"

worker-farm@^1.3.1:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/worker-farm/-/worker-farm-1.4.1.tgz#a438bc993a7a7d133bcb6547c95eca7cff4897d8"
  dependencies:
    errno "^0.1.4"
    xtend "^4.0.1"

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/wrap-ansi/-/wrap-ansi-2.1.0.tgz#d8fc3d284dd05794fe84973caecdd1cf824fdd85"
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"

write-file-atomic@^1.2.0:
  version "1.3.4"
  resolved "https://registry.yarnpkg.com/write-file-atomic/-/write-file-atomic-1.3.4.tgz#f807a4f0b1d9e913ae7a48112e6cc3af1991b45f"
  dependencies:
    graceful-fs "^4.1.11"
    imurmurhash "^0.1.4"
    slide "^1.1.5"

ws@^1.1.0:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/ws/-/ws-1.1.4.tgz#57f40d036832e5f5055662a397c4de76ed66bf61"
  dependencies:
    options ">=0.0.5"
    ultron "1.0.x"

ws@^2.2.2:
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/ws/-/ws-2.3.1.tgz#6b94b3e447cb6a363f785eaf94af6359e8e81c80"
  dependencies:
    safe-buffer "~5.0.1"
    ultron "~1.1.0"

xcode@^0.8.9:
  version "0.8.9"
  resolved "https://registry.yarnpkg.com/xcode/-/xcode-0.8.9.tgz#ec6765f70e9dccccc9f6e9a5b9b4e7e814b4cf35"
  dependencies:
    node-uuid "1.4.7"
    pegjs "0.9.0"
    simple-plist "0.1.4"

xml-name-validator@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/xml-name-validator/-/xml-name-validator-2.0.1.tgz#4d8b8f1eccd3419aa362061becef515e1e559635"

xmlbuilder@4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/xmlbuilder/-/xmlbuilder-4.0.0.tgz#98b8f651ca30aa624036f127d11cc66dc7b907a3"
  dependencies:
    lodash "^3.5.0"

xmldoc@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/xmldoc/-/xmldoc-0.4.0.tgz#d257224be8393eaacbf837ef227fd8ec25b36888"
  dependencies:
    sax "~1.1.1"

xmldom@0.1.x:
  version "0.1.27"
  resolved "https://registry.yarnpkg.com/xmldom/-/xmldom-0.1.27.tgz#d501f97b3bdb403af8ef9ecc20573187aadac0e9"

xtend@^4.0.0, xtend@^4.0.1, xtend@~4.0.1:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/xtend/-/xtend-4.0.1.tgz#a5c6d532be656e23db820efb943a1f04998d63af"

y18n@^3.2.1:
  version "3.2.1"
  resolved "https://registry.yarnpkg.com/y18n/-/y18n-3.2.1.tgz#6d15fba884c08679c0d77e88e7759e811e07fa41"

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/yallist/-/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"

yargs-parser@^4.2.0:
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/yargs-parser/-/yargs-parser-4.2.1.tgz#29cceac0dc4f03c6c87b4a9f217dd18c9f74871c"
  dependencies:
    camelcase "^3.0.0"

yargs-parser@^5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/yargs-parser/-/yargs-parser-5.0.0.tgz#275ecf0d7ffe05c77e64e7c86e4cd94bf0e1228a"
  dependencies:
    camelcase "^3.0.0"

yargs@^6.0.0, yargs@^6.4.0:
  version "6.6.0"
  resolved "https://registry.yarnpkg.com/yargs/-/yargs-6.6.0.tgz#782ec21ef403345f830a808ca3d513af56065208"
  dependencies:
    camelcase "^3.0.0"
    cliui "^3.2.0"
    decamelize "^1.1.1"
    get-caller-file "^1.0.1"
    os-locale "^1.4.0"
    read-pkg-up "^1.0.1"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^1.0.2"
    which-module "^1.0.0"
    y18n "^3.2.1"
    yargs-parser "^4.2.0"

yargs@^7.0.2:
  version "7.1.0"
  resolved "https://registry.yarnpkg.com/yargs/-/yargs-7.1.0.tgz#6ba318eb16961727f5d284f8ea003e8d6154d0c8"
  dependencies:
    camelcase "^3.0.0"
    cliui "^3.2.0"
    decamelize "^1.1.1"
    get-caller-file "^1.0.1"
    os-locale "^1.4.0"
    read-pkg-up "^1.0.1"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^1.0.2"
    which-module "^1.0.0"
    y18n "^3.2.1"
    yargs-parser "^5.0.0"

yargs@~3.10.0:
  version "3.10.0"
  resolved "https://registry.yarnpkg.com/yargs/-/yargs-3.10.0.tgz#f7ee7bd857dd7c1d2d38c0e74efbd681d1431fd1"
  dependencies:
    camelcase "^1.0.2"
    cliui "^2.1.0"
    decamelize "^1.0.0"
    window-size "0.1.0"
