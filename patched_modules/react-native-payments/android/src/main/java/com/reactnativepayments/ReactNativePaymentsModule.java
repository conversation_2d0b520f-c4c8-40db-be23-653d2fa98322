package com.reactnativepayments;

import android.view.WindowManager;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.annotation.NonNull;
import android.app.Fragment;
import android.app.FragmentManager;
import androidx.annotation.RequiresPermission;
import android.util.Log;

import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactBridge;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMapKeySetIterator;
import com.google.android.gms.common.GoogleApiAvailability;
import com.google.android.gms.common.api.GoogleApiClient;
import com.google.android.gms.common.api.BooleanResult;
import com.google.android.gms.common.api.ResultCallback;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.identity.intents.model.UserAddress;
import com.google.android.gms.wallet.*;
import com.google.android.gms.common.api.ApiException;
import com.google.android.gms.common.api.Status;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;

import com.facebook.react.bridge.ActivityEventListener;
import com.facebook.react.bridge.BaseActivityEventListener;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableNativeArray;
import com.facebook.react.bridge.WritableNativeMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class ReactNativePaymentsModule extends ReactContextBaseJavaModule {
    private static final int LOAD_MASKED_WALLET_REQUEST_CODE = 88;


    // Payments Client
    private PaymentsClient mPaymentsClient;

    // Callbacks
    private static Callback mShowSuccessCallback = null;
    private static Callback mShowErrorCallback = null;

    public static final String REACT_CLASS = "ReactNativePayments";

    private static ReactApplicationContext reactContext = null;

    private final ActivityEventListener mActivityEventListener = new BaseActivityEventListener() {
        @Override
        public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
            // retrieve the error code, if available
            int errorCode = -1;
            if (data != null) {
                errorCode = data.getIntExtra(WalletConstants.EXTRA_ERROR_CODE, -1);
            }

            switch (requestCode) {
                case LOAD_MASKED_WALLET_REQUEST_CODE:
                    switch (resultCode) {
                        case Activity.RESULT_OK:
                            if (data != null) {

                                PaymentData paymentData = PaymentData.getFromIntent(data);
                                String paymentInformation = paymentData.toJson();

                                Log.i(REACT_CLASS, "GOOGLE PAY SUCCESS " + paymentInformation);

                                JSONObject paymentMethodData;

                                try {
                                    paymentMethodData = new JSONObject(paymentInformation).getJSONObject("paymentMethodData");


                                    WritableNativeMap paymentDetails = new WritableNativeMap();
                                    paymentDetails.putString("description", paymentMethodData.getString("description"));

                                    WritableNativeMap tokenizationData = new WritableNativeMap();
                                    JSONObject tokenizationDataJson = paymentMethodData.getJSONObject("tokenizationData");
                                    tokenizationData.putString("type", tokenizationDataJson.getString("type"));
                                    tokenizationData.putString("token", tokenizationDataJson.getString("token"));
                                    paymentDetails.putMap("tokenizationData", tokenizationData);


                                    WritableNativeMap paymentToken = new WritableNativeMap();

                                    JSONObject paymentTokenJson = new JSONObject(tokenizationDataJson.getString("token"));

                                    if (tokenizationDataJson.getString("type").equals("PAYMENT_GATEWAY")) {
                                        try {
                                            // stripe
                                            String id = paymentTokenJson.getString("id");
                                            paymentToken.putString("id", id);
                                        } catch (JSONException e) {
                                            paymentToken.putString("tokenizationType", tokenizationDataJson.getString("type"));
                                            paymentToken.putString("token", tokenizationDataJson.getString("token"));
                                            
                                            paymentToken.putString("raw", paymentInformation);
                                        }
                                        
                                    } else {
                                        String protocolVersion = paymentTokenJson.getString("protocolVersion");
                                        String signature = paymentTokenJson.getString("signature");
                                        String signedMessage = paymentTokenJson.getString("signedMessage");

                                        paymentToken.putString("protocolVersion", protocolVersion);
                                        paymentToken.putString("signature", signature);
                                        paymentToken.putString("signedMessage", signedMessage);
                                    }
                                    

                                    paymentDetails.putMap("paymentToken", paymentToken);

                                    
                                    paymentDetails.putString("type", paymentMethodData.getString("type"));

                                    WritableNativeMap info = new WritableNativeMap();
                                    JSONObject infoJson = paymentMethodData.getJSONObject("info");
                                    info.putString("cardNetwork", infoJson.getString("cardNetwork"));
                                    info.putString("cardDetails", infoJson.getString("cardDetails"));
                                    paymentDetails.putMap("cardInfo", info);
                                    
                                    sendEvent(reactContext, "NativePayments:onuseraccept", paymentDetails);

                                    // Logging token string.
                                    // Log.d("GooglePaymentToken", paymentMethodData.getJSONObject("tokenizationData").getString("token"));
                                  } catch (JSONException e) {
                                    Log.e("handlePaymentSuccess", "Error: " + e.toString());
                                    return;
                                  }

                            }
                            break;
                        case Activity.RESULT_CANCELED:
                            sendEvent(reactContext, "NativePayments:onuserdismiss", null);

                            break;
                        default:
                            Log.i(REACT_CLASS, "GOOGLE PAY ERROR " + errorCode);
                            mShowErrorCallback.invoke(errorCode);
                            sendEvent(reactContext, "NativePayments:onuserdismiss", null);

                            break;
                    }
                    break;
                case WalletConstants.RESULT_ERROR:activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_SECURE);
//                    handleError(errorCode);
                    break;

                default:
                    super.onActivityResult(requestCode, resultCode, data);
                    break;
            }
        }
    };

    public ReactNativePaymentsModule(ReactApplicationContext context) {
        // Pass in the context to the constructor and save it so you can emit events
        // https://facebook.github.io/react-native/docs/native-modules-android.html#the-toast-module
        super(context);

        reactContext = context;

        reactContext.addActivityEventListener(mActivityEventListener);
    }

    @Override
    public String getName() {
        // Tell React the name of the module
        // https://facebook.github.io/react-native/docs/native-modules-android.html#the-toast-module
        return REACT_CLASS;
    }

    // Public Methods
    // ---------------------------------------------------------------------------------------------
    @ReactMethod
    public void getSupportedGateways(Callback errorCallback, Callback successCallback) {
        WritableNativeArray supportedGateways = new WritableNativeArray();

        successCallback.invoke(supportedGateways);
    }

    @ReactMethod
    public void canMakePayments(ReadableMap paymentMethodData, final Callback errorCallback, final Callback successCallback) {

        try {

            GoogleApiAvailability googleApiAvailability = GoogleApiAvailability.getInstance();
            int resultCode = googleApiAvailability.isGooglePlayServicesAvailable(getReactApplicationContext());
            if(resultCode != ConnectionResult.SUCCESS) {
                successCallback.invoke(false);
                return;
            }
            
            JSONObject isReadyToPayRequest = getBaseRequest();
            isReadyToPayRequest.put("allowedPaymentMethods", new JSONArray().put(getBaseCardPaymentMethod(paymentMethodData)));
            isReadyToPayRequest.put("existingPaymentMethodRequired", true);

            IsReadyToPayRequest request = IsReadyToPayRequest.fromJson(isReadyToPayRequest.toString());

            int environment = getEnvironmentFromPaymentMethodData(paymentMethodData);
            createPaymentsClient(getCurrentActivity(), environment);

            Task<Boolean> task = mPaymentsClient.isReadyToPay(request);
            task.addOnCompleteListener(
                new OnCompleteListener<Boolean>() {
                    @Override
                    public void onComplete(@NonNull Task<Boolean> task) {
                        boolean result = task.isSuccessful();
                        if (result) {
                            successCallback.invoke(task.getResult());
                        } else {
                            Log.w("isReadyToPay failed", task.getException());
                            Exception exception = task.getException();
                            if (exception != null) {
                                errorCallback.invoke(exception.getMessage());
                            } else {
                                errorCallback.invoke("payment failed");
                            }
                        }
                    }
            });
        } catch (Exception e) {
            errorCallback.invoke(e.getMessage());
        }
    }

    @ReactMethod
    public void abort(Callback errorCallback, Callback successCallback) {
        Log.i(REACT_CLASS, "GOOGLE PAY ABORT" + getCurrentActivity().toString());
        successCallback.invoke();
    }

    @ReactMethod
    public void show(
            ReadableMap paymentMethodData,
            ReadableMap details,
            ReadableMap options,
            Callback errorCallback,
            Callback successCallback
    ) {
        mShowSuccessCallback = successCallback;
        mShowErrorCallback = errorCallback;

        Log.i(REACT_CLASS, "GOOGLE PAY SHOW" + options);

        Boolean shouldRequestShipping = options.hasKey("requestShipping") && options.getBoolean("requestShipping")
                        || options.hasKey("requestPayerName") && options.getBoolean("requestPayerName")
                        || options.hasKey("requestPayerPhone") && options.getBoolean("requestPayerPhone");
        Boolean shouldRequestPayerPhone = options.hasKey("requestPayerPhone") && options.getBoolean("requestPayerPhone");


        ReadableMap total = details.getMap("total").getMap("amount");

        try {
            JSONObject paymentDataRequest = getBaseRequest();
            paymentDataRequest.put("allowedPaymentMethods", new JSONArray().put(getCardPaymentMethod(paymentMethodData)));
            paymentDataRequest.put("transactionInfo", getTransactionInfo(total));
            paymentDataRequest.put("merchantInfo", getMerchantInfo());


            paymentDataRequest.put("shippingAddressRequired", shouldRequestShipping);
            
            if (shouldRequestShipping) {
                JSONObject shippingAddressParameters = new JSONObject();
                shippingAddressParameters.put("phoneNumberRequired", shouldRequestPayerPhone);

                // JSONArray allowedCountryCodes = new JSONArray(Constants.SHIPPING_SUPPORTED_COUNTRIES);
                // shippingAddressParameters.put("allowedCountryCodes", allowedCountryCodes);
                paymentDataRequest.put("shippingAddressParameters", shippingAddressParameters);
            }
            
            PaymentDataRequest request =
                PaymentDataRequest.fromJson(paymentDataRequest.toString());
            
            int environment = getEnvironmentFromPaymentMethodData(paymentMethodData);
            createPaymentsClient(getCurrentActivity(), environment);
    
    
            AutoResolveHelper.resolveTask(
                mPaymentsClient.loadPaymentData(request), getCurrentActivity(), LOAD_MASKED_WALLET_REQUEST_CODE);
        } catch (Exception e) {
            errorCallback.invoke(e.getMessage());
        }
        
    }

    // Private Method
    // ---------------------------------------------------------------------------------------------
    
    private static JSONObject getBaseRequest() throws JSONException {
        return new JSONObject().put("apiVersion", 2).put("apiVersionMinor", 0);
    }

    private static JSONArray getAllowedCardAuthMethods(ReadableArray allowedAuthMethods) {
        JSONArray res = new JSONArray();

        int size = allowedAuthMethods.size();
        for (int i = 0; i < size; ++i) {
            String allowedAuthMethod = allowedAuthMethods.getString(i);
            switch (allowedAuthMethod) {
                case "panOnly":
                    res.put("PAN_ONLY");
                    break;
                case "cryptogram3ds":
                    res.put("CRYPTOGRAM_3DS");
                    break;
                default:
            }
        }

        return res;
    }

    private static JSONArray getAllowedCardNetworks(ReadableArray allowedCardNetworks) {
        JSONArray res = new JSONArray();

        int size = allowedCardNetworks.size();
        for (int i = 0; i < size; ++i) {
            String allowedCardNetwork = allowedCardNetworks.getString(i);
            switch (allowedCardNetwork) {
                case "visa":
                    res.put("VISA");
                    break;
                case "mastercard":
                    res.put("MASTERCARD");
                    break;
                case "amex":
                    res.put("AMEX");
                    break;
                case "discover":
                    res.put("DISCOVER");
                    break;
                case "interac":
                    res.put("INTERAC");
                    break;
                case "jcb":
                    res.put("JCB");
                    break;
                default:
                    res.put("OTHER");
            }
        }

        return res;
    }

    private static JSONObject getBaseCardPaymentMethod(ReadableMap paymentMethodData) throws JSONException {
        JSONObject cardPaymentMethod = new JSONObject();
        cardPaymentMethod.put("type", "CARD");

        JSONObject parameters = new JSONObject();

        ReadableArray allowedAuthMethods = paymentMethodData.getArray("allowedAuthMethods");
        if (allowedAuthMethods != null) {
            parameters.put("allowedAuthMethods", getAllowedCardAuthMethods(allowedAuthMethods));
        }
        ReadableArray allowedCardNetworks = paymentMethodData.getArray("supportedNetworks");
        if (allowedCardNetworks != null) {
            parameters.put("allowedCardNetworks", getAllowedCardNetworks(allowedCardNetworks));
        }

        cardPaymentMethod.put("parameters", parameters);

        return cardPaymentMethod;
    }

    private static JSONObject getGatewayTokenizationSpecification(ReadableMap tokenizationParameters) throws JSONException {
        String tokenizationType = tokenizationParameters.getString("tokenizationType");
        ReadableMap parameters = tokenizationParameters.getMap("parameters");
        
        JSONObject res = new JSONObject();

        if (tokenizationType.equals("GATEWAY_TOKEN")) {
            res.put("type", "PAYMENT_GATEWAY");

            JSONObject param = new JSONObject();
            param.put("gateway", parameters.getString("gateway"));
            
            ReadableMapKeySetIterator iterator = parameters.keySetIterator();

            while (iterator.hasNextKey()) {
                String key = iterator.nextKey();
                param.put(key, parameters.getString(key));
            }

            res.put("parameters", param);
        } else {
            res.put("type", "DIRECT");
            JSONObject param = new JSONObject();
            param.put("protocolVersion", "ECv2");
            param.put("publicKey", parameters.getString("publicKey"));

            res.put("parameters", param);
        }

        return res;
    }

    private static JSONObject getCardPaymentMethod(ReadableMap paymentMethodData) throws JSONException {
        JSONObject cardPaymentMethod = getBaseCardPaymentMethod(paymentMethodData);

        ReadableMap tokenizationParameters = paymentMethodData.getMap("paymentMethodTokenizationParameters");
        cardPaymentMethod.put("tokenizationSpecification", getGatewayTokenizationSpecification(tokenizationParameters));

        return cardPaymentMethod;
    }

    private static JSONObject getTransactionInfo(ReadableMap total) throws JSONException {
        JSONObject transactionInfo = new JSONObject();

        transactionInfo.put("totalPriceStatus", "FINAL");
        transactionInfo.put("totalPrice", total.getString("value"));
        transactionInfo.put("currencyCode", total.getString("currency"));
    
        return transactionInfo;
    }

    private static JSONObject getMerchantInfo() throws JSONException {
        return new JSONObject().put("merchantName", "Example Merchant");
    }

    
    private void sendEvent(
            ReactApplicationContext reactContext,
            String eventName,
            @Nullable WritableNativeMap params
    ) {
        reactContext
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                .emit(eventName, params);
    }

    private int getEnvironmentFromPaymentMethodData(ReadableMap paymentMethodData) {
        return paymentMethodData.hasKey("environment") && paymentMethodData.getString("environment").equals("TEST")
                ? WalletConstants.ENVIRONMENT_TEST
                : WalletConstants.ENVIRONMENT_PRODUCTION;
    }

    protected void createPaymentsClient(Activity activity, int environment) {
        Wallet.WalletOptions walletOptions =
            new Wallet.WalletOptions.Builder().setEnvironment(environment).build();
        mPaymentsClient =  Wallet.getPaymentsClient(activity, walletOptions);
    }
}
