/* 
  InfoPlist.strings
  Perkd

  Created by <PERSON> on 22/11/19.
  Copyright © 2019 Facebook. All rights reserved.
*/
NSCalendarsUsageDescription = "Secara otomatis menyimpan tanggal dan mengatur pengingat kalender";
NSCameraUsageDescription = "Untuk memindai kode QR, barcode atau mengambil foto kartu";
NSContactsUsageDescription =  "Cari kontak untuk berbagi kartu, penawaran, atau pesan";
NSFaceIDUsageDescription = "Gunakan ID Wajah untuk masuk ke akun anda lebih cepat dan aman";
NSLocationWhenInUseUsageDescription = "Sortir & tampilkan kartu dapat digunakan di dekat berdasarkan lokasi anda, dan sambung ke Wi-Fi";
NSLocationAlwaysUsageDescription = "Sortir & tampilkan kartu dapat digunakan di dekat berdasarkan lokasi anda, dan sambung ke Wi-Fi";
NSLocationUsageDescription = "Sortir & tampilkan kartu dapat digunakan di dekat berdasarkan lokasi anda, dan sambung ke Wi-Fi";
NSPhotoLibraryUsageDescription =  "Untuk memindai kode QR, barcode atau mengunggah gambar kartu";