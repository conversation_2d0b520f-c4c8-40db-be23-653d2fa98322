#import "AppDelegate.h"

#import <React/RCTBridge.h>
#import <React/RCTBundleURLProvider.h>
#import <React/RCTRootView.h>
#import <React/RCTLinkingManager.h>
#import <RNCPushNotificationIOS.h>
#import <ReactNativeNavigation/ReactNativeNavigation.h>
//#import "Notifications.h"
#import <TSBackgroundFetch/TSBackgroundFetch.h>
#import <UserNotifications/UserNotifications.h>
#import <Bugsnag/Bugsnag.h>
#import "RCTSpotlightSearch.h"

#import <SDWebImage/SDImageCache.h>

#ifdef DEBUG
#ifdef FB_SONARKIT_ENABLED
#import <FlipperKit/FlipperClient.h>
#import <FlipperKitNetworkPlugin/FlipperKitNetworkPlugin.h>
#import <FlipperKitLayoutPlugin/FlipperKitLayoutPlugin.h>
#import <FlipperKitUserDefaultsPlugin/FKUserDefaultsPlugin.h>
#import <SKIOSNetworkPlugin/SKIOSNetworkAdapter.h>
#import <FlipperKitReactPlugin/FlipperKitReactPlugin.h>
#import <React/RCTDevLoadingView.h>

static void InitializeFlipper(UIApplication *application) {
  FlipperClient *client = [FlipperClient sharedClient];
  SKDescriptorMapper *layoutDescriptorMapper = [[SKDescriptorMapper alloc] initWithDefaults];
  [client addPlugin:[[FlipperKitLayoutPlugin alloc] initWithRootNode:application withDescriptorMapper:layoutDescriptorMapper]];
  [client addPlugin:[[FKUserDefaultsPlugin alloc] initWithSuiteName:nil]];
  [client addPlugin:[FlipperKitReactPlugin new]];
  [client addPlugin:[[FlipperKitNetworkPlugin alloc] initWithNetworkAdapter:[SKIOSNetworkAdapter new]]];
  [client start];
}
#endif
#endif

@import Firebase;
@import Stripe;

@implementation LaunchPerfData

- (NSDictionary*) data
{
  return @{
    @"applicationInited": @(_applicationInited),
    @"applicationLaunched": @(_applicationLaunched),
    @"javascriptWillLoad": @(_javascriptWillLoad),
    @"javascriptLoaded": @(_javascriptLoaded)
  };
}

@end

@implementation AppDelegate

- (instancetype) init
{
  self = [super init];
  if (self) {
    _launchPerfData = [LaunchPerfData new];
    _launchPerfData.applicationInited = CACurrentMediaTime() * 1000;
  }
  return self;
}

- (BOOL)application:(UIApplication *)application willFinishLaunchingWithOptions:(NSDictionary<UIApplicationLaunchOptionsKey,id> *)launchOptions
{
  NSLog(@"willFinishLaunchingWithOptions");
  return YES;
}

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  NSLog(@"didFinishLaunchingWithOptions");
#ifdef DEBUG
  #ifdef FB_SONARKIT_ENABLED
    InitializeFlipper(application);
  #endif
#endif
  
  NSArray<NSString *> *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
  SDImageCache.defaultDiskCacheDirectory = [paths.firstObject stringByAppendingPathComponent:@"images/com.hackemist.SDImageCache"];
  
  [SDImageCache sharedImageCache].config.maxDiskAge = -1;

  [[NSNotificationCenter defaultCenter] addObserver:self
                       selector:@selector(onJavaScriptLoaded)
                         name:RCTJavaScriptDidLoadNotification
                         object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self
                       selector:@selector(onJavaScriptWillLoad)
                         name:RCTJavaScriptWillStartLoadingNotification
                         object:nil];

  _launchPerfData.applicationLaunched = CACurrentMediaTime() * 1000;
  
  [FIRApp configure];
  
//  [Notifications configure];
  
  [ReactNativeNavigation bootstrapWithDelegate:self launchOptions:launchOptions];
  #ifdef DEBUG
    [[ReactNativeNavigation getBridge] moduleForClass:[RCTDevLoadingView class]];
  #endif
//  NSURL *jsCodeLocation = [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index" fallbackResource:nil];
//    [ReactNativeNavigation bootstrap:jsCodeLocation launchOptions:launchOptions];
  
  // [REQUIRED] Register BackgroundFetch
  [[TSBackgroundFetch sharedInstance] didFinishLaunching];
  
  [Bugsnag start];
  
  NSArray *docPaths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
  NSString *docPath = [docPaths objectAtIndex:0];
    
  [[NSFileManager defaultManager] setAttributes:@{NSFileProtectionKey: NSFileProtectionCompleteUntilFirstUserAuthentication} ofItemAtPath:docPath error:nil];

  
  // Define UNUserNotificationCenter
  UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
  center.delegate = self;
  
  return YES;
}

- (void)applicationDidBecomeActive:(UIApplication *)application {
//  [FBSDKAppEvents activateApp];
}

- (void)applicationDidEnterBackground:(UIApplication *)application
{
  UIBackgroundTaskIdentifier taskId = [application beginBackgroundTaskWithExpirationHandler:nil];
  dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
    // do nothing for 5 seconds
    [NSThread sleepForTimeInterval:5.0f];
    [application endBackgroundTask:taskId];
  });
}

- (BOOL)application:(UIApplication *)application
            openURL:(NSURL *)url
            options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options
{
  BOOL stripeHandled = [StripeAPI handleStripeURLCallbackWithURL:url];
  if (stripeHandled) {
      return YES;
  } else {
      return [RCTLinkingManager application:application openURL:url options:options];
  }
  return NO;
}

- (BOOL)application:(UIApplication *)application continueUserActivity:(nonnull NSUserActivity *)userActivity
 restorationHandler:(nonnull void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler
{
  [RCTSpotlightSearch handleContinueUserActivity:userActivity];
  return [RCTLinkingManager application:application
                   continueUserActivity:userActivity
                     restorationHandler:restorationHandler];
}


- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
#if DEBUG
  return [NSURL URLWithString:[[[[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index" fallbackResource:nil] absoluteString] stringByAppendingString:@"&inlineSourceMap=true" ]];
#else
  return [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
#endif
}

// Required for the register event.
- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken
{
  [RNCPushNotificationIOS didRegisterForRemoteNotificationsWithDeviceToken:deviceToken];
}

// Required for the registrationError event.
- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error
{
  [RNCPushNotificationIOS didFailToRegisterForRemoteNotificationsWithError:error];
}

// Required for the notification event. You must call the completion handler after handling the remote notification.
- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo
fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler
{
  [RNCPushNotificationIOS didReceiveRemoteNotification:userInfo fetchCompletionHandler:completionHandler];
}

// IOS 10+ Required for localNotification event
- (void)userNotificationCenter:(UNUserNotificationCenter *)center
didReceiveNotificationResponse:(UNNotificationResponse *)response
         withCompletionHandler:(void (^)(void))completionHandler
{
  
  // schedule background  (response.notification.request.trigger UNCalendarNotificationTrigger)
  [RNCPushNotificationIOS didReceiveNotificationResponse:response];
  completionHandler();
}

//Called when a notification is delivered to a foreground app.

- (void)onJavaScriptWillLoad {
  NSLog(@"onJavaScriptWillLoad");
  _launchPerfData.javascriptWillLoad = CACurrentMediaTime() * 1000;
}

- (void)onJavaScriptLoaded {
  NSLog(@"onJavaScriptLoaded");
  _launchPerfData.javascriptLoaded = CACurrentMediaTime() * 1000;
#if DEBUG
  _window.rootViewController.view.backgroundColor = UIColor.blueColor;
#endif
}

- (NSArray<id<RCTBridgeModule>> *)extraModulesForBridge:(RCTBridge *)bridge {
	return [ReactNativeNavigation extraModulesForBridge:bridge];
}


@end
