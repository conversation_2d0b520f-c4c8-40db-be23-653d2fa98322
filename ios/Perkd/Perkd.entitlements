<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>aps-environment</key>
	<string>development</string>
	<key>com.apple.developer.associated-domains</key>
	<array>
		<string>applinks:app.perkd.me</string>
		<string>applinks:menu.perkd.me</string>
		<string>applinks:card.perkd.me</string>
		<string>applinks:receipt.perkd.me</string>
		<string>applinks:qr.perkd.me</string>
		<string>applinks:perkd.app</string>
	</array>
	<key>com.apple.developer.default-data-protection</key>
	<string>NSFileProtectionCompleteUntilFirstUserAuthentication</string>
	<key>com.apple.developer.in-app-payments</key>
	<array>
		<string>merchant.me.perkd</string>
		<string>merchant.me.perkd.dev</string>
		<string>merchant.mypay.me.perkd</string>
		<string>merchant.razer.me.perkd</string>
	</array>
	<key>com.apple.developer.networking.wifi-info</key>
	<true/>
	<key>com.apple.developer.nfc.readersession.formats</key>
	<array>
		<string>TAG</string>
	</array>
	<key>com.apple.security.application-groups</key>
	<array>
		<string>group.me.perkd.notify</string>
	</array>
	<key>keychain-access-groups</key>
	<array>
		<string>$(AppIdentifierPrefix)me.perkd</string>
	</array>
</dict>
</plist>
