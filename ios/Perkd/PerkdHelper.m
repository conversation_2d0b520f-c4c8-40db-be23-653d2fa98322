//
//  PerkdHelper.m
//  Perkd
//
//  Created by <PERSON> on 31/12/19.
//  Copyright © 2019 Facebook. All rights reserved.
//

#import "PerkdHelper.h"
#import <React/RCTLog.h>
#import <React/RCTUtils.h>
#import <CoreHaptics/CoreHaptics.h>
#import "AppDelegate.h"

@import CoreTelephony;

@interface PerkdHelper ()

@property (strong, nonatomic) CTCellularData* cellularData;

@property (assign, nonatomic) CTCellularDataRestrictedState cellularDataRestrictedState;

@end

@implementation PerkdHelper

- (instancetype) init
{
  self = [super init];
  if (self) {
    RCTLogInfo(@"PerkdHelper Init");
    _cellularData = [[CTCellularData alloc] init];
    __block PerkdHelper* weakSelf = self;
    _cellularDataRestrictedState = kCTCellularDataRestrictedStateUnknown;
    _cellularData.cellularDataRestrictionDidUpdateNotifier = ^(CTCellularDataRestrictedState state)
    {
      //获取联网状态
      RCTLogInfo(@"cellularDataRestrictionDidUpdateNotifier %@", @(state));
      weakSelf.cellularDataRestrictedState = state;
    };
  }
  return self;
}

- (NSDictionary*) constantsToExport {
  if (@available(iOS 13.0, *)) {
    return @{@"supportsHaptics": @(CHHapticEngine.capabilitiesForHardware.supportsHaptics)};
  } else {
    return @{@"supportsHaptics": @([[[UIDevice currentDevice] valueForKey:@"_feedbackSupportLevel"] isEqual:@(2)])};
  }
}

+ (BOOL)requiresMainQueueSetup
{
   return YES;
}

RCT_EXPORT_MODULE(PerkdHelper)

RCT_REMAP_METHOD(checkCellularDataRestriction,
  resolver:(RCTPromiseResolveBlock)resolve
  rejecter:(RCTPromiseRejectBlock)reject
) {
    resolve(@(_cellularDataRestrictedState));
}

RCT_EXPORT_METHOD(getNativeStartupTime:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject)
{
  RCTExecuteOnMainQueue(^{
    LaunchPerfData* launchPerfData = ((AppDelegate *)[[UIApplication sharedApplication] delegate]).launchPerfData;
    resolve(@(launchPerfData.applicationInited));
  });
}

RCT_EXPORT_METHOD(getSignature:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject)
{
  resolve(@"nQZt9i92PlpR");
}

- (void)invalidate
{
  _cellularData.cellularDataRestrictionDidUpdateNotifier = nil;
  _cellularData = nil;
}

@end
