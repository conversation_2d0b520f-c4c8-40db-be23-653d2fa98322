<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>com.transistorsoft.fetch</string>
	</array>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundleDocumentTypes</key>
	<array/>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>perkd</string>
				<string>fb348007068548134</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FacebookAdvertiserIDCollectionEnabled</key>
	<string>FALSE</string>
	<key>FacebookAppID</key>
	<string>348007068548134</string>
	<key>FacebookAutoLogAppEventsEnabled</key>
	<string>TRUE</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>Perkd</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>comgooglemaps</string>
		<string>maps</string>
		<string>iosamap</string>
		<string>baidumap</string>
		<string>whatsapp</string>
		<string>line</string>
		<string>wechat</string>
		<string>weixin</string>
		<string>fb-messenger-public</string>
		<string>fb</string>
		<string>instagram</string>
		<string>twitter</string>
		<string>sinaweibo</string>
		<string>vnd.youtube</string>
		<string>zalora</string>
		<string>shopeesg</string>
		<string>shopeetw</string>
		<string>shopeemy</string>
		<string>lazada</string>
		<string>pomelofashion</string>
		<string>asos</string>
		<string>taobao</string>
		<string>tmall</string>
		<string>com.amazon.mobile.shopping.web</string>
		<string>arrive</string>
		<string>grab</string>
		<string>foodpanda</string>
		<string>deliveroo</string>
		<string>ubereats</string>
		<string>uber</string>
		<string>gojek</string>
		<string>gpay</string>
		<string>paypal</string>
		<string>alipay</string>
		<string>alipayhk</string>
		<string>apaylater</string>
		<string>mwallet</string>
		<string>dbspaylah</string>
		<string>ocbcpao</string>
		<string>jkos</string>
		<string>alley</string>
		<string>mightysgapp</string>
		<string>TNGDWallet</string>
		<string>netspay</string>
		<string>boostapp</string>
		<string>hsbcpaymeapp</string>
		<string>venmo</string>
		<string>com-pxmart-pxpay</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NFCReaderUsageDescription</key>
	<string>Please allow access to your NFC to use this feature</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Please allow access to your Bluetooth to use this feature</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>Please allow access to your Bluetooth to use this feature</string>
	<key>NSCalendarsFullAccessUsageDescription</key>
	<string>Please allow access to your Calendars to use this feature</string>
	<key>NSCalendarsUsageDescription</key>
	<string>Please allow access to your Calendars to use this feature</string>
	<key>NSCameraUsageDescription</key>
	<string>Please allow access to your Camera to scan barcode/QR code</string>
	<key>NSContactsUsageDescription</key>
	<string>Please allow access to your Contacts to use this feature</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Enabling Face ID allows you quick and secure access to your account.</string>
	<key>NSLocationTemporaryUsageDescriptionDictionary</key>
	<dict>
		<key>Purpose</key>
		<string>To sort your cards by locations, show nearby stores and connect to in-store Wi-Fi.</string>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>To sort your cards by locations, show nearby stores and connect to in-store Wi-Fi.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>To sort your cards by locations, show nearby stores and connect to in-store Wi-Fi.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>For scanning QR Codes, barcodes or uploading images of your cards</string>
	<key>UIAppFonts</key>
	<array>
		<string>MaterialIcons.ttf</string>
		<string>Melbourne.otf</string>
		<string>Melbourne_bold.otf</string>
		<string>MelbourneMono.otf</string>
		<string>picon.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
	<key>bugsnag</key>
	<dict>
		<key>apiKey</key>
		<string>c6e0b29f43d58e0741887ab6930fd87b</string>
	</dict>
	<key>com.apple.developer.nfc.readersession.iso7816.select-identifiers</key>
	<array>
		<string>D2760000850100</string>
		<string>D2760000850101</string>
	</array>
</dict>
</plist>
