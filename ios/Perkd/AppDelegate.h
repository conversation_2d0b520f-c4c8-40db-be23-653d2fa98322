#import <React/RCTBridgeDelegate.h>
#import <UIKit/UIKit.h>
#import <UserNotifications/UNUserNotificationCenter.h>

@interface LaunchPerfData : NSObject

@property (assign, nonatomic) double applicationInited;
@property (assign, nonatomic) double applicationLaunched;
@property (assign, nonatomic) double javascriptWillLoad;
@property (assign, nonatomic) double javascriptLoaded;

- (NSDictionary*) data;

@end


@interface AppDelegate : UIResponder <UIApplicationDelegate, RCTBridgeDelegate, UNUserNotificationCenterDelegate>

@property (nonatomic, strong) UIWindow *window;

@property (nonatomic, assign) double startupTime; // in millisecond

@property (nonatomic, strong) LaunchPerfData* launchPerfData;

@end
