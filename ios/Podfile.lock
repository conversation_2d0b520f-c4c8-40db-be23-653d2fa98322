PODS:
  - boost (1.76.0)
  - BugsnagReactNative (7.20.2):
    - React-Core
  - BVLinearGradient (2.8.3):
    - React-Core
  - CocoaAsyncSocket (7.6.5)
  - CryptoSwift (1.8.4)
  - DACircularProgress (2.3.1)
  - DoubleConversion (1.1.6)
  - FBAEMKit (15.0.0):
    - FBSDKCoreKit_Basics (= 15.0.0)
  - FBLazyVector (0.67.5)
  - FBReactNativeSpec (0.67.5):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.67.5)
    - RCTTypeSafety (= 0.67.5)
    - React-Core (= 0.67.5)
    - React-jsi (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - FBSDKCoreKit (15.0.0):
    - FBAEMKit (= 15.0.0)
    - FBSDKCoreKit_Basics (= 15.0.0)
  - FBSDKCoreKit_Basics (15.0.0)
  - FBSDKGamingServicesKit (15.0.0):
    - FBSDKCoreKit (= 15.0.0)
    - FBSDKCoreKit_Basics (= 15.0.0)
    - FBSDKShareKit (= 15.0.0)
  - FBSDKLoginKit (15.0.0):
    - FBSDKCoreKit (= 15.0.0)
  - FBSDKShareKit (15.0.0):
    - FBSDKCoreKit (= 15.0.0)
  - Firebase/CoreOnly (8.15.0):
    - FirebaseCore (= 8.15.0)
  - FirebaseCore (8.15.0):
    - FirebaseCoreDiagnostics (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
  - FirebaseCoreDiagnostics (8.15.0):
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
    - nanopb (~> 2.30908.0)
  - Flipper (0.171.1):
    - Flipper-Folly (~> 2.6)
  - Flipper-Boost-iOSX (********.11)
  - Flipper-DoubleConversion (3.1.7)
  - Flipper-Fmt (7.1.7)
  - Flipper-Folly (2.6.7):
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt (= 7.1.7)
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.180)
  - Flipper-Glog (0.3.6)
  - Flipper-PeerTalk (0.0.4)
  - Flipper-RSocket (1.4.3):
    - Flipper-Folly (~> 2.6)
  - FlipperKit (0.171.1):
    - FlipperKit/Core (= 0.171.1)
  - FlipperKit/Core (0.171.1):
    - Flipper (~> 0.171.1)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
    - SocketRocket (~> 0.6.0)
  - FlipperKit/CppBridge (0.171.1):
    - Flipper (~> 0.171.1)
  - FlipperKit/FBCxxFollyDynamicConvert (0.171.1):
    - Flipper-Folly (~> 2.6)
  - FlipperKit/FBDefines (0.171.1)
  - FlipperKit/FKPortForwarding (0.171.1):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.171.1)
  - FlipperKit/FlipperKitLayoutHelpers (0.171.1):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutIOSDescriptors (0.171.1):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutPlugin (0.171.1):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - FlipperKit/FlipperKitLayoutIOSDescriptors
    - FlipperKit/FlipperKitLayoutTextSearchable
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutTextSearchable (0.171.1)
  - FlipperKit/FlipperKitNetworkPlugin (0.171.1):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.171.1):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.171.1):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.171.1):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMLKit/BarcodeScanning (3.2.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 2.2.0)
  - GoogleMLKit/MLKitCore (3.2.0):
    - MLKitCommon (~> 8.0.0)
  - GoogleToolboxForMac/DebugUtils (2.3.2):
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - GoogleToolboxForMac/Defines (2.3.2)
  - GoogleToolboxForMac/Logger (2.3.2):
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - "GoogleToolboxForMac/NSData+zlib (2.3.2)":
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - "GoogleToolboxForMac/NSDictionary+URLArguments (2.3.2)":
    - GoogleToolboxForMac/DebugUtils (= 2.3.2)
    - GoogleToolboxForMac/Defines (= 2.3.2)
    - "GoogleToolboxForMac/NSString+URLArguments (= 2.3.2)"
  - "GoogleToolboxForMac/NSString+URLArguments (2.3.2)"
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilitiesComponents (1.1.0):
    - GoogleUtilities/Logger
  - GTMSessionFetcher/Core (1.7.2)
  - HMSegmentedControl (1.5.6)
  - InputMask (6.1.0)
  - libevent (2.1.12)
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - lottie-ios (3.4.1)
  - lottie-react-native (5.1.4):
    - lottie-ios (~> 3.4.0)
    - React-Core
  - MBProgressHUD (1.1.0)
  - MLImage (1.0.0-beta3)
  - MLKitBarcodeScanning (2.2.0):
    - MLKitCommon (~> 8.0)
    - MLKitVision (~> 4.2)
  - MLKitCommon (8.0.0):
    - GoogleDataTransport (~> 9.0)
    - GoogleToolboxForMac/Logger (~> 2.1)
    - "GoogleToolboxForMac/NSData+zlib (~> 2.1)"
    - "GoogleToolboxForMac/NSDictionary+URLArguments (~> 2.1)"
    - GoogleUtilities/UserDefaults (~> 7.0)
    - GoogleUtilitiesComponents (~> 1.0)
    - GTMSessionFetcher/Core (~> 1.1)
    - Protobuf (~> 3.12)
  - MLKitVision (4.2.0):
    - GoogleToolboxForMac/Logger (~> 2.1)
    - "GoogleToolboxForMac/NSData+zlib (~> 2.1)"
    - GTMSessionFetcher/Core (~> 1.1)
    - MLImage (= 1.0.0-beta3)
    - MLKitCommon (~> 8.0)
    - Protobuf (~> 3.12)
  - MWPhotoBrowser (2.1.4):
    - DACircularProgress (~> 2.3.1)
    - MBProgressHUD (~> 1.1.0)
    - SDWebImage (~> 5.8)
  - nanopb (2.30908.0):
    - nanopb/decode (= 2.30908.0)
    - nanopb/encode (= 2.30908.0)
  - nanopb/decode (2.30908.0)
  - nanopb/encode (2.30908.0)
  - OpenSSL-Universal (1.1.180)
  - Permission-BluetoothPeripheral (3.6.1):
    - RNPermissions
  - Permission-Calendars (3.6.1):
    - RNPermissions
  - Permission-Camera (3.6.1):
    - RNPermissions
  - Permission-Contacts (3.6.1):
    - RNPermissions
  - Permission-FaceID (3.6.1):
    - RNPermissions
  - Permission-LocationAccuracy (3.6.1):
    - RNPermissions
  - Permission-LocationAlways (3.6.1):
    - RNPermissions
  - Permission-LocationWhenInUse (3.6.1):
    - RNPermissions
  - Permission-Notifications (3.6.1):
    - RNPermissions
  - Permission-PhotoLibrary (3.6.1):
    - RNPermissions
  - PromisesObjC (2.4.0)
  - Protobuf (3.29.3)
  - RCT-Folly (2021.06.28.00-v2):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.06.28.00-v2)
  - RCT-Folly/Default (2021.06.28.00-v2):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.67.5)
  - RCTSystemSetting (1.7.6):
    - React
  - RCTTypeSafety (0.67.5):
    - FBLazyVector (= 0.67.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.67.5)
    - React-Core (= 0.67.5)
  - React (0.67.5):
    - React-Core (= 0.67.5)
    - React-Core/DevSupport (= 0.67.5)
    - React-Core/RCTWebSocket (= 0.67.5)
    - React-RCTActionSheet (= 0.67.5)
    - React-RCTAnimation (= 0.67.5)
    - React-RCTBlob (= 0.67.5)
    - React-RCTImage (= 0.67.5)
    - React-RCTLinking (= 0.67.5)
    - React-RCTNetwork (= 0.67.5)
    - React-RCTSettings (= 0.67.5)
    - React-RCTText (= 0.67.5)
    - React-RCTVibration (= 0.67.5)
  - React-callinvoker (0.67.5)
  - React-Core (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.67.5)
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/CoreModulesHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/Default (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/DevSupport (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.67.5)
    - React-Core/RCTWebSocket (= 0.67.5)
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-jsinspector (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTBlobHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTImageHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTTextHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTWebSocket (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.67.5)
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-CoreModules (0.67.5):
    - FBReactNativeSpec (= 0.67.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.67.5)
    - React-Core/CoreModulesHeaders (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-RCTImage (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - React-cxxreact (0.67.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-callinvoker (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsinspector (= 0.67.5)
    - React-logger (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - React-runtimeexecutor (= 0.67.5)
  - React-jsi (0.67.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-jsi/Default (= 0.67.5)
  - React-jsi/Default (0.67.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
  - React-jsiexecutor (0.67.5):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-perflogger (= 0.67.5)
  - React-jsinspector (0.67.5)
  - React-logger (0.67.5):
    - glog
  - react-native-aes-encryption (0.2.0):
    - React
  - react-native-biometrics (3.0.1):
    - React-Core
  - react-native-blur (4.4.1):
    - React-Core
  - react-native-camera (4.2.1):
    - React-Core
    - react-native-camera/RCT (= 4.2.1)
    - react-native-camera/RN (= 4.2.1)
  - react-native-camera/BarcodeDetectorMLKit (4.2.1):
    - GoogleMLKit/BarcodeScanning
    - React-Core
    - react-native-camera/RCT
    - react-native-camera/RN
  - react-native-camera/RCT (4.2.1):
    - React-Core
  - react-native-camera/RN (4.2.1):
    - React-Core
  - react-native-carrier-info (1.1.2):
    - React
  - react-native-contacts (8.0.4):
    - React-Core
  - react-native-cookies (6.2.1):
    - React-Core
  - react-native-date-picker (4.2.11):
    - React-Core
  - react-native-exif (0.5.0):
    - React
  - react-native-fbsdk-next (11.2.1):
    - React-Core
    - react-native-fbsdk-next/Core (= 11.2.1)
    - react-native-fbsdk-next/Login (= 11.2.1)
    - react-native-fbsdk-next/Share (= 11.2.1)
  - react-native-fbsdk-next/Core (11.2.1):
    - FBSDKCoreKit (~> 15.0.0)
    - React-Core
  - react-native-fbsdk-next/Login (11.2.1):
    - FBSDKLoginKit (~> 15.0.0)
    - React-Core
  - react-native-fbsdk-next/Share (11.2.1):
    - FBSDKGamingServicesKit (~> 15.0.0)
    - FBSDKShareKit (~> 15.0.0)
    - React-Core
  - react-native-file-access (2.6.0):
    - React-Core
    - ZIPFoundation (= 0.9.11)
  - react-native-geocoder-reborn (0.9.0):
    - React
  - react-native-geolocation (3.0.6):
    - React-Core
  - react-native-geolocation-service (5.3.1):
    - React
  - react-native-hmac (0.1.1):
    - CryptoSwift
    - React-Core
  - react-native-image-editor (2.3.0):
    - React
    - React-RCTImage
  - react-native-image-picker (5.3.1):
    - React-Core
  - react-native-image-resizer (3.0.10):
    - React-Core
  - react-native-maps (1.8.1):
    - React-Core
  - react-native-maskview (1.1.3):
    - React
  - react-native-netinfo (9.4.1):
    - React-Core
  - react-native-network-info (5.2.1):
    - React
  - react-native-nfc-manager (3.14.8):
    - React-Core
  - react-native-orientation (3.1.3):
    - React
  - react-native-pager-view (6.0.2):
    - React-Core
  - react-native-payments (0.8.3):
    - React
    - Stripe
  - react-native-photo-view-ex (1.1.0):
    - React
  - react-native-safe-area-context (4.4.1):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - ReactCommon/turbomodule/core
  - react-native-shake (5.3.2):
    - React
  - react-native-sms (1.11.0):
    - React
  - react-native-spotlight-search (2.2.0):
    - React
  - react-native-sqlite-storage (5.0.0):
    - React
  - react-native-text-input-mask (3.1.5):
    - InputMask (~> 6.1.0)
    - React-Core
    - React-RCTText
  - react-native-webview (13.12.0):
    - React-Core
  - React-perflogger (0.67.5)
  - React-RCTActionSheet (0.67.5):
    - React-Core/RCTActionSheetHeaders (= 0.67.5)
  - React-RCTAnimation (0.67.5):
    - FBReactNativeSpec (= 0.67.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.67.5)
    - React-Core/RCTAnimationHeaders (= 0.67.5)
    - React-jsi (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - React-RCTBlob (0.67.5):
    - FBReactNativeSpec (= 0.67.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/RCTBlobHeaders (= 0.67.5)
    - React-Core/RCTWebSocket (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-RCTNetwork (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - React-RCTImage (0.67.5):
    - FBReactNativeSpec (= 0.67.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.67.5)
    - React-Core/RCTImageHeaders (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-RCTNetwork (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - React-RCTLinking (0.67.5):
    - FBReactNativeSpec (= 0.67.5)
    - React-Core/RCTLinkingHeaders (= 0.67.5)
    - React-jsi (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - React-RCTNetwork (0.67.5):
    - FBReactNativeSpec (= 0.67.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.67.5)
    - React-Core/RCTNetworkHeaders (= 0.67.5)
    - React-jsi (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - React-RCTSettings (0.67.5):
    - FBReactNativeSpec (= 0.67.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.67.5)
    - React-Core/RCTSettingsHeaders (= 0.67.5)
    - React-jsi (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - React-RCTText (0.67.5):
    - React-Core/RCTTextHeaders (= 0.67.5)
  - React-RCTVibration (0.67.5):
    - FBReactNativeSpec (= 0.67.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/RCTVibrationHeaders (= 0.67.5)
    - React-jsi (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - React-runtimeexecutor (0.67.5):
    - React-jsi (= 0.67.5)
  - ReactCommon/turbomodule/core (0.67.5):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-callinvoker (= 0.67.5)
    - React-Core (= 0.67.5)
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-logger (= 0.67.5)
    - React-perflogger (= 0.67.5)
  - ReactNativeART (1.2.0):
    - React
  - ReactNativeNavigation (7.27.1):
    - HMSegmentedControl
    - React-Core
    - React-RCTImage
    - React-RCTText
    - ReactNativeNavigation/Core (= 7.27.1)
  - ReactNativeNavigation/Core (7.27.1):
    - HMSegmentedControl
    - React-Core
    - React-RCTImage
    - React-RCTText
  - RealmJS (10.20.0-beta.5):
    - React
  - RNBackgroundFetch (4.1.10):
    - React-Core
  - RNBarcode (1.0.26):
    - React
  - RNCalendarEvents (2.2.0):
    - React
  - RNCAsyncStorage (1.19.3):
    - React-Core
  - RNCClipboard (1.11.2):
    - React-Core
  - RNCPicker (2.4.10):
    - React-Core
  - RNCPushNotificationIOS (1.10.1):
    - React-Core
  - RNDefaultPreference (1.4.4):
    - React-Core
  - RNDeviceInfo (13.0.0):
    - React-Core
  - RNFastImage (8.3.7):
    - React-Core
    - SDWebImage (~> 5.8)
    - SDWebImageWebPCoder (~> 0.6.1)
  - RNFBApp (14.11.1):
    - Firebase/CoreOnly (= 8.15.0)
    - React-Core
  - RNFS (2.18.0):
    - React
  - RNGestureHandler (2.9.0):
    - React-Core
  - RNImageColors (1.5.2):
    - React-Core
  - RNImageRotate (2.1.0):
    - React
    - React-RCTImage
  - RNInAppBrowser (3.7.0):
    - React-Core
  - RNKeychain (8.1.2):
    - React-Core
  - RNLocalize (2.2.6):
    - React-Core
  - RNNotifee (9.1.1):
    - React-Core
    - RNNotifee/NotifeeCore (= 9.1.1)
  - RNNotifee/NotifeeCore (9.1.1):
    - React-Core
  - RNPermissions (3.6.1):
    - React-Core
  - RNRate (1.2.12):
    - React-Core
  - RNReactNativeHapticFeedback (2.0.3):
    - React-Core
  - RNReanimated (2.14.4):
    - DoubleConversion
    - FBLazyVector
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNShare (9.4.1):
    - React-Core
  - RNSound (0.11.2):
    - React-Core
    - RNSound/Core (= 0.11.2)
  - RNSound/Core (0.11.2):
    - React-Core
  - RNTextSize (4.0.0-rc.1):
    - React
  - RNVectorIcons (10.2.0):
    - React-Core
  - SDWebImage (5.20.0):
    - SDWebImage/Core (= 5.20.0)
  - SDWebImage/Core (5.20.0)
  - SDWebImageWebPCoder (0.6.1):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.7)
  - SocketRocket (0.6.1)
  - Stripe (22.8.4):
    - Stripe/Stripe3DS2 (= 22.8.4)
    - StripeApplePay (= 22.8.4)
    - StripeCore (= 22.8.4)
    - StripeUICore (= 22.8.4)
  - Stripe/Stripe3DS2 (22.8.4):
    - StripeApplePay (= 22.8.4)
    - StripeCore (= 22.8.4)
    - StripeUICore (= 22.8.4)
  - StripeApplePay (22.8.4):
    - StripeCore (= 22.8.4)
  - StripeCore (22.8.4)
  - StripeUICore (22.8.4):
    - StripeCore (= 22.8.4)
  - tipsi-stripe (9.0.0):
    - React
    - Stripe (>= 21.3.1)
  - vision-camera-code-scanner (0.2.0):
    - GoogleMLKit/BarcodeScanning
    - React-Core
  - VisionCamera (2.15.4):
    - React
    - React-callinvoker
    - React-Core
  - Yoga (1.14.0)
  - YogaKit (1.18.1):
    - Yoga (~> 1.14)
  - ZIPFoundation (0.9.11)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - "BugsnagReactNative (from `../node_modules/@bugsnag/react-native`)"
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Flipper (= 0.171.1)
  - Flipper-Boost-iOSX (= ********.11)
  - Flipper-DoubleConversion (= 3.1.7)
  - Flipper-Fmt (= 7.1.7)
  - Flipper-Folly (= 2.6.7)
  - Flipper-Glog (= 0.3.6)
  - Flipper-PeerTalk (= 0.0.4)
  - Flipper-RSocket (= 1.4.3)
  - FlipperKit (= 0.171.1)
  - FlipperKit/Core (= 0.171.1)
  - FlipperKit/CppBridge (= 0.171.1)
  - FlipperKit/FBCxxFollyDynamicConvert (= 0.171.1)
  - FlipperKit/FBDefines (= 0.171.1)
  - FlipperKit/FKPortForwarding (= 0.171.1)
  - FlipperKit/FlipperKitHighlightOverlay (= 0.171.1)
  - FlipperKit/FlipperKitLayoutPlugin (= 0.171.1)
  - FlipperKit/FlipperKitLayoutTextSearchable (= 0.171.1)
  - FlipperKit/FlipperKitNetworkPlugin (= 0.171.1)
  - FlipperKit/FlipperKitReactPlugin (= 0.171.1)
  - FlipperKit/FlipperKitUserDefaultsPlugin (= 0.171.1)
  - FlipperKit/SKIOSNetworkPlugin (= 0.171.1)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleMLKit/BarcodeScanning (= 3.2.0)
  - lottie-ios (from `../node_modules/lottie-ios`)
  - lottie-react-native (from `../node_modules/lottie-react-native`)
  - MWPhotoBrowser (from `../src/ios/MWPhotoBrowser`)
  - OpenSSL-Universal (= 1.1.180)
  - Permission-BluetoothPeripheral (from `../node_modules/react-native-permissions/ios/BluetoothPeripheral`)
  - Permission-Calendars (from `../node_modules/react-native-permissions/ios/Calendars`)
  - Permission-Camera (from `../node_modules/react-native-permissions/ios/Camera`)
  - Permission-Contacts (from `../node_modules/react-native-permissions/ios/Contacts`)
  - Permission-FaceID (from `../node_modules/react-native-permissions/ios/FaceID`)
  - Permission-LocationAccuracy (from `../node_modules/react-native-permissions/ios/LocationAccuracy`)
  - Permission-LocationAlways (from `../node_modules/react-native-permissions/ios/LocationAlways`)
  - Permission-LocationWhenInUse (from `../node_modules/react-native-permissions/ios/LocationWhenInUse`)
  - Permission-Notifications (from `../node_modules/react-native-permissions/ios/Notifications`)
  - Permission-PhotoLibrary (from `../node_modules/react-native-permissions/ios/PhotoLibrary`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTSystemSetting (from `../node_modules/react-native-system-setting`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-aes-encryption (from `../node_modules/react-native-aes-encryption`)
  - react-native-biometrics (from `../node_modules/react-native-biometrics`)
  - "react-native-blur (from `../node_modules/@react-native-community/blur`)"
  - react-native-camera (from `../node_modules/react-native-camera`)
  - react-native-camera/BarcodeDetectorMLKit (from `../node_modules/react-native-camera`)
  - react-native-carrier-info (from `../node_modules/react-native-carrier-info`)
  - react-native-contacts (from `../node_modules/react-native-contacts`)
  - "react-native-cookies (from `../node_modules/@react-native-cookies/cookies`)"
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - react-native-exif (from `../node_modules/react-native-exif`)
  - react-native-fbsdk-next (from `../node_modules/react-native-fbsdk-next`)
  - react-native-file-access (from `../node_modules/react-native-file-access`)
  - react-native-geocoder-reborn (from `../node_modules/react-native-geocoder-reborn`)
  - "react-native-geolocation (from `../node_modules/@react-native-community/geolocation`)"
  - react-native-geolocation-service (from `../node_modules/react-native-geolocation-service`)
  - react-native-hmac (from `../node_modules/react-native-hmac`)
  - "react-native-image-editor (from `../node_modules/@react-native-community/image-editor`)"
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - "react-native-image-resizer (from `../node_modules/@bam.tech/react-native-image-resizer`)"
  - react-native-maps (from `../node_modules/react-native-maps`)
  - react-native-maskview (from `../node_modules/react-native-maskview`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-network-info (from `../node_modules/react-native-network-info`)
  - react-native-nfc-manager (from `../node_modules/react-native-nfc-manager`)
  - react-native-orientation (from `../node_modules/react-native-orientation`)
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-payments (from `../node_modules/react-native-payments`)
  - react-native-photo-view-ex (from `../node_modules/react-native-photo-view-ex`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-shake (from `../node_modules/react-native-shake`)
  - react-native-sms (from `../node_modules/react-native-sms`)
  - react-native-spotlight-search (from `../node_modules/react-native-spotlight-search`)
  - react-native-sqlite-storage (from `../node_modules/react-native-sqlite-storage`)
  - react-native-text-input-mask (from `../node_modules/react-native-text-input-mask`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "ReactNativeART (from `../node_modules/@react-native-community/art`)"
  - ReactNativeNavigation (from `../node_modules/react-native-navigation`)
  - RealmJS (from `../node_modules/realm`)
  - RNBackgroundFetch (from `../node_modules/react-native-background-fetch`)
  - RNBarcode (from `../node_modules/react-native-barcode-zxing`)
  - RNCalendarEvents (from `../node_modules/react-native-calendar-events`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - "RNCPushNotificationIOS (from `../node_modules/@react-native-community/push-notification-ios`)"
  - RNDefaultPreference (from `../node_modules/react-native-default-preference`)
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNImageColors (from `../node_modules/react-native-image-colors`)
  - RNImageRotate (from `../node_modules/react-native-image-rotate`)
  - RNInAppBrowser (from `../node_modules/react-native-inappbrowser-reborn`)
  - RNKeychain (from `../node_modules/react-native-keychain`)
  - RNLocalize (from `../node_modules/react-native-localize`)
  - "RNNotifee (from `../node_modules/@notifee/react-native`)"
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNRate (from `../node_modules/react-native-rate`)
  - RNReactNativeHapticFeedback (from `../node_modules/react-native-haptic-feedback`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNSound (from `../node_modules/react-native-sound`)
  - RNTextSize (from `../node_modules/react-native-text-size/ios`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - Stripe (= 22.8.4)
  - tipsi-stripe (from `../node_modules/tipsi-stripe`)
  - vision-camera-code-scanner (from `../node_modules/vision-camera-code-scanner`)
  - VisionCamera (from `../node_modules/react-native-vision-camera`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - CocoaAsyncSocket
    - CryptoSwift
    - DACircularProgress
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKGamingServicesKit
    - FBSDKLoginKit
    - FBSDKShareKit
    - Firebase
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - Flipper
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - Flipper-RSocket
    - FlipperKit
    - fmt
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GoogleUtilitiesComponents
    - GTMSessionFetcher
    - HMSegmentedControl
    - InputMask
    - libevent
    - libwebp
    - MBProgressHUD
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - nanopb
    - OpenSSL-Universal
    - PromisesObjC
    - Protobuf
    - SDWebImage
    - SDWebImageWebPCoder
    - SocketRocket
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeUICore
    - YogaKit
    - ZIPFoundation

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BugsnagReactNative:
    :path: "../node_modules/@bugsnag/react-native"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  lottie-ios:
    :path: "../node_modules/lottie-ios"
  lottie-react-native:
    :path: "../node_modules/lottie-react-native"
  MWPhotoBrowser:
    :path: "../src/ios/MWPhotoBrowser"
  Permission-BluetoothPeripheral:
    :path: "../node_modules/react-native-permissions/ios/BluetoothPeripheral"
  Permission-Calendars:
    :path: "../node_modules/react-native-permissions/ios/Calendars"
  Permission-Camera:
    :path: "../node_modules/react-native-permissions/ios/Camera"
  Permission-Contacts:
    :path: "../node_modules/react-native-permissions/ios/Contacts"
  Permission-FaceID:
    :path: "../node_modules/react-native-permissions/ios/FaceID"
  Permission-LocationAccuracy:
    :path: "../node_modules/react-native-permissions/ios/LocationAccuracy"
  Permission-LocationAlways:
    :path: "../node_modules/react-native-permissions/ios/LocationAlways"
  Permission-LocationWhenInUse:
    :path: "../node_modules/react-native-permissions/ios/LocationWhenInUse"
  Permission-Notifications:
    :path: "../node_modules/react-native-permissions/ios/Notifications"
  Permission-PhotoLibrary:
    :path: "../node_modules/react-native-permissions/ios/PhotoLibrary"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTSystemSetting:
    :path: "../node_modules/react-native-system-setting"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-aes-encryption:
    :path: "../node_modules/react-native-aes-encryption"
  react-native-biometrics:
    :path: "../node_modules/react-native-biometrics"
  react-native-blur:
    :path: "../node_modules/@react-native-community/blur"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-carrier-info:
    :path: "../node_modules/react-native-carrier-info"
  react-native-contacts:
    :path: "../node_modules/react-native-contacts"
  react-native-cookies:
    :path: "../node_modules/@react-native-cookies/cookies"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-exif:
    :path: "../node_modules/react-native-exif"
  react-native-fbsdk-next:
    :path: "../node_modules/react-native-fbsdk-next"
  react-native-file-access:
    :path: "../node_modules/react-native-file-access"
  react-native-geocoder-reborn:
    :path: "../node_modules/react-native-geocoder-reborn"
  react-native-geolocation:
    :path: "../node_modules/@react-native-community/geolocation"
  react-native-geolocation-service:
    :path: "../node_modules/react-native-geolocation-service"
  react-native-hmac:
    :path: "../node_modules/react-native-hmac"
  react-native-image-editor:
    :path: "../node_modules/@react-native-community/image-editor"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-image-resizer:
    :path: "../node_modules/@bam.tech/react-native-image-resizer"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-maskview:
    :path: "../node_modules/react-native-maskview"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-network-info:
    :path: "../node_modules/react-native-network-info"
  react-native-nfc-manager:
    :path: "../node_modules/react-native-nfc-manager"
  react-native-orientation:
    :path: "../node_modules/react-native-orientation"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-payments:
    :path: "../node_modules/react-native-payments"
  react-native-photo-view-ex:
    :path: "../node_modules/react-native-photo-view-ex"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-shake:
    :path: "../node_modules/react-native-shake"
  react-native-sms:
    :path: "../node_modules/react-native-sms"
  react-native-spotlight-search:
    :path: "../node_modules/react-native-spotlight-search"
  react-native-sqlite-storage:
    :path: "../node_modules/react-native-sqlite-storage"
  react-native-text-input-mask:
    :path: "../node_modules/react-native-text-input-mask"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  ReactNativeART:
    :path: "../node_modules/@react-native-community/art"
  ReactNativeNavigation:
    :path: "../node_modules/react-native-navigation"
  RealmJS:
    :path: "../node_modules/realm"
  RNBackgroundFetch:
    :path: "../node_modules/react-native-background-fetch"
  RNBarcode:
    :path: "../node_modules/react-native-barcode-zxing"
  RNCalendarEvents:
    :path: "../node_modules/react-native-calendar-events"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNCPushNotificationIOS:
    :path: "../node_modules/@react-native-community/push-notification-ios"
  RNDefaultPreference:
    :path: "../node_modules/react-native-default-preference"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNImageColors:
    :path: "../node_modules/react-native-image-colors"
  RNImageRotate:
    :path: "../node_modules/react-native-image-rotate"
  RNInAppBrowser:
    :path: "../node_modules/react-native-inappbrowser-reborn"
  RNKeychain:
    :path: "../node_modules/react-native-keychain"
  RNLocalize:
    :path: "../node_modules/react-native-localize"
  RNNotifee:
    :path: "../node_modules/@notifee/react-native"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNRate:
    :path: "../node_modules/react-native-rate"
  RNReactNativeHapticFeedback:
    :path: "../node_modules/react-native-haptic-feedback"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSound:
    :path: "../node_modules/react-native-sound"
  RNTextSize:
    :path: "../node_modules/react-native-text-size/ios"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  tipsi-stripe:
    :path: "../node_modules/tipsi-stripe"
  vision-camera-code-scanner:
    :path: "../node_modules/vision-camera-code-scanner"
  VisionCamera:
    :path: "../node_modules/react-native-vision-camera"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: d3549916092723fe0913ce40416e107f5cd682c6
  BugsnagReactNative: bf6f4ebababa8536726b3014c7d3e4af8c53d488
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  CryptoSwift: e64e11850ede528a02a0f3e768cec8e9d92ecb90
  DACircularProgress: 4dd437c0fc3da5161cb289e07ac449493d41db71
  DoubleConversion: 831926d9b8bf8166fd87886c4abab286c2422662
  FBAEMKit: d8312d8451ead46282adc7f3565ffc4965e3a4a7
  FBLazyVector: d2db9d00883282819d03bbd401b2ad4360d47580
  FBReactNativeSpec: 94da4d84ba3b1acf459103320882daa481a2b62d
  FBSDKCoreKit: 81879058dd06208c0820fc713d054ca54903e8ba
  FBSDKCoreKit_Basics: eebc9bb69a0e5d133b92dca53a20716227faa454
  FBSDKGamingServicesKit: e0766514d64d26a9f846e793c0a44bc4a236723b
  FBSDKLoginKit: 11995469cd7da16fd4d5245e8d2ec61060479270
  FBSDKShareKit: 9501792c3024580809f811a8a549868699d9bd32
  Firebase: 5f8193dff4b5b7c5d5ef72ae54bb76c08e2b841d
  FirebaseCore: 5743c5785c074a794d35f2fff7ecc254a91e08b1
  FirebaseCoreDiagnostics: 92e07a649aeb66352b319d43bdd2ee3942af84cb
  Flipper: 1037e97c33631c48b8f8977b710a1a4766058fd1
  Flipper-Boost-iOSX: fd1e2b8cbef7e662a122412d7ac5f5bea715403c
  Flipper-DoubleConversion: 57ffbe81ef95306cc9e69c4aa3aeeeeb58a6a28c
  Flipper-Fmt: 60cbdd92fc254826e61d669a5d87ef7015396a9b
  Flipper-Folly: 83af37379faa69497529e414bd43fbfc7cae259a
  Flipper-Glog: 1dfd6abf1e922806c52ceb8701a3599a79a200a6
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  Flipper-RSocket: d9d9ade67cbecf6ac10730304bf5607266dd2541
  FlipperKit: cf422436e0e9d5e8a5415bf0fc1bc6780dbf65e6
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 85ecdd10ee8d8ec362ef519a6a45ff9aa27b2e85
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMLKit: 0017a6a8372e1a182139b9def4d89be5d87ca5a7
  GoogleToolboxForMac: 8bef7c7c5cf7291c687cf5354f39f9db6399ad34
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GoogleUtilitiesComponents: 679b2c881db3b615a2777504623df6122dd20afe
  GTMSessionFetcher: 5595ec75acf5be50814f81e9189490412bad82ba
  HMSegmentedControl: 34c1f54d822d8308e7b24f5d901ec674dfa31352
  InputMask: 71d291dc54d2deaeac6512afb6ec2304228c0bb7
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  lottie-ios: 016449b5d8be0c3dcbcfa0a9988469999cd04c5d
  lottie-react-native: b702fab740cdb952a8e2354713d3beda63ff97b0
  MBProgressHUD: e7baa36a220447d8aeb12769bf0585582f3866d9
  MLImage: 489dfec109f21da8621b28d476401aaf7a0d4ff4
  MLKitBarcodeScanning: d92fe1911001ec36870162c5a0eb206f612b7169
  MLKitCommon: f6da6c5659618c070b50a80db01248ebe2964175
  MLKitVision: 96c96571190b7f63eddf4a12068ce8a8689e0d2c
  MWPhotoBrowser: a337e517e378fac0c5cae43af3eaa07d587e83a8
  nanopb: a0ba3315591a9ae0a16a309ee504766e90db0c96
  OpenSSL-Universal: 1aa4f6a6ee7256b83db99ec1ccdaa80d10f9af9b
  Permission-BluetoothPeripheral: 67708853584bb9208c76d36d0e0ea4eafb97ea5b
  Permission-Calendars: dd55f36958c398a487f141d4c89119907e34dca8
  Permission-Camera: bf6791b17c7f614b6826019fcfdcc286d3a107f6
  Permission-Contacts: 2484d274171688b1ca80978b949ac771259b51f0
  Permission-FaceID: e70223280292a1a5e4b8ad943b70cd9229a7d2c3
  Permission-LocationAccuracy: 76df17de5c6b8bc2eee34e61ee92cdd7a864c73d
  Permission-LocationAlways: 8d99b025c9f73c696e0cdb367e42525f2e9a26f2
  Permission-LocationWhenInUse: 3ba99e45c852763f730eabecec2870c2382b7bd4
  Permission-Notifications: 150484ae586eb9be4e32217582a78350a9bb31c3
  Permission-PhotoLibrary: 5b34ca67279f7201ae109cef36f9806a6596002d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  Protobuf: e2de96d939c642ce8da186343ff8929b35223212
  RCT-Folly: 803a9cfd78114b2ec0f140cfa6fa2a6bafb2d685
  RCTRequired: 412e994c1e570cf35378a32c18fd46e50634938b
  RCTSystemSetting: 5107b7350d63b3f7b42a1277d07e4e5d9df879be
  RCTTypeSafety: ef27340c728e6d673af345ed69e479a010c8a2d8
  React: 36b9f5116572e5b80f01e562bb1f1451e8848e47
  React-callinvoker: 91e62870884d3db3a0db33bbb1ba4e53fa5210ca
  React-Core: 765ccc3861be1b93c7d5ca37f6b06e2efd6e7999
  React-CoreModules: da2ddff50a92576b6d58fbfc80a62ba3f81d8a4e
  React-cxxreact: b54cffd4feb550c3213cd38db4a2a4bdd896f715
  React-jsi: 103674913e4159a07df20ef214c6b563e90e7b2e
  React-jsiexecutor: e9895ccae253323ca70f693945fecbba091f0abd
  React-jsinspector: ec4fe4f65ccf9d67c8429dda955d3412db8a25ef
  React-logger: 85f4ef06b9723714b2dfa5b0e5502b673b271b58
  react-native-aes-encryption: f207fe270f2eae051801fdf14669277b8538250f
  react-native-biometrics: 352e5a794bfffc46a0c86725ea7dc62deb085bdc
  react-native-blur: 32db1a8e94964c17f3f85424dd822b2ffa1a83c4
  react-native-camera: bcaeb010caa6c4d075babacd52ff2b577930351b
  react-native-carrier-info: f94c990c4e27017648738430afc01acf367e102f
  react-native-contacts: cdeb7709dcc626e5fd7ee3d3e3685357e877cbd5
  react-native-cookies: f54fcded06bb0cda05c11d86788020b43528a26c
  react-native-date-picker: 0c4fda40b3d4a0a98d20ee33f9755f51f9b578db
  react-native-exif: c43e7c18af8bfa133c79145671a0f036e413468e
  react-native-fbsdk-next: 5291a5d67aab4e649d072c50988033ff0d05b915
  react-native-file-access: a4398950e02999b5ab97f3055feb7ee28e6615a3
  react-native-geocoder-reborn: c31cbc630d9307ebbceea1dea2746d0054be35c4
  react-native-geolocation: 0f7fe8a4c2de477e278b0365cce27d089a8c5903
  react-native-geolocation-service: 608e1da71a1ac31b4de64d9ef2815f697978c55b
  react-native-hmac: 384cffc546826f255f809f9bd9382364a4004501
  react-native-image-editor: 9361a215c3991cafbe5e7f28cbbad6e72c9c2705
  react-native-image-picker: ec9b713e248760bfa0f879f0715391de4651a7cb
  react-native-image-resizer: fd0c333eca55147bd55c5e054cac95dcd0da6814
  react-native-maps: 3f67c9354b95bf9c9b072ed7b2a145693eecc963
  react-native-maskview: 3dd846fc0456c191d0a44164a5e72e04976ac191
  react-native-netinfo: fefd4e98d75cbdd6e85fc530f7111a8afdf2b0c5
  react-native-network-info: d1290ffc0bd0709e11436f5b8d7f605dcc5c4530
  react-native-nfc-manager: 15a9dd22258cb8e1a6bbb71179baf5c9137a933d
  react-native-orientation: f1caf84d65f1a4fd4511a18f2b924e634ad7a628
  react-native-pager-view: 592421df0259bf7a7a4fe85b74c24f3f39905605
  react-native-payments: 165a8db4dd9eee7d55ad6a9952d1c92b7200fc95
  react-native-photo-view-ex: 201dddcbe44fe531b824eae5e1b99b1f494c0588
  react-native-safe-area-context: 99b24a0c5acd0d5dcac2b1a7f18c49ea317be99a
  react-native-shake: 62aa5681863203090a087842da70183c442b97f8
  react-native-sms: 31fbb9d6ebb565ad32e409eaf9dc70329936a129
  react-native-spotlight-search: 392fe113864a986b21af13595b82fc434e43a6c0
  react-native-sqlite-storage: 418ef4afc5e6df6ce3574c4617e5f0b65cffde55
  react-native-text-input-mask: 22ca8eeef84d42a896f79428f7d175a5eb8b1c4e
  react-native-webview: 2c96ee66e5dad5ce7004d8e41dd71b524dd46ad5
  React-perflogger: d32ee13196f4ae2e4098fb7f8e7ed4f864c6fb0f
  React-RCTActionSheet: 81779c09e34a6a3d6b15783407ba9016a774f535
  React-RCTAnimation: b778eaaa42a884abcc5719035a7a0b2f54672658
  React-RCTBlob: 8edfc04c117decb0e7d4e6ab32bec91707e63ecb
  React-RCTImage: 2022097f1291bfebd0003e477318c72b07853578
  React-RCTLinking: bd8d889c65695181342541ce4420e9419845084c
  React-RCTNetwork: eae64b805d967bf3ece2cec3ad09218eeb32cb74
  React-RCTSettings: 0645af8aec5f40726e98d434a07ff58e75a81aa9
  React-RCTText: e55de507cda263ff58404c3e7d75bf76c2b80813
  React-RCTVibration: c3b8a3245267a3849b0c7cb91a37606bf5f3aa65
  React-runtimeexecutor: 434efc9e5b6d0f14f49867f130b39376c971c1aa
  ReactCommon: a30c2448e5a88bae6fcb0e3da124c14ae493dac1
  ReactNativeART: 78edc68dd4a1e675338cd0cd113319cf3a65f2ab
  ReactNativeNavigation: 94979dd1572a3f093fc85d4599360530a1bed8c8
  RealmJS: 772520fb85c19b65c2ea0c8f9aa6e790a905a377
  RNBackgroundFetch: 9fcdeee8aa00e1e1b3895ff9bbaaf10275288a87
  RNBarcode: c173577a7adf400efdce0646ef82f354d9b6ca73
  RNCalendarEvents: 7e65eb4a94f53c1744d1e275f7fafcfaa619f7a3
  RNCAsyncStorage: c913ede1fa163a71cea118ed4670bbaaa4b511bb
  RNCClipboard: 3f0451a8100393908bea5c5c5b16f96d45f30bfc
  RNCPicker: 0bc2f0a29abcca7b7ed44a2d036aac9ab6d25700
  RNCPushNotificationIOS: 87b8d16d3ede4532745e05b03c42cff33a36cc45
  RNDefaultPreference: 08bdb06cfa9188d5da97d4642dac745218d7fb31
  RNDeviceInfo: 55264dd7cc939dad6e9c231a7621311f5277f1dc
  RNFastImage: a7384db75df352500261e8e8f1ac2026def26102
  RNFBApp: 9646e09d041ea159b84584865212e4cf33acd179
  RNFS: 3ab21fa6c56d65566d1fb26c2228e2b6132e5e32
  RNGestureHandler: 071d7a9ad81e8b83fe7663b303d132406a7d8f39
  RNImageColors: 9ac05083b52d5c350e6972650ae3ba0e556466c1
  RNImageRotate: c9faf432fc8f530b60d94bb317f5f6576244fe5d
  RNInAppBrowser: e36d6935517101ccba0e875bac8ad7b0cb655364
  RNKeychain: a65256b6ca6ba6976132cc4124b238a5b13b3d9c
  RNLocalize: d4b8af4e442d4bcca54e68fc687a2129b4d71a81
  RNNotifee: 35b5f984d11083b02d7f2990b3408cd99a0aeeac
  RNPermissions: dcdb7b99796bbeda6975a6e79ad519c41b251b1c
  RNRate: ef3bcff84f39bb1d1e41c5593d3eea4aab2bd73a
  RNReactNativeHapticFeedback: afa5bf2794aecbb2dba2525329253da0d66656df
  RNReanimated: e0d473a3ffdcf1273f83cbc3e34cc47e2fff72ed
  RNShare: 32e97adc8d8c97d4a26bcdd3c45516882184f8b6
  RNSound: 6c156f925295bdc83e8e422e7d8b38d33bc71852
  RNTextSize: 7efa06531c6bb7ab6ed070b4ce0d1248cf06fcdf
  RNVectorIcons: 441de54a3b30f7d1be9354b5dc96ad6e2219948a
  SDWebImage: 73c6079366fea25fa4bb9640d5fb58f0893facd8
  SDWebImageWebPCoder: d0dac55073088d24b2ac1b191a71a8f8d0adac21
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  Stripe: 59f6659e8ea413def19c97c57e23f5ec6ea48cf8
  StripeApplePay: 6e9aebd3d248464836e9fbdaef6e5fb1817d56a6
  StripeCore: 193f27552fb98108ebfe0df11651fe5f38948305
  StripeUICore: 6a5bbea0164d63bd91000f001903e7901859e1a4
  tipsi-stripe: 262adc0a23fc0b8af1f8cb7778ee2ee681edd2b6
  vision-camera-code-scanner: dda884a7f3ec8243a2a6d6489b91860648371bca
  VisionCamera: cdc88c12d856644956e180a7648409be4d10e8a5
  Yoga: 099a946cbf84c9b32ffdc4278d72db26710ecf92
  YogaKit: f782866e155069a2cca2517aafea43200b01fd5a
  ZIPFoundation: b1f0de4eed33e74a676f76e12559ab6b75990197

PODFILE CHECKSUM: 6e19d94971a9dfdc832ec6464c9e0e5f503172fc

COCOAPODS: 1.16.2
