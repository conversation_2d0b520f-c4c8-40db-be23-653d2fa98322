//
//  RCTBarcodeScanning.m
//  Perkd
//
//  Created by <PERSON> on 30/4/22.
//

#import "RCTBarcodeScanning.h"

@import MLKitVision.MLKVisionImage;
@import MLKitBarcodeScanning.MLKBarcodeScanner;
@import MLKitBarcodeScanning.MLKBarcode;

@implementation RCTBarcodeScanningModule

RCT_EXPORT_MODULE(BarcodeScanningModule);

RCT_EXPORT_METHOD(processImage:(nonnull NSString *)url
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  NSURL *_url = [NSURL URLWithString:url];
  NSData *imageData = [NSData dataWithContentsOfURL:_url];
  UIImage *image = [UIImage imageWithData:imageData];
  
  MLKVisionImage *visionImage = [[MLKVisionImage alloc] initWithImage:image];
  visionImage.orientation = image.imageOrientation;
  
  <PERSON><PERSON><PERSON>BarcodeScanner *barcodeScanner = [MLKBarcodeScanner barcodeScanner];
  
  [barcodeScanner processImage:visionImage completion:^(NSArray<MLKBarcode *> * _Nullable barcodes, NSError * _Nullable error) {
    if (error != nil) {
      return reject(@"Barcode Scanning", @"Barcode Scanning failed", error);
    }
    
    NSMutableArray *result = [NSMutableArray array];
            
    for (MLKBarcode *barcode in barcodes) {
        [result addObject:@{
            @"format": @(barcode.format),
            @"valueType": @(barcode.valueType),
            @"displayValue": barcode.displayValue,
            @"rawValue": barcode.rawValue
        }];
    }
    
    resolve(result);
  }];
}

@end
