// CardCategorizer.swift
import Foundation
import NaturalLanguage
import os.log

@objc(CardCategorizer)
class CardCategorizer: NSObject {
    
    private let tagger: NLTagger
    private let logger = OSLog(subsystem: "com.perkd.cardcategorizer", category: "NLAnalysis")
    
    @objc
    static func requiresMainQueueSetup() -> Bool {
        return false
    }
    
    override init() {
        tagger = NLTagger(tagSchemes: [.lexicalClass, .nameType, .tokenType])
        super.init()
    }
    
    @objc(categorizeCard:withResolver:withRejecter:)
    func categorizeCard(_ cardName: String, resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        os_log("Analyzing card name: %{public}@", log: logger, type: .info, cardName)
        let result = analyzeCard(cardName)
        os_log("Analysis result - Category: %{public}@, Explanation: %{public}@", 
               log: logger, type: .info, 
               result.category, result.explanation)
        resolve([
            "category": result.category,
            "explanation": result.explanation
        ])
    }
    
    @objc(categorizeCards:withResolver:withRejecter:)
    func categorizeCards(_ cardNames: [String], resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        os_log("Analyzing multiple cards: %{public}@", log: logger, type: .info, cardNames)
        let results = cardNames.map { cardName -> [String: String] in
            let result = analyzeCard(cardName)
            return [
                "name": cardName,
                "category": result.category,
                "explanation": result.explanation
            ]
        }
        os_log("Batch analysis results: %{public}@", log: logger, type: .info, results)
        resolve(results)
    }
    
    private func analyzeCard(_ cardName: String) -> (category: String, explanation: String) {
        tagger.string = cardName
        
        var entities: [(String, String)] = []
        let range = cardName.startIndex..<cardName.endIndex
        
        // Log all available tag schemes
        os_log("Available tag schemes for text: %{public}@", log: logger, type: .debug, 
               NLTagger.availableTagSchemes(for: .word, language: .english).map { $0.rawValue }.joined(separator: ", "))
        
        // Get lexical class tags
        tagger.setLanguage(.english, range: range)
        let lexicalTags = getLexicalTags(for: range)
        os_log("Lexical analysis: %{public}@", log: logger, type: .debug, lexicalTags)
        
        // Get name type tags
        tagger.enumerateTags(in: range, unit: .word, scheme: .nameType) { tag, tokenRange in
            if let tag = tag {
                let entity = String(cardName[tokenRange])
                entities.append((entity, tag.rawValue))
                os_log("Found entity: %{public}@ with tag: %{public}@", log: logger, type: .debug, entity, tag.rawValue)
            }
            return true
        }
        
        let lowercasedName = cardName.lowercased()
        os_log("Detected entities: %{public}@", log: logger, type: .info, entities)
        
        // Business cards
        if entities.contains(where: { $0.1 == "Organization" }) ||
           lowercasedName.contains("visa") ||
           lowercasedName.contains("mastercard") ||
           lowercasedName.contains("business") {
            os_log("Matched business category", log: logger, type: .debug)
            return ("business", "Business/financial card detected")
        }
        
        // Gaming cards
        if lowercasedName.contains("pokemon") ||
           lowercasedName.contains("game") ||
           entities.contains(where: { $0.1 == "Product" && $0.0.lowercased().contains("game") }) {
            os_log("Matched gaming category", log: logger, type: .debug)
            return ("gaming", "Gaming card detected")
        }
        
        // Sports cards
        if entities.contains(where: { $0.1 == "PersonalName" }) &&
           (lowercasedName.contains("player") ||
            lowercasedName.contains("nfl") ||
            lowercasedName.contains("nba")) {
            os_log("Matched sports category", log: logger, type: .debug)
            return ("sports", "Sports card detected")
        }
        
        // Entertainment cards
        if lowercasedName.contains("movie") ||
           lowercasedName.contains("netflix") ||
           lowercasedName.contains("entertainment") {
            os_log("Matched entertainment category", log: logger, type: .debug)
            return ("entertainment", "Entertainment card detected")
        }
        
        os_log("No category match found", log: logger, type: .debug)
        return ("other", "Unrecognized card type")
    }
    
    private func getLexicalTags(for range: Range<String.Index>) -> [(String, String)] {
        var lexicalTags: [(String, String)] = []
        tagger.enumerateTags(in: range, unit: .word, scheme: .lexicalClass) { tag, tokenRange in
            if let tag = tag, let word = self.tagger.string?[tokenRange] {
                lexicalTags.append((String(word), tag.rawValue))
            }
            return true
        }
        return lexicalTags
    }
} 
