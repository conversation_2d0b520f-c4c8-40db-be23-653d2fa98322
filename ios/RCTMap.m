//
//  RCTMap.m
//  Perkd
//
//  Created by <PERSON> on 7/1/23.
//

#import "RCTMap.h"

@implementation RCTMapModule

// To export a module named RCTCalendarModule
RCT_EXPORT_MODULE(RCTMapModule);

RCT_EXPORT_METHOD(distanceMatrix: (nonnull NSDictionary *)from
                  to: (nonnull NSDictionary *)to
                  mode: (nonnull NSString *)mode
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  MKDirectionsRequest *request = [[MKDirectionsRequest alloc]init];
  
  double originLat = [[from objectForKey:@"lat"] doubleValue];
  double originLng = [[from objectForKey:@"lng"] doubleValue];
  double destinationLat = [[to objectForKey:@"lat"] doubleValue];
  double destinationLng = [[to objectForKey:@"lng"] doubleValue];
  
  MKPlacemark *originCoords = [[MKPlacemark alloc] initWithCoordinate:CLLocationCoordinate2DMake(originLat, originLng)];
  MKPlacemark *destinationCoords = [[MKPlacemark alloc] initWithCoordinate:CLLocationCoordinate2DMake(destinationLat, destinationLng)];
  
  [request setSource:[[MKMapItem alloc] initWithPlacemark:originCoords]];
  [request setDestination:[[MKMapItem alloc] initWithPlacemark:destinationCoords]];

  [request setTransportType:[mode isEqualToString:@"walking"] ? MKDirectionsTransportTypeWalking : MKDirectionsTransportTypeAutomobile];
  
  [request setRequestsAlternateRoutes:NO];
  
  MKDirections *directions = [[MKDirections alloc] initWithRequest:request];
      
  [directions calculateDirectionsWithCompletionHandler:^(MKDirectionsResponse *response, NSError *error) {
    if (!error && [response routes] > 0) {
        MKRoute *route = [[response routes] objectAtIndex:0];
            
        NSDictionary *res =@{
          @"eta": [NSNumber numberWithDouble:route.expectedTravelTime],
          @"distance": [NSNumber numberWithDouble: route.distance]
        };
      
      resolve(res);
    } else {
      reject(@"Map", @"DistanceMatrix failed", error);
    }
  }];
}

@end
