# Resolve react_native_pods.rb with node to allow for hoisting
def node_require(script)
   # Resolve script with node to allow for hoisting
   require Pod::Executable.execute_command('node', ['-p',
    "require.resolve(
      '#{script}',
      {paths: [process.argv[1]]},
    )", __dir__]).strip
end

require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'
require_relative('../node_modules/react-native-permissions/scripts/setup.rb')

platform :ios, '13.4'

target 'Perkd' do
  config = use_native_modules!

  # external
  # pod 'Firebase/MLVisionBarcodeModel'
  pod 'Stripe', '22.8.4'

  pod "MWPhotoBrowser", :path =>'../src/ios/MWPhotoBrowser'

  # RNPermissions
  setup_permissions([
    # 'AppTrackingTransparency',
    'Bluetooth',
    'Calendars',
    'CalendarsWriteOnly',
    'Camera',
    'Contacts',
    'FaceID',
    'LocationAccuracy',
    'LocationAlways',
    'LocationWhenInUse',
    # 'MediaLibrary',
    #   'Microphone',
    #   'Motion',
    'Notifications',
    'PhotoLibrary',
    'PhotoLibraryAddOnly',
    # 'Reminders',
    # 'Siri',
    #   'SpeechRecognition',
    #   'StoreKit',
  ])

  pod 'React-RCTText', :path => '../node_modules/react-native/Libraries/Text', :modular_headers => true

  pod 'react-native-camera', path: '../node_modules/react-native-camera', subspecs: [
    'BarcodeDetectorMLKit'
  ]
  
  pod 'GoogleMLKit/BarcodeScanning', '3.2.0'

  use_react_native!(
    :path => config[:reactNativePath],
    # to enable hermes on iOS, change `false` to `true` and then install pods
    :hermes_enabled => false
  )

  # Enables Flipper.
  #
  # Note that if you have use_frameworks! enabled, Flipper will not work and
  # you should disable the next line.
  use_flipper!({'Flipper' => '0.171.1'})
  post_install do |installer|
    react_native_post_install(installer)
    installer.pods_project.build_configurations.each do |config|
      config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
    end
    installer.pods_project.targets.each do |target|
      if target.name == 'Flipper'
        file_path = 'Pods/Flipper/xplat/Flipper/FlipperTransportTypes.h'
        contents = File.read(file_path)
        unless contents.include?('#include <functional>')
          File.chmod(0755, file_path)
          File.open(file_path, 'w') do |file|
            file.puts('#include <functional>')
            file.puts(contents)
          end
        end
      end
      
      target.build_configurations.each do |config|
        config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)', '_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION']
      end
    end
    __apply_Xcode_12_5_M1_post_install_workaround(installer)
  end
end
