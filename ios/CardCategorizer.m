#import <React/RCTBridgeModule.h>

@interface RCT_EXTERN_MODULE(CardCategorizer, NSObject)

RCT_EXTERN_METHOD(categorizeCard:(NSString *)cardName
                  withResolver:(RCTPromiseResolveBlock)resolve
                  withRejecter:(RCTPromiseRejectBlock)reject)

RCT_EXTERN_METHOD(categorizeCards:(NSArray *)cardNames
                  withResolver:(RCTPromiseResolveBlock)resolve
                  withRejecter:(RCTPromiseRejectBlock)reject)

@end 