//
//  NotificationService.swift
//  NotificationService
//
//  Created by <PERSON> on 14/8/19.
//  Copyright © 2019 Facebook. All rights reserved.
//

import UserNotifications

extension UNNotificationAttachment {
  static func create(imageFileIdentifier: String, data: NSData, options: [NSObject : AnyObject]?) -> UNNotificationAttachment? {
    
    let fileManager = FileManager.default
    let tmpSubFolderName = ProcessInfo.processInfo.globallyUniqueString
    let fileURLPath      = NSURL(fileURLWithPath: NSTemporaryDirectory())
    let tmpSubFolderURL  = fileURLPath.appendingPathComponent(tmpSubFolderName, isDirectory: true)
    
    do {
      try fileManager.createDirectory(at: tmpSubFolderURL!, withIntermediateDirectories: true, attributes: nil)
      let fileURL = tmpSubFolderURL?.appendingPathComponent(imageFileIdentifier)
      try data.write(to: fileURL!, options: [])
      let imageAttachment = try UNNotificationAttachment.init(identifier: imageFileIdentifier, url: fileURL!, options: options)
      return imageAttachment
    } catch let error {
      print("error \(error)")
    }
    
    return nil
  }
}

class NotificationService: UNNotificationServiceExtension {
  
  var contentHandler: ((UNNotificationContent) -> Void)?
  var bestAttemptContent: UNMutableNotificationContent?
  
  
  override func didReceive(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
    self.contentHandler = contentHandler
    bestAttemptContent = (request.content.mutableCopy() as? UNMutableNotificationContent)
    
    func failGracefully() {
      contentHandler(request.content)
    }
    
    guard let content = (request.content.mutableCopy() as? UNMutableNotificationContent) else {
      return failGracefully()
    }
    
//    guard let apnsData = content.userInfo["data"] as? [String: Any] else {
//      return failGracefully()
//    }
    
    guard let attachmentURL = content.userInfo["image"] as? String else {
      return failGracefully()
    }
    
    guard let imageData = NSData(contentsOf:NSURL(string: attachmentURL)! as URL) else { return failGracefully() }
    
    let imageFileIdentifier = (attachmentURL as NSString).lastPathComponent
    
    guard let attachment = UNNotificationAttachment.create(imageFileIdentifier: imageFileIdentifier, data: imageData, options: nil) else { return failGracefully() }
    
    content.attachments = [attachment]
    
    let defaults = UserDefaults(suiteName: "group.me.perkd.notify")
    let newBadgeNum = (defaults?.integer(forKey: "notificationBadge") ?? 0) + (content.badge ?? 0).intValue
    content.badge = newBadgeNum as NSNumber;
    defaults?.set(content.badge, forKey: "notificationBadge");
    
    
    contentHandler(content.copy() as! UNNotificationContent)
  }
  
  
  
  override func serviceExtensionTimeWillExpire() {
    // Called just before the extension will be terminated by the system.
    // Use this as an opportunity to deliver your "best attempt" at modified content, otherwise the original push payload will be used.
    if let contentHandler = contentHandler, let bestAttemptContent =  bestAttemptContent {
      contentHandler(bestAttemptContent)
    }
  }
  
}
