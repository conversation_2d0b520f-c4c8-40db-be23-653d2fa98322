// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		06BAE213173AE3785CBA5F69 /* libPods-Perkd.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 43F335C7BF09BFE078233120 /* libPods-Perkd.a */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		C807BD502D5114470011077B /* CardCategorizer.swift in Sources */ = {isa = PBXBuildFile; fileRef = C807BD4F2D5114470011077B /* CardCategorizer.swift */; };
		C807BD512D5114470011077B /* CardCategorizer.m in Sources */ = {isa = PBXBuildFile; fileRef = C807BD4E2D5114470011077B /* CardCategorizer.m */; };
		C80ACEF7281800BC002D8476 /* NotificationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = C80ACEF6281800BC002D8476 /* NotificationService.swift */; };
		C80ACEFB281800BC002D8476 /* NotificationService.appex in Embed App Extensions */ = {isa = PBXBuildFile; fileRef = C80ACEF4281800BB002D8476 /* NotificationService.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		C8197F9D2DB9F89700BF533B /* x.realm in Resources */ = {isa = PBXBuildFile; fileRef = C8197F9C2DB9F89700BF533B /* x.realm */; };
		C85026E5296942AF00FFF0FC /* RCTMap.m in Sources */ = {isa = PBXBuildFile; fileRef = C85026E4296942AF00FFF0FC /* RCTMap.m */; };
		C890B949281CEA68009166B2 /* RCTBarcodeScanning.m in Sources */ = {isa = PBXBuildFile; fileRef = C890B948281CEA68009166B2 /* RCTBarcodeScanning.m */; };
		C890B956281D5374009166B2 /* wifion.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C890B94D281D5374009166B2 /* wifion.m4a */; };
		C890B957281D5374009166B2 /* fail.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C890B94E281D5374009166B2 /* fail.m4a */; };
		C890B958281D5374009166B2 /* chord.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C890B94F281D5374009166B2 /* chord.m4a */; };
		C890B959281D5374009166B2 /* beep.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = C890B950281D5374009166B2 /* beep.mp3 */; };
		C890B95A281D5374009166B2 /* success.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C890B951281D5374009166B2 /* success.m4a */; };
		C890B95B281D5374009166B2 /* notify.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C890B952281D5374009166B2 /* notify.m4a */; };
		C890B95C281D5374009166B2 /* upsell.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C890B953281D5374009166B2 /* upsell.m4a */; };
		C890B95D281D5374009166B2 /* completed.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C890B954281D5374009166B2 /* completed.m4a */; };
		C890B95E281D5374009166B2 /* scan.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C890B955281D5374009166B2 /* scan.m4a */; };
		C8913F9B29CD585900E6E8F2 /* successbell.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C8913F9A29CD585900E6E8F2 /* successbell.m4a */; };
		C8913F9D29CD595C00E6E8F2 /* cashier.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C8913F9C29CD595B00E6E8F2 /* cashier.m4a */; };
		C8913F9F29CD596400E6E8F2 /* bellchord.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C8913F9E29CD596400E6E8F2 /* bellchord.m4a */; };
		C8913FA129CD596C00E6E8F2 /* servicebell.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C8913FA029CD596C00E6E8F2 /* servicebell.m4a */; };
		C8913FA329CD597400E6E8F2 /* magic.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C8913FA229CD597400E6E8F2 /* magic.m4a */; };
		C8913FA529CD597D00E6E8F2 /* done.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C8913FA429CD597D00E6E8F2 /* done.m4a */; };
		C8913FA729CD598500E6E8F2 /* correct.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C8913FA629CD598500E6E8F2 /* correct.m4a */; };
		C8913FA929CD598D00E6E8F2 /* happy.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C8913FA829CD598D00E6E8F2 /* happy.m4a */; };
		C8913FAB29CD599500E6E8F2 /* cash.m4a in Resources */ = {isa = PBXBuildFile; fileRef = C8913FAA29CD599500E6E8F2 /* cash.m4a */; };
		C89824FB2816DF4E00C9530B /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = C89824FA2816DF4E00C9530B /* GoogleService-Info.plist */; };
		C89825092816E26300C9530B /* File.swift in Sources */ = {isa = PBXBuildFile; fileRef = C89825082816E26300C9530B /* File.swift */; };
		C898250C2816E3DB00C9530B /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = C898250A2816E3DB00C9530B /* LaunchScreen.xib */; };
		C898250F2816E52400C9530B /* PerkdHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = C898250E2816E52400C9530B /* PerkdHelper.m */; };
		C89825192816EF0500C9530B /* Melbourne_bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = C89825132816EF0500C9530B /* Melbourne_bold.otf */; };
		C898251A2816EF0500C9530B /* Melbourne.otf in Resources */ = {isa = PBXBuildFile; fileRef = C89825142816EF0500C9530B /* Melbourne.otf */; };
		C898251B2816EF0500C9530B /* MelbourneMono.otf in Resources */ = {isa = PBXBuildFile; fileRef = C89825152816EF0500C9530B /* MelbourneMono.otf */; };
		C898251E2816EF0500C9530B /* picon.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C89825182816EF0500C9530B /* picon.ttf */; };
		C8BCCF39281DAFCF00415C11 /* applets in Resources */ = {isa = PBXBuildFile; fileRef = C8BCCF38281DAFCF00415C11 /* applets */; };
		C8BCCF3B281DAFE100415C11 /* shared in Resources */ = {isa = PBXBuildFile; fileRef = C8BCCF3A281DAFE100415C11 /* shared */; };
		C8EA2AC7281E4DB9008350AF /* RNBackgroundFetch+AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = C8EA2AC6281E4DB9008350AF /* RNBackgroundFetch+AppDelegate.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		C80ACEF9281800BC002D8476 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C80ACEF3281800BB002D8476;
			remoteInfo = NotificationService;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		C80ACEDC2817E0C1002D8476 /* Embed App Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				C80ACEFB281800BC002D8476 /* NotificationService.appex in Embed App Extensions */,
			);
			name = "Embed App Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* PerkdTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PerkdTests.m; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* Perkd.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Perkd.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = Perkd/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AppDelegate.m; path = Perkd/AppDelegate.m; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = Perkd/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = Perkd/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = Perkd/main.m; sourceTree = "<group>"; };
		2BDD7D2DA9ACB12132AB829A /* Pods-Perkd.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Perkd.debug.xcconfig"; path = "Target Support Files/Pods-Perkd/Pods-Perkd.debug.xcconfig"; sourceTree = "<group>"; };
		3BD7C3B7206D21ABA0629D0A /* Pods-Perkd-PerkdTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Perkd-PerkdTests.release.xcconfig"; path = "Target Support Files/Pods-Perkd-PerkdTests/Pods-Perkd-PerkdTests.release.xcconfig"; sourceTree = "<group>"; };
		43F335C7BF09BFE078233120 /* libPods-Perkd.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-Perkd.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		5AC86D823316389AB3ACE987 /* Pods-Perkd-PerkdTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Perkd-PerkdTests.debug.xcconfig"; path = "Target Support Files/Pods-Perkd-PerkdTests/Pods-Perkd-PerkdTests.debug.xcconfig"; sourceTree = "<group>"; };
		ADAB26D04A8A48E1793F0BAF /* Pods-Perkd.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Perkd.release.xcconfig"; path = "Target Support Files/Pods-Perkd/Pods-Perkd.release.xcconfig"; sourceTree = "<group>"; };
		C05A6A737AF36D21B82F5BB8 /* libPods-Perkd-PerkdTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-Perkd-PerkdTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		C807BD4E2D5114470011077B /* CardCategorizer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CardCategorizer.m; sourceTree = "<group>"; };
		C807BD4F2D5114470011077B /* CardCategorizer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CardCategorizer.swift; sourceTree = "<group>"; };
		C80ACEF4281800BB002D8476 /* NotificationService.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = NotificationService.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		C80ACEF6281800BC002D8476 /* NotificationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationService.swift; sourceTree = "<group>"; };
		C80ACEF8281800BC002D8476 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		C8197F9C2DB9F89700BF533B /* x.realm */ = {isa = PBXFileReference; lastKnownFileType = file; name = x.realm; path = ../../android/app/src/main/assets/x.realm; sourceTree = "<group>"; };
		C85026E329693DA800FFF0FC /* RCTMap.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RCTMap.h; sourceTree = "<group>"; };
		C85026E4296942AF00FFF0FC /* RCTMap.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RCTMap.m; sourceTree = "<group>"; };
		C890B947281CE9A9009166B2 /* RCTBarcodeScanning.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RCTBarcodeScanning.h; sourceTree = "<group>"; };
		C890B948281CEA68009166B2 /* RCTBarcodeScanning.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RCTBarcodeScanning.m; sourceTree = "<group>"; };
		C890B94C281D4F79009166B2 /* NotificationService.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = NotificationService.entitlements; sourceTree = "<group>"; };
		C890B94D281D5374009166B2 /* wifion.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = wifion.m4a; path = ../android/app/src/main/res/raw/wifion.m4a; sourceTree = "<group>"; };
		C890B94E281D5374009166B2 /* fail.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = fail.m4a; path = ../android/app/src/main/res/raw/fail.m4a; sourceTree = "<group>"; };
		C890B94F281D5374009166B2 /* chord.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = chord.m4a; path = ../android/app/src/main/res/raw/chord.m4a; sourceTree = "<group>"; };
		C890B950281D5374009166B2 /* beep.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = beep.mp3; path = ../android/app/src/main/res/raw/beep.mp3; sourceTree = "<group>"; };
		C890B951281D5374009166B2 /* success.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = success.m4a; path = ../android/app/src/main/res/raw/success.m4a; sourceTree = "<group>"; };
		C890B952281D5374009166B2 /* notify.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = notify.m4a; path = ../android/app/src/main/res/raw/notify.m4a; sourceTree = "<group>"; };
		C890B953281D5374009166B2 /* upsell.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = upsell.m4a; path = ../android/app/src/main/res/raw/upsell.m4a; sourceTree = "<group>"; };
		C890B954281D5374009166B2 /* completed.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = completed.m4a; path = ../android/app/src/main/res/raw/completed.m4a; sourceTree = "<group>"; };
		C890B955281D5374009166B2 /* scan.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = scan.m4a; path = ../android/app/src/main/res/raw/scan.m4a; sourceTree = "<group>"; };
		C8913F9A29CD585900E6E8F2 /* successbell.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = successbell.m4a; path = ../android/app/src/main/res/raw/successbell.m4a; sourceTree = "<group>"; };
		C8913F9C29CD595B00E6E8F2 /* cashier.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = cashier.m4a; path = ../android/app/src/main/res/raw/cashier.m4a; sourceTree = "<group>"; };
		C8913F9E29CD596400E6E8F2 /* bellchord.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = bellchord.m4a; path = ../android/app/src/main/res/raw/bellchord.m4a; sourceTree = "<group>"; };
		C8913FA029CD596C00E6E8F2 /* servicebell.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = servicebell.m4a; path = ../android/app/src/main/res/raw/servicebell.m4a; sourceTree = "<group>"; };
		C8913FA229CD597400E6E8F2 /* magic.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = magic.m4a; path = ../android/app/src/main/res/raw/magic.m4a; sourceTree = "<group>"; };
		C8913FA429CD597D00E6E8F2 /* done.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = done.m4a; path = ../android/app/src/main/res/raw/done.m4a; sourceTree = "<group>"; };
		C8913FA629CD598500E6E8F2 /* correct.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = correct.m4a; path = ../android/app/src/main/res/raw/correct.m4a; sourceTree = "<group>"; };
		C8913FA829CD598D00E6E8F2 /* happy.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = happy.m4a; path = ../android/app/src/main/res/raw/happy.m4a; sourceTree = "<group>"; };
		C8913FAA29CD599500E6E8F2 /* cash.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; name = cash.m4a; path = ../android/app/src/main/res/raw/cash.m4a; sourceTree = "<group>"; };
		C89824FA2816DF4E00C9530B /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "Perkd/GoogleService-Info.plist"; sourceTree = "<group>"; };
		C89824FE2816DF6F00C9530B /* PerkdRelease.entitlements */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.entitlements; name = PerkdRelease.entitlements; path = Perkd/PerkdRelease.entitlements; sourceTree = "<group>"; };
		C89824FF2816DF7000C9530B /* Perkd.entitlements */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.entitlements; name = Perkd.entitlements; path = Perkd/Perkd.entitlements; sourceTree = "<group>"; };
		C89825072816E26300C9530B /* Perkd-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Perkd-Bridging-Header.h"; sourceTree = "<group>"; };
		C89825082816E26300C9530B /* File.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = File.swift; sourceTree = "<group>"; };
		C898250B2816E3DB00C9530B /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Perkd/Base.lproj/LaunchScreen.xib; sourceTree = "<group>"; };
		C898250D2816E52300C9530B /* PerkdHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = PerkdHelper.h; path = Perkd/PerkdHelper.h; sourceTree = "<group>"; };
		C898250E2816E52400C9530B /* PerkdHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = PerkdHelper.m; path = Perkd/PerkdHelper.m; sourceTree = "<group>"; };
		C89825132816EF0500C9530B /* Melbourne_bold.otf */ = {isa = PBXFileReference; lastKnownFileType = file; name = Melbourne_bold.otf; path = ../../src/resources/fonts/Melbourne_bold.otf; sourceTree = "<group>"; };
		C89825142816EF0500C9530B /* Melbourne.otf */ = {isa = PBXFileReference; lastKnownFileType = file; name = Melbourne.otf; path = ../../src/resources/fonts/Melbourne.otf; sourceTree = "<group>"; };
		C89825152816EF0500C9530B /* MelbourneMono.otf */ = {isa = PBXFileReference; lastKnownFileType = file; name = MelbourneMono.otf; path = ../../src/resources/fonts/MelbourneMono.otf; sourceTree = "<group>"; };
		C89825182816EF0500C9530B /* picon.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = picon.ttf; path = ../../src/resources/fonts/picon.ttf; sourceTree = "<group>"; };
		C8BCCF38281DAFCF00415C11 /* applets */ = {isa = PBXFileReference; lastKnownFileType = folder; name = applets; path = ../android/app/src/main/assets/applets; sourceTree = "<group>"; };
		C8BCCF3A281DAFE100415C11 /* shared */ = {isa = PBXFileReference; lastKnownFileType = folder; name = shared; path = ../android/app/src/main/assets/shared; sourceTree = "<group>"; };
		C8E7352D282FB28E00F6563C /* zh-HK */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-HK"; path = "Perkd/zh-HK.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		C8E7352E282FB29300F6563C /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "Perkd/zh-Hans.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		C8E7352F282FB29700F6563C /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "Perkd/zh-Hant.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		C8E73530282FB2AC00F6563C /* id */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = id; path = Perkd/id.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		C8E73531282FB2B100F6563C /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = Perkd/ja.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		C8E73532282FB2B800F6563C /* ko */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ko; path = Perkd/ko.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		C8E73533282FB2BC00F6563C /* ms */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ms; path = Perkd/ms.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		C8EA2AC6281E4DB9008350AF /* RNBackgroundFetch+AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = "RNBackgroundFetch+AppDelegate.m"; path = "../node_modules/react-native-background-fetch/ios/RNBackgroundFetch/RNBackgroundFetch+AppDelegate.m"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				06BAE213173AE3785CBA5F69 /* libPods-Perkd.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C80ACEF1281800BB002D8476 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* PerkdTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* PerkdTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = PerkdTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* Perkd */ = {
			isa = PBXGroup;
			children = (
				C8913FAA29CD599500E6E8F2 /* cash.m4a */,
				C8913FA829CD598D00E6E8F2 /* happy.m4a */,
				C8913FA629CD598500E6E8F2 /* correct.m4a */,
				C8913FA429CD597D00E6E8F2 /* done.m4a */,
				C8913FA229CD597400E6E8F2 /* magic.m4a */,
				C8913FA029CD596C00E6E8F2 /* servicebell.m4a */,
				C8913F9E29CD596400E6E8F2 /* bellchord.m4a */,
				C8913F9C29CD595B00E6E8F2 /* cashier.m4a */,
				C8913F9A29CD585900E6E8F2 /* successbell.m4a */,
				C8EA2AC6281E4DB9008350AF /* RNBackgroundFetch+AppDelegate.m */,
				C890B950281D5374009166B2 /* beep.mp3 */,
				C890B94F281D5374009166B2 /* chord.m4a */,
				C890B954281D5374009166B2 /* completed.m4a */,
				C890B94E281D5374009166B2 /* fail.m4a */,
				C890B952281D5374009166B2 /* notify.m4a */,
				C890B955281D5374009166B2 /* scan.m4a */,
				C890B951281D5374009166B2 /* success.m4a */,
				C890B953281D5374009166B2 /* upsell.m4a */,
				C890B94D281D5374009166B2 /* wifion.m4a */,
				C898250D2816E52300C9530B /* PerkdHelper.h */,
				C898250E2816E52400C9530B /* PerkdHelper.m */,
				C898250A2816E3DB00C9530B /* LaunchScreen.xib */,
				C89825082816E26300C9530B /* File.swift */,
				C89824FF2816DF7000C9530B /* Perkd.entitlements */,
				C89824FE2816DF6F00C9530B /* PerkdRelease.entitlements */,
				C89824FA2816DF4E00C9530B /* GoogleService-Info.plist */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.m */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				13B07FB71A68108700A75B9A /* main.m */,
				C89825072816E26300C9530B /* Perkd-Bridging-Header.h */,
				C890B947281CE9A9009166B2 /* RCTBarcodeScanning.h */,
				C890B948281CEA68009166B2 /* RCTBarcodeScanning.m */,
				C85026E329693DA800FFF0FC /* RCTMap.h */,
				C85026E4296942AF00FFF0FC /* RCTMap.m */,
			);
			name = Perkd;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				C89825102816EE8E00C9530B /* Resources */,
				C89825112816EEC100C9530B /* Resources */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				43F335C7BF09BFE078233120 /* libPods-Perkd.a */,
				C05A6A737AF36D21B82F5BB8 /* libPods-Perkd-PerkdTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				C807BD4E2D5114470011077B /* CardCategorizer.m */,
				C807BD4F2D5114470011077B /* CardCategorizer.swift */,
				C8BCCF3A281DAFE100415C11 /* shared */,
				C8BCCF38281DAFCF00415C11 /* applets */,
				C89825122816EEEF00C9530B /* Resources */,
				13B07FAE1A68108700A75B9A /* Perkd */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* PerkdTests */,
				C80ACEF5281800BC002D8476 /* NotificationService */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				8673C524BBF6C461001999F0 /* Pods */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* Perkd.app */,
				C80ACEF4281800BB002D8476 /* NotificationService.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8673C524BBF6C461001999F0 /* Pods */ = {
			isa = PBXGroup;
			children = (
				2BDD7D2DA9ACB12132AB829A /* Pods-Perkd.debug.xcconfig */,
				ADAB26D04A8A48E1793F0BAF /* Pods-Perkd.release.xcconfig */,
				5AC86D823316389AB3ACE987 /* Pods-Perkd-PerkdTests.debug.xcconfig */,
				3BD7C3B7206D21ABA0629D0A /* Pods-Perkd-PerkdTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		C80ACEF5281800BC002D8476 /* NotificationService */ = {
			isa = PBXGroup;
			children = (
				C890B94C281D4F79009166B2 /* NotificationService.entitlements */,
				C80ACEF6281800BC002D8476 /* NotificationService.swift */,
				C80ACEF8281800BC002D8476 /* Info.plist */,
			);
			path = NotificationService;
			sourceTree = "<group>";
		};
		C89825102816EE8E00C9530B /* Resources */ = {
			isa = PBXGroup;
			children = (
			);
			path = Resources;
			sourceTree = "<group>";
		};
		C89825112816EEC100C9530B /* Resources */ = {
			isa = PBXGroup;
			children = (
			);
			path = Resources;
			sourceTree = "<group>";
		};
		C89825122816EEEF00C9530B /* Resources */ = {
			isa = PBXGroup;
			children = (
				C8197F9C2DB9F89700BF533B /* x.realm */,
				C89825132816EF0500C9530B /* Melbourne_bold.otf */,
				C89825142816EF0500C9530B /* Melbourne.otf */,
				C89825152816EF0500C9530B /* MelbourneMono.otf */,
				C89825182816EF0500C9530B /* picon.ttf */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* Perkd */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "Perkd" */;
			buildPhases = (
				98B24310D7A2E4414E583272 /* [CP] Check Pods Manifest.lock */,
				C80ACEDC2817E0C1002D8476 /* Embed App Extensions */,
				FD10A7F022414F080027D42C /* Start Packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				8F473981C97A5F30109FB74D /* [CP] Embed Pods Frameworks */,
				B663299385D6E3BDFE2CBC9A /* [CP] Copy Pods Resources */,
				A05B77481FDD4B6C630B950E /* [CP-User] [RNFB] Core Configuration */,
			);
			buildRules = (
			);
			dependencies = (
				C80ACEFA281800BC002D8476 /* PBXTargetDependency */,
			);
			name = Perkd;
			productName = Perkd;
			productReference = 13B07F961A680F5B00A75B9A /* Perkd.app */;
			productType = "com.apple.product-type.application";
		};
		C80ACEF3281800BB002D8476 /* NotificationService */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C80ACEFC281800BC002D8476 /* Build configuration list for PBXNativeTarget "NotificationService" */;
			buildPhases = (
				C80ACEF0281800BB002D8476 /* Sources */,
				C80ACEF1281800BB002D8476 /* Frameworks */,
				C80ACEF2281800BB002D8476 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = NotificationService;
			productName = NotificationService;
			productReference = C80ACEF4281800BB002D8476 /* NotificationService.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1330;
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1330;
					};
					C80ACEF3281800BB002D8476 = {
						CreatedOnToolsVersion = 13.3.1;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "Perkd" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-HK",
				"zh-Hans",
				"zh-Hant",
				id,
				ja,
				ko,
				ms,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* Perkd */,
				C80ACEF3281800BB002D8476 /* NotificationService */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C8913F9D29CD595C00E6E8F2 /* cashier.m4a in Resources */,
				C890B958281D5374009166B2 /* chord.m4a in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				C8913FA529CD597D00E6E8F2 /* done.m4a in Resources */,
				C890B959281D5374009166B2 /* beep.mp3 in Resources */,
				C898250C2816E3DB00C9530B /* LaunchScreen.xib in Resources */,
				C898251B2816EF0500C9530B /* MelbourneMono.otf in Resources */,
				C8913FAB29CD599500E6E8F2 /* cash.m4a in Resources */,
				C898251A2816EF0500C9530B /* Melbourne.otf in Resources */,
				C8BCCF39281DAFCF00415C11 /* applets in Resources */,
				C8197F9D2DB9F89700BF533B /* x.realm in Resources */,
				C898251E2816EF0500C9530B /* picon.ttf in Resources */,
				C890B95B281D5374009166B2 /* notify.m4a in Resources */,
				C8BCCF3B281DAFE100415C11 /* shared in Resources */,
				C8913FA129CD596C00E6E8F2 /* servicebell.m4a in Resources */,
				C8913F9B29CD585900E6E8F2 /* successbell.m4a in Resources */,
				C890B95C281D5374009166B2 /* upsell.m4a in Resources */,
				C8913F9F29CD596400E6E8F2 /* bellchord.m4a in Resources */,
				C89825192816EF0500C9530B /* Melbourne_bold.otf in Resources */,
				C890B95D281D5374009166B2 /* completed.m4a in Resources */,
				C8913FA329CD597400E6E8F2 /* magic.m4a in Resources */,
				C890B956281D5374009166B2 /* wifion.m4a in Resources */,
				C890B95E281D5374009166B2 /* scan.m4a in Resources */,
				C890B95A281D5374009166B2 /* success.m4a in Resources */,
				C8913FA729CD598500E6E8F2 /* correct.m4a in Resources */,
				C890B957281D5374009166B2 /* fail.m4a in Resources */,
				C89824FB2816DF4E00C9530B /* GoogleService-Info.plist in Resources */,
				C8913FA929CD598D00E6E8F2 /* happy.m4a in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C80ACEF2281800BB002D8476 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nexport NODE_BINARY=/usr/local/bin/node\n../node_modules/react-native/scripts/react-native-xcode.sh\n";
		};
		8F473981C97A5F30109FB74D /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Perkd/Pods-Perkd-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Perkd/Pods-Perkd-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Perkd/Pods-Perkd-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		98B24310D7A2E4414E583272 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Perkd-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		A05B77481FDD4B6C630B950E /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		B663299385D6E3BDFE2CBC9A /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Perkd/Pods-Perkd-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Perkd/Pods-Perkd-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Perkd/Pods-Perkd-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */,
				C89825092816E26300C9530B /* File.swift in Sources */,
				C890B949281CEA68009166B2 /* RCTBarcodeScanning.m in Sources */,
				C8EA2AC7281E4DB9008350AF /* RNBackgroundFetch+AppDelegate.m in Sources */,
				C898250F2816E52400C9530B /* PerkdHelper.m in Sources */,
				C85026E5296942AF00FFF0FC /* RCTMap.m in Sources */,
				C807BD502D5114470011077B /* CardCategorizer.swift in Sources */,
				C807BD512D5114470011077B /* CardCategorizer.m in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C80ACEF0281800BB002D8476 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C80ACEF7281800BC002D8476 /* NotificationService.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		C80ACEFA281800BC002D8476 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = C80ACEF3281800BB002D8476 /* NotificationService */;
			targetProxy = C80ACEF9281800BC002D8476 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		C898250A2816E3DB00C9530B /* LaunchScreen.xib */ = {
			isa = PBXVariantGroup;
			children = (
				C898250B2816E3DB00C9530B /* Base */,
				C8E7352D282FB28E00F6563C /* zh-HK */,
				C8E7352E282FB29300F6563C /* zh-Hans */,
				C8E7352F282FB29700F6563C /* zh-Hant */,
				C8E73530282FB2AC00F6563C /* id */,
				C8E73531282FB2B100F6563C /* ja */,
				C8E73532282FB2B800F6563C /* ko */,
				C8E73533282FB2BC00F6563C /* ms */,
			);
			name = LaunchScreen.xib;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2BDD7D2DA9ACB12132AB829A /* Pods-Perkd.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Perkd/Perkd.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7CTB4T7Y55;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/BVLinearGradient\"",
					"\"${PODS_ROOT}/Headers/Public/Base64\"",
					"\"${PODS_ROOT}/Headers/Public/BugsnagReactNative\"",
					"\"${PODS_ROOT}/Headers/Public/CocoaAsyncSocket\"",
					"\"${PODS_ROOT}/Headers/Public/CodePush\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/FBReactNativeSpec\"",
					"\"${PODS_ROOT}/Headers/Public/Firebase\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCore\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCoreDiagnostics\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseInstallations\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Fmt\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Glog\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-PeerTalk\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-RSocket\"",
					"\"${PODS_ROOT}/Headers/Public/FlipperKit\"",
					"\"${PODS_ROOT}/Headers/Public/GCDWebServer\"",
					"\"${PODS_ROOT}/Headers/Public/GTMSessionFetcher\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleAPIClientForREST\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleDataTransport\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleToolboxForMac\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleUtilities\"",
					"\"${PODS_ROOT}/Headers/Public/InputMask\"",
					"\"${PODS_ROOT}/Headers/Public/JWT\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-BluetoothPeripheral\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-Calendars\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-Camera\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-Contacts\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-FaceID\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-LocationAccuracy\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-LocationAlways\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-LocationWhenInUse\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-Notifications\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-PhotoLibrary\"",
					"\"${PODS_ROOT}/Headers/Public/PromisesObjC\"",
					"\"${PODS_ROOT}/Headers/Public/Protobuf\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTSystemSetting\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNBackgroundFetch\"",
					"\"${PODS_ROOT}/Headers/Public/RNBarcode\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNCClipboard\"",
					"\"${PODS_ROOT}/Headers/Public/RNCMaskedView\"",
					"\"${PODS_ROOT}/Headers/Public/RNCPushNotificationIOS\"",
					"\"${PODS_ROOT}/Headers/Public/RNCalendarEvents\"",
					"\"${PODS_ROOT}/Headers/Public/RNDefaultPreference\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBApp\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBMLVision\"",
					"\"${PODS_ROOT}/Headers/Public/RNFS\"",
					"\"${PODS_ROOT}/Headers/Public/RNFastImage\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNImageColors\"",
					"\"${PODS_ROOT}/Headers/Public/RNImageRotate\"",
					"\"${PODS_ROOT}/Headers/Public/RNInAppBrowser\"",
					"\"${PODS_ROOT}/Headers/Public/RNKeychain\"",
					"\"${PODS_ROOT}/Headers/Public/RNLocalize\"",
					"\"${PODS_ROOT}/Headers/Public/RNPermissions\"",
					"\"${PODS_ROOT}/Headers/Public/RNRate\"",
					"\"${PODS_ROOT}/Headers/Public/RNReactNativeHapticFeedback\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSound\"",
					"\"${PODS_ROOT}/Headers/Public/RNTextSize\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeART\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeNavigation\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImage\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImageWebPCoder\"",
					"\"${PODS_ROOT}/Headers/Public/SSZipArchive\"",
					"\"${PODS_ROOT}/Headers/Public/Stripe\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/YogaKit\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/libevent\"",
					"\"${PODS_ROOT}/Headers/Public/libwebp\"",
					"\"${PODS_ROOT}/Headers/Public/lottie-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/nanopb\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-aes-encryption\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-biometrics\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-blur\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-camera\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-carrier-info\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-contacts\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-cookies\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-data-picker\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-date-picker\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-exif\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-fbsdk-next\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-file-access\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-geocoder-reborn\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-geolocation\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-geolocation-service\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-image-editor\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-image-picker\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-image-resizer\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-maps\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-netinfo\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-network-info\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-notification\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-orientation\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-pager-view\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-payments\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-photo-view-ex\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-shake\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-sms\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-spotlight-search\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-sqlite-storage\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-text-input-mask\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webview\"",
					"\"${PODS_ROOT}/Headers/Public/tipsi-stripe\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/RealmJS/Headers\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"$(inherited)",
					"${PODS_ROOT}/Firebase/CoreOnly/Sources",
					"\"${PODS_TARGET_SRCROOT}/Sources/FBLPromises/include\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_TARGET_SRCROOT)/include/\"",
				);
				INFOPLIST_FILE = Perkd/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 6.5.3;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = me.perkd;
				PRODUCT_NAME = Perkd;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Perkd-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ADAB26D04A8A48E1793F0BAF /* Pods-Perkd.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Perkd/Perkd.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7CTB4T7Y55;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/BVLinearGradient\"",
					"\"${PODS_ROOT}/Headers/Public/Base64\"",
					"\"${PODS_ROOT}/Headers/Public/BugsnagReactNative\"",
					"\"${PODS_ROOT}/Headers/Public/CocoaAsyncSocket\"",
					"\"${PODS_ROOT}/Headers/Public/CodePush\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/FBReactNativeSpec\"",
					"\"${PODS_ROOT}/Headers/Public/Firebase\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCore\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCoreDiagnostics\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseInstallations\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Fmt\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Glog\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-PeerTalk\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-RSocket\"",
					"\"${PODS_ROOT}/Headers/Public/FlipperKit\"",
					"\"${PODS_ROOT}/Headers/Public/GCDWebServer\"",
					"\"${PODS_ROOT}/Headers/Public/GTMSessionFetcher\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleAPIClientForREST\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleDataTransport\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleToolboxForMac\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleUtilities\"",
					"\"${PODS_ROOT}/Headers/Public/InputMask\"",
					"\"${PODS_ROOT}/Headers/Public/JWT\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-BluetoothPeripheral\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-Calendars\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-Camera\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-Contacts\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-FaceID\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-LocationAccuracy\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-LocationAlways\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-LocationWhenInUse\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-Notifications\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-PhotoLibrary\"",
					"\"${PODS_ROOT}/Headers/Public/PromisesObjC\"",
					"\"${PODS_ROOT}/Headers/Public/Protobuf\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTSystemSetting\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNBackgroundFetch\"",
					"\"${PODS_ROOT}/Headers/Public/RNBarcode\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNCClipboard\"",
					"\"${PODS_ROOT}/Headers/Public/RNCMaskedView\"",
					"\"${PODS_ROOT}/Headers/Public/RNCPushNotificationIOS\"",
					"\"${PODS_ROOT}/Headers/Public/RNCalendarEvents\"",
					"\"${PODS_ROOT}/Headers/Public/RNDefaultPreference\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBApp\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBMLVision\"",
					"\"${PODS_ROOT}/Headers/Public/RNFS\"",
					"\"${PODS_ROOT}/Headers/Public/RNFastImage\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNImageColors\"",
					"\"${PODS_ROOT}/Headers/Public/RNImageRotate\"",
					"\"${PODS_ROOT}/Headers/Public/RNInAppBrowser\"",
					"\"${PODS_ROOT}/Headers/Public/RNKeychain\"",
					"\"${PODS_ROOT}/Headers/Public/RNLocalize\"",
					"\"${PODS_ROOT}/Headers/Public/RNPermissions\"",
					"\"${PODS_ROOT}/Headers/Public/RNRate\"",
					"\"${PODS_ROOT}/Headers/Public/RNReactNativeHapticFeedback\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSound\"",
					"\"${PODS_ROOT}/Headers/Public/RNTextSize\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeART\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeNavigation\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImage\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImageWebPCoder\"",
					"\"${PODS_ROOT}/Headers/Public/SSZipArchive\"",
					"\"${PODS_ROOT}/Headers/Public/Stripe\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/YogaKit\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/libevent\"",
					"\"${PODS_ROOT}/Headers/Public/libwebp\"",
					"\"${PODS_ROOT}/Headers/Public/lottie-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/nanopb\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-aes-encryption\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-biometrics\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-blur\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-camera\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-carrier-info\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-contacts\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-cookies\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-data-picker\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-date-picker\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-exif\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-fbsdk-next\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-file-access\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-geocoder-reborn\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-geolocation\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-geolocation-service\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-image-editor\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-image-picker\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-image-resizer\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-maps\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-netinfo\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-network-info\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-notification\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-orientation\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-pager-view\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-payments\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-photo-view-ex\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-shake\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-sms\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-spotlight-search\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-sqlite-storage\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-text-input-mask\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webview\"",
					"\"${PODS_ROOT}/Headers/Public/tipsi-stripe\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/RealmJS/Headers\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"$(inherited)",
					"${PODS_ROOT}/Firebase/CoreOnly/Sources",
					"\"${PODS_TARGET_SRCROOT}/Sources/FBLPromises/include\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_TARGET_SRCROOT)/include/\"",
				);
				INFOPLIST_FILE = Perkd/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 6.5.3;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = me.perkd;
				PRODUCT_NAME = Perkd;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Perkd-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		C80ACEFD281800BC002D8476 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = NotificationService/NotificationService.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 3;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 7CTB4T7Y55;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationService/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationService;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 6.0.3;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = me.perkd.NotificationService;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		C80ACEFE281800BC002D8476 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = NotificationService/NotificationService.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 3;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 7CTB4T7Y55;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationService/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationService;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 6.0.3;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = me.perkd.NotificationService;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "Perkd" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "Perkd" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C80ACEFC281800BC002D8476 /* Build configuration list for PBXNativeTarget "NotificationService" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C80ACEFD281800BC002D8476 /* Debug */,
				C80ACEFE281800BC002D8476 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
