import { requireNativeComponent } from 'react-native';

export const Platform = {
	OS: 'ios',
	Version: '10.0',
	select: () => {},
};

export const Dimensions = {
	get: () => ({ width: 77.8, height: 158 }),
};

export const LayoutAnimation = {
	Types: {},
	Properties: {},
};

export const Animated = {
	createAnimatedComponent: () => {},
};

export const NativeModules = {
	RNSound: { prepare: () => {} },
	NotificationApp: { apps: [] },
	PerkdHelper: {},
	RNGestureHandlerModule: {},
};

export { requireNativeComponent };

export class NativeEventEmitter {}

export const I18nManager = {
	isRTL: false,
};

export const StyleSheet = {
	create: () => ({
		dark: {},
	}),
};
