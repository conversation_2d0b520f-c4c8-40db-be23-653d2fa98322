{"cardMaster": {"name": "VIP (DEV)", "brand": {"short": "NOVELA", "long": "", "color": "#63DECC"}, "commerce": {"type": "retail", "fulfilments": [], "pricings": [], "discounts": [{"offerMasterId": "5f3a95637c3b4d670f439717", "name": "10% Member Discount", "kind": "percentage", "value": 0.1, "currency": "SGD", "code": "", "use": "always", "prerequisite": {}, "entitled": {}, "targetType": "item", "targetSelection": "all", "allocation": {"method": "across"}, "qualifiers": {"redeem": {}}, "productId": "", "variantId": ""}, {"use": "always", "prerequisite": {}, "entitled": {}, "kind": "percentage", "value": 1, "currency": "SGD", "targetType": "shipping", "targetSelection": "all", "allocation": {"method": "across"}, "qualifiers": {"redeem": {}}}], "taxes": [{"title": "GST", "rate": 0.07}], "taxIncluded": true}, "payments": {"payee": "Perkd-Demo", "currency": {"code": "SGD", "symbol": "S$", "precision": 2}, "limits": {"min": 10, "max": 1000}, "methods": {"applepay": {"provider": "stripe", "countryCode": "SG", "currencyCode": "SGD", "merchantIdentifier": "merchant.me.perkd.dev", "supportedNetworks": ["visa", "mastercard", "amex"], "version": "5.0.1"}, "googlepay": {"provider": "stripe", "countryCode": "SG", "currencyCode": "SGD", "supportedNetworks": ["visa", "mastercard", "amex"], "allowedAuthMethods": ["panOnly", "cryptogram3ds"], "version": "5.0.1", "environment": "TEST"}, "alipay": {"provider": "stripe", "countryCode": "SG", "currencyCode": "SGD"}, "linepay": {"provider": "mypay", "countryCode": "SG", "currencyCode": "SGD"}, "wechat": {"provider": "stripe", "countryCode": "SG", "currencyCode": "SGD"}, "xfers": {"provider": "xfers", "countryCode": "SG", "currencyCode": "SGD"}}, "taxes": [{"title": "GST", "rate": 0.07}]}, "tenant": {"code": "novelasg"}, "state": "active", "visible": true, "publishedAt": "2020-08-20T10:01:03.183Z", "createdAt": "2017-05-09T01:51:16.000Z", "modifiedAt": "2020-08-20T10:01:03.238Z", "deletedAt": null, "x": {"id": 2436}, "a3": {"id": "5A2452", "membershipConfig": null, "pointsConfig": null, "introConfig": null, "publishedAt": "2020-08-20T10:01:03.237Z"}, "id": "5dc14832a5e2a16cc3099b13", "issuerId": "5c1360d2d750e33c37067641"}}