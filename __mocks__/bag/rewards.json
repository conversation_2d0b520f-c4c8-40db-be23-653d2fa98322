{"rewards": [{"masterId": "591134249afb39c37d09253d", "name": "NOVELA Reward", "brand": "NOVELA Singapore", "startTime": "2020-11-10T04:15:31.624Z", "endTime": "2021-11-09T15:59:59.999Z", "logoImage": {"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/logo/997b1e5b-23ba-4513-8e0e-b389f9c1f4fb.png"}, "qualifiers": {"issuestamps": {"totalPrice": {"$gte": 150}}}, "levels": [{"name": "NOVELA $20 Cash Voucher", "level": 1, "description": "Collect 3 stamps to get $20 Cash Voucher.\r\nGet 1 stamp with min. S$150 spend in a single receipt at Novela stores.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta id=\"viewport\" name=\"viewport\"><style>@font-face { font-family: MelbourneOld; src: url('fonts/Melbourne.ttf');}@font-face { font-family: MelbourneOld; font-weight: bold; src: url('fonts/Melbourne-Bold.ttf');}@font-face { font-family: Melbourne; src: url('fonts/Melbourne.otf');}@font-face { font-family: Melbourne; font-weight: bold; src: url('fonts/Melbourne-Bold.otf');}* { font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif; -webkit-text-size-adjust: none; margin: 0; padding: 0;}body { font-size: 16px;/* EN 16px ZH 14px JA 13px Ko 13px */}#frame { font-size: 1em; margin: 5px 15px 1em;}ul { padding: 0 0 0 1.13em; list-style-type: disc;}li { padding: 0 0 0.32em 0; vertical-align: top; text-align: left; font-size: 1em; line-height: 1.2em; color: #FFFFFF; color: rgba(255,255,255,0.6);}h4 { font-weight: bold; font-size: 1em; color: #FFFFFF; color: rgba(255,255,255,0.8); margin: 0 0 0.32em;}#frame.size1 { font-size: 1.16em;}#frame.size2 { font-size: 1.5em;}#frame.size3 { font-size: 2em;}#frame.size4 { font-size: 2.3em;}#frame.size5 { font-size: 2.8em;}</style><title>Terms & Conditions</title></head><body><div id=\"frame\" class=\"\"> <script> (function(doc) { var frame = document.getElementById('frame'); var deviceWidth; var contentSize; if(window.screen.width){ deviceWidth = window.screen.width; } else { deviceWidth = 320; } if (navigator.userAgent.indexOf('Android') >= 0) { viewport.setAttribute(\"content\", \"width=device-width\"); if(window.devicePixelRatio){ deviceWidth = deviceWidth / window.devicePixelRatio; } } else { var contentWidth = 'width=' + deviceWidth + 'px'; viewport.setAttribute(\"content\", contentWidth); } if(deviceWidth >= 640){ contentSize = 'size3'; } else if(deviceWidth >= 480){ contentSize = 'size2'; } else if(deviceWidth >= 375){ contentSize = 'size1'; } else { contentSize = ''; } frame.setAttribute(\"class\", contentSize); }(document));</script> <h4>Terms & Conditions</h4> <ul><li>This voucher can only be used once.</li><li>This voucher is redeemable against the original price of the item.</li><li>This voucher is not exchangeable for cash.</li><li>This voucher is valid for 2 months upon the issued date.</li><li>Only one voucher can be redeemed per transaction.</li></ul></div></body></html>", "startTime": "2020-11-10T04:15:31.624Z", "endTime": "2021-11-09T15:59:59.999Z", "imageNdx": 0, "stamps": [{"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}], "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/d7383394-1541-4a94-82af-0fa10f74d178.jpg"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/stamp/83b133d3-d0e9-464a-8154-9ea708c409f6.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/stamp/6825b192-d908-4662-a56c-96c7e7f7841f.png"}], "issuedAt": "2020-11-14T08:52:24.453Z", "completedAt": null, "fullyRedeemedAt": null, "offerIds": []}, {"name": "NOVELA $30 Cash Voucher", "level": 2, "description": "Collect 4 stamps to get $30 Cash Voucher.\r\nGet 1 stamp with min. S$150 spend in a single receipt at Novela stores.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta id=\"viewport\" name=\"viewport\"><style>@font-face { font-family: MelbourneOld; src: url('fonts/Melbourne.ttf');}@font-face { font-family: MelbourneOld; font-weight: bold; src: url('fonts/Melbourne-Bold.ttf');}@font-face { font-family: Melbourne; src: url('fonts/Melbourne.otf');}@font-face { font-family: Melbourne; font-weight: bold; src: url('fonts/Melbourne-Bold.otf');}* { font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif; -webkit-text-size-adjust: none; margin: 0; padding: 0;}body { font-size: 16px;/* EN 16px ZH 14px JA 13px Ko 13px */}#frame { font-size: 1em; margin: 5px 15px 1em;}ul { padding: 0 0 0 1.13em; list-style-type: disc;}li { padding: 0 0 0.32em 0; vertical-align: top; text-align: left; font-size: 1em; line-height: 1.2em; color: #FFFFFF; color: rgba(255,255,255,0.6);}h4 { font-weight: bold; font-size: 1em; color: #FFFFFF; color: rgba(255,255,255,0.8); margin: 0 0 0.32em;}#frame.size1 { font-size: 1.16em;}#frame.size2 { font-size: 1.5em;}#frame.size3 { font-size: 2em;}#frame.size4 { font-size: 2.3em;}#frame.size5 { font-size: 2.8em;}</style><title>Terms &amp; Conditions</title></head><body><div id=\"frame\" class=\"\"> <script> (function(doc) { var frame = document.getElementById('frame'); var deviceWidth; var contentSize; if(window.screen.width){ deviceWidth = window.screen.width; } else { deviceWidth = 320; } if (navigator.userAgent.indexOf('Android') >= 0) { viewport.setAttribute(\"content\", \"width=device-width\"); if(window.devicePixelRatio){ deviceWidth = deviceWidth / window.devicePixelRatio; } } else { var contentWidth = 'width=' + deviceWidth + 'px'; viewport.setAttribute(\"content\", contentWidth); } if(deviceWidth >= 640){ contentSize = 'size3'; } else if(deviceWidth >= 480){ contentSize = 'size2'; } else if(deviceWidth >= 375){ contentSize = 'size1'; } else { contentSize = ''; } frame.setAttribute(\"class\", contentSize); }(document));</script> <h4>Terms &amp; Conditions</h4> <ul><li>This voucher can only be used once.</li><li>This voucher is redeemable against the original price of the item.</li><li>This voucher is not exchangeable for cash.</li><li>This voucher is valid for 2 months upon the issued date.</li><li>Only one voucher can be redeemed per transaction.</li></ul></div></body></html>", "startTime": "2020-11-10T04:15:31.624Z", "endTime": "2021-11-09T15:59:59.999Z", "imageNdx": 0, "stamps": [{"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}], "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/71691401-51b9-4a16-9771-773d5a3dac51.jpg"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/stamp/83b133d3-d0e9-464a-8154-9ea708c409f6.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/stamp/6825b192-d908-4662-a56c-96c7e7f7841f.png"}], "issuedAt": "2020-11-14T08:52:24.453Z", "completedAt": null, "fullyRedeemedAt": null, "offerIds": []}, {"name": "NOVELA $50 Cash Voucher", "level": 3, "description": "Collect 6 stamps to get $50 Cash Voucher.\r\nGet 1 stamp with min. S$150 spend in a single receipt at Novela stores.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta id=\"viewport\" name=\"viewport\"><style>@font-face { font-family: MelbourneOld; src: url('fonts/Melbourne.ttf');}@font-face { font-family: MelbourneOld; font-weight: bold; src: url('fonts/Melbourne-Bold.ttf');}@font-face { font-family: Melbourne; src: url('fonts/Melbourne.otf');}@font-face { font-family: Melbourne; font-weight: bold; src: url('fonts/Melbourne-Bold.otf');}* { font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif; -webkit-text-size-adjust: none; margin: 0; padding: 0;}body { font-size: 16px;/* EN 16px ZH 14px JA 13px Ko 13px */}#frame { font-size: 1em; margin: 5px 15px 1em;}ul { padding: 0 0 0 1.13em; list-style-type: disc;}li { padding: 0 0 0.32em 0; vertical-align: top; text-align: left; font-size: 1em; line-height: 1.2em; color: #FFFFFF; color: rgba(255,255,255,0.6);}h4 { font-weight: bold; font-size: 1em; color: #FFFFFF; color: rgba(255,255,255,0.8); margin: 0 0 0.32em;}#frame.size1 { font-size: 1.16em;}#frame.size2 { font-size: 1.5em;}#frame.size3 { font-size: 2em;}#frame.size4 { font-size: 2.3em;}#frame.size5 { font-size: 2.8em;}</style><title>Terms & Conditions</title></head><body><div id=\"frame\" class=\"\"> <script> (function(doc) { var frame = document.getElementById('frame'); var deviceWidth; var contentSize; if(window.screen.width){ deviceWidth = window.screen.width; } else { deviceWidth = 320; } if (navigator.userAgent.indexOf('Android') >= 0) { viewport.setAttribute(\"content\", \"width=device-width\"); if(window.devicePixelRatio){ deviceWidth = deviceWidth / window.devicePixelRatio; } } else { var contentWidth = 'width=' + deviceWidth + 'px'; viewport.setAttribute(\"content\", contentWidth); } if(deviceWidth >= 640){ contentSize = 'size3'; } else if(deviceWidth >= 480){ contentSize = 'size2'; } else if(deviceWidth >= 375){ contentSize = 'size1'; } else { contentSize = ''; } frame.setAttribute(\"class\", contentSize); }(document));</script> <h4>Terms & Conditions</h4> <ul><li>This voucher can only be used once.</li><li>This voucher is redeemable against the original price of the item.</li><li>This voucher is not exchangeable for cash.</li><li>This voucher is valid for 2 months upon the issued date.</li><li>Only one voucher can be redeemed per transaction.</li></ul></div></body></html>", "startTime": "2020-11-10T04:15:31.624Z", "endTime": "2021-11-09T15:59:59.999Z", "imageNdx": 0, "stamps": [{"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}], "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/606c7b82-6897-4ff5-8c84-c27c92c8c901.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/stamp/83b133d3-d0e9-464a-8154-9ea708c409f6.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/stamp/6825b192-d908-4662-a56c-96c7e7f7841f.png"}], "issuedAt": "2020-11-14T08:52:24.453Z", "completedAt": null, "fullyRedeemedAt": null, "offerIds": []}, {"name": "NOVELA $70 Cash Voucher", "level": 4, "description": "Collect 8 stamps to get $70 Cash Voucher.\r\nGet 1 stamp with min. S$150 spend in a single receipt at Novela stores.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta id=\"viewport\" name=\"viewport\"><style>@font-face { font-family: MelbourneOld; src: url('fonts/Melbourne.ttf');}@font-face { font-family: MelbourneOld; font-weight: bold; src: url('fonts/Melbourne-Bold.ttf');}@font-face { font-family: Melbourne; src: url('fonts/Melbourne.otf');}@font-face { font-family: Melbourne; font-weight: bold; src: url('fonts/Melbourne-Bold.otf');}* { font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif; -webkit-text-size-adjust: none; margin: 0; padding: 0;}body { font-size: 16px;/* EN 16px ZH 14px JA 13px Ko 13px */}#frame { font-size: 1em; margin: 5px 15px 1em;}ul { padding: 0 0 0 1.13em; list-style-type: disc;}li { padding: 0 0 0.32em 0; vertical-align: top; text-align: left; font-size: 1em; line-height: 1.2em; color: #FFFFFF; color: rgba(255,255,255,0.6);}h4 { font-weight: bold; font-size: 1em; color: #FFFFFF; color: rgba(255,255,255,0.8); margin: 0 0 0.32em;}#frame.size1 { font-size: 1.16em;}#frame.size2 { font-size: 1.5em;}#frame.size3 { font-size: 2em;}#frame.size4 { font-size: 2.3em;}#frame.size5 { font-size: 2.8em;}</style><title>Terms &amp; Conditions</title></head><body><div id=\"frame\" class=\"\"> <script> (function(doc) { var frame = document.getElementById('frame'); var deviceWidth; var contentSize; if(window.screen.width){ deviceWidth = window.screen.width; } else { deviceWidth = 320; } if (navigator.userAgent.indexOf('Android') >= 0) { viewport.setAttribute(\"content\", \"width=device-width\"); if(window.devicePixelRatio){ deviceWidth = deviceWidth / window.devicePixelRatio; } } else { var contentWidth = 'width=' + deviceWidth + 'px'; viewport.setAttribute(\"content\", contentWidth); } if(deviceWidth >= 640){ contentSize = 'size3'; } else if(deviceWidth >= 480){ contentSize = 'size2'; } else if(deviceWidth >= 375){ contentSize = 'size1'; } else { contentSize = ''; } frame.setAttribute(\"class\", contentSize); }(document));</script> <h4>Terms &amp; Conditions</h4> <ul><li>This voucher can only be used once.</li><li>This voucher is redeemable against the original price of the item.</li><li>This voucher is not exchangeable for cash.</li><li>This voucher is valid for 2 months upon the issued date.</li><li>Only one voucher can be redeemed per transaction.</li></ul></div></body></html>", "startTime": "2020-11-10T04:15:31.624Z", "endTime": "2021-11-09T15:59:59.999Z", "imageNdx": 0, "stamps": [{"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "NOVELA Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}], "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/cb20f5dd-7dd4-469a-b4c8-8e5f9d5ebc09.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/stamp/83b133d3-d0e9-464a-8154-9ea708c409f6.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/stamp/6825b192-d908-4662-a56c-96c7e7f7841f.png"}], "issuedAt": "2020-11-14T08:52:24.453Z", "completedAt": null, "fullyRedeemedAt": null, "offerIds": []}], "transactions": [], "reminders": {}, "triggers": {}, "options": {"manual": false}, "state": "active", "shareModes": [], "globalize": {"t": {"zh-Hant-HK": {"name": "NOVELA 獎勵計畫", "levels": [{"name": "$20元代金券", "description": "您只要一次性消费至少$150, 将可以获取一个印章。集满3个印章，您将得到$20元代金券。", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 14px;/*EN 16pxZH 14px JA 13px Ko 13px*/}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}</style><title>使用条款</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>使用条款</h4><ul><li>一张代金券只可以使用一次。</li><li>此代金券不能用于限定产品。</li><li>本店并不提供把代金券换成现金的服务。</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/edbc1435-2536-472b-97ad-c807fd9a3d13.jpg"}]}, {"name": "$30元代金券", "description": "您只要一次性消费至少$150, 将可以获取一个印章。再集满4个印章，您将得到$30元代金券。", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 14px;/*EN 16pxZH 14px JA 13px Ko 13px*/}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}</style><title>使用条款</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>使用条款</h4><ul><li>一张代金券只可以使用一次。</li><li>此代金券不能用于限定产品。</li><li>本店并不提供把代金券换成现金的服务。</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/8ce5828f-b95c-4d39-b00a-b730deb94ac6.jpg"}]}, {"name": "$50元代金券", "description": "您只要一次性消费至少$150, 将可以获取一个印章。再集满6个印章，您将得到$50元代金券。", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 14px;/*EN 16pxZH 14px JA 13px Ko 13px*/}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}</style><title>使用条款</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>使用条款</h4><ul><li>一张代金券只可以使用一次。</li><li>此代金券不能用于限定产品。</li><li>本店并不提供把代金券换成现金的服务。</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/7bc3574c-0112-4f76-837b-32d5448550c0.png"}]}, {"name": "$70元代金券", "description": "您只要一次性消费至少$150, 将可以获取一个印章。再集满8个印章，您将得到$70元代金券。", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 14px;/*EN 16pxZH 14px JA 13px Ko 13px*/}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}</style><title>使用条款</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>使用条款</h4><ul><li>一张代金券只可以使用一次。</li><li>此代金券不能用于限定产品。</li><li>本店并不提供把代金券换成现金的服务。</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/70f84c36-4ae7-447f-9167-2f20e1082e25.png"}]}]}, "zh-Hant": {"name": "NOVELA 獎勵計畫", "levels": [{"name": "$20元代金券", "description": "您只要一次性消费至少$150, 将可以获取一个印章。集满3个印章，您将得到$20元代金券。", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 14px;/*EN 16pxZH 14px JA 13px Ko 13px*/}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}</style><title>使用条款</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>使用条款</h4><ul><li>一张代金券只可以使用一次。</li><li>此代金券不能用于限定产品。</li><li>本店并不提供把代金券换成现金的服务。</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/74ccd346-1199-456b-9285-6928edf8b14d.jpg"}]}, {"name": "$30元代金券", "description": "您只要一次性消费至少$150, 将可以获取一个印章。再集满4个印章，您将得到$30元代金券。", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 14px;/*EN 16pxZH 14px JA 13px Ko 13px*/}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}</style><title>使用条款</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>使用条款</h4><ul><li>一张代金券只可以使用一次。</li><li>此代金券不能用于限定产品。</li><li>本店并不提供把代金券换成现金的服务。</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/ccd91c3e-4e4b-4503-b88e-6e57736bcbd9.jpg"}]}, {"name": "$50元代金券", "description": "您只要一次性消费至少$150, 将可以获取一个印章。再集满6个印章，您将得到$50元代金券。", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 14px;/*EN 16pxZH 14px JA 13px Ko 13px*/}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}</style><title>使用条款</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>使用条款</h4><ul><li>一张代金券只可以使用一次。</li><li>此代金券不能用于限定产品。</li><li>本店并不提供把代金券换成现金的服务。</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/7a9b344a-baa2-4100-b3d0-48f889e77f72.png"}]}, {"name": "$70元代金券", "description": "您只要一次性消费至少$150, 将可以获取一个印章。再集满8个印章，您将得到$70元代金券。", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 14px;/*EN 16pxZH 14px JA 13px Ko 13px*/}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}</style><title>使用条款</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>使用条款</h4><ul><li>一张代金券只可以使用一次。</li><li>此代金券不能用于限定产品。</li><li>本店并不提供把代金券换成现金的服务。</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/42d24382-c970-4492-907b-b1f76d6d0a86.png"}]}]}, "zh-Hans": {"name": "NOVELA 奖励计划", "levels": [{"name": "$20元代金券", "description": "您只要一次性消费至少$150, 将可以获取一个印章。集满3个印章，您将得到$20元代金券。", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 14px;/*EN 16pxZH 14px JA 13px Ko 13px*/}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}</style><title>使用条款</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>使用条款</h4><ul><li>一张代金券只可以使用一次。</li><li>此代金券不能用于限定产品。</li><li>本店并不提供把代金券换成现金的服务。</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/958e7ad0-2e69-40c8-848c-19bca8206c15.jpg"}]}, {"name": "$30元代金券", "description": "您只要一次性消费至少$150, 将可以获取一个印章。再集满4个印章，您将得到$30元代金券。", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 14px;/*EN 16pxZH 14px JA 13px Ko 13px*/}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}</style><title>使用条款</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>使用条款</h4><ul><li>一张代金券只可以使用一次。</li><li>此代金券不能用于限定产品。</li><li>本店并不提供把代金券换成现金的服务。</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/2929c294-d092-451d-94a8-51d8f9ce9bbc.jpg"}]}, {"name": "$50元代金券", "description": "您只要一次性消费至少$150, 将可以获取一个印章。再集满6个印章，您将得到$50元代金券。", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 14px;/*EN 16pxZH 14px JA 13px Ko 13px*/}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}</style><title>使用条款</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>使用条款</h4><ul><li>一张代金券只可以使用一次。</li><li>此代金券不能用于限定产品。</li><li>本店并不提供把代金券换成现金的服务。</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/e551e29a-3ecf-443f-a4d1-df33e6216691.png"}]}, {"name": "$70元代金券", "description": "您只要一次性消费至少$150, 将可以获取一个印章。再集满8个印章，您将得到$70元代金券。", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 14px;/*EN 16pxZH 14px JA 13px Ko 13px*/}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}</style><title>使用条款</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>使用条款</h4><ul><li>一张代金券只可以使用一次。</li><li>此代金券不能用于限定产品。</li><li>本店并不提供把代金券换成现金的服务。</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/8dd27d5e-24e8-464b-ba11-1422c686cf91.png"}]}]}, "en": {"name": "NOVELA Reward", "shortName": "NRS", "levels": [{"name": "NOVELA $20 Cash Voucher", "description": "Collect 3 stamps to get $20 Cash Voucher.\r\nGet 1 stamp with min. S$150 spend in a single receipt at Novela stores.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta id=\"viewport\" name=\"viewport\"><style>@font-face { font-family: MelbourneOld; src: url('fonts/Melbourne.ttf');}@font-face { font-family: MelbourneOld; font-weight: bold; src: url('fonts/Melbourne-Bold.ttf');}@font-face { font-family: Melbourne; src: url('fonts/Melbourne.otf');}@font-face { font-family: Melbourne; font-weight: bold; src: url('fonts/Melbourne-Bold.otf');}* { font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif; -webkit-text-size-adjust: none; margin: 0; padding: 0;}body { font-size: 16px;/* EN 16px ZH 14px JA 13px Ko 13px */}#frame { font-size: 1em; margin: 5px 15px 1em;}ul { padding: 0 0 0 1.13em; list-style-type: disc;}li { padding: 0 0 0.32em 0; vertical-align: top; text-align: left; font-size: 1em; line-height: 1.2em; color: #FFFFFF; color: rgba(255,255,255,0.6);}h4 { font-weight: bold; font-size: 1em; color: #FFFFFF; color: rgba(255,255,255,0.8); margin: 0 0 0.32em;}#frame.size1 { font-size: 1.16em;}#frame.size2 { font-size: 1.5em;}#frame.size3 { font-size: 2em;}#frame.size4 { font-size: 2.3em;}#frame.size5 { font-size: 2.8em;}</style><title>Terms & Conditions</title></head><body><div id=\"frame\" class=\"\"> <script> (function(doc) { var frame = document.getElementById('frame'); var deviceWidth; var contentSize; if(window.screen.width){ deviceWidth = window.screen.width; } else { deviceWidth = 320; } if (navigator.userAgent.indexOf('Android') >= 0) { viewport.setAttribute(\"content\", \"width=device-width\"); if(window.devicePixelRatio){ deviceWidth = deviceWidth / window.devicePixelRatio; } } else { var contentWidth = 'width=' + deviceWidth + 'px'; viewport.setAttribute(\"content\", contentWidth); } if(deviceWidth >= 640){ contentSize = 'size3'; } else if(deviceWidth >= 480){ contentSize = 'size2'; } else if(deviceWidth >= 375){ contentSize = 'size1'; } else { contentSize = ''; } frame.setAttribute(\"class\", contentSize); }(document));</script> <h4>Terms & Conditions</h4> <ul><li>This voucher can only be used once.</li><li>This voucher is redeemable against the original price of the item.</li><li>This voucher is not exchangeable for cash.</li><li>This voucher is valid for 2 months upon the issued date.</li><li>Only one voucher can be redeemed per transaction.</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/d7383394-1541-4a94-82af-0fa10f74d178.jpg"}]}, {"name": "NOVELA $30 Cash Voucher", "description": "Collect 4 stamps to get $30 Cash Voucher.\r\nGet 1 stamp with min. S$150 spend in a single receipt at Novela stores.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta id=\"viewport\" name=\"viewport\"><style>@font-face { font-family: MelbourneOld; src: url('fonts/Melbourne.ttf');}@font-face { font-family: MelbourneOld; font-weight: bold; src: url('fonts/Melbourne-Bold.ttf');}@font-face { font-family: Melbourne; src: url('fonts/Melbourne.otf');}@font-face { font-family: Melbourne; font-weight: bold; src: url('fonts/Melbourne-Bold.otf');}* { font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif; -webkit-text-size-adjust: none; margin: 0; padding: 0;}body { font-size: 16px;/* EN 16px ZH 14px JA 13px Ko 13px */}#frame { font-size: 1em; margin: 5px 15px 1em;}ul { padding: 0 0 0 1.13em; list-style-type: disc;}li { padding: 0 0 0.32em 0; vertical-align: top; text-align: left; font-size: 1em; line-height: 1.2em; color: #FFFFFF; color: rgba(255,255,255,0.6);}h4 { font-weight: bold; font-size: 1em; color: #FFFFFF; color: rgba(255,255,255,0.8); margin: 0 0 0.32em;}#frame.size1 { font-size: 1.16em;}#frame.size2 { font-size: 1.5em;}#frame.size3 { font-size: 2em;}#frame.size4 { font-size: 2.3em;}#frame.size5 { font-size: 2.8em;}</style><title>Terms &amp; Conditions</title></head><body><div id=\"frame\" class=\"\"> <script> (function(doc) { var frame = document.getElementById('frame'); var deviceWidth; var contentSize; if(window.screen.width){ deviceWidth = window.screen.width; } else { deviceWidth = 320; } if (navigator.userAgent.indexOf('Android') >= 0) { viewport.setAttribute(\"content\", \"width=device-width\"); if(window.devicePixelRatio){ deviceWidth = deviceWidth / window.devicePixelRatio; } } else { var contentWidth = 'width=' + deviceWidth + 'px'; viewport.setAttribute(\"content\", contentWidth); } if(deviceWidth >= 640){ contentSize = 'size3'; } else if(deviceWidth >= 480){ contentSize = 'size2'; } else if(deviceWidth >= 375){ contentSize = 'size1'; } else { contentSize = ''; } frame.setAttribute(\"class\", contentSize); }(document));</script> <h4>Terms &amp; Conditions</h4> <ul><li>This voucher can only be used once.</li><li>This voucher is redeemable against the original price of the item.</li><li>This voucher is not exchangeable for cash.</li><li>This voucher is valid for 2 months upon the issued date.</li><li>Only one voucher can be redeemed per transaction.</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/71691401-51b9-4a16-9771-773d5a3dac51.jpg"}]}, {"name": "NOVELA $50 Cash Voucher", "description": "Collect 6 stamps to get $50 Cash Voucher.\r\nGet 1 stamp with min. S$150 spend in a single receipt at Novela stores.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta id=\"viewport\" name=\"viewport\"><style>@font-face { font-family: MelbourneOld; src: url('fonts/Melbourne.ttf');}@font-face { font-family: MelbourneOld; font-weight: bold; src: url('fonts/Melbourne-Bold.ttf');}@font-face { font-family: Melbourne; src: url('fonts/Melbourne.otf');}@font-face { font-family: Melbourne; font-weight: bold; src: url('fonts/Melbourne-Bold.otf');}* { font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif; -webkit-text-size-adjust: none; margin: 0; padding: 0;}body { font-size: 16px;/* EN 16px ZH 14px JA 13px Ko 13px */}#frame { font-size: 1em; margin: 5px 15px 1em;}ul { padding: 0 0 0 1.13em; list-style-type: disc;}li { padding: 0 0 0.32em 0; vertical-align: top; text-align: left; font-size: 1em; line-height: 1.2em; color: #FFFFFF; color: rgba(255,255,255,0.6);}h4 { font-weight: bold; font-size: 1em; color: #FFFFFF; color: rgba(255,255,255,0.8); margin: 0 0 0.32em;}#frame.size1 { font-size: 1.16em;}#frame.size2 { font-size: 1.5em;}#frame.size3 { font-size: 2em;}#frame.size4 { font-size: 2.3em;}#frame.size5 { font-size: 2.8em;}</style><title>Terms & Conditions</title></head><body><div id=\"frame\" class=\"\"> <script> (function(doc) { var frame = document.getElementById('frame'); var deviceWidth; var contentSize; if(window.screen.width){ deviceWidth = window.screen.width; } else { deviceWidth = 320; } if (navigator.userAgent.indexOf('Android') >= 0) { viewport.setAttribute(\"content\", \"width=device-width\"); if(window.devicePixelRatio){ deviceWidth = deviceWidth / window.devicePixelRatio; } } else { var contentWidth = 'width=' + deviceWidth + 'px'; viewport.setAttribute(\"content\", contentWidth); } if(deviceWidth >= 640){ contentSize = 'size3'; } else if(deviceWidth >= 480){ contentSize = 'size2'; } else if(deviceWidth >= 375){ contentSize = 'size1'; } else { contentSize = ''; } frame.setAttribute(\"class\", contentSize); }(document));</script> <h4>Terms & Conditions</h4> <ul><li>This voucher can only be used once.</li><li>This voucher is redeemable against the original price of the item.</li><li>This voucher is not exchangeable for cash.</li><li>This voucher is valid for 2 months upon the issued date.</li><li>Only one voucher can be redeemed per transaction.</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/606c7b82-6897-4ff5-8c84-c27c92c8c901.png"}]}, {"name": "NOVELA $70 Cash Voucher", "description": "Collect 8 stamps to get $70 Cash Voucher.\r\nGet 1 stamp with min. S$150 spend in a single receipt at Novela stores.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta id=\"viewport\" name=\"viewport\"><style>@font-face { font-family: MelbourneOld; src: url('fonts/Melbourne.ttf');}@font-face { font-family: MelbourneOld; font-weight: bold; src: url('fonts/Melbourne-Bold.ttf');}@font-face { font-family: Melbourne; src: url('fonts/Melbourne.otf');}@font-face { font-family: Melbourne; font-weight: bold; src: url('fonts/Melbourne-Bold.otf');}* { font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif; -webkit-text-size-adjust: none; margin: 0; padding: 0;}body { font-size: 16px;/* EN 16px ZH 14px JA 13px Ko 13px */}#frame { font-size: 1em; margin: 5px 15px 1em;}ul { padding: 0 0 0 1.13em; list-style-type: disc;}li { padding: 0 0 0.32em 0; vertical-align: top; text-align: left; font-size: 1em; line-height: 1.2em; color: #FFFFFF; color: rgba(255,255,255,0.6);}h4 { font-weight: bold; font-size: 1em; color: #FFFFFF; color: rgba(255,255,255,0.8); margin: 0 0 0.32em;}#frame.size1 { font-size: 1.16em;}#frame.size2 { font-size: 1.5em;}#frame.size3 { font-size: 2em;}#frame.size4 { font-size: 2.3em;}#frame.size5 { font-size: 2.8em;}</style><title>Terms &amp; Conditions</title></head><body><div id=\"frame\" class=\"\"> <script> (function(doc) { var frame = document.getElementById('frame'); var deviceWidth; var contentSize; if(window.screen.width){ deviceWidth = window.screen.width; } else { deviceWidth = 320; } if (navigator.userAgent.indexOf('Android') >= 0) { viewport.setAttribute(\"content\", \"width=device-width\"); if(window.devicePixelRatio){ deviceWidth = deviceWidth / window.devicePixelRatio; } } else { var contentWidth = 'width=' + deviceWidth + 'px'; viewport.setAttribute(\"content\", contentWidth); } if(deviceWidth >= 640){ contentSize = 'size3'; } else if(deviceWidth >= 480){ contentSize = 'size2'; } else if(deviceWidth >= 375){ contentSize = 'size1'; } else { contentSize = ''; } frame.setAttribute(\"class\", contentSize); }(document));</script> <h4>Terms &amp; Conditions</h4> <ul><li>This voucher can only be used once.</li><li>This voucher is redeemable against the original price of the item.</li><li>This voucher is not exchangeable for cash.</li><li>This voucher is valid for 2 months upon the issued date.</li><li>Only one voucher can be redeemed per transaction.</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/c3.perkd.me/reward/cb20f5dd-7dd4-469a-b4c8-8e5f9d5ebc09.png"}]}]}}, "default": "en"}, "purgeTime": "2022-11-09T15:59:59.999Z", "when": {"sent": "2020-11-14T08:52:24.460Z", "issued": "2020-11-14T08:52:24.460Z", "received": "2020-11-14T09:51:25.856Z", "notified": "2020-11-14T09:33:35.139Z", "viewed": "2020-11-14T09:33:35.139Z", "extended": null, "withdrawn": null, "completed": null, "cancelled": null, "recovered": null}, "reasons": {}, "tenant": {"code": "novelasg"}, "createdAt": "2020-11-14T08:52:24.916Z", "modifiedAt": "2020-11-17T16:33:36.079Z", "deletedAt": null, "id": "5faf9ac813303539e9a21379", "cardId": "5faa13e3c4dffc61e5667ffb", "personId": "5f6c8b5a74a3c0718b094d1d"}]}