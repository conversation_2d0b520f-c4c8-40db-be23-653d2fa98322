{"offers": [{"offerId": "abc", "images": [{"url": "https://xxx"}], "masterId": "821897003178", "name": "$ OFF ACROSS ALL", "discount": {"name": "$10 OFF ACROSS ALL ITEMS", "qualifiers": {}, "kind": "fixed", "value": 10, "currency": "SGD", "use": "combine", "targetType": "item", "targetSelection": "all", "allocation": {"method": "across", "limit": 1, "remain": 1}}, "redemption": {"limit": 1, "remain": 1}}, {"offerId": "abc", "images": [{"url": "https://xxx"}], "masterId": "821900837034", "name": "% OFF ACROSS ALL", "discount": {"name": "10% OFF ACROSS ALL ITEMS", "qualifiers": {}, "kind": "percentage", "value": 0.1, "currency": "SGD", "use": "combine", "targetType": "item", "targetSelection": "all", "allocation": {"method": "across", "limit": 1, "remain": 1}}, "redemption": {"limit": 1, "remain": 1}}, {"offerId": "abc", "images": [{"url": "https://xxx"}], "masterId": "821901394090", "name": "% OFF ACROSS ENTITLED", "discount": {"name": "5% OFF ACROSS ENTITLED", "qualifiers": {"redeem": {}, "prerequisite": {}, "entitled": {"variantId": {"$in": ["37073447026858", "37073447911594"]}}}, "kind": "percentage", "value": 0.05, "currency": "SGD", "use": "combine", "targetType": "item", "targetSelection": "entitled", "allocation": {"method": "across", "limit": 1, "remain": 1}}, "redemption": {"limit": 1, "remain": 1}}, {"offerId": "abc", "images": [{"url": "https://xxx"}], "masterId": "821903327402", "name": "$ OFF EACH ENTITLED", "discount": {"name": "$123.45 OFF EACH ENTITLED", "qualifiers": {"redeem": {}, "prerequisite": {}, "entitled": {"variantId": {"$in": ["37073446240426", "37073447026858"]}}}, "kind": "fixed", "value": 123.45, "currency": "SGD", "use": "combine", "targetType": "item", "targetSelection": "entitled", "allocation": {"method": "each", "limit": null, "remain": null}}, "redemption": {"limit": 1, "remain": 1}}, {"offerId": "abc", "images": [{"url": "https://xxx"}], "masterId": "821903818922", "name": "$ OFF ACROSS ENTITLED", "discount": {"name": "$81.95 OFF ACROSS ENTITLED", "qualifiers": {"redeem": {"$sum": {"property": "quantity", "query": {"variantId": {"$in": ["37073446240426", "37073447911594"]}}, "compare": {"$gte": 3}}}, "prerequisite": {"variantId": {"$in": ["37073446240426", "37073447911594"]}}, "entitled": {"variantId": {"$in": ["37073446240426", "37073447911594"]}}}, "kind": "fixed", "value": 81.95, "currency": "SGD", "use": "combine", "targetType": "item", "targetSelection": "entitled", "allocation": {"method": "across", "limit": 1, "remain": 1}}, "redemption": {"limit": 1, "remain": 1}}, {"offerId": "abc", "images": [{"url": "https://xxx"}], "masterId": "837077565610", "name": "Buy 2XY get 1XZ FREE", "discount": {"name": "PWP Buy 2X (A or E), Get 1Y (A or D) FREE", "qualifiers": {"redeem": {"$sum": {"property": "quantity", "query": {"variantId": {"$in": ["37073446240426", "37084994896042"]}}, "compare": {"$gte": 2}}}, "prerequisite": {"variantId": {"$in": ["37073446240426", "37084994896042"]}}, "entitled": {"variantId": {"$in": ["37073446240426", "37084934537386"]}}}, "entitled": {"prerequisiteQuantity": 2, "entitledQuantity": 1, "items": []}, "kind": "percentage", "value": 1, "currency": "SGD", "use": "combine", "targetType": "item", "targetSelection": "entitled", "allocation": {"method": "each", "limit": 3, "remain": 3}}, "redemption": {"limit": 1, "remain": 1}}, {"offerId": "abc", "images": [{"url": "https://xxx"}], "masterId": "837083660458", "name": "$ OFF EACH ENTITLED", "discount": {"name": "$50 OFF on each Product B & D", "qualifiers": {"redeem": {}, "prerequisite": {}, "entitled": {"variantId": {"$in": ["37073447026858", "37084934537386"]}}}, "kind": "fixed", "value": 50, "currency": "SGD", "use": "combine", "targetType": "item", "targetSelection": "entitled", "allocation": {"method": "each", "limit": null, "remain": null}}, "redemption": {"limit": 1, "remain": 1}}, {"offerId": "toQualify", "images": [{"url": "https://xxx"}], "masterId": "1", "name": "MIN $500 to get 10% OFF ALL ITEMS", "discount": {"name": "MIN $500 to get 10% OFF ALL ITEMS", "qualifiers": {"redeem": {"subtotalPrice": {"$gte": 500}}, "prerequisite": {}, "entitled": {"variantId": {"$in": ["37073447026858", "37084934537386"]}}}, "kind": "percentage", "value": 0.1, "currency": "SGD", "use": "combine", "targetType": "item", "targetSelection": "all", "allocation": {"method": "each", "limit": null, "remain": null}}, "redemption": {"limit": 1, "remain": 1}}]}