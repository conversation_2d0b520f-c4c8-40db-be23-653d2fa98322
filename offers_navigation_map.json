{"screens": [{"id": "offer_list", "name": "Offer List", "type": "Primary Screen", "file_path": "src/containers/Offer/List.js", "route_path": "OfferList", "content_summary": "Displays a sectioned list of offers grouped by status (upcoming, available, redeemed, expired, etc.) with preview images and metadata", "tags": ["list", "offers", "status", "grouped"], "ui_elements_of_note": [{"name": "Section Headers", "type": "header", "navigation_role": "Organizes offers by status categories (upcoming, available, redeemed, expired)"}, {"name": "Offer List Items", "type": "card", "navigation_role": "Tappable items that navigate to individual offer detail view"}, {"name": "Unread Badge", "type": "indicator", "navigation_role": "Visual indicator for unread offers"}, {"name": "Total Badge", "type": "indicator", "navigation_role": "Shows count when multiple offers are grouped under same master"}], "entry_conditions": "Accessed when user has multiple offers available from a card", "user_actions_available": ["tap_offer_item", "scroll", "back"]}, {"id": "offers_detail", "name": "Offers Detail", "type": "Primary Screen", "file_path": "src/containers/Offer/Offers.js", "route_path": "Offers", "content_summary": "Paginated view showing individual offer details with swipeable cards, redemption options, and sharing capabilities", "tags": ["detail", "offers", "swipe", "redeem"], "ui_elements_of_note": [{"name": "ViewPager", "type": "carousel", "navigation_role": "Allows horizontal swiping between multiple offers"}, {"name": "Close Button", "type": "button", "navigation_role": "Closes the offer detail view and returns to previous screen"}, {"name": "Pagination TopBar", "type": "component", "navigation_role": "Shows current offer position and total count"}], "entry_conditions": "Accessed from offer list item tap, card widget, or direct navigation with offer index", "user_actions_available": ["swipe_horizontal", "close", "redeem", "share", "view_shop"]}, {"id": "offer_item_detail", "name": "Offer <PERSON><PERSON>", "type": "Component", "file_path": "src/components/Offer/render/OfferItem.js", "route_path": null, "content_summary": "Detailed view of individual offer with image, description, terms, redemption slider, and action buttons", "tags": ["component", "detail", "redemption", "actions"], "ui_elements_of_note": [{"name": "Offer Image", "type": "image", "navigation_role": "Visual representation of the offer with parallax scroll effect"}, {"name": "Slide to Redeem", "type": "slider", "navigation_role": "Interactive slider that triggers offer redemption flow"}, {"name": "Share Button", "type": "button", "navigation_role": "Opens sharing interface for transferring offers"}, {"name": "Shop Button", "type": "button", "navigation_role": "Opens shopping interface or external app"}, {"name": "Status View", "type": "card", "navigation_role": "Shows offer status and may trigger status-specific actions"}], "entry_conditions": "Rendered within Offers Detail screen as individual offer content", "user_actions_available": ["scroll", "slide_to_redeem", "share", "shop", "select_redemption_mode"]}, {"id": "offer_redeem", "name": "Offer Redemption", "type": "Modal", "file_path": "src/containers/Offer/Redeem.js", "route_path": "<PERSON>er<PERSON><PERSON><PERSON>", "content_summary": "Full-screen redemption interface showing barcode, redemption code, and offer details for in-store or online use", "tags": ["modal", "redemption", "barcode", "code"], "ui_elements_of_note": [{"name": "Barcode Display", "type": "barcode", "navigation_role": "Visual barcode for scanning at point of sale"}, {"name": "Redemption Code", "type": "text", "navigation_role": "Tappable code that can be copied to clipboard"}, {"name": "<PERSON>", "type": "button", "navigation_role": "Closes redemption screen and returns to previous view"}, {"name": "Copy Code <PERSON>", "type": "button", "navigation_role": "Copies redemption code for online use"}, {"name": "Authentication Animation", "type": "animation", "navigation_role": "Visual feedback during code authentication"}], "entry_conditions": "Triggered after successful offer redemption slide or when skipCode is false", "user_actions_available": ["copy_code", "long_press_code", "done", "scroll"]}, {"id": "ticket_item_detail", "name": "Ticket Item <PERSON>ail", "type": "Component", "file_path": "src/components/Offer/TicketItem.js", "route_path": null, "content_summary": "Specialized ticket view with check-in functionality, event details, calendar integration, and location services", "tags": ["component", "ticket", "checkin", "event"], "ui_elements_of_note": [{"name": "Check-in Button", "type": "button", "navigation_role": "Initiates NFC/QR code scanning for event check-in"}, {"name": "Add to Calendar", "type": "button", "navigation_role": "Adds event to device calendar with reminder"}, {"name": "Navigation Button", "type": "button", "navigation_role": "Opens maps application for directions to venue"}, {"name": "Share Button", "type": "button", "navigation_role": "Shares ticket with other users"}, {"name": "Flip View", "type": "card", "navigation_role": "Toggles between front view and terms/conditions"}], "entry_conditions": "Rendered for ticket-type offers within the offers detail view", "user_actions_available": ["checkin", "add_calendar", "navigate_to_venue", "share", "flip_card", "recall_transfer"]}, {"id": "offer_front_image", "name": "Offer Front Image", "type": "Component", "file_path": "src/components/Offer/Front.js", "route_path": null, "content_summary": "Reusable offer image component with discount value overlay and placeholder handling", "tags": ["component", "image", "discount"], "ui_elements_of_note": [{"name": "Auto Resize Image", "type": "image", "navigation_role": "Displays offer promotional image with automatic sizing"}, {"name": "Discount Value Overlay", "type": "overlay", "navigation_role": "Shows discount percentage or value on top of image"}], "entry_conditions": "Used within offer detail views to display promotional images", "user_actions_available": ["view"]}], "transitions": [{"source_id": "card_detail_widget", "target_id": "offer_list", "action_type": "Tap", "element_involved": "Offer Widget", "conditions": "Multiple offers available for card", "transition_type": "<PERSON><PERSON>"}, {"source_id": "card_detail_widget", "target_id": "offers_detail", "action_type": "Tap", "element_involved": "Offer Widget", "conditions": "Single offer or direct offer access", "transition_type": "<PERSON><PERSON>"}, {"source_id": "offer_list", "target_id": "offers_detail", "action_type": "Tap", "element_involved": "Offer List Item", "conditions": "User selects specific offer from list", "transition_type": "<PERSON><PERSON>"}, {"source_id": "offers_detail", "target_id": "offer_redeem", "action_type": "Slide", "element_involved": "Slide to Redeem <PERSON>", "conditions": "Offer is redeemable and skipCode is false", "transition_type": "<PERSON><PERSON>"}, {"source_id": "offer_redeem", "target_id": "offers_detail", "action_type": "Tap", "element_involved": "<PERSON>", "conditions": "User completes redemption or wants to return", "transition_type": "Pop"}, {"source_id": "offers_detail", "target_id": "share_interface", "action_type": "Tap", "element_involved": "Share Button", "conditions": "Offer supports sharing and user has share permissions", "transition_type": "Modal"}, {"source_id": "offers_detail", "target_id": "external_shop", "action_type": "Tap", "element_involved": "Shop Button", "conditions": "Offer has shopping integration enabled", "transition_type": "External"}, {"source_id": "offers_detail", "target_id": "card_detail", "action_type": "Tap", "element_involved": "Close Button", "conditions": "User wants to return to card view", "transition_type": "Pop"}, {"source_id": "offer_list", "target_id": "card_detail", "action_type": "Back", "element_involved": "Hardware Back Button", "conditions": "User navigates back from offer list", "transition_type": "Pop"}, {"source_id": "ticket_item_detail", "target_id": "nfc_scanner", "action_type": "Tap", "element_involved": "Check-in Button", "conditions": "Ticket requires NFC check-in", "transition_type": "Modal"}, {"source_id": "ticket_item_detail", "target_id": "qr_scanner", "action_type": "Tap", "element_involved": "Check-in Button", "conditions": "Ticket requires QR code scanning", "transition_type": "Modal"}, {"source_id": "ticket_item_detail", "target_id": "calendar_app", "action_type": "Tap", "element_involved": "Add to Calendar Button", "conditions": "User wants to add event to calendar", "transition_type": "External"}, {"source_id": "ticket_item_detail", "target_id": "maps_app", "action_type": "Tap", "element_involved": "Navigation Button", "conditions": "User wants directions to venue", "transition_type": "External"}, {"source_id": "offers_detail", "target_id": "offers_detail", "action_type": "Swipe", "element_involved": "ViewPager", "conditions": "Multiple offers available for horizontal navigation", "transition_type": "<PERSON> S<PERSON>"}]}