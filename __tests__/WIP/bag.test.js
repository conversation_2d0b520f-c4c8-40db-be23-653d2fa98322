import '../../__mocks__/globals';
import Bag from 'lib/checkout/bag';

import { bag as bagData } from '../../__mocks__/bag/bag.json';
import { cardMaster } from '../../__mocks__/bag/cardMasters.json';
import { products } from '../../__mocks__/bag/products.json';
import { offers } from '../../__mocks__/bag/offers.json';
import { reward } from '../../__mocks__/bag/rewards.json';

const self = {},
	CARD_ID = 'card_1',
	FULFILLMENT = {
		title: 'delivery',
		quantity: 1,
		unitPrice: 5,
		price: 5,
	},
	MEMBERSHIP_DISCOUNT_ID = '5f3a95637c3b4d670f439717',
	VOUCHER_40_ID = '5f3e2ec1e5f32f0f879345e4',
	VOUCHER_40 = offers.find(d => d.id === VOUCHER_40_ID),
	TONER_ID = '33436623536268',
	TONER = products.find(i => i.variantId === TONER_ID),
	JURLIQUE_SIG_SERUM_ID = '34051674669196',
	JURLIQUE_SIG_SERUM = products.find(i => i.variantId === JURLIQUE_SIG_SERUM_ID);	// unitPrice 130

beforeAll(() => {
	init();
	self.bag.addItems([{ ...JURLIQUE_SIG_SERUM, quantity: 1 }]);
	// self.bag.addItems([{ ...TONER, quantity: 1 }]);
});

describe('Discounts', () => {
	// -- item discount --

	test('Jurlique Signature Serum should save $49', () => {
		const serum = self.bag.items.find(i => i.variantId === JURLIQUE_SIG_SERUM_ID),
			{ discount } = serum || {};
		// 40($40 voucher) + 9(10% membership discount)
		expect(discount.amount).toBe(49);
	});

	test('should get free shipping', () => {
		const { fulfillment } = self.bag,
			{ unitPrice, price, discount } = fulfillment;

		expect(price).toBe(0);
		expect(discount.amount).toBe(unitPrice);
	});

	test('totalPrice should be $81', () => {
		// 130(unitPrice) - 40($40 voucher) - 9(10% membership discount) = 81
		console.log('>> self.bag', self.bag);
		expect(self.bag.totalPrice).toBe(81);
	});

	// -- shipping discount --
});

// --

function init() {
	const	{ id, payments = {}, commerce = {} } = cardMaster,
		{ currency } = payments,
		{ discounts = [], taxes = [], taxIncluded } = commerce,
		excludeDiscounts = [],
		policy = { discounts: [], excludeDiscounts, taxes, taxIncluded };

	self.masterDiscounts = discounts.map(d => ({ id,
		name: d.name,
		masterId: d.offerMasterId,
		discount: d,
		images: [{ url: '' }],
		redemption: { limit: null, remain: null },
		modify: false }));

	self.bag = new Bag(CARD_ID, { ...bagData, policy, currency, fulfillment: FULFILLMENT });

	self.bag.applyPolicy({ offers: [ ...self.masterDiscounts, ...offers ], reward });
}
