import '../../__mocks__/globals';
import Bag from 'lib/checkout/bag';

import { bag as bagData } from '../../__mocks__/bag/bag.json';

const self = {};

describe('unit test function priceAndTax', () => {
	const TAXES = [{ title: 'GST', rate: 0.07 }],
		ITEMS = [
			{ variantId: 'A', unitPrice: 348, quantity: 1 },
			// { variantId: 'B', unitPrice: 100, quantity: 1 },
		],
		FULFILLMENT = { unitPrice: 10, quantity: 1 },
		OFFERS = [];

	function unitTest() {
		return self.bag.priceAndTax();
	}

	test('price with tax included', () => {
		initBag(ITEMS, OFFERS, TAXES, true, FULFILLMENT);
		// console.log('>> bag_result', JSON.stringify(self.bag, null, 2));
		expect(unitTest()).toBe(1);
	});
});

// -- private functions -- //
function initBag(items, offers, taxes, taxIncluded, fulfillment = {}) {
	const policy = { taxes };

	self.bag = new Bag('id', 'test', { ...bagData, policy, fulfillment });
	self.bag.empty();
	self.bag.applyPolicy({ offers, taxIncluded });
	self.bag.addItems(items);
}
