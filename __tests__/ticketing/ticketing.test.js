import '../../__mocks__/globals';
import { forEach } from 'lodash';
import { Show, Shows, showOrder } from '../trials/Bag/ticketing2';

import { data as Data } from '../../__mocks__/ticketing/event.json';

const myShows = new Shows(Data),
	aShow2 = myShows.getShow('2020-12-23', '20:15'),
	aShow3 = myShows.getShow('2020-12-31', '21:30');

describe('test shows', () => {
	console.log('Show2 Before:', aShow2.prices);
	aShow2.add('1223-1000', 8); // cap at 4
	aShow2.deduct('1223-1000', 2);
	aShow2.deduct('1223-1000', 2);
	aShow2.deduct('1223-1000', 2);
	aShow2.add('1223-700', 3);
	aShow2.deduct('1223-700', 2);
	aShow2.add('1223-400', 5);
	aShow2.deduct('1223-700', 2);
	console.log('Show2 After:', aShow2.prices);

	// aShow3.add('1231-700', 8); // cap at 4
	// aShow3.deduct('1231-700', 0);
	// aShow3.add('1231-400', 6); // no cap

	// console.log('Show3 After:', aShow3.prices);

	showOrder(myShows);

	test('each variant should be <= limit.variants.max', () => {
		// Show2.forEach((price) => {
		// });
		// expect(Show2Tickets).toBeLessThanOrEqual(Data.limit.variants.max);
	});

	test('sum of clustered variant.quantity should be <= limit.clusters.max', () => {
		const Show2Quantity = aShow2.prices[0].quantity + aShow2.prices[1].quantity + aShow2.prices[2].quantity;
		expect(Show2Quantity).toBeLessThanOrEqual(Data.limit.clusters[0].max);
	});

	test('cross clustered variant.quantity should be <= limit.clusters.max', () => {
		const Show2n3Quantity = aShow2.prices[1].quantity + aShow3.prices[2].quantity;
		expect(Show2n3Quantity).toBeLessThanOrEqual(Data.limit.clusters[1].max);
	});

	test('order quantity should be <= limit.quantity.max', () => {
		expect(myShows.order.quantity).toBeLessThanOrEqual(Data.limit.quantity.max);
	});
});
