import '../../__mocks__/globals';
import Bag from 'lib/checkout/bag';

import { bag as bagData } from '../../__mocks__/bag/bag.json';
import { products } from '../../__mocks__/bag/products.json';
import CASES from '../../__mocks__/bag/cases.json';

const self = {},
	JURLIQUE = mapItem('34051674669196'), // unitPrice: $130
	KIEHLS = mapItem('33436623536268'); // unitPrice: $95

/**
 * Unit tests of different offer discounts with a fixed set of items
 */

describe('Discount Basic Unit Test', () => {
	const x = 'all', // change to integer to test specific case
		start = x >= 0 ? x : 0,
		end = x >= 0 ? x + 1 : CASES.length;

	for (let i = start; i < end; i++) {
		const CASE = CASES[i],
			{ offers: OFFERS, items } = CASE,
			ITEMS = [
				{ ...JURLIQUE, quantity: 4 }, // total: $520
				{ ...KIEHLS, quantity: 1 }, // total: $95
			];	// subtotal: $615

		initBag(ITEMS, OFFERS);
		// console.log(`self.BAG: ${JSON.stringify(self.bag, null, 2)}`);

		describe(`${i} ${CASE.name}`, () => {
			const BAG = self.bag;
			let SUM_D = 0, SUM_P = 0;

			test('item price & discount calc', () => {
				for (let j = 0; j < ITEMS.length; j++) {
					const RESULT = BAG.items[j],
						EXPECT = items[j];

					SUM_D += RESULT.discount.amount;
					SUM_P += RESULT.price;

					expect(RESULT.discount.amount).toBe(EXPECT.discount.amount);
					expect(RESULT.price).toBe(EXPECT.price);
					expect(RESULT.price).not.toBeLessThan(0);
				}
			});

			test('total discounts', () => {
				const discounts = BAG.discountsApplied,
					savings = discounts.isArray && discounts.length ? 0 : BAG.discountsApplied[0].amount;

				expect(round(SUM_D)).toBe(savings);
				expect(round(SUM_D)).toBe(BAG.totalDiscounts);
			});

			test('subtotal price', () => {
				expect(round(SUM_P)).toBe(BAG.subtotalPrice);
			});
		});
	}
});

// -- private functions -- //
function initBag(items, offers) {
	self.bag = new Bag('id', { ...bagData });
	self.bag.empty();
	const errors = self.bag.applyPolicy({ offers });
	if (errors) {
		console.log(JSON.stringify(errors, null, 2));
	} else {
		self.bag.addItems(items);
	}
}

function mapItem(variantId) {
	return products.find(i => i.variantId === variantId);
}

function round(value, decimal = 2) {
	return Number(value.toFixed(decimal));
}
