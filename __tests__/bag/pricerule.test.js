import '../../__mocks__/globals';

import Bag from 'lib/checkout/bag';
import { bag as bagData } from '../../__mocks__/bag/bag.json';
import { offers } from '../../__mocks__/bag/offers.json';
import ORDERS from '../../__mocks__/shopify/orders.json';

/* How to use this test script
	1. Paste a Shopify Order Data into orders.json
	2. Create Offer in offers.json w/discounts === price rule of the discounts_applied in Order
	3. Set offer masterId = priceruleId
	4. Run this test to audit Bag Data against Shopify Data
 * */
const self = {};

describe('Bag Calculation should match w/Shopify Order', () => {
	const x = 'all', // change to integer to test specific case
		start = x >= 0 ? x : 0,
		end = x >= 0 ? x + 1 : ORDERS.length;

	for (let i = start; i < end; i++) {
		const { order } = ORDERS[i],
			ITEMS = transform(order.line_items),
			FULFILLMENT = transform2(order.shipping_lines)[0],
			TAXES = [{ title: order.tax_lines[0].title, rate: order.tax_lines[0].rate }],
			taxIncluded = order.taxes_included,
			OFFERS = [ mapOffer(order.discount_applications[0].code.slice(order.discount_applications[0].code.length - 12)) ];

		/* Bag calc should match Shopify's calc on
		order level     : tax_lines, total_price, subtotal_price, total_tax, total_discounts (exact match)
		line_items      : discount_allocations (float)
		shipping_lines  : discount_allocations, discounted_price (float)
	 * */

		describe(`${i} orderId: ${order.id}`, () => {
			beforeAll(async () => {
				await initBag(ITEMS, OFFERS, TAXES, taxIncluded, FULFILLMENT);
				// console.log(`BAG_RESULT: ${JSON.stringify(self.bag, null, 2)}`);
			});

			// check if discount applied in Order matches to Offers TO-DO
			test('matching shopify.pricerule & offer.discount', () => {
				expect(self.bag.totalPrice).toBe(Number(order.total_price));
			});
			// if match, continue testing, else skip to next one

			// order level metric audits
			test('totalPrice', () => {
				expect(self.bag.totalPrice).toBe(Number(order.total_price));
			});
			test('subtotalPrice', () => {
				expect(self.bag.subtotalPrice).toBe(Number(order.subtotal_price));
			});
			test('totalTax', () => {
				expect(self.bag.totalTax).toBeCloseTo(Number(order.total_tax), 1);
			});
			test('totalDiscounts', () => {
				expect(self.bag.totalDiscounts).toBe(Number(order.total_discounts));
			});

			// shipping & line item level metric audits
			test('discount allocations in each line item', () => {
				const RESULT = self.bag.items.reduce((acc, item) => acc + sumOfAmt(item.discount.allocations), 0),
					EXPECT = order.line_items.reduce((acc, item) => acc + sumOfAmt(item.discount_allocations), 0);
				expect(RESULT).toBeCloseTo(EXPECT);
			});
			test('discount allocation in fulfillment', () => {
				const discountType = self.bag.discountsApplied[0].targetType;

				if (discountType === 'shipping') {
					expect(self.bag.fulfillment.price).toBe(Number(order.shipping_lines[0].discounted_price));
					expect(self.bag.fulfillment.discount.allocations[0].amount).toBe(Number(order.shipping_lines[0].discount_allocations[0].amount));
				} else {
					expect(self.bag.fulfillment.discount.amount).toBe(0);
				}
			});
		});
	}
});

// -- private functions -- //
/**
 * Using: applyPolicy, addItems, setFulfillment
 */

function initBag(items, offers, taxes, taxIncluded, fulfillment) {
	self.bag = new Bag('id', { ...bagData, taxes, taxIncluded });
	self.bag.empty();

	const errors = self.bag.applyPolicy({ offers });
	if (errors) {
		console.log(`applyPolicy Invalid Offers: ${JSON.stringify(errors, null, 2)}`);
	} else self.bag.addItems(items);

	return self.bag.setFulfillment(fulfillment)
		.then(res => res)
		.catch(err => console.log(`setFulfill error: ${JSON.stringify(err, null, 2)}`));
}

function mapOffer(masterId) {
	return offers.find(i => i.masterId === masterId);
}

function transform(lineItems) {
	return lineItems.map((lineItem) => {
		const newItem = {
			title: lineItem.title,
			unitPrice: parseFloat(lineItem.price), // convert to number
			quantity: lineItem.quantity || 1,
			variantId: lineItem.variant_id.toString(), // convert to string
			taxes: lineItem.tax_lines.length > 0
				? [{ title: lineItem.tax_lines[0].title, rate: lineItem.tax_lines[0].rate }] : [],
		};
		return newItem;
	});
}

function transform2(shippingLines) {
	return shippingLines.map((shippingLine) => {
		const newShipping = {
			type: 'store',
			destination: { country: 'SG' },
			price: parseFloat(shippingLine.price),
			taxes: shippingLine.tax_lines.length > 0
				? [{ title: shippingLine.tax_lines[0].title, rate: shippingLine.tax_lines[0].rate }]
				: [{ title: 'skip tax', rate: 0 }],
		};
		return newShipping;
	});
}
function sumOfAmt(array) {
	return array.reduce((accum, current) => accum + Number(current.amount), 0);
}
