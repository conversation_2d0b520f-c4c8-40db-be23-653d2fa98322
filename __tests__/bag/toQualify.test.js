import '../../__mocks__/globals';
import Bag from 'lib/checkout/bag';

import { bag as bagData } from '../../__mocks__/bag/bag.json';
import { offers } from '../../__mocks__/bag/offers.json';
import { rewards } from '../../__mocks__/bag/rewards.json';

const self = {},
	ITEMS = [{ title: 'A', unitPrice: 95, quantity: 1, variantId: '37073446240426' }],
	OFFERS = mapOffers('toQualify'),
	REWARD = rewards.find(i => i.masterId === '591134249afb39c37d09253d');

describe('unit test function toQualifyOffers', () => {
	function unitTest() {
		return self.bag.toQualifyOffers();
	}

	test('offers to qualify', () => {
		initBag(ITEMS, OFFERS, REWARD);
		// console.log(`toQualify: ${JSON.stringify(self.bag.toQualify, null, 2)}`);
		expect(unitTest()).toBeTruthy();
	});
});

// -- private functions -- //
function initBag(items, offers, reward) {
	self.bag = new Bag('id', { ...bagData });
	self.bag.empty();
	self.bag.applyPolicy({ offers, reward });
	self.bag.addItems(items);
}

function mapOffers(testname) {
	return offers.filter(i => i.offerId === testname);
}
