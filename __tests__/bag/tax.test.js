import '../../__mocks__/globals';
import Bag from 'lib/checkout/bag';

import { bag as bagData } from '../../__mocks__/bag/bag.json';
import { products } from '../../__mocks__/bag/products.json';
import { offers } from '../../__mocks__/bag/offers.json';

const self = {},
	A = mapItem('37073446240426'),
	B = mapItem('37073447026858'),
	D = mapItem('37084934537386'),
	E = mapItem('37084994896042');

afterEach(() => {
	self.bag.empty();
});

describe('Basic Order-Level Taxes Only', () => {
	const TAXES = [{ title: 'GST', rate: 0.05 }],
		ITEMS = [{ ...A, quantity: 1 }, { ...B, quantity: 2 }, { ...D, quantity: 3 }];

	test('price inclusive tax', () => {
		initBag(ITEMS, [], TAXES, true);
		const TOTALTAX = round((sumOfPrice(self.bag.items) * TAXES[0].rate) / (1 + TAXES[0].rate));

		expect(self.bag.totalTax).toBe(TOTALTAX);
		expect(self.bag.totalPrice).toBe(self.bag.subtotalPrice);
	});

	test('price not inclusive tax', () => {
		initBag(ITEMS, [], TAXES, false);
		const TOTALTAX = round(sumOfPrice(self.bag.items) * TAXES[0].rate);

		expect(self.bag.totalTax).toBe(TOTALTAX);
		expect(self.bag.totalPrice - TOTALTAX).toBeCloseTo(self.bag.subtotalPrice);
	});
});

describe('Combined Item, Fulfillment & Order Taxes', () => {
	// Sum of I.F.O Tax should equal to bag.totalTax
	const TAXES = [{ title: 'GST', rate: 0.07 }],
		ITEMS = [
			{ ...A, quantity: 3 },
			{ ...D, quantity: 2, taxes: [{ title: 'Tax Override', rate: 0.02 }] },
			{ ...E, quantity: 2, taxes: [{ title: 'Skip Tax', rate: 0 }] },
		],
		FULFILLMENT = { type: 'store', destination: { country: 'SG' }, price: 10 };

	test('Price Inclusive Tax', () => {
		initBag(ITEMS, [], TAXES, true, FULFILLMENT);

		const SELF = self.bag,
			itemsTax = SELF.items.reduce((acc, item) => acc + sumOfPrice(item.taxes), 0),
			fulfillTax = sumOfPrice(SELF.fulfillment.taxes),
			orderTax = sumOfPrice(SELF.taxes),
			totalTax = round(itemsTax + fulfillTax + orderTax);

		expect(SELF.totalTax).toBe(totalTax);
		expect(SELF.subtotalPrice).toBe(sumOfPrice(SELF.items));
	});

	test('Price Not Inclusive Tax', () => {
		initBag(ITEMS, [], TAXES, false, FULFILLMENT);

		const SELF = self.bag,
			itemsTax = SELF.items.reduce((acc, item) => acc + sumOfPrice(item.taxes), 0),
			fulfillTax = sumOfPrice(SELF.fulfillment.taxes),
			orderTax = sumOfPrice(SELF.taxes),
			totalTax = round(itemsTax + fulfillTax + orderTax);

		expect(SELF.totalTax).toBe(totalTax);
		expect(SELF.totalPrice - totalTax - SELF.fulfillment.price).toBe(SELF.subtotalPrice);
	});
});

describe(' Combined Base Tax, Tax Override, Skip Tax, Offer', () => {
	/**
	 * reference: https://discount-tests.myshopify.com/admin/orders/3057217700010?orderListBeta=true
	 * 1. Add Item: 3A, 1B, 2E, 1D
	 * 2. Base 7% Tax across all products
	 * 2. No Tax on Shipping Fee
	 * 3. 16% VAT Tax Override on Product E
	 * 4. $50 Discount each on B & D
	 */

	const TAXES = [{ title: 'GST', rate: 0.07 }],
		FULFILLMENT = { type: 'store', destination: { country: 'SG' }, price: 20, taxes: [{ title: 'Skip Tax', rate: 0 }] },
		ITEMS = [
			{ ...A, quantity: 3 }, { ...B, quantity: 1 }, { ...D, quantity: 1 },
			{ ...E, quantity: 2, taxes: [{ title: 'VAT', rate: 0.16 }] },
		],
		OFFERS = [ offers.find(i => i.masterId === '837083660458') ];

	test('total tax, subtotal price & total discounts should match with Shopify', () => {
		initBag(ITEMS, OFFERS, TAXES, true, FULFILLMENT);
		// console.log(`BAG_RESULT: ${JSON.stringify(self.bag, null, 2)}`);

		expect(self.bag.totalTax).toBe(54.5);
		expect(self.bag.taxes[0].price).toBe(23.88);
		expect(self.bag.subtotalPrice).toBe(587);
		expect(self.bag.totalDiscounts).toBe(85);
	});
});

// -- private functions -- //

function initBag(items, offers, taxes, taxIncluded, fulfillment) {
	self.bag = new Bag('id', { ...bagData, taxes, taxIncluded, fulfillment });
	self.bag.addItems(items);

	const error = self.bag.applyPolicy({ offers });
	if (error) { console.log(`applyPolicy ERROR: ${JSON.stringify(error, null, 2)}`); }
}

function mapItem(variantId) {
	return products.find(i => i.variantId === variantId);
}

function sumOfPrice(array) {
	return array.reduce((accum, current) => accum + current.price, 0);
}

function round(value, decimal = 2) {
	return Number(value.toFixed(decimal));
}
